com.tencent.wea.xlsRes.starp.table_TextEntryData
excel/xls/W_SP文本表文本配置.xlsx sheet:Sheet1
rows {
  stringId: "UI_SPGame_Download_HDNotLoadTip_Button1"
  content: "立即加载"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_HDNotLoadTip_Button2"
  content: "取消"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_TransferTip"
  content: "目标点在初生岛屿外，【岛屿资源包】下载完成后才可前往哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_LogInTip"
  content: "角色位置在初生岛屿外，【岛屿资源包】下载完成后才可前往哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_VisitTip"
  content: "拜访好友需要先完成【岛屿资源包】下载哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_GVGTip"
  content: "参加掠夺战玩法需要先完成【岛屿资源包】下载哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_MutiPVETip"
  content: "挑战溯灵秘境需要先完成【岛屿资源包】下载哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_TowerTip"
  content: "挑战封印之塔需要先完成【岛屿资源包】下载哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_UnderlandTip"
  content: "挑战遗迹需要先完成【岛屿资源包】下载哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_MazeTip"
  content: "挑战栖地需要先完成【岛屿资源包】下载哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_AwardCantReceive"
  content: "资源包下载完成后可领取"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_AwardReceiveSuccess"
  content: "奖励已通过邮件发放！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_AwardReceived"
  content: "此奖励已领取过！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_WindowTitle"
  content: "资源下载"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_WindowPreUnfinished"
  content: "需先下载前置资源"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_WindowLoadResource"
  content: "加载资源"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_Pic1"
  content: "T_SP_BagDownload_Select1"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_Pic2"
  content: "T_SP_BagDownload_Select2"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Download_Pic3"
  content: "T_SP_BagDownload_Select3"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Get_Pet_Txt"
  content: "获得{0}啦！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Breed_Empty"
  content: "暂无可供繁育的材料"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Hatch_Empty"
  content: "暂无可供孵化的材料"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Hatch_NotEnough"
  content: "孵化器空间不足"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Attr_Def"
  content: "防御"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Attr_health"
  content: "生命"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Attr_Level"
  content: "级"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Room_RoleBanTip"
  content: "当前被封禁中，{0}:{1}:{2}后可前往世界"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Room_RoleDelCdTip"
  content: "世界管理员将您请离了世界，{0}:{1}:{2}后将离开世界"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_Progress"
  content: "<taskRed>{0}/{1}</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_MapProgress"
  content: "<taskMapRed>{0}/{1}</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_MapFinish"
  content: "<taskMapGreen>{0}/{1}</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_TemporaryTaskIcon"
  content: "T_UI_SP_Task_TaskIcon_04"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Free_Confirm"
  content: "是否确认与这些啾灵告别？(告别后啾灵会立即消失)"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Free_Success"
  content: "啾灵告别成功 "
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Free_SurplusCapacity"
  content: "放生后据点中枢容量：{0}/{1}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Split"
  content: "拆分"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Guild_NoTerminalTip"
  content: "该公会未拥有据点中枢"
  switch: 1
}
rows {
  stringId: "UI_SPGame_LevelUp"
  content: "等级提升至{0}级"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Remove_Terminal_Tip"
  content: "是否确定要拆除啾灵中枢？"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_ChainRewardTitle"
  content: "完成系列任务可获得"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_NoneChainRewardTitle"
  content: "完成任务可获得"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TeamPet_Lv"
  content: "{0}级"
  switch: 1
}
rows {
  stringId: "UI_SPGame_SearchFind"
  content: "搜索发现"
  switch: 1
}
rows {
  stringId: "UI_SPGame_HistoryRecord"
  content: "历史记录"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Flit_SortNumber"
  content: "编号"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Flit_Attribute"
  content: "属性"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Flit_All"
  content: "全部"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Flit_AlwaysExist"
  content: "常驻"
  switch: 1
}
rows {
  stringId: "UI_SPGame_UsePet_Lv"
  content: "角色等级不足{0}级，不能使用该啾灵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_GuildName"
  content: "公会名称："
  switch: 1
}
rows {
  stringId: "UI_SPGame_GuildMedal"
  content: "公会徽章："
  switch: 1
}
rows {
  stringId: "UI_SPGame_EstablishTitle"
  content: "创建公会"
  switch: 1
}
rows {
  stringId: "UI_SPGame_JoinGuildTitle"
  content: "加入公会"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_DescTip"
  content: "快来我的山海寻灵，和我一起探索建设吧！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Room_CreatingWorldSaveFailTip"
  content: "保存失败，请稍后再试！"
  switch: 1
}
rows {
  stringId: "UI_SPGameSettingFrame_QualityTips"
  content: "当前设备的画质设置推荐为{0}，设置更高档的画质将导致设备发热和卡顿"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_SettinFrameQuality_Low"
  content: "低"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_SettinFrameQuality_Middle"
  content: "标准"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_SettinFrameQuality_High"
  content: "高清"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_SettinFrameQuality_Super"
  content: "极致"
  switch: 1
}
rows {
  stringId: "UI_SPGame_MemberManagerTitle"
  content: "成员管理"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Guild_OwnTerminalJoinDescriptionUp"
  content: "与个人据点中枢对话，加入公会。"
  switch: 1
}
rows {
  stringId: "UI_SPGame_JoinMemberList"
  content: "申请列表"
  switch: 1
}
rows {
  stringId: "UI_SPGame_KickMemberList"
  content: "待踢出列表"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Xinwu_Dh"
  content: "还未获得该信物哦"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TeamPVE_SuccessLevelUnlock"
  content: "{0}副本已解锁"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TeamPVE_QuitCountDown"
  content: "{0}秒后自动退出场景"
  switch: 1
}
rows {
  stringId: "UI_Team_RecentPlayerData_Empty"
  content: "快邀请好友一起挑战副本吧"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Invasion_LevelText"
  content: "入侵等级{0}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Shop_Buy_BagIsFull"
  content: "背包已满，无法购买"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Shop_Buyback_BagIsFull"
  content: "背包已满，无法回购"
  switch: 1
}
rows {
  stringId: "UI_SPGame_AchieveLevel_Ordinary"
  content: "一般"
  switch: 1
}
rows {
  stringId: "UI_SPGame_AchieveLevel_Uncommon"
  content: "稀有"
  switch: 1
}
rows {
  stringId: "UI_SPGame_AchieveLevel_Rare"
  content: "罕见"
  switch: 1
}
rows {
  stringId: "UI_SPGame_AchieveLevel_Rarity"
  content: "珍贵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PartnerWeapon_SummonErrorTip"
  content: "请解除啾灵武器后重试"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PartnerWeapon_NoSaddleTip"
  content: "请解锁啾灵的背带后再试"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PartnerWeapon_HaveSaddleTip"
  content: "长按右侧按钮召唤啾灵至身边装备"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Thermometer_ColdTips"
  content: "当前气温过低"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Thermometer_NormalTips"
  content: "当前气温适宜"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Thermometer_HotTips"
  content: "当前气温过高"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_BanPlayerLeaveTip"
  content: "你已经被世界管理员封禁，即将离开世界！"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_BanPlayerEnterTip"
  content: "你已经被世界管理员封禁，{0}后方可进入世界！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PartnerSkill_ColdDownTips"
  content: "技能冷却中！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Ride_HaveSaddleTip"
  content: "长按右侧按钮召唤啾灵至身边乘骑"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Ride_NoSaddleTip"
  content: "请解锁啾灵的鞍具后再试"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Task_CancelTaskTip"
  content: "已取消任务追踪"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Feed_Tip"
  content: "{0}吃了{1}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Transfer_Guild_Leader_Tip"
  content: "是否将公会会长转移至{0}，您将失去公会的管理权限？"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Kick_Member_Tip"
  content: "是否确认将该成员移除部落?"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Cannot_Dismiss_Guild"
  content: "当前部落存在其他玩家，无法关闭加速功能"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Refrigerator _Full"
  content: "电冰箱已满"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Freezer_Full"
  content: "冷冻箱已满"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_DeletePlayer"
  content: "48小时后将永久从房间中踢出该玩家，是否确定？48h内可在房间成员管理处撤销踢出操作。"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_AlreadyDeletePlayer"
  content: "你已经被世界管理员踢出世界！"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_PreDeletePlayer"
  content: "你已被世界管理员踢出世界，{0}后将离开世界！在此期间内世界管理员可撤销踢出操作。"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_DoNotDeletePlayer"
  content: "只能踢出角色等级小于{0}级的玩家！"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_ManagerTransferTip"
  content: "管理员身份已转让！"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Room_ApplyManagerTip"
  content: "该房间现可继承管理员权限，是否继承管理员权限？"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_Guild_ConfirmJoin"
  content: "是否确认加入{0}?"
  switch: 1
}
