syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";

message SPWorldPVPEntranceData {
    option (resKey) = "Id";
    optional int32 Id = 1;  //入口ID
    optional string BlueprintPath = 2; // 入口交互对象BP
    optional string InteractType = 3; // 入口交互类型
    optional string PVPEnName = 4; // 入口名称
    optional int32 IsClose = 5; //是否关闭
    optional int32 PosX = 6; // 交互对象X坐标
    optional int32 PosY = 7; // 交互对象Y坐标
    optional int32 PosZ = 8; // 交互对象Z坐标
    optional int32 RotZ = 9; // 交互对象Z轴旋转
    optional int32 Offset = 10; // 入口位置角色偏移
}

message table_SPWorldPVPEntranceData {
  repeated SPWorldPVPEntranceData rows = 1;
}
