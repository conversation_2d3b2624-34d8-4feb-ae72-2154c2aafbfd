syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";

message SPTriggerTlogConfig {
	option (resKey) = "id";
	optional int32 id = 1;  // id
	optional SPMainProcessCompleteType completeType = 2;	//触发类型
	repeated string completeParams = 3; // 触发参数
	optional int32 reportId = 4;	// 上报Id
	optional SPMainProcessReportType reportType = 5;	// 上报方式
	repeated string reportParams = 6; // 上报参数
	optional bool isReportPos = 7;	// 是否上报位置
	optional bool isReportMaterial = 8;	// 是否上报核心材料数量
}

message table_SPTriggerTlogConfig {
  repeated SPTriggerTlogConfig rows = 1;
}