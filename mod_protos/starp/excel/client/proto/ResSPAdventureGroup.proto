syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";
import "ResSPConditionStarP.proto";

// 冒险团加速表
message SPAdventureGroupSpeedUp { // @noSvr
    option (resKey) = "level";
    optional int32 level = 1;         // 玩家等级
    optional int32 speedUpExpMax = 2; // 加速经验额度
    optional int32 ratio = 3;         // 加速倍数(万分比)
}

message table_SPAdventureGroupSpeedUp {// @noSvr
    repeated SPAdventureGroupSpeedUp rows = 1;
}