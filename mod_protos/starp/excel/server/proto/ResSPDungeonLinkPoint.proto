syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";

message SPDungeonLinkPoint {
    option (resKey) = "Id";
    optional int32 Id = 1;  // Id
    optional int32 MapID = 2; //MapID
    optional int32 PointID = 3; // 接口ID
    optional float PosX = 4;  // 坐标X
    optional float PosY = 5;  // 坐标Y
    optional float PosZ = 6;  // 坐标Z
    optional float RotX = 7;  // 旋转X
    optional float RotY = 8;  // 旋转Y
    optional float RotZ = 9;  // 旋转Z
}

message table_SPDungeonLinkPoint {
  repeated SPDungeonLinkPoint rows = 1;
}
