com.tencent.wea.xlsRes.starp.table_SPLotteryReward
excel/xls/J_SP奖池奖励配置表.xlsx sheet:扭蛋机奖励
rows {
  rewardId: 1001001
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3001
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品1"
}
rows {
  rewardId: 1001002
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3002
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品2"
}
rows {
  rewardId: 1001003
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3003
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品3"
}
rows {
  rewardId: 1001004
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3004
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品4"
}
rows {
  rewardId: 1001005
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3005
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品5"
}
rows {
  rewardId: 1001006
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3006
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品6"
}
rows {
  rewardId: 1001007
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3007
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品7"
}
rows {
  rewardId: 1001008
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3008
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品8"
}
rows {
  rewardId: 1001009
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3009
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品9"
}
rows {
  rewardId: 1001010
  poolId: 1001
  groupId: 1
  itemTypes: SPLRIT_LetsGo
  itemIds: 3010
  itemNums: 1
  weight: 10
  limit: 1
  name: "测试物品10"
}
