com.tencent.wea.xlsRes.starp.table_SPLocationTrace
excel/xls/Z_SP坐标追踪表.xlsx sheet:任务组
rows {
  jumpId: 1001
  locationType: SPLT_FixPoint
  posX: 179850.0
  posY: 243639.0
  posZ: 15004.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1002
  locationType: SPLT_FixPoint
  posX: 183700.0
  posY: 234450.0
  posZ: 12700.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 800
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1003
  locationType: SPLT_NPC
  value: 201001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 450
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1004
  locationType: SPLT_FixPoint
  posX: 154600.0
  posY: 210210.0
  posZ: 3130.0
  radius: 4000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1005
  locationType: SPLT_Recommand
  value: 1006
  value: 1007
  value: 1008
  value: 1009
  value: 1010
  value: 1071
  value: 1072
  value: 1073
  recommand {
    num: 1
    cond {
      conditionType: SPTRCT_TerminalDistanceOver
      value: 5000
    }
  }
}
rows {
  jumpId: 1006
  locationType: SPLT_FixPoint
  posX: 145350.0
  posY: 228211.0
  posZ: 4050.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1007
  locationType: SPLT_FixPoint
  posX: 156850.0
  posY: 255000.0
  posZ: 4350.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1008
  locationType: SPLT_FixPoint
  posX: 179650.0
  posY: 229550.0
  posZ: 11150.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1009
  locationType: SPLT_FixPoint
  posX: 181675.0
  posY: 268030.0
  posZ: 462.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1010
  locationType: SPLT_FixPoint
  posX: 174394.0
  posY: 204572.0
  posZ: 5172.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1071
  locationType: SPLT_FixPoint
  posX: 109078.0
  posY: 221475.0
  posZ: 370.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1072
  locationType: SPLT_FixPoint
  posX: 128400.0
  posY: 169000.0
  posZ: 5500.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1073
  locationType: SPLT_FixPoint
  posX: 147035.0
  posY: 133220.0
  posZ: 522.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1011
  locationType: SPLT_FixPoint
  posX: 163250.0
  posY: 243450.0
  posZ: 9200.0
  radius: 3000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1012
  locationType: SPLT_FixPoint
  posX: 145900.0
  posY: 239100.0
  posZ: 4500.0
  radius: 3000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1013
  locationType: SPLT_FixPoint
  posX: 130750.0
  posY: 237350.0
  posZ: 4300.0
  radius: 3000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1014
  locationType: SPLT_NPC
  value: 208005
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1015
  locationType: SPLT_Underland
  value: 201003
  value: 201004
  value: 201016
  radius: 1000
  recommand {
    num: 1
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1016
  locationType: SPLT_Underland
  value: 201009
  value: 201014
  value: 201012
  radius: 1000
  recommand {
    num: 1
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1017
  locationType: SPLT_Maze
  value: 301001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1018
  locationType: SPLT_Building
  value: 1001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1019
  locationType: SPLT_Building
  value: 1011
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1020
  locationType: SPLT_Building
  value: 1008
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1021
  locationType: SPLT_Building
  value: 1009
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1022
  locationType: SPLT_Building
  value: 1010
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1055
  locationType: SPLT_Building
  value: 1012
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1070
  locationType: SPLT_Building
  value: 1005
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1023
  locationType: SPLT_Building
  value: 2001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 500
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1024
  locationType: SPLT_Recommand
  value: 1025
  value: 1026
  value: 1028
  value: 1029
  recommand {
    num: 1
    cond {
      conditionType: SPTRCT_TerminalDistanceOver
      value: 5000
    }
  }
}
rows {
  jumpId: 1025
  locationType: SPLT_FixPoint
  posX: 106225.0
  posY: 130160.0
  posZ: 1722.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1026
  locationType: SPLT_FixPoint
  posX: 65367.0
  posY: 119434.0
  posZ: 1781.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1027
  locationType: SPLT_FixPoint
  posX: 54550.0
  posY: 84310.0
  posZ: 10550.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1028
  locationType: SPLT_FixPoint
  posX: 95922.0
  posY: 82965.0
  posZ: 8921.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1029
  locationType: SPLT_FixPoint
  posX: 89598.0
  posY: 64345.0
  posZ: 3544.0
  radius: 2500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1030
  locationType: SPLT_FixPoint
  posX: 100399.0
  posY: 142645.0
  posZ: 4589.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1031
  locationType: SPLT_FixPoint
  posX: 116981.0
  posY: 105681.0
  posZ: 5168.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1052
  locationType: SPLT_FixPoint
  posX: 125792.0
  posY: 109482.0
  posZ: 7675.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1053
  locationType: SPLT_FixPoint
  posX: 128256.0
  posY: 102509.0
  posZ: 7321.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1054
  locationType: SPLT_FixPoint
  posX: 130122.0
  posY: 94632.0
  posZ: 6948.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1032
  locationType: SPLT_FixPoint
  posX: -54300.0
  posY: 74850.0
  posZ: 11250.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 1056
  locationType: SPLT_FixPoint
  posX: -55040.0
  posY: 75174.0
  posZ: 11225.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1057
  locationType: SPLT_FixPoint
  posX: -54933.0
  posY: 73882.0
  posZ: 11219.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1058
  locationType: SPLT_FixPoint
  posX: -60056.0
  posY: 62524.0
  posZ: 13526.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1033
  locationType: SPLT_Maze
  value: 301002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1034
  locationType: SPLT_Maze
  value: 302001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1035
  locationType: SPLT_Maze
  value: 303001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1036
  locationType: SPLT_Maze
  value: 303002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1037
  locationType: SPLT_Maze
  value: 304001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1038
  locationType: SPLT_Maze
  value: 304002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1039
  locationType: SPLT_Maze
  value: 305001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1040
  locationType: SPLT_WorldBoss
  value: 201001002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1041
  locationType: SPLT_WorldBoss
  value: 201001003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1042
  locationType: SPLT_WorldBoss
  value: 201002001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1043
  locationType: SPLT_WorldBoss
  value: 201003002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1044
  locationType: SPLT_WorldBoss
  value: 201003003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1045
  locationType: SPLT_WorldBoss
  value: 201003004
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1046
  locationType: SPLT_WorldBoss
  value: 201004001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1047
  locationType: SPLT_WorldBoss
  value: 201004002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1048
  locationType: SPLT_WorldBoss
  value: 201004003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1049
  locationType: SPLT_WorldBoss
  value: 201005001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1050
  locationType: SPLT_Tower
  value: 401001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1051
  locationType: SPLT_Tower
  value: 403001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 1059
  locationType: SPLT_WorldBoss
  value: 201005003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1060
  locationType: SPLT_WorldBoss
  value: 201002002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1061
  locationType: SPLT_WorldBoss
  value: 201003001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1062
  locationType: SPLT_WorldBoss
  value: 201004004
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1063
  locationType: SPLT_NPC
  value: 101001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1064
  locationType: SPLT_NPC
  value: 202006
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1065
  locationType: SPLT_NPC
  value: 202007
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1066
  locationType: SPLT_NPC
  value: 202008
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1067
  locationType: SPLT_NPC
  value: 300001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1068
  locationType: SPLT_NPC
  value: 300002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 1069
  locationType: SPLT_NPC
  value: 300003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2000
  locationType: SPLT_Building
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 2001
  locationType: SPLT_Underland
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2002
  locationType: SPLT_Maze
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2003
  locationType: SPLT_Tower
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2004
  locationType: SPLT_WorldBoss
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2005
  locationType: SPLT_MultPlayerPVE
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIChanlengeable
      value: 1
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2006
  locationType: SPLT_Maze
  value: 301001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2007
  locationType: SPLT_Maze
  value: 301002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2008
  locationType: SPLT_Maze
  value: 302001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2009
  locationType: SPLT_Maze
  value: 303002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2010
  locationType: SPLT_Maze
  value: 304001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2011
  locationType: SPLT_Maze
  value: 303001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2012
  locationType: SPLT_FixPoint
  posX: 163350.0
  posY: 260340.0
  posZ: 3460.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 2013
  locationType: SPLT_FixPoint
  posX: 148700.0
  posY: 249470.0
  posZ: 4220.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 2014
  locationType: SPLT_Tower
  value: 401001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2015
  locationType: SPLT_Tower
  value: 403001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2016
  locationType: SPLT_Tower
  value: 404001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2017
  locationType: SPLT_Tower
  value: 405001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2018
  locationType: SPLT_Underland
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIReceivedFirstPassAward
      value: 0
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2019
  locationType: SPLT_Maze
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIReceivedFirstPassAward
      value: 0
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2020
  locationType: SPLT_Tower
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIReceivedFirstPassAward
      value: 0
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2021
  locationType: SPLT_WorldBoss
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
    cond {
      conditionType: SPTRCT_POIReceivedFirstPassAward
      value: 0
    }
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 2101
  locationType: SPLT_FixPoint
  posX: 157007.0
  posY: 255371.0
  posZ: 4234.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2102
  locationType: SPLT_FixPoint
  posX: 155703.0
  posY: 135651.0
  posZ: 945.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2103
  locationType: SPLT_FixPoint
  posX: 151778.0
  posY: 125250.0
  posZ: 146.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2104
  locationType: SPLT_FixPoint
  posX: -54933.0
  posY: 73882.0
  posZ: 11219.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2105
  locationType: SPLT_FixPoint
  posX: -55039.0
  posY: 75174.0
  posZ: 11225.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2106
  locationType: SPLT_FixPoint
  posX: -51471.0
  posY: 58432.0
  posZ: 12477.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2107
  locationType: SPLT_FixPoint
  posX: 125792.0
  posY: 109482.0
  posZ: 7675.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2108
  locationType: SPLT_FixPoint
  posX: 119426.0
  posY: 107569.0
  posZ: 6219.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2109
  locationType: SPLT_FixPoint
  posX: 128256.0
  posY: 102509.0
  posZ: 7321.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2110
  locationType: SPLT_FixPoint
  posX: 188518.0
  posY: -116019.0
  posZ: 38695.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2111
  locationType: SPLT_FixPoint
  posX: 188893.0
  posY: -115744.0
  posZ: 38709.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2112
  locationType: SPLT_FixPoint
  posX: 192549.0
  posY: -115384.0
  posZ: 38715.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2113
  locationType: SPLT_Choppable
  value: 4
  radius: 1000
  recommand {
    num: 3
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2114
  locationType: SPLT_Choppable
  value: 5
  radius: 1000
  recommand {
    num: 3
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2115
  locationType: SPLT_Choppable
  value: 6
  radius: 1000
  recommand {
    num: 3
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 2116
  locationType: SPLT_Choppable
  value: 7
  radius: 1000
  recommand {
    num: 3
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 3001
  locationType: SPLT_Building
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 3002
  locationType: SPLT_Building
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 3003
  locationType: SPLT_Building
  value: 1023
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 3004
  locationType: SPLT_Building
  value: 1023
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 3005
  locationType: SPLT_Building
  value: 1023
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 3006
  locationType: SPLT_HostBuilding
  value: 3023
  radius: 1000
  recommand {
    num: 1
    type: SPTRLT_Nearest
  }
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 204010101
  locationType: SPLT_NPC
  value: 204009
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204010201
  locationType: SPLT_NPC
  value: 204006
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204010301
  locationType: SPLT_FixPoint
  posX: 81236.0
  posY: 6932.0
  posZ: 4418.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204010302
  locationType: SPLT_FixPoint
  posX: 46145.0
  posY: 25165.0
  posZ: 8910.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204010303
  locationType: SPLT_Maze
  value: 304001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204010401
  locationType: SPLT_NPC
  value: 204006
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204020101
  locationType: SPLT_FixPoint
  posX: 63239.0
  posY: -42126.0
  posZ: 11991.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204020201
  locationType: SPLT_NPC
  value: 204010
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204020301
  locationType: SPLT_FixPoint
  posX: 91235.0
  posY: -76905.0
  posZ: 15705.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204030101
  locationType: SPLT_FixPoint
  posX: 82325.0
  posY: -39026.0
  posZ: 6722.0
  radius: 7000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204030201
  locationType: SPLT_FixPoint
  posX: 95445.0
  posY: -44850.0
  posZ: 4985.0
  radius: 10000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204030202
  locationType: SPLT_FixPoint
  posX: 94185.0
  posY: -45470.0
  posZ: 4665.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204030401
  locationType: SPLT_NPC
  value: 204002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204040101
  locationType: SPLT_NPC
  value: 204003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204040102
  locationType: SPLT_NPC
  value: 204004
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204040103
  locationType: SPLT_NPC
  value: 204005
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204050101
  locationType: SPLT_FixPoint
  posX: 136530.0
  posY: -70190.0
  posZ: 25030.0
  radius: 6000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204050102
  locationType: SPLT_FixPoint
  posX: 116495.0
  posY: -61015.0
  posZ: 24085.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204070101
  locationType: SPLT_NPC
  value: 204008
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204070201
  locationType: SPLT_FixPoint
  posX: 51060.0
  posY: -115526.0
  posZ: 1385.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204070202
  locationType: SPLT_FixPoint
  posX: 44570.0
  posY: -120307.0
  posZ: 253.0
  radius: 10000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204070301
  locationType: SPLT_NPC
  value: 204012
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204080201
  locationType: SPLT_NPC
  value: 204013
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204080301
  locationType: SPLT_NPC
  value: 204006
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204080401
  locationType: SPLT_Tower
  value: 404001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 204080501
  locationType: SPLT_NPC
  value: 204014
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204090101
  locationType: SPLT_FixPoint
  posX: 114712.0
  posY: 20572.0
  posZ: 1731.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204090201
  locationType: SPLT_Underland
  value: 204017
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204090301
  locationType: SPLT_FixPoint
  posX: 126944.0
  posY: -5208.0
  posZ: 1712.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204090401
  locationType: SPLT_FixPoint
  posX: 125104.0
  posY: -26031.0
  posZ: 11828.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204100101
  locationType: SPLT_NPC
  value: 104003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204100201
  locationType: SPLT_NPC
  value: 104003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204100301
  locationType: SPLT_NPC
  value: 204001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204110101
  locationType: SPLT_FixPoint
  posX: 176138.0
  posY: -87718.0
  posZ: 21807.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 204110201
  locationType: SPLT_FixPoint
  posX: 188788.0
  posY: -88905.0
  posZ: 14501.0
  radius: 20000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 204110301
  locationType: SPLT_NPC
  value: 105001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202010101
  locationType: SPLT_NPC
  value: 202004
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202010201
  locationType: SPLT_FixPoint
  posX: 33502.0
  posY: 108372.0
  posZ: 6846.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202010301
  locationType: SPLT_NPC
  value: 202004
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202020101
  locationType: SPLT_NPC
  value: 202005
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202020201
  locationType: SPLT_FixPoint
  posX: 117290.0
  posY: 63310.0
  posZ: 16380.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202030101
  locationType: SPLT_NPC
  value: 202003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202030201
  locationType: SPLT_FixPoint
  posX: 45619.0
  posY: 54110.0
  posZ: 2613.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202030301
  locationType: SPLT_NPC
  value: 202003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202040101
  locationType: SPLT_NPC
  value: 202002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202040201
  locationType: SPLT_FixPoint
  posX: 33620.0
  posY: 61760.0
  posZ: 270.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202040301
  locationType: SPLT_NPC
  value: 202002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202050101
  locationType: SPLT_NPC
  value: 205005
  posX: 181144.0
  posY: -137608.0
  posZ: 29078.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050102
  locationType: SPLT_NPC
  value: 205002
  posX: 75585.0
  posY: -196244.0
  posZ: 140.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050201
  locationType: SPLT_NPC
  value: 205006
  posX: 180919.0
  posY: -291686.0
  posZ: 7751.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050202
  locationType: SPLT_FixPoint
  posX: 236451.0
  posY: -290411.0
  posZ: 8292.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050203
  locationType: SPLT_FixPoint
  posX: 243485.0
  posY: -246330.0
  posZ: 3705.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050204
  locationType: SPLT_FixPoint
  posX: 267616.0
  posY: -238488.0
  posZ: 10640.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050205
  locationType: SPLT_NPC
  value: 205007
  posX: 269701.0
  posY: -187872.0
  posZ: 14196.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050301
  locationType: SPLT_NPC
  value: 205008
  posX: 263445.0
  posY: -112116.0
  posZ: 3061.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050302
  locationType: SPLT_FixPoint
  posX: 247955.0
  posY: -136783.0
  posZ: 31060.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050401
  locationType: SPLT_NPC
  value: 205009
  posX: 235696.0
  posY: -82818.0
  posZ: 84.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050402
  locationType: SPLT_FixPoint
  posX: 212530.0
  posY: -109918.0
  posZ: 23010.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050403
  locationType: SPLT_FixPoint
  posX: 210778.0
  posY: -106128.0
  posZ: 21536.0
  radius: 500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050404
  locationType: SPLT_FixPoint
  posX: 213647.0
  posY: -109385.0
  posZ: 17921.0
  radius: 500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050405
  locationType: SPLT_FixPoint
  posX: 216443.0
  posY: -110720.0
  posZ: 13150.0
  radius: 500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050406
  locationType: SPLT_FixPoint
  posX: 211877.0
  posY: -107767.0
  posZ: 9597.0
  radius: 500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050407
  locationType: SPLT_FixPoint
  posX: 218921.0
  posY: -107822.0
  posZ: 7435.0
  radius: 500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050408
  locationType: SPLT_FixPoint
  posX: 223146.0
  posY: -98153.0
  posZ: 50.0
  radius: 500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050501
  locationType: SPLT_NPC
  value: 205010
  posX: 165849.0
  posY: -217504.0
  posZ: 55994.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202050502
  locationType: SPLT_FixPoint
  posX: 228086.0
  posY: -162739.0
  posZ: 62588.0
  radius: 250
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202050503
  locationType: SPLT_FixPoint
  posX: 172677.0
  posY: -193245.0
  posZ: 75665.0
  radius: 250
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_004"
  }
}
rows {
  jumpId: 202050504
  locationType: SPLT_FixPoint
  posX: 202359.0
  posY: -132347.0
  posZ: 42264.0
  radius: 250
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_005"
  }
}
rows {
  jumpId: 202050505
  locationType: SPLT_FixPoint
  posX: 294125.0
  posY: -228597.0
  posZ: 9191.0
  radius: 250
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_006"
  }
}
rows {
  jumpId: 202050601
  locationType: SPLT_NPC
  value: 205011
  posX: 191372.0
  posY: -158061.0
  posZ: 57738.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 10001
  locationType: SPLT_NPC
  value: 201005
  posX: 141335.0
  posY: 217187.0
  posZ: 3889.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 10002
  locationType: SPLT_NPC
  value: 201006
  posX: 137066.0
  posY: 149464.0
  posZ: 18090.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 10003
  locationType: SPLT_NPC
  value: 201007
  posX: 146513.0
  posY: 163695.0
  posZ: 27534.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 10004
  locationType: SPLT_FixPoint
  posX: 124737.0
  posY: 207412.0
  posZ: 1065.0
  radius: 5000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 10005
  locationType: SPLT_NPC
  value: 101001
  posX: 108266.0
  posY: 227229.0
  posZ: 158.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 10006
  locationType: SPLT_NPC
  value: 201009
  posX: 107244.0
  posY: 226328.0
  posZ: 283.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 10007
  locationType: SPLT_FixPoint
  posX: 156600.0
  posY: 221918.0
  posZ: 3800.0
  radius: 1500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 10008
  locationType: SPLT_FixPoint
  posX: 155758.0
  posY: 215408.0
  posZ: 3817.0
  radius: 3500
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 10009
  locationType: SPLT_FixPoint
  posX: 143704.0
  posY: 153601.0
  posZ: 17107.0
  radius: 2000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 10010
  locationType: SPLT_FixPoint
  posX: 163043.0
  posY: 132291.0
  posZ: 1450.0
  radius: 100
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 10011
  locationType: SPLT_FixPoint
  posX: 157771.0
  posY: 148627.0
  posZ: 12598.0
  radius: 100
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin_02.StarP_FX_ZhiYin_02"
  }
}
rows {
  jumpId: 10012
  locationType: SPLT_FixPoint
  posX: 124737.0
  posY: 207412.0
  posZ: 1065.0
  radius: 10
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202080101
  locationType: SPLT_FixPoint
  posX: -244309.0
  posY: 37990.0
  posZ: 9345.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202080202
  locationType: SPLT_WorldBoss
  value: 201008003
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202080201
  locationType: SPLT_NPC
  value: 208001
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202080203
  locationType: SPLT_FixPoint
  posX: -199788.0
  posY: 24683.0
  posZ: 0.0
  radius: 6000
  param {
    enableTrace: 0
    showDistance: false
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 0
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202080301
  locationType: SPLT_NPC
  value: 208009
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
rows {
  jumpId: 202080302
  locationType: SPLT_FixPoint
  posX: -215440.0
  posY: 23088.0
  posZ: 32000.0
  radius: 4300
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: true
    lightCircleMat: "/Game/Feature/StarP/Assets/Effect/Materials/SOC/MI_StarP_DepthFade_003.MI_StarP_DepthFade_003"
  }
}
rows {
  jumpId: 202080401
  locationType: SPLT_FixPoint
  posX: -152981.0
  posY: -16425.0
  posZ: 374.0
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: true
    showLightCircle: false
    lightColumMat: "/Game/Feature/StarP/Assets/Effect/Particle/VFX_Scence/StarP_FX_ZhiYin.StarP_FX_ZhiYin"
  }
}
rows {
  jumpId: 202080402
  locationType: SPLT_NPC
  value: 208002
  radius: 1000
  param {
    enableTrace: 1
    showDistance: true
    maxShowDistance: 5000
    showOutOfScreen: true
    iconHeightOffset: 300
    showLightColumn: false
    showLightCircle: false
  }
}
