com.tencent.wea.xlsRes.starp.table_SPMonsterVoice
excel/xls/X_SP星兽叫声表.xlsx sheet:物理材质属性表
rows {
  Id: 1000101
}
rows {
  Id: 2000101
  Bank: "SP_XingShou_CaiJi"
  BeHit: "Play_VO_caiji_hit"
  Attack: "Play_VO_caiji_attack"
  Skill: "Play_VO_caiji_skill"
  Encounter: "Play_VO_caiji_escape"
  Dead: "Play_VO_caiji_death"
  IllustrationOpen: "Play_VO_caiji_logo"
  Captured: "Play_VO_caiji_failedcapture"
  Touched: "Play_VO_caiji_touched"
  FullStomach: "Play_VO_caiji_befed"
  CasualA: "Play_VO_caiji_rest_01"
  CasualB: "Play_VO_caiji_rest_02"
  CasualC: "Play_VO_caiji_rest_03"
  CasualD: "Play_VO_caiji_rest_04"
  GetUp: "Play_VO_caiji_getup"
  Mine: "Play_VO_caiji_SOC"
  Log: "Play_VO_caiji_SOC"
  ReactionA: "Play_VO_caiji_reaction_01"
  ReactionB: "Play_VO_caiji_reaction_02"
  ReactionC: "Play_VO_caiji_reaction_03"
}
rows {
  Id: 2000201
  Bank: "SP_XingShou_TianGou"
  BeHit: "Play_VO_tiangou_hit"
  Attack: "Play_VO_tiangou_attack"
  Skill: "Play_VO_tiangou_skill"
  Encounter: "Play_VO_tiangou_escape"
  Dead: "Play_VO_tiangou_death"
  IllustrationOpen: "Play_VO_tiangou_logo"
  Captured: "Play_VO_tiangou_failedcapture"
  Touched: "Play_VO_tiangou_touched"
  FullStomach: "Play_VO_tiangou_befed"
  CasualA: "Play_VO_tiangou_rest_01"
  CasualB: "Play_VO_tiangou_rest_02"
  CasualC: "Play_VO_tiangou_rest_03"
  CasualD: "Play_VO_tiangou_rest_04"
  GetUp: "Play_VO_tiangou_getup"
  Mine: "Play_VO_tiangou_SOC"
  Log: "Play_VO_tiangou_SOC"
  ReactionA: "Play_VO_tiangou_reaction_01"
  ReactionB: "Play_VO_tiangou_reaction_02"
  ReactionC: "Play_VO_tiangou_reaction_03"
}
rows {
  Id: 2000301
  Bank: "SP_XingShou_Dilaer"
  BeHit: "Play_VO_dilaer_hit"
  Attack: "Play_VO_dilaer_attack"
  Skill: "Play_VO_dilaer_skill"
  Encounter: "Play_VO_dilaer_escape"
  Dead: "Play_VO_dilaer_death"
  IllustrationOpen: "Play_VO_dilaer_logo"
  Captured: "Play_VO_dilaer_failedcapture"
  Touched: "Play_VO_dilaer_touched"
  FullStomach: "Play_VO_dilaer_befed"
  CasualA: "Play_VO_dilaer_rest_01"
  CasualB: "Play_VO_dilaer_rest_02"
  CasualC: "Play_VO_dilaer_rest_03"
  CasualD: "Play_VO_dilaer_rest_04"
  GetUp: "Play_VO_dilaer_getup"
  Mine: "Play_VO_dilaer_SOC"
  Log: "Play_VO_dilaer_SOC"
  ReactionA: "Play_VO_dilaer_reaction_01"
  ReactionB: "Play_VO_dilaer_reaction_02"
  ReactionC: "Play_VO_dilaer_reaction_03"
}
rows {
  Id: 2000401
  Bank: "SP_XingShou_JinGangShu"
  BeHit: "Play_VO_jingangshu_hit"
  Attack: "Play_VO_jingangshu_attack"
  Skill: "Play_VO_jingangshu_skill"
  Encounter: "Play_VO_jingangshu_escape"
  Dead: "Play_VO_jingangshu_death"
  IllustrationOpen: "Play_VO_jingangshu_logo"
  Captured: "Play_VO_jingangshu_failedcapture"
  Touched: "Play_VO_jingangshu_touched"
  FullStomach: "Play_VO_jingangshu_befed"
  CasualA: "Play_VO_jingangshu_rest_01"
  CasualB: "Play_VO_jingangshu_rest_02"
  CasualC: "Play_VO_jingangshu_rest_03"
  CasualD: "Play_VO_jingangshu_rest_04"
  GetUp: "Play_VO_jingangshu_getup"
  Mine: "Play_VO_jingangshu_SOC"
  Log: "Play_VO_jingangshu_SOC"
  ReactionA: "Play_VO_jingangshu_reaction_01"
  ReactionB: "Play_VO_jingangshu_reaction_02"
  ReactionC: "Play_VO_jingangshu_reaction_03"
}
rows {
  Id: 2000501
  Bank: "SP_XingShou_CaoDie"
  BeHit: "Play_VO_caodie_hit"
  Attack: "Play_VO_caodie_attack"
  Skill: "Play_VO_caodie_skill"
  Encounter: "Play_VO_caodie_escape"
  Dead: "Play_VO_caodie_death"
  IllustrationOpen: "Play_VO_caodie_logo"
  Captured: "Play_VO_caodie_failedcapture"
  Touched: "Play_VO_caodie_touched"
  FullStomach: "Play_VO_caodie_befed"
  CasualA: "Play_VO_caodie_rest_01"
  CasualB: "Play_VO_caodie_rest_02"
  CasualC: "Play_VO_caodie_rest_03"
  CasualD: "Play_VO_caodie_rest_04"
  GetUp: "Play_VO_caodie_getup"
  Mine: "Play_VO_caodie_SOC"
  Log: "Play_VO_caodie_SOC"
  ReactionA: "Play_VO_caodie_reaction_01"
  ReactionB: "Play_VO_caodie_reaction_02"
  ReactionC: "Play_VO_caodie_reaction_03"
}
rows {
  Id: 2000601
  Bank: "SP_XingShou_SuanNiNiu"
  BeHit: "Play_VO_suanniniu_hit"
  Attack: "Play_VO_suanniniu_attack"
  Skill: "Play_VO_suanniniu_skill"
  Encounter: "Play_VO_suanniniu_escape"
  Dead: "Play_VO_suanniniu_death"
  IllustrationOpen: "Play_VO_suanniniu_logo"
  Captured: "Play_VO_suanniniu_failedcapture"
  Touched: "Play_VO_suanniniu_touched"
  FullStomach: "Play_VO_suanniniu_befed"
  CasualA: "Play_VO_suanniniu_rest_01"
  CasualB: "Play_VO_suanniniu_rest_02"
  CasualC: "Play_VO_suanniniu_rest_03"
  CasualD: "Play_VO_suanniniu_rest_04"
  GetUp: "Play_VO_suanniniu_getup"
  Mine: "Play_VO_suanniniu_SOC"
  Log: "Play_VO_suanniniu_SOC"
  ReactionA: "Play_VO_suanniniu_reaction_01"
  ReactionB: "Play_VO_suanniniu_reaction_02"
  ReactionC: "Play_VO_suanniniu_reaction_03"
}
rows {
  Id: 2000701
  Bank: "SP_XingShou_HaoLengWa"
  BeHit: "Play_VO_haolengwa_hit"
  Attack: "Play_VO_haolengwa_attack"
  Skill: "Play_VO_haolengwa_skill"
  Encounter: "Play_VO_haolengwa_escape"
  Dead: "Play_VO_haolengwa_death"
  IllustrationOpen: "Play_VO_haolengwa_logo"
  Captured: "Play_VO_haolengwa_failedcapture"
  Touched: "Play_VO_haolengwa_touched"
  FullStomach: "Play_VO_haolengwa_befed"
  CasualA: "Play_VO_haolengwa_rest_01"
  CasualB: "Play_VO_haolengwa_rest_02"
  CasualC: "Play_VO_haolengwa_rest_03"
  CasualD: "Play_VO_haolengwa_rest_04"
  GetUp: "Play_VO_haolengwa_getup"
  Mine: "Play_VO_haolengwa_SOC"
  Log: "Play_VO_haolengwa_SOC"
  ReactionA: "Play_VO_haolengwa_reaction_01"
  ReactionB: "Play_VO_haolengwa_reaction_02"
  ReactionC: "Play_VO_haolengwa_reaction_03"
}
rows {
  Id: 2000801
  Bank: "SP_XingShou_QingTongNiao"
  BeHit: "Play_VO_qingtongniao_hit"
  Attack: "Play_VO_qingtongniao_attack"
  Skill: "Play_VO_qingtongniao_skill"
  Encounter: "Play_VO_qingtongniao_escape"
  Dead: "Play_VO_qingtongniao_death"
  IllustrationOpen: "Play_VO_qingtongniao_logo"
  Captured: "Play_VO_qingtongniao_failedcapture"
  Touched: "Play_VO_qingtongniao_touched"
  FullStomach: "Play_VO_qingtongniao_befed"
  CasualA: "Play_VO_qingtongniao_rest_01"
  CasualB: "Play_VO_qingtongniao_rest_02"
  CasualC: "Play_VO_qingtongniao_rest_03"
  CasualD: "Play_VO_qingtongniao_rest_04"
  GetUp: "Play_VO_qingtongniao_getup"
  Mine: "Play_VO_qingtongniao_SOC"
  Log: "Play_VO_qingtongniao_SOC"
  ReactionA: "Play_VO_qingtongniao_reaction_01"
  ReactionB: "Play_VO_qingtongniao_reaction_02"
  ReactionC: "Play_VO_qingtongniao_reaction_03"
}
rows {
  Id: 2000901
  Bank: "SP_XingShou_Longma"
  BeHit: "Play_VO_longma_hit"
  Attack: "Play_VO_longma_attack"
  Skill: "Play_VO_longma_skill"
  Encounter: "Play_VO_longma_escape"
  Dead: "Play_VO_longma_death"
  IllustrationOpen: "Play_VO_longma_logo"
  Captured: "Play_VO_longma_failedcapture"
  Touched: "Play_VO_longma_touched"
  FullStomach: "Play_VO_longma_befed"
  CasualA: "Play_VO_longma_rest_01"
  CasualB: "Play_VO_longma_rest_02"
  CasualC: "Play_VO_longma_rest_03"
  CasualD: "Play_VO_longma_rest_04"
  GetUp: "Play_VO_longma_getup"
  Mine: "Play_VO_longma_SOC"
  Log: "Play_VO_longma_SOC"
  ReactionA: "Play_VO_longma_reaction_01"
  ReactionB: "Play_VO_longma_reaction_02"
  ReactionC: "Play_VO_longma_reaction_03"
}
rows {
  Id: 2001001
  Bank: "SP_XingShou_YaoMao"
  BeHit: "Play_VO_yaomao_hit"
  Attack: "Play_VO_yaomao_attack"
  Skill: "Play_VO_yaomao_skill"
  Encounter: "Play_VO_yaomao_escape"
  Dead: "Play_VO_yaomao_death"
  IllustrationOpen: "Play_VO_yaomao_logo"
  Captured: "Play_VO_yaomao_failedcapture"
  Touched: "Play_VO_yaomao_touched"
  FullStomach: "Play_VO_yaomao_befed"
  CasualA: "Play_VO_yaomao_rest_01"
  CasualB: "Play_VO_yaomao_rest_02"
  CasualC: "Play_VO_yaomao_rest_03"
  CasualD: "Play_VO_yaomao_rest_04"
  GetUp: "Play_VO_yaomao_getup"
  Mine: "Play_VO_yaomao_SOC"
  Log: "Play_VO_yaomao_SOC"
  ReactionA: "Play_VO_yaomao_reaction_01"
  ReactionB: "Play_VO_yaomao_reaction_02"
  ReactionC: "Play_VO_yaomao_reaction_03"
}
rows {
  Id: 2001101
  Bank: "SP_XingShou_RongRongYang"
  BeHit: "Play_VO_rongrongyang_hit"
  Attack: "Play_VO_rongrongyang_attack"
  Skill: "Play_VO_rongrongyang_skill"
  Encounter: "Play_VO_rongrongyang_escape"
  Dead: "Play_VO_rongrongyang_death"
  IllustrationOpen: "Play_VO_rongrongyang_logo"
  Captured: "Play_VO_rongrongyang_failedcapture"
  Touched: "Play_VO_rongrongyang_touched"
  FullStomach: "Play_VO_rongrongyang_befed"
  CasualA: "Play_VO_rongrongyang_rest_01"
  CasualB: "Play_VO_rongrongyang_rest_02"
  CasualC: "Play_VO_rongrongyang_rest_03"
  CasualD: "Play_VO_rongrongyang_rest_04"
  GetUp: "Play_VO_rongrongyang_getup"
  Mine: "Play_VO_rongrongyang_SOC"
  Log: "Play_VO_rongrongyang_SOC"
  ReactionA: "Play_VO_rongrongyang_reaction_01"
  ReactionB: "Play_VO_rongrongyang_reaction_02"
  ReactionC: "Play_VO_rongrongyang_reaction_03"
}
rows {
  Id: 2001201
  Bank: "SP_XingShou_MiFeng"
  BeHit: "Play_VO_mifeng_hit"
  Attack: "Play_VO_mifeng_attack"
  Skill: "Play_VO_mifeng_skill"
  Encounter: "Play_VO_mifeng_escape"
  Dead: "Play_VO_mifeng_death"
  IllustrationOpen: "Play_VO_mifeng_logo"
  Captured: "Play_VO_mifeng_failedcapture"
  Touched: "Play_VO_mifeng_touched"
  FullStomach: "Play_VO_mifeng_befed"
  CasualA: "Play_VO_mifeng_rest_01"
  CasualB: "Play_VO_mifeng_rest_02"
  CasualC: "Play_VO_mifeng_rest_03"
  CasualD: "Play_VO_mifeng_rest_04"
  GetUp: "Play_VO_mifeng_getup"
  Mine: "Play_VO_mifeng_SOC"
  Log: "Play_VO_mifeng_SOC"
  ReactionA: "Play_VO_mifeng_reaction_01"
  ReactionB: "Play_VO_mifeng_reaction_02"
  ReactionC: "Play_VO_mifeng_reaction_03"
}
rows {
  Id: 2001301
  Bank: "SP_XingShou_ZhuLiYe"
  BeHit: "Play_VO_zhuliye_hit"
  Attack: "Play_VO_zhuliye_attack"
  Skill: "Play_VO_zhuliye_skill"
  Encounter: "Play_VO_zhuliye_escape"
  Dead: "Play_VO_zhuliye_death"
  IllustrationOpen: "Play_VO_zhuliye_logo"
  Captured: "Play_VO_zhuliye_failedcapture"
  Touched: "Play_VO_zhuliye_touched"
  FullStomach: "Play_VO_zhuliye_befed"
  CasualA: "Play_VO_zhuliye_rest_01"
  CasualB: "Play_VO_zhuliye_rest_02"
  CasualC: "Play_VO_zhuliye_rest_03"
  CasualD: "Play_VO_zhuliye_rest_04"
  GetUp: "Play_VO_zhuliye_getup"
  Mine: "Play_VO_zhuliye_SOC"
  Log: "Play_VO_zhuliye_SOC"
  ReactionA: "Play_VO_zhuliye_reaction_01"
  ReactionB: "Play_VO_zhuliye_reaction_02"
  ReactionC: "Play_VO_zhuliye_reaction_03"
}
rows {
  Id: 2001401
  Bank: "SP_XingShou_HuJiao"
  BeHit: "Play_VO_hujiao_hit"
  Attack: "Play_VO_hujiao_attack"
  Skill: "Play_VO_hujiao_skill"
  Encounter: "Play_VO_hujiao_escape"
  Dead: "Play_VO_hujiao_death"
  IllustrationOpen: "Play_VO_hujiao_logo"
  Captured: "Play_VO_hujiao_failedcapture"
  Touched: "Play_VO_hujiao_touched"
  FullStomach: "Play_VO_hujiao_befed"
  CasualA: "Play_VO_hujiao_rest_01"
  CasualB: "Play_VO_hujiao_rest_02"
  CasualC: "Play_VO_hujiao_rest_03"
  CasualD: "Play_VO_hujiao_rest_04"
  GetUp: "Play_VO_hujiao_getup"
  Mine: "Play_VO_hujiao_SOC"
  Log: "Play_VO_hujiao_SOC"
  ReactionA: "Play_VO_hujiao_reaction_01"
  ReactionB: "Play_VO_hujiao_reaction_02"
  ReactionC: "Play_VO_hujiao_reaction_03"
}
rows {
  Id: 2001501
  Bank: "SP_XingShou_NiuNieNie"
  BeHit: "Play_VO_niunienie_hit"
  Attack: "Play_VO_niunienie_attack"
  Skill: "Play_VO_niunienie_skill"
  Encounter: "Play_VO_niunienie_escape"
  Dead: "Play_VO_niunienie_death"
  IllustrationOpen: "Play_VO_niunienie_logo"
  Captured: "Play_VO_niunienie_failedcapture"
  Touched: "Play_VO_niunienie_touched"
  FullStomach: "Play_VO_niunienie_befed"
  CasualA: "Play_VO_niunienie_rest_01"
  CasualB: "Play_VO_niunienie_rest_02"
  CasualC: "Play_VO_niunienie_rest_03"
  CasualD: "Play_VO_niunienie_rest_04"
  GetUp: "Play_VO_niunienie_getup"
  Mine: "Play_VO_niunienie_SOC"
  Log: "Play_VO_niunienie_SOC"
  ReactionA: "Play_VO_niunienie_reaction_01"
  ReactionB: "Play_VO_niunienie_reaction_02"
  ReactionC: "Play_VO_niunienie_reaction_03"
}
rows {
  Id: 2001601
  Bank: "SP_XingShou_HuZhongJun"
  BeHit: "Play_VO_huzhongjun_hit"
  Attack: "Play_VO_huzhongjun_attack"
  Skill: "Play_VO_huzhongjun_skill"
  Encounter: "Play_VO_huzhongjun_escape"
  Dead: "Play_VO_huzhongjun_death"
  IllustrationOpen: "Play_VO_huzhongjun_logo"
  Captured: "Play_VO_huzhongjun_failedcapture"
  Touched: "Play_VO_huzhongjun_touched"
  FullStomach: "Play_VO_huzhongjun_befed"
  CasualA: "Play_VO_huzhongjun_rest_01"
  CasualB: "Play_VO_huzhongjun_rest_02"
  CasualC: "Play_VO_huzhongjun_rest_03"
  CasualD: "Play_VO_huzhongjun_rest_04"
  GetUp: "Play_VO_huzhongjun_getup"
  Mine: "Play_VO_huzhongjun_SOC"
  Log: "Play_VO_huzhongjun_SOC"
  ReactionA: "Play_VO_huzhongjun_reaction_01"
  ReactionB: "Play_VO_huzhongjun_reaction_02"
  ReactionC: "Play_VO_huzhongjun_reaction_03"
}
rows {
  Id: 2001701
  Bank: "SP_XingShou_YueHeXian"
  BeHit: "Play_VO_yuehexian_hit"
  Attack: "Play_VO_yuehexian_attack"
  Skill: "Play_VO_yuehexian_skill"
  Encounter: "Play_VO_yuehexian_escape"
  Dead: "Play_VO_yuehexian_death"
  IllustrationOpen: "Play_VO_yuehexian_logo"
  Captured: "Play_VO_yuehexian_failedcapture"
  Touched: "Play_VO_yuehexian_touched"
  FullStomach: "Play_VO_yuehexian_befed"
  CasualA: "Play_VO_yuehexian_rest_01"
  CasualB: "Play_VO_yuehexian_rest_02"
  CasualC: "Play_VO_yuehexian_rest_03"
  CasualD: "Play_VO_yuehexian_rest_04"
  GetUp: "Play_VO_yuehexian_getup"
  Mine: "Play_VO_yuehexian_SOC"
  Log: "Play_VO_yuehexian_SOC"
  ReactionA: "Play_VO_yuehexian_reaction_01"
  ReactionB: "Play_VO_yuehexian_reaction_02"
  ReactionC: "Play_VO_yuehexian_reaction_03"
}
rows {
  Id: 2013301
  Bank: "SP_XingShou_YueHeXian"
  BeHit: "Play_VO_yuehexian_hit"
  Attack: "Play_VO_yuehexian_attack"
  Skill: "Play_VO_yuehexian_skill"
  Encounter: "Play_VO_yuehexian_escape"
  Dead: "Play_VO_yuehexian_death"
  IllustrationOpen: "Play_VO_yuehexian_logo"
  Captured: "Play_VO_yuehexian_failedcapture"
  Touched: "Play_VO_yuehexian_touched"
  FullStomach: "Play_VO_yuehexian_befed"
  CasualA: "Play_VO_yuehexian_rest_01"
  CasualB: "Play_VO_yuehexian_rest_02"
  CasualC: "Play_VO_yuehexian_rest_03"
  CasualD: "Play_VO_yuehexian_rest_04"
  GetUp: "Play_VO_yuehexian_getup"
  Mine: "Play_VO_yuehexian_SOC"
  Log: "Play_VO_yuehexian_SOC"
  ReactionA: "Play_VO_yuehexian_reaction_01"
  ReactionB: "Play_VO_yuehexian_reaction_02"
  ReactionC: "Play_VO_yuehexian_reaction_03"
}
rows {
  Id: 2001801
  Bank: "SP_XingShou_Huaidandan"
  BeHit: "Play_VO_huaidandan_hit"
  Attack: "Play_VO_huaidandan_attack"
  Skill: "Play_VO_huaidandan_skill"
  Encounter: "Play_VO_huaidandan_escape"
  Dead: "Play_VO_huaidandan_death"
  IllustrationOpen: "Play_VO_huaidandan_logo"
  Captured: "Play_VO_huaidandan_failedcapture"
  Touched: "Play_VO_huaidandan_touched"
  FullStomach: "Play_VO_huaidandan_befed"
  CasualA: "Play_VO_huaidandan_rest_01"
  CasualB: "Play_VO_huaidandan_rest_02"
  CasualC: "Play_VO_huaidandan_rest_03"
  CasualD: "Play_VO_huaidandan_rest_04"
  GetUp: "Play_VO_huaidandan_getup"
  Mine: "Play_VO_huaidandan_SOC"
  Log: "Play_VO_huaidandan_SOC"
  ReactionA: "Play_VO_huaidandan_reaction_01"
  ReactionB: "Play_VO_huaidandan_reaction_02"
  ReactionC: "Play_VO_huaidandan_reaction_03"
}
rows {
  Id: 2001901
  Bank: "SP_XingShou_Chuanshanke"
  BeHit: "Play_VO_chuanshanke_hit"
  Attack: "Play_VO_chuanshanke_attack"
  Skill: "Play_VO_chuanshanke_skill"
  Encounter: "Play_VO_chuanshanke_escape"
  Dead: "Play_VO_chuanshanke_death"
  IllustrationOpen: "Play_VO_chuanshanke_logo"
  Captured: "Play_VO_chuanshanke_failedcapture"
  Touched: "Play_VO_chuanshanke_touched"
  FullStomach: "Play_VO_chuanshanke_befed"
  CasualA: "Play_VO_chuanshanke_rest_01"
  CasualB: "Play_VO_chuanshanke_rest_02"
  CasualC: "Play_VO_chuanshanke_rest_03"
  CasualD: "Play_VO_chuanshanke_rest_04"
  GetUp: "Play_VO_chuanshanke_getup"
  Mine: "Play_VO_chuanshanke_SOC"
  Log: "Play_VO_chuanshanke_SOC"
  ReactionA: "Play_VO_chuanshanke_reaction_01"
  ReactionB: "Play_VO_chuanshanke_reaction_02"
  ReactionC: "Play_VO_chuanshanke_reaction_03"
}
rows {
  Id: 2002001
  Bank: "SP_XingShou_Songsongyu"
  BeHit: "Play_VO_songsongyu_hit"
  Attack: "Play_VO_songsongyu_attack"
  Skill: "Play_VO_songsongyu_skill"
  Encounter: "Play_VO_songsongyu_escape"
  Dead: "Play_VO_songsongyu_death"
  IllustrationOpen: "Play_VO_songsongyu_logo"
  Captured: "Play_VO_songsongyu_failedcapture"
  Touched: "Play_VO_songsongyu_touched"
  FullStomach: "Play_VO_songsongyu_befed"
  CasualA: "Play_VO_songsongyu_rest_01"
  CasualB: "Play_VO_songsongyu_rest_02"
  CasualC: "Play_VO_songsongyu_rest_03"
  CasualD: "Play_VO_songsongyu_rest_04"
  GetUp: "Play_VO_songsongyu_getup"
  Mine: "Play_VO_songsongyu_SOC"
  Log: "Play_VO_songsongyu_SOC"
  ReactionA: "Play_VO_songsongyu_reaction_01"
  ReactionB: "Play_VO_songsongyu_reaction_02"
  ReactionC: "Play_VO_songsongyu_reaction_03"
}
rows {
  Id: 2002101
  Bank: "SP_XingShou_CuiLingYing"
  BeHit: "Play_VO_cuilingying_hit"
  Attack: "Play_VO_cuilingying_attack"
  Skill: "Play_VO_cuilingying_skill"
  Encounter: "Play_VO_cuilingying_escape"
  Dead: "Play_VO_cuilingying_death"
  IllustrationOpen: "Play_VO_cuilingying_logo"
  Captured: "Play_VO_cuilingying_failedcapture"
  Touched: "Play_VO_cuilingying_touched"
  FullStomach: "Play_VO_cuilingying_befed"
  CasualA: "Play_VO_cuilingying_rest_01"
  CasualB: "Play_VO_cuilingying_rest_02"
  CasualC: "Play_VO_cuilingying_rest_03"
  CasualD: "Play_VO_cuilingying_rest_04"
  GetUp: "Play_VO_cuilingying_getup"
  Mine: "Play_VO_cuilingying_SOC"
  Log: "Play_VO_cuilingying_SOC"
  ReactionA: "Play_VO_cuilingying_reaction_01"
  ReactionB: "Play_VO_cuilingying_reaction_02"
  ReactionC: "Play_VO_cuilingying_reaction_03"
}
rows {
  Id: 2002201
  Bank: "SP_XingShou_ShanXingXing"
  BeHit: "Play_VO_shanxingxing_hit"
  Attack: "Play_VO_shanxingxing_attack"
  Skill: "Play_VO_shanxingxing_skill"
  Encounter: "Play_VO_shanxingxing_escape"
  Dead: "Play_VO_shanxingxing_death"
  IllustrationOpen: "Play_VO_shanxingxing_logo"
  Captured: "Play_VO_shanxingxing_failedcapture"
  Touched: "Play_VO_shanxingxing_touched"
  FullStomach: "Play_VO_shanxingxing_befed"
  CasualA: "Play_VO_shanxingxing_rest_01"
  CasualB: "Play_VO_shanxingxing_rest_02"
  CasualC: "Play_VO_shanxingxing_rest_03"
  CasualD: "Play_VO_shanxingxing_rest_04"
  GetUp: "Play_VO_shanxingxing_getup"
  Mine: "Play_VO_shanxingxing_SOC"
  Log: "Play_VO_shanxingxing_SOC"
  ReactionA: "Play_VO_shanxingxing_reaction_01"
  ReactionB: "Play_VO_shanxingxing_reaction_02"
  ReactionC: "Play_VO_shanxingxing_reaction_03"
}
rows {
  Id: 2002301
  Bank: "SP_XingShou_HuDieXian"
  BeHit: "Play_VO_hudiexian_hit"
  Attack: "Play_VO_hudiexian_attack"
  Skill: "Play_VO_hudiexian_skill"
  Encounter: "Play_VO_hudiexian_escape"
  Dead: "Play_VO_hudiexian_death"
  IllustrationOpen: "Play_VO_hudiexian_logo"
  Captured: "Play_VO_hudiexian_failedcapture"
  Touched: "Play_VO_hudiexian_touched"
  FullStomach: "Play_VO_hudiexian_befed"
  CasualA: "Play_VO_hudiexian_rest_01"
  CasualB: "Play_VO_hudiexian_rest_02"
  CasualC: "Play_VO_hudiexian_rest_03"
  CasualD: "Play_VO_hudiexian_rest_04"
  GetUp: "Play_VO_hudiexian_getup"
  Mine: "Play_VO_hudiexian_SOC"
  Log: "Play_VO_hudiexian_SOC"
  ReactionA: "Play_VO_hudiexian_reaction_01"
  ReactionB: "Play_VO_hudiexian_reaction_02"
  ReactionC: "Play_VO_hudiexian_reaction_03"
}
rows {
  Id: 2002401
  Bank: "SP_XingShou_CangYunQingLong"
  BeHit: "Play_VO_cangyunqinglong_hit"
  Attack: "Play_VO_cangyunqinglong_attack"
  Skill: "Play_VO_cangyunqinglong_skill"
  Encounter: "Play_VO_cangyunqinglong_escape"
  Dead: "Play_VO_cangyunqinglong_death"
  IllustrationOpen: "Play_VO_cangyunqinglong_logo"
  Captured: "Play_VO_cangyunqinglong_failedcapture"
  Touched: "Play_VO_cangyunqinglong_touched"
  FullStomach: "Play_VO_cangyunqinglong_befed"
  CasualA: "Play_VO_cangyunqinglong_rest_01"
  CasualB: "Play_VO_cangyunqinglong_rest_02"
  CasualC: "Play_VO_cangyunqinglong_rest_03"
  CasualD: "Play_VO_cangyunqinglong_rest_04"
  GetUp: "Play_VO_cangyunqinglong_getup"
  Mine: "Play_VO_cangyunqinglong_SOC"
  Log: "Play_VO_cangyunqinglong_SOC"
  ReactionA: "Play_VO_cangyunqinglong_reaction_01"
  ReactionB: "Play_VO_cangyunqinglong_reaction_02"
  ReactionC: "Play_VO_cangyunqinglong_reaction_03"
}
rows {
  Id: 2002501
  Bank: "SP_XingShou_XingHaiJi"
  BeHit: "Play_VO_xinghaiji_hit"
  Attack: "Play_VO_xinghaiji_attack"
  Skill: "Play_VO_xinghaiji_skill"
  Encounter: "Play_VO_xinghaiji_escape"
  Dead: "Play_VO_xinghaiji_death"
  IllustrationOpen: "Play_VO_xinghaiji_logo"
  Captured: "Play_VO_xinghaiji_failedcapture"
  Touched: "Play_VO_xinghaiji_touched"
  FullStomach: "Play_VO_xinghaiji_befed"
  CasualA: "Play_VO_xinghaiji_rest_01"
  CasualB: "Play_VO_xinghaiji_rest_02"
  CasualC: "Play_VO_xinghaiji_rest_03"
  CasualD: "Play_VO_xinghaiji_rest_04"
  GetUp: "Play_VO_xinghaiji_getup"
  Mine: "Play_VO_xinghaiji_SOC"
  Log: "Play_VO_xinghaiji_SOC"
  ReactionA: "Play_VO_xinghaiji_reaction_01"
  ReactionB: "Play_VO_xinghaiji_reaction_02"
  ReactionC: "Play_VO_xinghaiji_reaction_03"
}
rows {
  Id: 2002601
  Bank: "SP_XingShou_YanShiShi"
  BeHit: "Play_VO_shiyanshi_hit"
  Attack: "Play_VO_shiyanshi_attack"
  Skill: "Play_VO_shiyanshi_skill"
  Encounter: "Play_VO_shiyanshi_escape"
  Dead: "Play_VO_shiyanshi_death"
  IllustrationOpen: "Play_VO_shiyanshi_logo"
  Captured: "Play_VO_shiyanshi_failedcapture"
  Touched: "Play_VO_shiyanshi_touched"
  FullStomach: "Play_VO_shiyanshi_befed"
  CasualA: "Play_VO_shiyanshi_rest_01"
  CasualB: "Play_VO_shiyanshi_rest_02"
  CasualC: "Play_VO_shiyanshi_rest_03"
  CasualD: "Play_VO_shiyanshi_rest_04"
  GetUp: "Play_VO_shiyanshi_getup"
  Mine: "Play_VO_shiyanshi_SOC"
  Log: "Play_VO_shiyanshi_SOC"
  ReactionA: "Play_VO_yanshishi_reaction_01"
  ReactionB: "Play_VO_yanshishi_reaction_02"
  ReactionC: "Play_VO_yanshishi_reaction_03"
}
rows {
  Id: 2002701
  Bank: "SP_XingShou_ChiYingZhuQue"
  BeHit: "Play_VO_chiyingzhuque_hit"
  Attack: "Play_VO_chiyingzhuque_attack"
  Skill: "Play_VO_chiyingzhuque_skill"
  Encounter: "Play_VO_chiyingzhuque_escape"
  Dead: "Play_VO_chiyingzhuque_death"
  IllustrationOpen: "Play_VO_chiyingzhuque_logo"
  Captured: "Play_VO_chiyingzhuque_failedcapture"
  Touched: "Play_VO_chiyingzhuque_touched"
  FullStomach: "Play_VO_chiyingzhuque_befed"
  CasualA: "Play_VO_chiyingzhuque_rest_01"
  CasualB: "Play_VO_chiyingzhuque_rest_02"
  CasualC: "Play_VO_chiyingzhuque_rest_03"
  CasualD: "Play_VO_chiyingzhuque_rest_04"
  GetUp: "Play_VO_chiyingzhuque_getup"
  Mine: "Play_VO_chiyingzhuque_SOC"
  Log: "Play_VO_chiyingzhuque_SOC"
  ReactionA: "Play_VO_chiyingzhuque_reaction_01"
  ReactionB: "Play_VO_chiyingzhuque_reaction_02"
  ReactionC: "Play_VO_chiyingzhuque_reaction_03"
}
rows {
  Id: 2002801
  Bank: "SP_XingShou_DanXiaoGui"
  BeHit: "Play_VO_danxiaogui_hit"
  Attack: "Play_VO_danxiaogui_attack"
  Skill: "Play_VO_danxiaogui_skill"
  Encounter: "Play_VO_danxiaogui_escape"
  Dead: "Play_VO_danxiaogui_death"
  IllustrationOpen: "Play_VO_danxiaogui_logo"
  Captured: "Play_VO_danxiaogui_failedcapture"
  Touched: "Play_VO_danxiaogui_touched"
  FullStomach: "Play_VO_danxiaogui_befed"
  CasualA: "Play_VO_danxiaogui_rest_01"
  CasualB: "Play_VO_danxiaogui_rest_02"
  CasualC: "Play_VO_danxiaogui_rest_03"
  CasualD: "Play_VO_danxiaogui_rest_04"
  GetUp: "Play_VO_danxiaogui_getup"
  Mine: "Play_VO_danxiaogui_SOC"
  Log: "Play_VO_danxiaogui_SOC"
  ReactionA: "Play_VO_danxiaogui_reaction_01"
  ReactionB: "Play_VO_danxiaogui_reaction_02"
  ReactionC: "Play_VO_danxiaogui_reaction_03"
}
rows {
  Id: 2002901
  Bank: "SP_XingShou_BingRenBao"
  BeHit: "Play_VO_bingrenbao_hit"
  Attack: "Play_VO_bingrenbao_attack"
  Skill: "Play_VO_bingrenbao_skill"
  Encounter: "Play_VO_bingrenbao_escape"
  Dead: "Play_VO_bingrenbao_death"
  IllustrationOpen: "Play_VO_bingrenbao_logo"
  Captured: "Play_VO_bingrenbao_failedcapture"
  Touched: "Play_VO_bingrenbao_touched"
  FullStomach: "Play_VO_bingrenbao_befed"
  CasualA: "Play_VO_bingrenbao_rest_01"
  CasualB: "Play_VO_bingrenbao_rest_02"
  CasualC: "Play_VO_bingrenbao_rest_03"
  CasualD: "Play_VO_bingrenbao_rest_04"
  GetUp: "Play_VO_bingrenbao_getup"
  Mine: "Play_VO_bingrenbao_SOC"
  Log: "Play_VO_bingrenbao_SOC"
  ReactionA: "Play_VO_bingrenbao_reaction_01"
  ReactionB: "Play_VO_bingrenbao_reaction_02"
  ReactionC: "Play_VO_bingrenbao_reaction_03"
}
rows {
  Id: 2003001
  Bank: "SP_XingShou_LongBaiBai"
  BeHit: "Play_VO_longbaibai_hit"
  Attack: "Play_VO_longbaibai_attack"
  Skill: "Play_VO_longbaibai_skill"
  Encounter: "Play_VO_longbaibai_escape"
  Dead: "Play_VO_longbaibai_death"
  IllustrationOpen: "Play_VO_longbaibai_logo"
  Captured: "Play_VO_longbaibai_failedcapture"
  Touched: "Play_VO_longbaibai_touched"
  FullStomach: "Play_VO_longbaibai_befed"
  CasualA: "Play_VO_longbaibai_rest_01"
  CasualB: "Play_VO_longbaibai_rest_02"
  CasualC: "Play_VO_longbaibai_rest_03"
  CasualD: "Play_VO_longbaibai_rest_04"
  GetUp: "Play_VO_longbaibai_getup"
  Mine: "Play_VO_longbaibai_SOC"
  Log: "Play_VO_longbaibai_SOC"
  ReactionA: "Play_VO_longbaibai_reaction_01"
  ReactionB: "Play_VO_longbaibai_reaction_02"
  ReactionC: "Play_VO_longbaibai_reaction_03"
}
rows {
  Id: 2003101
  Bank: "SP_XingShou_Zilingying"
  BeHit: "Play_VO_zilingying_hit"
  Attack: "Play_VO_zilingying_attack"
  Skill: "Play_VO_zilingying_skill"
  Encounter: "Play_VO_zilingying_escape"
  Dead: "Play_VO_zilingying_death"
  IllustrationOpen: "Play_VO_zilingying_logo"
  Captured: "Play_VO_zilingying_failedcapture"
  Touched: "Play_VO_zilingying_touched"
  FullStomach: "Play_VO_zilingying_befed"
  CasualA: "Play_VO_zilingying_rest_01"
  CasualB: "Play_VO_zilingying_rest_02"
  CasualC: "Play_VO_zilingying_rest_03"
  CasualD: "Play_VO_zilingying_rest_04"
  GetUp: "Play_VO_zilingying_getup"
  Mine: "Play_VO_zilingying_SOC"
  Log: "Play_VO_zilingying_SOC"
  ReactionA: "Play_VO_zilingying_reaction_01"
  ReactionB: "Play_VO_zilingying_reaction_02"
  ReactionC: "Play_VO_zilingying_reaction_03"
}
rows {
  Id: 2003201
  Bank: "SP_XingShou_Maomeier"
  BeHit: "Play_VO_maomeier_hit"
  Attack: "Play_VO_maomeier_attack"
  Skill: "Play_VO_maomeier_skill"
  Encounter: "Play_VO_maomeier_escape"
  Dead: "Play_VO_maomeier_death"
  IllustrationOpen: "Play_VO_maomeier_logo"
  Captured: "Play_VO_maomeier_failedcapture"
  Touched: "Play_VO_maomeier_touched"
  FullStomach: "Play_VO_maomeier_befed"
  CasualA: "Play_VO_maomeier_rest_01"
  CasualB: "Play_VO_maomeier_rest_02"
  CasualC: "Play_VO_maomeier_rest_03"
  CasualD: "Play_VO_maomeier_rest_04"
  GetUp: "Play_VO_maomeier_getup"
  Mine: "Play_VO_maomeier_SOC"
  Log: "Play_VO_maomeier_SOC"
  ReactionA: "Play_VO_maomeier_reaction_01"
  ReactionB: "Play_VO_maomeier_reaction_02"
  ReactionC: "Play_VO_maomeier_reaction_03"
}
rows {
  Id: 2003301
  Bank: "SP_XingShou_Maomeiji"
  BeHit: "Play_VO_maomeiji_hit"
  Attack: "Play_VO_maomeiji_attack"
  Skill: "Play_VO_maomeiji_skill"
  Encounter: "Play_VO_maomeiji_escape"
  Dead: "Play_VO_maomeiji_death"
  IllustrationOpen: "Play_VO_maomeiji_logo"
  Captured: "Play_VO_maomeiji_failedcapture"
  Touched: "Play_VO_maomeiji_touched"
  FullStomach: "Play_VO_maomeiji_befed"
  CasualA: "Play_VO_maomeiji_rest_01"
  CasualB: "Play_VO_maomeiji_rest_02"
  CasualC: "Play_VO_maomeiji_rest_03"
  CasualD: "Play_VO_maomeiji_rest_04"
  GetUp: "Play_VO_maomeiji_getup"
  Mine: "Play_VO_maomeiji_SOC"
  Log: "Play_VO_maomeiji_SOC"
  ReactionA: "Play_VO_maomeiji_reaction_01"
  ReactionB: "Play_VO_maomeiji_reaction_02"
  ReactionC: "Play_VO_maomeiji_reaction_03"
}
rows {
  Id: 2003401
  Bank: "SP_XingShou_XuanMa"
  BeHit: "Play_VO_xuanma_hit"
  Attack: "Play_VO_xuanma_attack"
  Skill: "Play_VO_xuanma_skill"
  Encounter: "Play_VO_xuanma_escape"
  Dead: "Play_VO_xuanma_death"
  IllustrationOpen: "Play_VO_xuanma_logo"
  Captured: "Play_VO_xuanma_failedcapture"
  Touched: "Play_VO_xuanma_touched"
  FullStomach: "Play_VO_xuanma_befed"
  CasualA: "Play_VO_xuanma_rest_01"
  CasualB: "Play_VO_xuanma_rest_02"
  CasualC: "Play_VO_xuanma_rest_03"
  CasualD: "Play_VO_xuanma_rest_04"
  GetUp: "Play_VO_xuanma_getup"
  Mine: "Play_VO_xuanma_SOC"
  Log: "Play_VO_xuanma_SOC"
  ReactionA: "Play_VO_xuanma_reaction_01"
  ReactionB: "Play_VO_xuanma_reaction_02"
  ReactionC: "Play_VO_xuanma_reaction_03"
}
rows {
  Id: 2003501
  Bank: "SP_XingShou_XiaoYaoKun"
  BeHit: "Play_VO_xiaoyaokun_hit"
  Attack: "Play_VO_xiaoyaokun_attack"
  Skill: "Play_VO_xiaoyaokun_skill"
  Encounter: "Play_VO_xiaoyaokun_escape"
  Dead: "Play_VO_xiaoyaokun_death"
  IllustrationOpen: "Play_VO_xiaoyaokun_logo"
  Captured: "Play_VO_xiaoyaokun_failedcapture"
  Touched: "Play_VO_xiaoyaokun_touched"
  FullStomach: "Play_VO_xiaoyaokun_befed"
  CasualA: "Play_VO_xiaoyaokun_rest_01"
  CasualB: "Play_VO_xiaoyaokun_rest_02"
  CasualC: "Play_VO_xiaoyaokun_rest_03"
  CasualD: "Play_VO_xiaoyaokun_rest_04"
  GetUp: "Play_VO_xiaoyaokun_getup"
  Mine: "Play_VO_xiaoyaokun_SOC"
  Log: "Play_VO_xiaoyaokun_SOC"
  ReactionA: "Play_VO_xiaoyaokun_reaction_01"
  ReactionB: "Play_VO_xiaoyaokun_reaction_02"
  ReactionC: "Play_VO_xiaoyaokun_reaction_03"
}
rows {
  Id: 2003601
  Bank: "SP_XingShou_LeiDianLu"
  BeHit: "Play_VO_leidianlu_hit"
  Attack: "Play_VO_leidianlu_attack"
  Skill: "Play_VO_leidianlu_skill"
  Encounter: "Play_VO_leidianlu_escape"
  Dead: "Play_VO_leidianlu_death"
  IllustrationOpen: "Play_VO_leidianlu_logo"
  Captured: "Play_VO_leidianlu_failedcapture"
  Touched: "Play_VO_leidianlu_touched"
  FullStomach: "Play_VO_leidianlu_befed"
  CasualA: "Play_VO_leidianlu_rest_01"
  CasualB: "Play_VO_leidianlu_rest_02"
  CasualC: "Play_VO_leidianlu_rest_03"
  CasualD: "Play_VO_leidianlu_rest_04"
  GetUp: "Play_VO_leidianlu_getup"
  Mine: "Play_VO_leidianlu_SOC"
  Log: "Play_VO_leidianlu_SOC"
  ReactionA: "Play_VO_leidianlu_reaction_01"
  ReactionB: "Play_VO_leidianlu_reaction_02"
  ReactionC: "Play_VO_leidianlu_reaction_03"
}
rows {
  Id: 2003701
  Bank: "SP_XingShou_LeiHou"
  BeHit: "Play_VO_leihou_hit"
  Attack: "Play_VO_leihou_attack"
  Skill: "Play_VO_leihou_skill"
  Encounter: "Play_VO_leihou_escape"
  Dead: "Play_VO_leihou_death"
  IllustrationOpen: "Play_VO_leihou_logo"
  Captured: "Play_VO_leihou_failedcapture"
  Touched: "Play_VO_leihou_touched"
  FullStomach: "Play_VO_leihou_befed"
  CasualA: "Play_VO_leihou_rest_01"
  CasualB: "Play_VO_leihou_rest_02"
  CasualC: "Play_VO_leihou_rest_03"
  CasualD: "Play_VO_leihou_rest_04"
  GetUp: "Play_VO_leihou_getup"
  Mine: "Play_VO_leihou_SOC"
  Log: "Play_VO_leihou_SOC"
  ReactionA: "Play_VO_leihou_reaction_01"
  ReactionB: "Play_VO_leihou_reaction_02"
  ReactionC: "Play_VO_leihou_reaction_03"
}
rows {
  Id: 2003801
  Bank: "SP_XingShou_XuanBingBaiHu"
  BeHit: "Play_VO_xuanbingbaihu_hit"
  Attack: "Play_VO_xuanbingbaihu_attack"
  Skill: "Play_VO_xuanbingbaihu_skill"
  Encounter: "Play_VO_xuanbingbaihu_escape"
  Dead: "Play_VO_xuanbingbaihu_death"
  IllustrationOpen: "Play_VO_xuanbingbaihu_logo"
  Captured: "Play_VO_xuanbingbaihu_failedcapture"
  Touched: "Play_VO_xuanbingbaihu_touched"
  FullStomach: "Play_VO_xuanbingbaihu_befed"
  CasualA: "Play_VO_xuanbingbaihu_rest_01"
  CasualB: "Play_VO_xuanbingbaihu_rest_02"
  CasualC: "Play_VO_xuanbingbaihu_rest_03"
  CasualD: "Play_VO_xuanbingbaihu_rest_04"
  GetUp: "Play_VO_xuanbingbaihu_getup"
  Mine: "Play_VO_xuanbingbaihu_SOC"
  Log: "Play_VO_xuanbingbaihu_SOC"
  ReactionA: "Play_VO_xuanbingbaihu_reaction_01"
  ReactionB: "Play_VO_xuanbingbaihu_reaction_02"
  ReactionC: "Play_VO_xuanbingbaihu_reaction_03"
}
rows {
  Id: 2003901
  Bank: "SP_XingShou_HuaSanXi"
  BeHit: "Play_VO_huasanxi_hit"
  Attack: "Play_VO_huasanxi_attack"
  Skill: "Play_VO_huasanxi_skill"
  Encounter: "Play_VO_huasanxi_escape"
  Dead: "Play_VO_huasanxi_death"
  IllustrationOpen: "Play_VO_huasanxi_logo"
  Captured: "Play_VO_huasanxi_failedcapture"
  Touched: "Play_VO_huasanxi_touched"
  FullStomach: "Play_VO_huasanxi_befed"
  CasualA: "Play_VO_huasanxi_rest_01"
  CasualB: "Play_VO_huasanxi_rest_02"
  CasualC: "Play_VO_huasanxi_rest_03"
  CasualD: "Play_VO_huasanxi_rest_04"
  GetUp: "Play_VO_huasanxi_getup"
  Mine: "Play_VO_huasanxi_SOC"
  Log: "Play_VO_huasanxi_SOC"
  ReactionA: "Play_VO_huasanxi_reaction_01"
  ReactionB: "Play_VO_huasanxi_reaction_02"
  ReactionC: "Play_VO_huasanxi_reaction_03"
}
rows {
  Id: 2004001
  Bank: "SP_XingShou_ShuiYunJun"
  BeHit: "Play_VO_shuiyunjun_hit"
  Attack: "Play_VO_shuiyunjun_attack"
  Skill: "Play_VO_shuiyunjun_skill"
  Encounter: "Play_VO_shuiyunjun_escape"
  Dead: "Play_VO_shuiyunjun_death"
  IllustrationOpen: "Play_VO_shuiyunjun_logo"
  Captured: "Play_VO_shuiyunjun_failedcapture"
  Touched: "Play_VO_shuiyunjun_touched"
  FullStomach: "Play_VO_shuiyunjun_befed"
  CasualA: "Play_VO_shuiyunjun_rest_01"
  CasualB: "Play_VO_shuiyunjun_rest_02"
  CasualC: "Play_VO_shuiyunjun_rest_03"
  CasualD: "Play_VO_shuiyunjun_rest_04"
  GetUp: "Play_VO_shuiyunjun_getup"
  Mine: "Play_VO_shuiyunjun_SOC"
  Log: "Play_VO_shuiyunjun_SOC"
  ReactionA: "Play_VO_shuiyunjun_reaction_01"
  ReactionB: "Play_VO_shuiyunjun_reaction_02"
  ReactionC: "Play_VO_shuiyunjun_reaction_03"
}
rows {
  Id: 2004101
  Bank: "SP_XingShou_BiHaiXuanWu"
  BeHit: "Play_VO_bihaixuanwu_hit"
  Attack: "Play_VO_bihaixuanwu_attack"
  Skill: "Play_VO_bihaixuanwu_skill"
  Encounter: "Play_VO_bihaixuanwu_escape"
  Dead: "Play_VO_bihaixuanwu_death"
  IllustrationOpen: "Play_VO_bihaixuanwu_logo"
  Captured: "Play_VO_bihaixuanwu_failedcapture"
  Touched: "Play_VO_bihaixuanwu_touched"
  FullStomach: "Play_VO_bihaixuanwu_befed"
  CasualA: "Play_VO_bihaixuanwu_rest_01"
  CasualB: "Play_VO_bihaixuanwu_rest_02"
  CasualC: "Play_VO_bihaixuanwu_rest_03"
  CasualD: "Play_VO_bihaixuanwu_rest_04"
  GetUp: "Play_VO_bihaixuanwu_getup"
  Mine: "Play_VO_bihaixuanwu_SOC"
  Log: "Play_VO_bihaixuanwu_SOC"
  ReactionA: "Play_VO_bihaixuanwu_reaction_01"
  ReactionB: "Play_VO_bihaixuanwu_reaction_02"
  ReactionC: "Play_VO_bihaixuanwu_reaction_03"
}
rows {
  Id: 2004201
  Bank: "SP_XingShou_YanShiFu"
  BeHit: "Play_VO_yanshifu_hit"
  Attack: "Play_VO_yanshifu_attack"
  Skill: "Play_VO_yanshifu_skill"
  Encounter: "Play_VO_yanshifu_escape"
  Dead: "Play_VO_yanshifu_death"
  IllustrationOpen: "Play_VO_yanshifu_logo"
  Captured: "Play_VO_yanshifu_failedcapture"
  Touched: "Play_VO_yanshifu_touched"
  FullStomach: "Play_VO_yanshifu_befed"
  CasualA: "Play_VO_yanshifu_rest_01"
  CasualB: "Play_VO_yanshifu_rest_02"
  CasualC: "Play_VO_yanshifu_rest_03"
  CasualD: "Play_VO_yanshifu_rest_04"
  GetUp: "Play_VO_yanshifu_getup"
  Mine: "Play_VO_yanshifu_SOC"
  Log: "Play_VO_yanshifu_SOC"
  ReactionA: "Play_VO_yanshifu_reaction_01"
  ReactionB: "Play_VO_yanshifu_reaction_02"
  ReactionC: "Play_VO_yanshifu_reaction_03"
}
rows {
  Id: 2004301
}
rows {
  Id: 2004401
  Bank: "SP_XingShou_BingDiJun"
  BeHit: "Play_VO_bingdijun_hit"
  Attack: "Play_VO_bingdijun_attack"
  Skill: "Play_VO_bingdijun_skill"
  Encounter: "Play_VO_bingdijun_escape"
  Dead: "Play_VO_bingdijun_death"
  IllustrationOpen: "Play_VO_bingdijun_logo"
  Captured: "Play_VO_bingdijun_failedcapture"
  Touched: "Play_VO_bingdijun_touched"
  FullStomach: "Play_VO_bingdijun_befed"
  CasualA: "Play_VO_bingdijun_rest_01"
  CasualB: "Play_VO_bingdijun_rest_02"
  CasualC: "Play_VO_bingdijun_rest_03"
  CasualD: "Play_VO_bingdijun_rest_04"
  GetUp: "Play_VO_bingdijun_getup"
  Mine: "Play_VO_bingdijun_SOC"
  Log: "Play_VO_bingdijun_SOC"
  ReactionA: "Play_VO_bingdijun_reaction_01"
  ReactionB: "Play_VO_bingdijun_reaction_02"
  ReactionC: "Play_VO_bingdijun_reaction_03"
}
rows {
  Id: 2004501
  Bank: "SP_XingShou_YuHuaLu"
  BeHit: "Play_VO_yuhualu_hit"
  Attack: "Play_VO_yuhualu_attack"
  Skill: "Play_VO_yuhualu_skill"
  Encounter: "Play_VO_yuhualu_escape"
  Dead: "Play_VO_yuhualu_death"
  IllustrationOpen: "Play_VO_yuhualu_logo"
  Captured: "Play_VO_yuhualu_failedcapture"
  Touched: "Play_VO_yuhualu_touched"
  FullStomach: "Play_VO_yuhualu_befed"
  CasualA: "Play_VO_yuhualu_rest_01"
  CasualB: "Play_VO_yuhualu_rest_02"
  CasualC: "Play_VO_yuhualu_rest_03"
  CasualD: "Play_VO_yuhualu_rest_04"
  GetUp: "Play_VO_yuhualu_getup"
  Mine: "Play_VO_yuhualu_SOC"
  Log: "Play_VO_yuhualu_SOC"
  ReactionA: "Play_VO_yuhualu_reaction_01"
  ReactionB: "Play_VO_yuhualu_reaction_02"
  ReactionC: "Play_VO_yuhualu_reaction_03"
}
rows {
  Id: 2004601
}
rows {
  Id: 2004701
}
rows {
  Id: 2004801
  Bank: "SP_XingShou_JinYingWei"
  BeHit: "Play_VO_jinyingwei_hit"
  Attack: "Play_VO_jinyingwei_attack"
  Skill: "Play_VO_jinyingwei_skill"
  Encounter: "Play_VO_jinyingwei_escape"
  Dead: "Play_VO_jinyingwei_death"
  IllustrationOpen: "Play_VO_jinyingwei_logo"
  Captured: "Play_VO_jinyingwei_failedcapture"
  Touched: "Play_VO_jinyingwei_touched"
  FullStomach: "Play_VO_jinyingwei_befed"
  CasualA: "Play_VO_jinyingwei_rest_01"
  CasualB: "Play_VO_jinyingwei_rest_02"
  CasualC: "Play_VO_jinyingwei_rest_03"
  CasualD: "Play_VO_jinyingwei_rest_04"
  GetUp: "Play_VO_jinyingwei_getup"
  Mine: "Play_VO_jinyingwei_SOC"
  Log: "Play_VO_jinyingwei_SOC"
  ReactionA: "Play_VO_jinyingwei_reaction_01"
  ReactionB: "Play_VO_jinyingwei_reaction_02"
  ReactionC: "Play_VO_jinyingwei_reaction_03"
}
rows {
  Id: 2004901
  Bank: "SP_XingShou_YueELing"
  BeHit: "Play_VO_yueeling_hit"
  Attack: "Play_VO_yueeling_attack"
  Skill: "Play_VO_yueeling_skill"
  Encounter: "Play_VO_yueeling_escape"
  Dead: "Play_VO_yueeling_death"
  IllustrationOpen: "Play_VO_yueeling_logo"
  Captured: "Play_VO_yueeling_failedcapture"
  Touched: "Play_VO_yueeling_touched"
  FullStomach: "Play_VO_yueeling_befed"
  CasualA: "Play_VO_yueeling_rest_01"
  CasualB: "Play_VO_yueeling_rest_02"
  CasualC: "Play_VO_yueeling_rest_03"
  CasualD: "Play_VO_yueeling_rest_04"
  GetUp: "Play_VO_yueeling_getup"
  Mine: "Play_VO_yueeling_SOC"
  Log: "Play_VO_yueeling_SOC"
  ReactionA: "Play_VO_yueeling_reaction_01"
  ReactionB: "Play_VO_yueeling_reaction_02"
  ReactionC: "Play_VO_yueeling_reaction_03"
}
rows {
  Id: 2005001
  Bank: "SP_XingShou_PuPu"
  BeHit: "Play_VO_pupu_hit"
  Attack: "Play_VO_pupu_attack"
  Skill: "Play_VO_pupu_skill"
  Encounter: "Play_VO_pupu_escape"
  Dead: "Play_VO_pupu_death"
  IllustrationOpen: "Play_VO_pupu_logo"
  Captured: "Play_VO_pupu_failedcapture"
  Touched: "Play_VO_pupu_touched"
  FullStomach: "Play_VO_pupu_befed"
  CasualA: "Play_VO_pupu_rest_01"
  CasualB: "Play_VO_pupu_rest_02"
  CasualC: "Play_VO_pupu_rest_03"
  CasualD: "Play_VO_pupu_rest_04"
  GetUp: "Play_VO_pupu_getup"
  Mine: "Play_VO_pupu_SOC"
  Log: "Play_VO_pupu_SOC"
  ReactionA: "Play_VO_pupu_reaction_01"
  ReactionB: "Play_VO_pupu_reaction_02"
  ReactionC: "Play_VO_pupu_reaction_03"
}
rows {
  Id: 2013201
  Bank: "SP_XingShou_PuPu"
  BeHit: "Play_VO_pupu_hit"
  Attack: "Play_VO_pupu_attack"
  Skill: "Play_VO_pupu_skill"
  Encounter: "Play_VO_pupu_escape"
  Dead: "Play_VO_pupu_death"
  IllustrationOpen: "Play_VO_pupu_logo"
  Captured: "Play_VO_pupu_failedcapture"
  Touched: "Play_VO_pupu_touched"
  FullStomach: "Play_VO_pupu_befed"
  CasualA: "Play_VO_pupu_rest_01"
  CasualB: "Play_VO_pupu_rest_02"
  CasualC: "Play_VO_pupu_rest_03"
  CasualD: "Play_VO_pupu_rest_04"
  GetUp: "Play_VO_pupu_getup"
  Mine: "Play_VO_pupu_SOC"
  Log: "Play_VO_pupu_SOC"
  ReactionA: "Play_VO_pupu_reaction_01"
  ReactionB: "Play_VO_pupu_reaction_02"
  ReactionC: "Play_VO_pupu_reaction_03"
}
rows {
  Id: 2005101
  Bank: "SP_XingShou_MengQi"
  BeHit: "Play_VO_mengqi_hit"
  Attack: "Play_VO_mengqi_attack"
  Skill: "Play_VO_mengqi_skill"
  Encounter: "Play_VO_mengqi_escape"
  Dead: "Play_VO_mengqi_death"
  IllustrationOpen: "Play_VO_mengqi_logo"
  Captured: "Play_VO_mengqi_failedcapture"
  Touched: "Play_VO_mengqi_touched"
  FullStomach: "Play_VO_mengqi_befed"
  CasualA: "Play_VO_mengqi_rest_01"
  CasualB: "Play_VO_mengqi_rest_02"
  CasualC: "Play_VO_mengqi_rest_03"
  CasualD: "Play_VO_mengqi_rest_04"
  GetUp: "Play_VO_mengqi_getup"
  Mine: "Play_VO_mengqi_SOC"
  Log: "Play_VO_mengqi_SOC"
  ReactionA: "Play_VO_mengqi_reaction_01"
  ReactionB: "Play_VO_mengqi_reaction_02"
  ReactionC: "Play_VO_mengqi_reaction_03"
}
rows {
  Id: 2005201
}
rows {
  Id: 2005401
  Bank: "SP_XingShou_BaJin"
  BeHit: "Play_VO_bajin_hit"
  Attack: "Play_VO_bajin_attack"
  Skill: "Play_VO_bajin_skill"
  Encounter: "Play_VO_bajin_escape"
  Dead: "Play_VO_bajin_death"
  IllustrationOpen: "Play_VO_bajin_logo"
  Captured: "Play_VO_bajin_failedcapture"
  Touched: "Play_VO_bajin_touched"
  FullStomach: "Play_VO_bajin_befed"
  CasualA: "Play_VO_bajin_rest_01"
  CasualB: "Play_VO_bajin_rest_02"
  CasualC: "Play_VO_bajin_rest_03"
  CasualD: "Play_VO_bajin_rest_04"
  GetUp: "Play_VO_bajin_getup"
  Mine: "Play_VO_bajin_SOC"
  Log: "Play_VO_bajin_SOC"
  ReactionA: "Play_VO_bajin_reaction_01"
  ReactionB: "Play_VO_bajin_reaction_02"
  ReactionC: "Play_VO_bajin_reaction_03"
}
rows {
  Id: 2005501
  Bank: "SP_XingShou_ErLiang"
  BeHit: "Play_VO_erliang_hit"
  Attack: "Play_VO_erliang_attack"
  Skill: "Play_VO_erliang_skill"
  Encounter: "Play_VO_erliang_escape"
  Dead: "Play_VO_erliang_death"
  IllustrationOpen: "Play_VO_erliang_logo"
  Captured: "Play_VO_erliang_failedcapture"
  Touched: "Play_VO_erliang_touched"
  FullStomach: "Play_VO_erliang_befed"
  CasualA: "Play_VO_erliang_rest_01"
  CasualB: "Play_VO_erliang_rest_02"
  CasualC: "Play_VO_erliang_rest_03"
  CasualD: "Play_VO_erliang_rest_04"
  GetUp: "Play_VO_erliang_getup"
  Mine: "Play_VO_erliang_SOC"
  Log: "Play_VO_erliang_SOC"
  ReactionA: "Play_VO_erliang_reaction_01"
  ReactionB: "Play_VO_erliang_reaction_02"
  ReactionC: "Play_VO_erliang_reaction_03"
}
rows {
  Id: 2005601
}
rows {
  Id: 2005701
  Bank: "SP_XingShou_YunChiLong"
  BeHit: "Play_VO_yunchilong_hit"
  Attack: "Play_VO_yunchilong_attack"
  Skill: "Play_VO_yunchilong_skill"
  Encounter: "Play_VO_yunchilong_escape"
  Dead: "Play_VO_yunchilong_death"
  IllustrationOpen: "Play_VO_yunchilong_logo"
  Captured: "Play_VO_yunchilong_failedcapture"
  Touched: "Play_VO_yunchilong_touched"
  FullStomach: "Play_VO_yunchilong_befed"
  CasualA: "Play_VO_yunchilong_rest_01"
  CasualB: "Play_VO_yunchilong_rest_02"
  CasualC: "Play_VO_yunchilong_rest_03"
  CasualD: "Play_VO_yunchilong_rest_04"
  GetUp: "Play_VO_yunchilong_getup"
  Mine: "Play_VO_yunchilong_SOC"
  Log: "Play_VO_yunchilong_SOC"
  ReactionA: "Play_VO_yunchilong_reaction_01"
  ReactionB: "Play_VO_yunchilong_reaction_02"
  ReactionC: "Play_VO_yunchilong_reaction_03"
}
rows {
  Id: 2005801
}
rows {
  Id: 2005901
  Bank: "SP_XingShou_MengYunYao"
  BeHit: "Play_VO_mengyunyao_hit"
  Attack: "Play_VO_mengyunyao_attack"
  Skill: "Play_VO_mengyunyao_skill"
  Encounter: "Play_VO_mengyunyao_escape"
  Dead: "Play_VO_mengyunyao_death"
  IllustrationOpen: "Play_VO_mengyunyao_logo"
  Captured: "Play_VO_mengyunyao_failedcapture"
  Touched: "Play_VO_mengyunyao_touched"
  FullStomach: "Play_VO_mengyunyao_befed"
  CasualA: "Play_VO_mengyunyao_rest_01"
  CasualB: "Play_VO_mengyunyao_rest_02"
  CasualC: "Play_VO_mengyunyao_rest_03"
  CasualD: "Play_VO_mengyunyao_rest_04"
  GetUp: "Play_VO_mengyunyao_getup"
  Mine: "Play_VO_mengyunyao_SOC"
  Log: "Play_VO_mengyunyao_SOC"
  ReactionA: "Play_VO_mengyunyao_reaction_01"
  ReactionB: "Play_VO_mengyunyao_reaction_02"
  ReactionC: "Play_VO_mengyunyao_reaction_03"
}
rows {
  Id: 2006001
}
rows {
  Id: 2006101
}
rows {
  Id: 2006201
  Bank: "SP_XingShou_YeLingHu"
  BeHit: "Play_VO_yelinghu_hit"
  Attack: "Play_VO_yelinghu_attack"
  Skill: "Play_VO_yelinghu_skill"
  Encounter: "Play_VO_yelinghu_escape"
  Dead: "Play_VO_yelinghu_death"
  IllustrationOpen: "Play_VO_yelinghu_logo"
  Captured: "Play_VO_yelinghu_failedcapture"
  Touched: "Play_VO_yelinghu_touched"
  FullStomach: "Play_VO_yelinghu_befed"
  CasualA: "Play_VO_yelinghu_rest_01"
  CasualB: "Play_VO_yelinghu_rest_02"
  CasualC: "Play_VO_yelinghu_rest_03"
  CasualD: "Play_VO_yelinghu_rest_04"
  GetUp: "Play_VO_yelinghu_getup"
  Mine: "Play_VO_yelinghu_SOC"
  Log: "Play_VO_yelinghu_SOC"
  ReactionA: "Play_VO_yelinghu_reaction_01"
  ReactionB: "Play_VO_yelinghu_reaction_02"
  ReactionC: "Play_VO_yelinghu_reaction_03"
}
rows {
  Id: 2006301
  Bank: "SP_XingShou_HuiWuYao"
  BeHit: "Play_VO_huiwuyao_hit"
  Attack: "Play_VO_huiwuyao_attack"
  Skill: "Play_VO_huiwuyao_skill"
  Encounter: "Play_VO_huiwuyao_escape"
  Dead: "Play_VO_huiwuyao_death"
  IllustrationOpen: "Play_VO_huiwuyao_logo"
  Captured: "Play_VO_huiwuyao_failedcapture"
  Touched: "Play_VO_huiwuyao_touched"
  FullStomach: "Play_VO_huiwuyao_befed"
  CasualA: "Play_VO_huiwuyao_rest_01"
  CasualB: "Play_VO_huiwuyao_rest_02"
  CasualC: "Play_VO_huiwuyao_rest_03"
  CasualD: "Play_VO_huiwuyao_rest_04"
  GetUp: "Play_VO_huiwuyao_getup"
  Mine: "Play_VO_huiwuyao_SOC"
  Log: "Play_VO_huiwuyao_SOC"
  ReactionA: "Play_VO_huiwuyao_reaction_01"
  ReactionB: "Play_VO_huiwuyao_reaction_02"
  ReactionC: "Play_VO_huiwuyao_reaction_03"
}
rows {
  Id: 2006401
  Bank: "SP_XingShou_YingShiFu"
  BeHit: "Play_VO_yingshifu_hit"
  Attack: "Play_VO_yingshifu_attack"
  Skill: "Play_VO_yingshifu_skill"
  Encounter: "Play_VO_yingshifu_escape"
  Dead: "Play_VO_yingshifu_death"
  IllustrationOpen: "Play_VO_yingshifu_logo"
  Captured: "Play_VO_yingshifu_failedcapture"
  Touched: "Play_VO_yingshifu_touched"
  FullStomach: "Play_VO_yingshifu_befed"
  CasualA: "Play_VO_yingshifu_rest_01"
  CasualB: "Play_VO_yingshifu_rest_02"
  CasualC: "Play_VO_yingshifu_rest_03"
  CasualD: "Play_VO_yingshifu_rest_04"
  GetUp: "Play_VO_yingshifu_getup"
  Mine: "Play_VO_yingshifu_SOC"
  Log: "Play_VO_yingshifu_SOC"
  ReactionA: "Play_VO_yingshifu_reaction_01"
  ReactionB: "Play_VO_yingshifu_reaction_02"
  ReactionC: "Play_VO_yingshifu_reaction_03"
}
rows {
  Id: 2006501
}
rows {
  Id: 2006601
  Bank: "SP_XingShou_HaiYangXiong"
  BeHit: "Play_VO_haiyangxiong_hit"
  Attack: "Play_VO_haiyangxiong_attack"
  Skill: "Play_VO_haiyangxiong_skill"
  Encounter: "Play_VO_haiyangxiong_escape"
  Dead: "Play_VO_haiyangxiong_death"
  IllustrationOpen: "Play_VO_haiyangxiong_logo"
  Captured: "Play_VO_haiyangxiong_failedcapture"
  Touched: "Play_VO_haiyangxiong_touched"
  FullStomach: "Play_VO_haiyangxiong_befed"
  CasualA: "Play_VO_haiyangxiong_rest_01"
  CasualB: "Play_VO_haiyangxiong_rest_02"
  CasualC: "Play_VO_haiyangxiong_rest_03"
  CasualD: "Play_VO_haiyangxiong_rest_04"
  GetUp: "Play_VO_haiyangxiong_getup"
  Mine: "Play_VO_haiyangxiong_SOC"
  Log: "Play_VO_haiyangxiong_SOC"
  ReactionA: "Play_VO_haiyangxiong_reaction_01"
  ReactionB: "Play_VO_haiyangxiong_reaction_02"
  ReactionC: "Play_VO_haiyangxiong_reaction_03"
}
rows {
  Id: 2006701
}
rows {
  Id: 2006801
  Bank: "SP_XingShou_JinZongZhu"
  BeHit: "Play_VO_jinzongzhu_hit"
  Attack: "Play_VO_jinzongzhu_attack"
  Skill: "Play_VO_jinzongzhu_skill"
  Encounter: "Play_VO_jinzongzhu_escape"
  Dead: "Play_VO_jinzongzhu_death"
  IllustrationOpen: "Play_VO_jinzongzhu_logo"
  Captured: "Play_VO_jinzongzhu_failedcapture"
  Touched: "Play_VO_jinzongzhu_touched"
  FullStomach: "Play_VO_jinzongzhu_befed"
  CasualA: "Play_VO_jinzongzhu_rest_01"
  CasualB: "Play_VO_jinzongzhu_rest_02"
  CasualC: "Play_VO_jinzongzhu_rest_03"
  CasualD: "Play_VO_jinzongzhu_rest_04"
  GetUp: "Play_VO_jinzongzhu_getup"
  Mine: "Play_VO_jinzongzhu_SOC"
  Log: "Play_VO_jinzongzhu_SOC"
  ReactionA: "Play_VO_jinzongzhu_reaction_01"
  ReactionB: "Play_VO_jinzongzhu_reaction_02"
  ReactionC: "Play_VO_jinzongzhu_reaction_03"
}
rows {
  Id: 2006901
}
rows {
  Id: 2007001
  Bank: "SP_XingShou_QingYingXiongMao"
  BeHit: "Play_VO_qingyingxiongmao_hit"
  Attack: "Play_VO_qingyingxiongmao_attack"
  Skill: "Play_VO_qingyingxiongmao_skill"
  Encounter: "Play_VO_qingyingxiongmao_escape"
  Dead: "Play_VO_qingyingxiongmao_death"
  IllustrationOpen: "Play_VO_qingyingxiongmao_logo"
  Captured: "Play_VO_qingyingxiongmao_failedcapture"
  Touched: "Play_VO_qingyingxiongmao_touched"
  FullStomach: "Play_VO_qingyingxiongmao_befed"
  CasualA: "Play_VO_qingyingxiongmao_rest_01"
  CasualB: "Play_VO_qingyingxiongmao_rest_02"
  CasualC: "Play_VO_qingyingxiongmao_rest_03"
  CasualD: "Play_VO_qingyingxiongmao_rest_04"
  GetUp: "Play_VO_qingyingxiongmao_getup"
  Mine: "Play_VO_qingyingxiongmao_SOC"
  Log: "Play_VO_qingyingxiongmao_SOC"
  ReactionA: "Play_VO_qingyingxiongmao_reaction_01"
  ReactionB: "Play_VO_qingyingxiongmao_reaction_02"
  ReactionC: "Play_VO_qingyingxiongmao_reaction_03"
}
rows {
  Id: 2007101
  Bank: "SP_XingShou_QingXiuJun"
  BeHit: "Play_VO_qingxiujun_hit"
  Attack: "Play_VO_qingxiujun_attack"
  Skill: "Play_VO_qingxiujun_skill"
  Encounter: "Play_VO_qingxiujun_escape"
  Dead: "Play_VO_qingxiujun_death"
  IllustrationOpen: "Play_VO_qingxiujun_logo"
  Captured: "Play_VO_qingxiujun_failedcapture"
  Touched: "Play_VO_qingxiujun_touched"
  FullStomach: "Play_VO_qingxiujun_befed"
  CasualA: "Play_VO_qingxiujun_rest_01"
  CasualB: "Play_VO_qingxiujun_rest_02"
  CasualC: "Play_VO_qingxiujun_rest_03"
  CasualD: "Play_VO_qingxiujun_rest_04"
  GetUp: "Play_VO_qingxiujun_getup"
  Mine: "Play_VO_qingxiujun_SOC"
  Log: "Play_VO_qingxiujun_SOC"
  ReactionA: "Play_VO_qingxiujun_reaction_01"
  ReactionB: "Play_VO_qingxiujun_reaction_02"
  ReactionC: "Play_VO_qingxiujun_reaction_03"
}
rows {
  Id: 2007201
}
rows {
  Id: 2007301
  Bank: "SP_XingShou_HuoQuanShu"
  BeHit: "Play_VO_huoquanshu_hit"
  Attack: "Play_VO_huoquanshu_attack"
  Skill: "Play_VO_huoquanshu_skill"
  Encounter: "Play_VO_huoquanshu_escape"
  Dead: "Play_VO_huoquanshu_death"
  IllustrationOpen: "Play_VO_huoquanshu_logo"
  Captured: "Play_VO_huoquanshu_failedcapture"
  Touched: "Play_VO_huoquanshu_touched"
  FullStomach: "Play_VO_huoquanshu_befed"
  CasualA: "Play_VO_huoquanshu_rest_01"
  CasualB: "Play_VO_huoquanshu_rest_02"
  CasualC: "Play_VO_huoquanshu_rest_03"
  CasualD: "Play_VO_huoquanshu_rest_04"
  GetUp: "Play_VO_huoquanshu_getup"
  Mine: "Play_VO_huoquanshu_SOC"
  Log: "Play_VO_huoquanshu_SOC"
  ReactionA: "Play_VO_huoquanshu_reaction_01"
  ReactionB: "Play_VO_huoquanshu_reaction_02"
  ReactionC: "Play_VO_huoquanshu_reaction_03"
}
rows {
  Id: 2007401
}
rows {
  Id: 2007501
  Bank: "SP_XingShou_ChiXiongMao"
  BeHit: "Play_VO_chixiongmao_hit"
  Attack: "Play_VO_chixiongmao_attack"
  Skill: "Play_VO_chixiongmao_skill"
  Encounter: "Play_VO_chixiongmao_escape"
  Dead: "Play_VO_chixiongmao_death"
  IllustrationOpen: "Play_VO_chixiongmao_logo"
  Captured: "Play_VO_chixiongmao_failedcapture"
  Touched: "Play_VO_chixiongmao_touched"
  FullStomach: "Play_VO_chixiongmao_befed"
  CasualA: "Play_VO_chixiongmao_rest_01"
  CasualB: "Play_VO_chixiongmao_rest_02"
  CasualC: "Play_VO_chixiongmao_rest_03"
  CasualD: "Play_VO_chixiongmao_rest_04"
  GetUp: "Play_VO_chixiongmao_getup"
  Mine: "Play_VO_chixiongmao_SOC"
  Log: "Play_VO_chixiongmao_SOC"
  ReactionA: "Play_VO_chixiongmao_reaction_01"
  ReactionB: "Play_VO_chixiongmao_reaction_02"
  ReactionC: "Play_VO_chixiongmao_reaction_03"
}
rows {
  Id: 2007601
}
rows {
  Id: 2007701
  Bank: "SP_XingShou_ShiMeiZhu"
  BeHit: "Play_VO_shimeizhu_hit"
  Attack: "Play_VO_shimeizhu_attack"
  Skill: "Play_VO_shimeizhu_skill"
  Encounter: "Play_VO_shimeizhu_escape"
  Dead: "Play_VO_shimeizhu_death"
  IllustrationOpen: "Play_VO_shimeizhu_logo"
  Captured: "Play_VO_shimeizhu_failedcapture"
  Touched: "Play_VO_shimeizhu_touched"
  FullStomach: "Play_VO_shimeizhu_befed"
  CasualA: "Play_VO_shimeizhu_rest_01"
  CasualB: "Play_VO_shimeizhu_rest_02"
  CasualC: "Play_VO_shimeizhu_rest_03"
  CasualD: "Play_VO_shimeizhu_rest_04"
  GetUp: "Play_VO_shimeizhu_getup"
  Mine: "Play_VO_shimeizhu_SOC"
  Log: "Play_VO_shimeizhu_SOC"
  ReactionA: "Play_VO_shimeizhu_reaction_01"
  ReactionB: "Play_VO_shimeizhu_reaction_02"
  ReactionC: "Play_VO_shimeizhu_reaction_03"
}
rows {
  Id: 2007801
}
rows {
  Id: 2007901
  Bank: "SP_XingShou_QianShanNiu"
  BeHit: "Play_VO_qianshanniu_hit"
  Attack: "Play_VO_qianshanniu_attack"
  Skill: "Play_VO_qianshanniu_skill"
  Encounter: "Play_VO_qianshanniu_escape"
  Dead: "Play_VO_qianshanniu_death"
  IllustrationOpen: "Play_VO_qianshanniu_logo"
  Captured: "Play_VO_qianshanniu_failedcapture"
  Touched: "Play_VO_qianshanniu_touched"
  FullStomach: "Play_VO_qianshanniu_befed"
  CasualA: "Play_VO_qianshanniu_rest_01"
  CasualB: "Play_VO_qianshanniu_rest_02"
  CasualC: "Play_VO_qianshanniu_rest_03"
  CasualD: "Play_VO_qianshanniu_rest_04"
  GetUp: "Play_VO_qianshanniu_getup"
  Mine: "Play_VO_qianshanniu_SOC"
  Log: "Play_VO_qianshanniu_SOC"
  ReactionA: "Play_VO_qianshanniu_reaction_01"
  ReactionB: "Play_VO_qianshanniu_reaction_02"
  ReactionC: "Play_VO_qianshanniu_reaction_03"
}
rows {
  Id: 2008001
  Bank: "SP_XingShou_RongYuanShan"
  BeHit: "Play_VO_rongyuanshan_hit"
  Attack: "Play_VO_rongyuanshan_attack"
  Skill: "Play_VO_rongyuanshan_skill"
  Encounter: "Play_VO_rongyuanshan_escape"
  Dead: "Play_VO_rongyuanshan_death"
  IllustrationOpen: "Play_VO_rongyuanshan_logo"
  Captured: "Play_VO_rongyuanshan_failedcapture"
  Touched: "Play_VO_rongyuanshan_touched"
  FullStomach: "Play_VO_rongyuanshan_befed"
  CasualA: "Play_VO_rongyuanshan_rest_01"
  CasualB: "Play_VO_rongyuanshan_rest_02"
  CasualC: "Play_VO_rongyuanshan_rest_03"
  CasualD: "Play_VO_rongyuanshan_rest_04"
  GetUp: "Play_VO_rongyuanshan_getup"
  Mine: "Play_VO_rongyuanshan_SOC"
  Log: "Play_VO_rongyuanshan_SOC"
  ReactionA: "Play_VO_rongyuanshan_reaction_01"
  ReactionB: "Play_VO_rongyuanshan_reaction_02"
  ReactionC: "Play_VO_rongyuanshan_reaction_03"
}
rows {
  Id: 2008401
  Bank: "SP_XingShou_XingXingQuan"
  BeHit: "Play_VO_xingxingquan_hit"
  Attack: "Play_VO_xingxingquan_attack"
  Skill: "Play_VO_xingxingquan_skill"
  Encounter: "Play_VO_xingxingquan_escape"
  Dead: "Play_VO_xingxingquan_death"
  IllustrationOpen: "Play_VO_xingxingquan_logo"
  Captured: "Play_VO_xingxingquan_failedcapture"
  Touched: "Play_VO_xingxingquan_touched"
  FullStomach: "Play_VO_xingxingquan_befed"
  CasualA: "Play_VO_xingxingquan_rest_01"
  CasualB: "Play_VO_xingxingquan_rest_02"
  CasualC: "Play_VO_xingxingquan_rest_03"
  CasualD: "Play_VO_xingxingquan_rest_04"
  GetUp: "Play_VO_xingxingquan_getup"
  Mine: "Play_VO_xingxingquan_SOC"
  Log: "Play_VO_xingxingquan_SOC"
  ReactionA: "Play_VO_xingxingquan_reaction_01"
  ReactionB: "Play_VO_xingxingquan_reaction_02"
  ReactionC: "Play_VO_xingxingquan_reaction_03"
}
rows {
  Id: 2009001
  Bank: "SP_XingShou_QingHuaYang"
  BeHit: "Play_VO_qinghuayang_hit"
  Attack: "Play_VO_qinghuayang_attack"
  Skill: "Play_VO_qinghuayang_skill"
  Encounter: "Play_VO_qinghuayang_escape"
  Dead: "Play_VO_qinghuayang_death"
  IllustrationOpen: "Play_VO_qinghuayang_logo"
  Captured: "Play_VO_qinghuayang_failedcapture"
  Touched: "Play_VO_qinghuayang_touched"
  FullStomach: "Play_VO_qinghuayang_befed"
  CasualA: "Play_VO_qinghuayang_rest_01"
  CasualB: "Play_VO_qinghuayang_rest_02"
  CasualC: "Play_VO_qinghuayang_rest_03"
  CasualD: "Play_VO_qinghuayang_rest_04"
  GetUp: "Play_VO_qinghuayang_getup"
  Mine: "Play_VO_qinghuayang_SOC"
  Log: "Play_VO_qinghuayang_SOC"
  ReactionA: "Play_VO_qinghuayang_reaction_01"
  ReactionB: "Play_VO_qinghuayang_reaction_02"
  ReactionC: "Play_VO_qinghuayang_reaction_03"
}
rows {
  Id: 2009101
  Bank: "SP_XingShou_LanBeiKe"
  BeHit: "Play_VO_lanbeike_hit"
  Attack: "Play_VO_lanbeike_attack"
  Skill: "Play_VO_lanbeike_skill"
  Encounter: "Play_VO_lanbeike_escape"
  Dead: "Play_VO_lanbeike_death"
  IllustrationOpen: "Play_VO_lanbeike_logo"
  Captured: "Play_VO_lanbeike_failedcapture"
  Touched: "Play_VO_lanbeike_touched"
  FullStomach: "Play_VO_lanbeike_befed"
  CasualA: "Play_VO_lanbeike_rest_01"
  CasualB: "Play_VO_lanbeike_rest_02"
  CasualC: "Play_VO_lanbeike_rest_03"
  CasualD: "Play_VO_lanbeike_rest_04"
  GetUp: "Play_VO_lanbeike_getup"
  Mine: "Play_VO_lanbeike_SOC"
  Log: "Play_VO_lanbeike_SOC"
  ReactionA: "Play_VO_lanbeike_reaction_01"
  ReactionB: "Play_VO_lanbeike_reaction_02"
  ReactionC: "Play_VO_lanbeike_reaction_03"
}
rows {
  Id: 2008301
  Bank: "SP_XingShou_RuiJinChan"
  BeHit: "Play_VO_ruijinchan_hit"
  Attack: "Play_VO_ruijinchan_attack"
  Skill: "Play_VO_ruijinchan_skill"
  Encounter: "Play_VO_ruijinchan_escape"
  Dead: "Play_VO_ruijinchan_death"
  IllustrationOpen: "Play_VO_ruijinchan_logo"
  Captured: "Play_VO_ruijinchan_failedcapture"
  Touched: "Play_VO_ruijinchan_touched"
  FullStomach: "Play_VO_ruijinchan_befed"
  CasualA: "Play_VO_ruijinchan_rest_01"
  CasualB: "Play_VO_ruijinchan_rest_02"
  CasualC: "Play_VO_ruijinchan_rest_03"
  CasualD: "Play_VO_ruijinchan_rest_04"
  GetUp: "Play_VO_ruijinchan_getup"
  Mine: "Play_VO_ruijinchan_SOC"
  Log: "Play_VO_ruijinchan_SOC"
  ReactionA: "Play_VO_ruijinchan_reaction_01"
  ReactionB: "Play_VO_ruijinchan_reaction_02"
  ReactionC: "Play_VO_ruijinchan_reaction_03"
}
rows {
  Id: 2009401
  Bank: "SP_XingShou_MoGuiYu"
  BeHit: "Play_VO_moguiyu_hit"
  Attack: "Play_VO_moguiyu_attack"
  Skill: "Play_VO_moguiyu_skill"
  Encounter: "Play_VO_moguiyu_escape"
  Dead: "Play_VO_moguiyu_death"
  IllustrationOpen: "Play_VO_moguiyu_logo"
  Captured: "Play_VO_moguiyu_failedcapture"
  Touched: "Play_VO_moguiyu_touched"
  FullStomach: "Play_VO_moguiyu_befed"
  CasualA: "Play_VO_moguiyu_rest_01"
  CasualB: "Play_VO_moguiyu_rest_02"
  CasualC: "Play_VO_moguiyu_rest_03"
  CasualD: "Play_VO_moguiyu_rest_04"
  GetUp: "Play_VO_moguiyu_getup"
  Mine: "Play_VO_moguiyu_SOC"
  Log: "Play_VO_moguiyu_SOC"
  ReactionA: "Play_VO_moguiyu_reaction_01"
  ReactionB: "Play_VO_moguiyu_reaction_02"
  ReactionC: "Play_VO_moguiyu_reaction_03"
}
rows {
  Id: 2009601
  Bank: "SP_XingShou_ShuiDiXie"
  BeHit: "Play_VO_shuidixie_hit"
  Attack: "Play_VO_shuidixie_attack"
  Skill: "Play_VO_shuidixie_skill"
  Encounter: "Play_VO_shuidixie_escape"
  Dead: "Play_VO_shuidixie_death"
  IllustrationOpen: "Play_VO_shuidixie_logo"
  Captured: "Play_VO_shuidixie_failedcapture"
  Touched: "Play_VO_shuidixie_touched"
  FullStomach: "Play_VO_shuidixie_befed"
  CasualA: "Play_VO_shuidixie_rest_01"
  CasualB: "Play_VO_shuidixie_rest_02"
  CasualC: "Play_VO_shuidixie_rest_03"
  CasualD: "Play_VO_shuidixie_rest_04"
  GetUp: "Play_VO_shuidixie_getup"
  Mine: "Play_VO_shuidixie_SOC"
  Log: "Play_VO_shuidixie_SOC"
  ReactionA: "Play_VO_shuidixie_reaction_01"
  ReactionB: "Play_VO_shuidixie_reaction_02"
  ReactionC: "Play_VO_shuidixie_reaction_03"
}
rows {
  Id: 2009701
  Bank: "SP_XingShou_NanGuaHou"
  BeHit: "Play_VO_nanguahou_hit"
  Attack: "Play_VO_nanguahou_attack"
  Skill: "Play_VO_nanguahou_skill"
  Encounter: "Play_VO_nanguahou_escape"
  Dead: "Play_VO_nanguahou_death"
  IllustrationOpen: "Play_VO_nanguahou_logo"
  Captured: "Play_VO_nanguahou_failedcapture"
  Touched: "Play_VO_nanguahou_touched"
  FullStomach: "Play_VO_nanguahou_befed"
  CasualA: "Play_VO_nanguahou_rest_01"
  CasualB: "Play_VO_nanguahou_rest_02"
  CasualC: "Play_VO_nanguahou_rest_03"
  CasualD: "Play_VO_nanguahou_rest_04"
  GetUp: "Play_VO_nanguahou_getup"
  Mine: "Play_VO_nanguahou_SOC"
  Log: "Play_VO_nanguahou_SOC"
  ReactionA: "Play_VO_nanguahou_reaction_01"
  ReactionB: "Play_VO_nanguahou_reaction_02"
  ReactionC: "Play_VO_nanguahou_reaction_03"
}
rows {
  Id: 2009801
  Bank: "SP_XingShou_YanGuMiao"
  BeHit: "Play_VO_yangumiao_hit"
  Attack: "Play_VO_yangumiao_attack"
  Skill: "Play_VO_yangumiao_skill"
  Encounter: "Play_VO_yangumiao_escape"
  Dead: "Play_VO_yangumiao_death"
  IllustrationOpen: "Play_VO_yangumiao_logo"
  Captured: "Play_VO_yangumiao_failedcapture"
  Touched: "Play_VO_yangumiao_touched"
  FullStomach: "Play_VO_yangumiao_befed"
  CasualA: "Play_VO_yangumiao_rest_01"
  CasualB: "Play_VO_yangumiao_rest_02"
  CasualC: "Play_VO_yangumiao_rest_03"
  CasualD: "Play_VO_yangumiao_rest_04"
  GetUp: "Play_VO_yangumiao_getup"
  Mine: "Play_VO_yangumiao_SOC"
  Log: "Play_VO_yangumiao_SOC"
  ReactionA: "Play_VO_yangumiao_reaction_01"
  ReactionB: "Play_VO_yangumiao_reaction_02"
  ReactionC: "Play_VO_yangumiao_reaction_03"
}
rows {
  Id: 2012501
  Bank: "SP_XingShou_YanGuMiao"
  BeHit: "Play_VO_yangumiao_hit"
  Attack: "Play_VO_yangumiao_attack"
  Skill: "Play_VO_yangumiao_skill"
  Encounter: "Play_VO_yangumiao_escape"
  Dead: "Play_VO_yangumiao_death"
  IllustrationOpen: "Play_VO_yangumiao_logo"
  Captured: "Play_VO_yangumiao_failedcapture"
  Touched: "Play_VO_yangumiao_touched"
  FullStomach: "Play_VO_yangumiao_befed"
  CasualA: "Play_VO_yangumiao_rest_01"
  CasualB: "Play_VO_yangumiao_rest_02"
  CasualC: "Play_VO_yangumiao_rest_03"
  CasualD: "Play_VO_yangumiao_rest_04"
  GetUp: "Play_VO_yangumiao_getup"
  Mine: "Play_VO_yangumiao_SOC"
  Log: "Play_VO_yangumiao_SOC"
  ReactionA: "Play_VO_yangumiao_reaction_01"
  ReactionB: "Play_VO_yangumiao_reaction_02"
  ReactionC: "Play_VO_yangumiao_reaction_03"
}
rows {
  Id: 2010601
  Bank: "SP_XingShou_XingXingQuan"
  BeHit: "Play_VO_xingxingquan_hit"
  Attack: "Play_VO_xingxingquan_attack"
  Skill: "Play_VO_xingxingquan_skill"
  Encounter: "Play_VO_xingxingquan_escape"
  Dead: "Play_VO_xingxingquan_death"
  IllustrationOpen: "Play_VO_xingxingquan_logo"
  Captured: "Play_VO_xingxingquan_failedcapture"
  Touched: "Play_VO_xingxingquan_touched"
  FullStomach: "Play_VO_xingxingquan_befed"
  CasualA: "Play_VO_xingxingquan_rest_01"
  CasualB: "Play_VO_xingxingquan_rest_02"
  CasualC: "Play_VO_xingxingquan_rest_03"
  CasualD: "Play_VO_xingxingquan_rest_04"
  GetUp: "Play_VO_xingxingquan_getup"
  Mine: "Play_VO_xingxingquan_SOC"
  Log: "Play_VO_xingxingquan_SOC"
  ReactionA: "Play_VO_xingxingquan_reaction_01"
  ReactionB: "Play_VO_xingxingquan_reaction_02"
  ReactionC: "Play_VO_xingxingquan_reaction_03"
}
rows {
  Id: 2010801
  Bank: "SP_XingShou_TianLu"
  BeHit: "Play_VO_tianlu_hit"
  Attack: "Play_VO_tianlu_attack"
  Skill: "Play_VO_tianlu_skill"
  Encounter: "Play_VO_tianlu_escape"
  Dead: "Play_VO_tianlu_death"
  IllustrationOpen: "Play_VO_tianlu_logo"
  Captured: "Play_VO_tianlu_failedcapture"
  Touched: "Play_VO_tianlu_touched"
  FullStomach: "Play_VO_tianlu_befed"
  CasualA: "Play_VO_tianlu_rest_01"
  CasualB: "Play_VO_tianlu_rest_02"
  CasualC: "Play_VO_tianlu_rest_03"
  CasualD: "Play_VO_tianlu_rest_04"
  GetUp: "Play_VO_tianlu_getup"
  Mine: "Play_VO_tianlu_SOC"
  Log: "Play_VO_tianlu_SOC"
  ReactionA: "Play_VO_tianlu_reaction_01"
  ReactionB: "Play_VO_tianlu_reaction_02"
  ReactionC: "Play_VO_tianlu_reaction_03"
}
rows {
  Id: 2012801
  Bank: "SP_XingShou_ShuiDiXie"
  BeHit: "Play_VO_shuidixie_hit"
  Attack: "Play_VO_shuidixie_attack"
  Skill: "Play_VO_shuidixie_skill"
  Encounter: "Play_VO_shuidixie_escape"
  Dead: "Play_VO_shuidixie_death"
  IllustrationOpen: "Play_VO_shuidixie_logo"
  Captured: "Play_VO_shuidixie_failedcapture"
  Touched: "Play_VO_shuidixie_touched"
  FullStomach: "Play_VO_shuidixie_befed"
  CasualA: "Play_VO_shuidixie_rest_01"
  CasualB: "Play_VO_shuidixie_rest_02"
  CasualC: "Play_VO_shuidixie_rest_03"
  CasualD: "Play_VO_shuidixie_rest_04"
  GetUp: "Play_VO_shuidixie_getup"
  Mine: "Play_VO_shuidixie_SOC"
  Log: "Play_VO_shuidixie_SOC"
  ReactionA: "Play_VO_shuidixie_reaction_01"
  ReactionB: "Play_VO_shuidixie_reaction_02"
  ReactionC: "Play_VO_shuidixie_reaction_03"
}
rows {
  Id: 2013101
  Bank: "SP_XingShou_Longma"
  BeHit: "Play_VO_longma_hit"
  Attack: "Play_VO_longma_attack"
  Skill: "Play_VO_longma_skill"
  Encounter: "Play_VO_longma_escape"
  Dead: "Play_VO_longma_death"
  IllustrationOpen: "Play_VO_longma_logo"
  Captured: "Play_VO_longma_failedcapture"
  Touched: "Play_VO_longma_touched"
  FullStomach: "Play_VO_longma_befed"
  CasualA: "Play_VO_longma_rest_01"
  CasualB: "Play_VO_longma_rest_02"
  CasualC: "Play_VO_longma_rest_03"
  CasualD: "Play_VO_longma_rest_04"
  GetUp: "Play_VO_longma_getup"
  Mine: "Play_VO_longma_SOC"
  Log: "Play_VO_longma_SOC"
  ReactionA: "Play_VO_longma_reaction_01"
  ReactionB: "Play_VO_longma_reaction_02"
  ReactionC: "Play_VO_longma_reaction_03"
}
rows {
  Id: 2013501
  Bank: "SP_XingShou_FaJiaoHou"
  BeHit: "Play_VO_fajiaohou_hit"
  Attack: "Play_VO_fajiaohou_attack"
  Skill: "Play_VO_fajiaohou_skill"
  Encounter: "Play_VO_fajiaohou_escape"
  Dead: "Play_VO_fajiaohou_death"
  IllustrationOpen: "Play_VO_fajiaohou_logo"
  Captured: "Play_VO_fajiaohou_failedcapture"
  Touched: "Play_VO_fajiaohou_touched"
  FullStomach: "Play_VO_fajiaohou_befed"
  CasualA: "Play_VO_fajiaohou_rest_01"
  CasualB: "Play_VO_fajiaohou_rest_02"
  CasualC: "Play_VO_fajiaohou_rest_03"
  CasualD: "Play_VO_fajiaohou_rest_04"
  GetUp: "Play_VO_fajiaohou_getup"
  Mine: "Play_VO_fajiaohou_SOC"
  Log: "Play_VO_fajiaohou_SOC"
  ReactionA: "Play_VO_fajiaohou_reaction_01"
  ReactionB: "Play_VO_fajiaohou_reaction_02"
  ReactionC: "Play_VO_fajiaohou_reaction_03"
}
rows {
  Id: 2010701
  Bank: "SP_XingShou_DaoDanYou"
  BeHit: "Play_VO_daodanyou_hit"
  Attack: "Play_VO_daodanyou_attack"
  Skill: "Play_VO_daodanyou_skill"
  Encounter: "Play_VO_daodanyou_escape"
  Dead: "Play_VO_daodanyou_death"
  IllustrationOpen: "Play_VO_daodanyou_logo"
  Captured: "Play_VO_daodanyou_failedcapture"
  Touched: "Play_VO_daodanyou_touched"
  FullStomach: "Play_VO_daodanyou_befed"
  CasualA: "Play_VO_daodanyou_rest_01"
  CasualB: "Play_VO_daodanyou_rest_02"
  CasualC: "Play_VO_daodanyou_rest_03"
  CasualD: "Play_VO_daodanyou_rest_04"
  GetUp: "Play_VO_daodanyou_getup"
  Mine: "Play_VO_daodanyou_SOC"
  Log: "Play_VO_daodanyou_SOC"
  ReactionA: "Play_VO_daodanyou_reaction_01"
  ReactionB: "Play_VO_daodanyou_reaction_02"
  ReactionC: "Play_VO_daodanyou_reaction_03"
}
rows {
  Id: 2009201
  Bank: "SP_XingShou_YueZhiXingLing"
  BeHit: "Play_VO_yuezhixingling_hit"
  Attack: "Play_VO_yuezhixingling_attack"
  Skill: "Play_VO_yuezhixingling_skill"
  Encounter: "Play_VO_yuezhixingling_escape"
  Dead: "Play_VO_yuezhixingling_death"
  IllustrationOpen: "Play_VO_yuezhixingling_logo"
  Captured: "Play_VO_yuezhixingling_failedcapture"
  Touched: "Play_VO_yuezhixingling_touched"
  FullStomach: "Play_VO_yuezhixingling_befed"
  CasualA: "Play_VO_yuezhixingling_rest_01"
  CasualB: "Play_VO_yuezhixingling_rest_02"
  CasualC: "Play_VO_yuezhixingling_rest_03"
  CasualD: "Play_VO_yuezhixingling_rest_04"
  GetUp: "Play_VO_yuezhixingling_getup"
  Mine: "Play_VO_yuezhixingling_SOC"
  Log: "Play_VO_yuezhixingling_SOC"
  ReactionA: "Play_VO_yuezhixingling_reaction_01"
  ReactionB: "Play_VO_yuezhixingling_reaction_02"
  ReactionC: "Play_VO_yuezhixingling_reaction_03"
}
rows {
  Id: 2013001
  Bank: "SP_XingShou_YueZhiXingLing"
  BeHit: "Play_VO_yuezhixingling_hit"
  Attack: "Play_VO_yuezhixingling_attack"
  Skill: "Play_VO_yuezhixingling_skill"
  Encounter: "Play_VO_yuezhixingling_escape"
  Dead: "Play_VO_yuezhixingling_death"
  IllustrationOpen: "Play_VO_yuezhixingling_logo"
  Captured: "Play_VO_yuezhixingling_failedcapture"
  Touched: "Play_VO_yuezhixingling_touched"
  FullStomach: "Play_VO_yuezhixingling_befed"
  CasualA: "Play_VO_yuezhixingling_rest_01"
  CasualB: "Play_VO_yuezhixingling_rest_02"
  CasualC: "Play_VO_yuezhixingling_rest_03"
  CasualD: "Play_VO_yuezhixingling_rest_04"
  GetUp: "Play_VO_yuezhixingling_getup"
  Mine: "Play_VO_yuezhixingling_SOC"
  Log: "Play_VO_yuezhixingling_SOC"
  ReactionA: "Play_VO_yuezhixingling_reaction_01"
  ReactionB: "Play_VO_yuezhixingling_reaction_02"
  ReactionC: "Play_VO_yuezhixingling_reaction_03"
}
rows {
  Id: 2005301
  Bank: "SP_XingShou_TuBaoShenYou"
  BeHit: "Play_VO_tubaoshenyou_hit"
  Attack: "Play_VO_tubaoshenyou_attack"
  Skill: "Play_VO_tubaoshenyou_skill"
  Encounter: "Play_VO_tubaoshenyou_escape"
  Dead: "Play_VO_tubaoshenyou_death"
  IllustrationOpen: "Play_VO_tubaoshenyou_logo"
  Captured: "Play_VO_tubaoshenyou_failedcapture"
  Touched: "Play_VO_tubaoshenyou_touched"
  FullStomach: "Play_VO_tubaoshenyou_befed"
  CasualA: "Play_VO_tubaoshenyou_rest_01"
  CasualB: "Play_VO_tubaoshenyou_rest_02"
  CasualC: "Play_VO_tubaoshenyou_rest_03"
  CasualD: "Play_VO_tubaoshenyou_rest_04"
  GetUp: "Play_VO_tubaoshenyou_getup"
  Mine: "Play_VO_tubaoshenyou_SOC"
  Log: "Play_VO_tubaoshenyou_SOC"
  ReactionA: "Play_VO_tubaoshenyou_reaction_01"
  ReactionB: "Play_VO_tubaoshenyou_reaction_02"
  ReactionC: "Play_VO_tubaoshenyou_reaction_03"
}
rows {
  Id: 2010401
  Bank: "SP_XingShou_PaoPaoE"
  BeHit: "Play_VO_xizaoe_hit"
  Attack: "Play_VO_xizaoe_attack"
  Skill: "Play_VO_xizaoe_skill"
  Encounter: "Play_VO_xizaoe_escape"
  Dead: "Play_VO_xizaoe_death"
  IllustrationOpen: "Play_VO_xizaoe_logo"
  Captured: "Play_VO_xizaoe_failedcapture"
  Touched: "Play_VO_xizaoe_touched"
  FullStomach: "Play_VO_xizaoe_befed"
  CasualA: "Play_VO_xizaoe_rest_01"
  CasualB: "Play_VO_xizaoe_rest_02"
  CasualC: "Play_VO_xizaoe_rest_03"
  CasualD: "Play_VO_xizaoe_rest_04"
  GetUp: "Play_VO_xizaoe_getup"
  Mine: "Play_VO_xizaoe_SOC"
  Log: "Play_VO_xizaoe_SOC"
  ReactionA: "Play_VO_xizaoe_reaction_01"
  ReactionB: "Play_VO_xizaoe_reaction_02"
  ReactionC: "Play_VO_xizaoe_reaction_03"
}
rows {
  Id: 2011401
  Bank: "SP_XingShou_XingMeng"
  BeHit: "Play_VO_xingmeng_hit"
  Attack: "Play_VO_xingmeng_attack"
  Skill: "Play_VO_xingmeng_skill"
  Encounter: "Play_VO_xingmeng_escape"
  Dead: "Play_VO_xingmeng_death"
  IllustrationOpen: "Play_VO_xingmeng_logo"
  Captured: "Play_VO_xingmeng_failedcapture"
  Touched: "Play_VO_xingmeng_touched"
  FullStomach: "Play_VO_xingmeng_befed"
  CasualA: "Play_VO_xingmeng_rest_01"
  CasualB: "Play_VO_xingmeng_rest_02"
  CasualC: "Play_VO_xingmeng_rest_03"
  CasualD: "Play_VO_xingmeng_rest_04"
  GetUp: "Play_VO_xingmeng_getup"
  Mine: "Play_VO_xingmeng_SOC"
  Log: "Play_VO_xingmeng_SOC"
  ReactionA: "Play_VO_xingmeng_reaction_01"
  ReactionB: "Play_VO_xingmeng_reaction_02"
  ReactionC: "Play_VO_xingmeng_reaction_03"
}
rows {
  Id: 2011501
  Bank: "SP_XingShou_HuiWuYao"
  BeHit: "Play_VO_huiwuyao_hit"
  Attack: "Play_VO_huiwuyao_attack"
  Skill: "Play_VO_huiwuyao_skill"
  Encounter: "Play_VO_huiwuyao_escape"
  Dead: "Play_VO_huiwuyao_death"
  IllustrationOpen: "Play_VO_huiwuyao_logo"
  Captured: "Play_VO_huiwuyao_failedcapture"
  Touched: "Play_VO_huiwuyao_touched"
  FullStomach: "Play_VO_huiwuyao_befed"
  CasualA: "Play_VO_huiwuyao_rest_01"
  CasualB: "Play_VO_huiwuyao_rest_02"
  CasualC: "Play_VO_huiwuyao_rest_03"
  CasualD: "Play_VO_huiwuyao_rest_04"
  GetUp: "Play_VO_huiwuyao_getup"
  Mine: "Play_VO_huiwuyao_SOC"
  Log: "Play_VO_huiwuyao_SOC"
  ReactionA: "Play_VO_huiwuyao_reaction_01"
  ReactionB: "Play_VO_huiwuyao_reaction_02"
  ReactionC: "Play_VO_huiwuyao_reaction_03"
}
rows {
  Id: 2011801
  Bank: "SP_XingShou_CangYunQingLong"
  BeHit: "Play_VO_cangyunqinglong_hit"
  Attack: "Play_VO_cangyunqinglong_attack"
  Skill: "Play_VO_cangyunqinglong_skill"
  Encounter: "Play_VO_cangyunqinglong_escape"
  Dead: "Play_VO_cangyunqinglong_death"
  IllustrationOpen: "Play_VO_cangyunqinglong_logo"
  Captured: "Play_VO_cangyunqinglong_failedcapture"
  Touched: "Play_VO_cangyunqinglong_touched"
  FullStomach: "Play_VO_cangyunqinglong_befed"
  CasualA: "Play_VO_cangyunqinglong_rest_01"
  CasualB: "Play_VO_cangyunqinglong_rest_02"
  CasualC: "Play_VO_cangyunqinglong_rest_03"
  CasualD: "Play_VO_cangyunqinglong_rest_04"
  GetUp: "Play_VO_cangyunqinglong_getup"
  Mine: "Play_VO_cangyunqinglong_SOC"
  Log: "Play_VO_cangyunqinglong_SOC"
  ReactionA: "Play_VO_cangyunqinglong_reaction_01"
  ReactionB: "Play_VO_cangyunqinglong_reaction_02"
  ReactionC: "Play_VO_cangyunqinglong_reaction_03"
}
rows {
  Id: 2012201
  Bank: "SP_XingShou_HuaSanXi"
  BeHit: "Play_VO_huasanxi_hit"
  Attack: "Play_VO_huasanxi_attack"
  Skill: "Play_VO_huasanxi_skill"
  Encounter: "Play_VO_huasanxi_escape"
  Dead: "Play_VO_huasanxi_death"
  IllustrationOpen: "Play_VO_huasanxi_logo"
  Captured: "Play_VO_huasanxi_failedcapture"
  Touched: "Play_VO_huasanxi_touched"
  FullStomach: "Play_VO_huasanxi_befed"
  CasualA: "Play_VO_huasanxi_rest_01"
  CasualB: "Play_VO_huasanxi_rest_02"
  CasualC: "Play_VO_huasanxi_rest_03"
  CasualD: "Play_VO_huasanxi_rest_04"
  GetUp: "Play_VO_huasanxi_getup"
  Mine: "Play_VO_huasanxi_SOC"
  Log: "Play_VO_huasanxi_SOC"
  ReactionA: "Play_VO_huasanxi_reaction_01"
  ReactionB: "Play_VO_huasanxi_reaction_02"
  ReactionC: "Play_VO_huasanxi_reaction_03"
}
rows {
  Id: 2012301
  Bank: "SP_XingShou_QianShanNiu"
  BeHit: "Play_VO_qianshanniu_hit"
  Attack: "Play_VO_qianshanniu_attack"
  Skill: "Play_VO_qianshanniu_skill"
  Encounter: "Play_VO_qianshanniu_escape"
  Dead: "Play_VO_qianshanniu_death"
  IllustrationOpen: "Play_VO_qianshanniu_logo"
  Captured: "Play_VO_qianshanniu_failedcapture"
  Touched: "Play_VO_qianshanniu_touched"
  FullStomach: "Play_VO_qianshanniu_befed"
  CasualA: "Play_VO_qianshanniu_rest_01"
  CasualB: "Play_VO_qianshanniu_rest_02"
  CasualC: "Play_VO_qianshanniu_rest_03"
  CasualD: "Play_VO_qianshanniu_rest_04"
  GetUp: "Play_VO_qianshanniu_getup"
  Mine: "Play_VO_qianshanniu_SOC"
  Log: "Play_VO_qianshanniu_SOC"
  ReactionA: "Play_VO_qianshanniu_reaction_01"
  ReactionB: "Play_VO_qianshanniu_reaction_02"
  ReactionC: "Play_VO_qianshanniu_reaction_03"
}
rows {
  Id: 2012701
  Bank: "SP_XingShou_HuJiao"
  BeHit: "Play_VO_hujiao_hit"
  Attack: "Play_VO_hujiao_attack"
  Skill: "Play_VO_hujiao_skill"
  Encounter: "Play_VO_hujiao_escape"
  Dead: "Play_VO_hujiao_death"
  IllustrationOpen: "Play_VO_hujiao_logo"
  Captured: "Play_VO_hujiao_failedcapture"
  Touched: "Play_VO_hujiao_touched"
  FullStomach: "Play_VO_hujiao_befed"
  CasualA: "Play_VO_hujiao_rest_01"
  CasualB: "Play_VO_hujiao_rest_02"
  CasualC: "Play_VO_hujiao_rest_03"
  CasualD: "Play_VO_hujiao_rest_04"
  GetUp: "Play_VO_hujiao_getup"
  Mine: "Play_VO_hujiao_SOC"
  Log: "Play_VO_hujiao_SOC"
  ReactionA: "Play_VO_hujiao_reaction_01"
  ReactionB: "Play_VO_hujiao_reaction_02"
  ReactionC: "Play_VO_hujiao_reaction_03"
}
rows {
  Id: 2013401
  Bank: "SP_XingShou_Maomeiji"
  BeHit: "Play_VO_maomeiji_hit"
  Attack: "Play_VO_maomeiji_attack"
  Skill: "Play_VO_maomeiji_skill"
  Encounter: "Play_VO_maomeiji_escape"
  Dead: "Play_VO_maomeiji_death"
  IllustrationOpen: "Play_VO_maomeiji_logo"
  Captured: "Play_VO_maomeiji_failedcapture"
  Touched: "Play_VO_maomeiji_touched"
  FullStomach: "Play_VO_maomeiji_befed"
  CasualA: "Play_VO_maomeiji_rest_01"
  CasualB: "Play_VO_maomeiji_rest_02"
  CasualC: "Play_VO_maomeiji_rest_03"
  CasualD: "Play_VO_maomeiji_rest_04"
  GetUp: "Play_VO_maomeiji_getup"
  Mine: "Play_VO_maomeiji_SOC"
  Log: "Play_VO_maomeiji_SOC"
  ReactionA: "Play_VO_maomeiji_reaction_01"
  ReactionB: "Play_VO_maomeiji_reaction_02"
  ReactionC: "Play_VO_maomeiji_reaction_03"
}
rows {
  Id: 2013901
  Bank: "SP_XingShou_ZhuLiYe"
  BeHit: "Play_VO_zhuliye_hit"
  Attack: "Play_VO_zhuliye_attack"
  Skill: "Play_VO_zhuliye_skill"
  Encounter: "Play_VO_zhuliye_escape"
  Dead: "Play_VO_zhuliye_death"
  IllustrationOpen: "Play_VO_zhuliye_logo"
  Captured: "Play_VO_zhuliye_failedcapture"
  Touched: "Play_VO_zhuliye_touched"
  FullStomach: "Play_VO_zhuliye_befed"
  CasualA: "Play_VO_zhuliye_rest_01"
  CasualB: "Play_VO_zhuliye_rest_02"
  CasualC: "Play_VO_zhuliye_rest_03"
  CasualD: "Play_VO_zhuliye_rest_04"
  GetUp: "Play_VO_zhuliye_getup"
  Mine: "Play_VO_zhuliye_SOC"
  Log: "Play_VO_zhuliye_SOC"
  ReactionA: "Play_VO_zhuliye_reaction_01"
  ReactionB: "Play_VO_zhuliye_reaction_02"
  ReactionC: "Play_VO_zhuliye_reaction_03"
}
rows {
  Id: 2008801
  Bank: "SP_XingShou_YunHaiQiLin"
  BeHit: "Play_VO_yunhaiqilin_hit"
  Attack: "Play_VO_yunhaiqilin_attack"
  Skill: "Play_VO_yunhaiqilin_skill"
  Encounter: "Play_VO_yunhaiqilin_escape"
  Dead: "Play_VO_yunhaiqilin_death"
  IllustrationOpen: "Play_VO_yunhaiqilin_logo"
  Captured: "Play_VO_yunhaiqilin_failedcapture"
  Touched: "Play_VO_yunhaiqilin_touched"
  FullStomach: "Play_VO_yunhaiqilin_befed"
  CasualA: "Play_VO_yunhaiqilin_rest_01"
  CasualB: "Play_VO_yunhaiqilin_rest_02"
  CasualC: "Play_VO_yunhaiqilin_rest_03"
  CasualD: "Play_VO_yunhaiqilin_rest_04"
  GetUp: "Play_VO_yunhaiqilin_getup"
  Mine: "Play_VO_yunhaiqilin_SOC"
  Log: "Play_VO_yunhaiqilin_SOC"
  ReactionA: "Play_VO_yunhaiqilin_reaction_01"
  ReactionB: "Play_VO_yunhaiqilin_reaction_02"
  ReactionC: "Play_VO_yunhaiqilin_reaction_03"
}
rows {
  Id: 2013601
  Bank: "SP_XingShou_GongChengShi"
  BeHit: "Play_VO_gongchengshi_hit"
  Attack: "Play_VO_gongchengshi_attack"
  Skill: "Play_VO_gongchengshi_skill"
  Encounter: "Play_VO_gongchengshi_escape"
  Dead: "Play_VO_gongchengshi_death"
  IllustrationOpen: "Play_VO_gongchengshi_logo"
  Captured: "Play_VO_gongchengshi_failedcapture"
  Touched: "Play_VO_gongchengshi_touched"
  FullStomach: "Play_VO_gongchengshi_befed"
  CasualA: "Play_VO_gongchengshi_rest_01"
  CasualB: "Play_VO_gongchengshi_rest_02"
  CasualC: "Play_VO_gongchengshi_rest_03"
  CasualD: "Play_VO_gongchengshi_rest_04"
  GetUp: "Play_VO_gongchengshi_getup"
  Mine: "Play_VO_gongchengshi_SOC"
  Log: "Play_VO_gongchengshi_SOC"
  ReactionA: "Play_VO_gongchengshi_reaction_01"
  ReactionB: "Play_VO_gongchengshi_reaction_02"
  ReactionC: "Play_VO_gongchengshi_reaction_03"
}
rows {
  Id: 2000102
}
rows {
  Id: 2000502
}
rows {
  Id: 2014001
  Bank: "SP_XingShou_ShuiYunJun"
  BeHit: "Play_VO_shuiyunjun_hit"
  Attack: "Play_VO_shuiyunjun_attack"
  Skill: "Play_VO_shuiyunjun_skill"
  Encounter: "Play_VO_shuiyunjun_escape"
  Dead: "Play_VO_shuiyunjun_death"
  IllustrationOpen: "Play_VO_shuiyunjun_logo"
  Captured: "Play_VO_shuiyunjun_failedcapture"
  Touched: "Play_VO_shuiyunjun_touched"
  FullStomach: "Play_VO_shuiyunjun_befed"
  CasualA: "Play_VO_shuiyunjun_rest_01"
  CasualB: "Play_VO_shuiyunjun_rest_02"
  CasualC: "Play_VO_shuiyunjun_rest_03"
  CasualD: "Play_VO_shuiyunjun_rest_04"
  GetUp: "Play_VO_shuiyunjun_getup"
  Mine: "Play_VO_shuiyunjun_SOC"
  Log: "Play_VO_shuiyunjun_SOC"
  ReactionA: "Play_VO_shuiyunjun_reaction_01"
  ReactionB: "Play_VO_shuiyunjun_reaction_02"
  ReactionC: "Play_VO_shuiyunjun_reaction_03"
}
rows {
  Id: 2014101
  Bank: "SP_XingShou_HuDieXian"
  BeHit: "Play_VO_hudiexian_hit"
  Attack: "Play_VO_hudiexian_attack"
  Skill: "Play_VO_hudiexian_skill"
  Encounter: "Play_VO_hudiexian_escape"
  Dead: "Play_VO_hudiexian_death"
  IllustrationOpen: "Play_VO_hudiexian_logo"
  Captured: "Play_VO_hudiexian_failedcapture"
  Touched: "Play_VO_hudiexian_touched"
  FullStomach: "Play_VO_hudiexian_befed"
  CasualA: "Play_VO_hudiexian_rest_01"
  CasualB: "Play_VO_hudiexian_rest_02"
  CasualC: "Play_VO_hudiexian_rest_03"
  CasualD: "Play_VO_hudiexian_rest_04"
  GetUp: "Play_VO_hudiexian_getup"
  Mine: "Play_VO_hudiexian_SOC"
  Log: "Play_VO_hudiexian_SOC"
  ReactionA: "Play_VO_hudiexian_reaction_01"
  ReactionB: "Play_VO_hudiexian_reaction_02"
  ReactionC: "Play_VO_hudiexian_reaction_03"
}
rows {
  Id: 2014201
  Bank: "SP_XingShou_RongYuanShan"
  BeHit: "Play_VO_rongyuanshan_hit"
  Attack: "Play_VO_rongyuanshan_attack"
  Skill: "Play_VO_rongyuanshan_skill"
  Encounter: "Play_VO_rongyuanshan_escape"
  Dead: "Play_VO_rongyuanshan_death"
  IllustrationOpen: "Play_VO_rongyuanshan_logo"
  Captured: "Play_VO_rongyuanshan_failedcapture"
  Touched: "Play_VO_rongyuanshan_touched"
  FullStomach: "Play_VO_rongyuanshan_befed"
  CasualA: "Play_VO_rongyuanshan_rest_01"
  CasualB: "Play_VO_rongyuanshan_rest_02"
  CasualC: "Play_VO_rongyuanshan_rest_03"
  CasualD: "Play_VO_rongyuanshan_rest_04"
  GetUp: "Play_VO_rongyuanshan_getup"
  Mine: "Play_VO_rongyuanshan_SOC"
  Log: "Play_VO_rongyuanshan_SOC"
  ReactionA: "Play_VO_rongyuanshan_reaction_01"
  ReactionB: "Play_VO_rongyuanshan_reaction_02"
  ReactionC: "Play_VO_rongyuanshan_reaction_03"
}
rows {
  Id: 2014301
  Bank: "SP_XingShou_CangYunQingLong"
  BeHit: "Play_VO_cangyunqinglong_hit"
  Attack: "Play_VO_cangyunqinglong_attack"
  Skill: "Play_VO_cangyunqinglong_skill"
  Encounter: "Play_VO_cangyunqinglong_escape"
  Dead: "Play_VO_cangyunqinglong_death"
  IllustrationOpen: "Play_VO_cangyunqinglong_logo"
  Captured: "Play_VO_cangyunqinglong_failedcapture"
  Touched: "Play_VO_cangyunqinglong_touched"
  FullStomach: "Play_VO_cangyunqinglong_befed"
  CasualA: "Play_VO_cangyunqinglong_rest_01"
  CasualB: "Play_VO_cangyunqinglong_rest_02"
  CasualC: "Play_VO_cangyunqinglong_rest_03"
  CasualD: "Play_VO_cangyunqinglong_rest_04"
  GetUp: "Play_VO_cangyunqinglong_getup"
  Mine: "Play_VO_cangyunqinglong_SOC"
  Log: "Play_VO_cangyunqinglong_SOC"
  ReactionA: "Play_VO_cangyunqinglong_reaction_01"
  ReactionB: "Play_VO_cangyunqinglong_reaction_02"
  ReactionC: "Play_VO_cangyunqinglong_reaction_03"
}
rows {
  Id: 2014401
  Bank: "SP_XingShou_SuanNiNiu"
  BeHit: "Play_VO_suanniniu_hit"
  Attack: "Play_VO_suanniniu_attack"
  Skill: "Play_VO_suanniniu_skill"
  Encounter: "Play_VO_suanniniu_escape"
  Dead: "Play_VO_suanniniu_death"
  IllustrationOpen: "Play_VO_suanniniu_logo"
  Captured: "Play_VO_suanniniu_failedcapture"
  Touched: "Play_VO_suanniniu_touched"
  FullStomach: "Play_VO_suanniniu_befed"
  CasualA: "Play_VO_suanniniu_rest_01"
  CasualB: "Play_VO_suanniniu_rest_02"
  CasualC: "Play_VO_suanniniu_rest_03"
  CasualD: "Play_VO_suanniniu_rest_04"
  GetUp: "Play_VO_suanniniu_getup"
  Mine: "Play_VO_suanniniu_SOC"
  Log: "Play_VO_suanniniu_SOC"
  ReactionA: "Play_VO_suanniniu_reaction_01"
  ReactionB: "Play_VO_suanniniu_reaction_02"
  ReactionC: "Play_VO_suanniniu_reaction_03"
}
rows {
  Id: 2990001
}
rows {
  Id: 2102201
}
rows {
  Id: 2102301
}
rows {
  Id: 2102401
}
rows {
  Id: 2104001
}
rows {
  Id: 2102202
}
rows {
  Id: 2200601
}
