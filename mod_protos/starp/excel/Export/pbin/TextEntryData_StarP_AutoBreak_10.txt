com.tencent.wea.xlsRes.starp.table_TextEntryData
excel/xls/W_SP文本表文本配置.xlsx sheet:Sheet1
rows {
  stringId: "UI_SPGame_Skill_attribute_Water"
  content: "水属性啾灵，会受到更高的土属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_Earth"
  content: "土属性啾灵，会受到更高的木属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_Wood"
  content: "木属性啾灵，会受到更高的金属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_Gold"
  content: "金属性啾灵，会受到更高的火属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_Ice"
  content: "冰属性啾灵，会受到更高的火属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_Dragon"
  content: "龙属性啾灵，会受到更高的冰属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_Yao"
  content: "妖属性啾灵，会受到更高的龙属性伤害。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_attribute_None"
  content: "无属性啾灵，不被其他属性克制。<trace type=GuideWindow id=12 >查看属性克制</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Skill_Battle_Replace"
  content: "该啾灵处于战斗中，无法切换技能"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_PET_IS_BATHE"
  content: "啾灵正在泡温泉，没有响应你的召唤！"
  switch: 1
}
rows {
  stringId: "UI_SPGAME_PET_IS_SLEEP"
  content: "啾灵正在睡眠中，没有响应你的召唤！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_SaveBox_NoSameItem"
  content: "{0}中没有同类道具，无法快速放入！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_SaveBox_MoveItem"
  content: "同类型道具已经存入{0}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_SaveBox_NotEnough"
  content: " {0}已满，无法放入！"
  switch: 1
}
rows {
  stringId: "SPCantCaptureMyPet_Tex"
  content: "不可抓捕自己的啾灵奥~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Forbid_Jump_In_Mount"
  content: "当前处于骑乘状态，无法建造！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Build_Terminal_NotBest"
  content: "当前位置不是最好的建立据点区域！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Build_Terminal_NotBest_Confirm"
  content: "当前位置不是最好的建立据点区域，是否确认在这里建造据点？"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Tech_PreTech_Unlock"
  content: "前置科技:{0}已解锁"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Tech_PreTech_lock"
  content: "前置科技:{0}未解锁"
  switch: 1
}
rows {
  stringId: "UI_SPGame_EnterTeam_Tips"
  content: "{0}<Mail_Yellow>{1}</>已生效"
  switch: 1
}
rows {
  stringId: "UI_SPGame_OwnSkills_Tips"
  content: "无需学习此啾灵固有技能"
  switch: 1
}
rows {
  stringId: "UI_SPGame_EquipSlot_Tool_Tip_Axe"
  content: "该槽位只能装备斧头"
  switch: 1
}
rows {
  stringId: "UI_SPGame_EquipSlot_Tool_Tip_Pickaxe"
  content: "该槽位只能装备镐子"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Star_Upgrade"
  content: "选择要强化的啾灵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Star_Swallow"
  content: "选择要被吞噬的啾灵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PartnerWeapon_ClickHaveSaddleTip"
  content: "点击右侧伙伴技能按钮召唤啾灵至身边装备"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Ride_ClickHaveSaddleTip"
  content: "点击右侧伙伴技能按钮召唤啾灵至身边乘骑"
  switch: 1
}
rows {
  stringId: "UI_SPGame_ShieldGoBackByCallingPathFinding"
  content: "召唤啾灵时不可回城"
  switch: 1
}
rows {
  stringId: "UI_SPGame_ShieldCallingPathFindingByGoBack"
  content: "回城时不可召唤啾灵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_MainWinNum"
  content: "赛季胜场:{0}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_MainContinueWinTimes"
  content: "当前连胜:{0}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_MainNoContinueWin"
  content: "暂无连胜"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareSelectTip"
  content: "请至少保留一项选中"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareSelectElement"
  content: "属性：{0}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareSelectJob"
  content: "职业：{0}"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareSelectAll"
  content: "全部"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareTeamTip1"
  content: "缺少前排"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareTeamTip2"
  content: "缺少输出"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareTeamTip3"
  content: "缺少辅助"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PrepareTeamTip4"
  content: "属性单一"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_NoActivateTip"
  content: "未满足开启条件，无法激活！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortDefault"
  content: "默认排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortById"
  content: "图鉴编号排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortByHp"
  content: "生命值排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortByAtk"
  content: "物攻排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortByAtkSp"
  content: "法攻排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortByDef"
  content: "物防排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_SortByDefSp"
  content: "法防排序"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetFilterAny"
  content: "任意数量"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_RankMatch"
  content: "排位"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_QuikMatch"
  content: "快速战斗"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetListBlank"
  content: "请先获取更多啾灵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetListPage"
  content: "第{0}/{1}页"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetListPreviousBlank"
  content: "上1页"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetListNextPage"
  content: "下1页"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetCantGoBattle"
  content: "不可上阵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetCantGoBattleTips"
  content: "此啾灵在PVP中不可上阵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetGoBattle"
  content: "拖动到此区域上阵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetCancelGoBattle"
  content: "拖动到此区域下阵"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetBattleMessage"
  content: "阵容信息"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetRecommendElement"
  content: "高威胁属性"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetElementReasonable"
  content: "属性搭配合理"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetFrontRowJob"
  content: "前排职业："
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetBackRowJob"
  content: "后排职业："
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetFrontRowJobLack"
  content: "缺少前排"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetBackRowJobLack"
  content: "缺少后排"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetFrontRowJobName"
  content: "前排"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PetJobReasonable"
  content: "职业搭配合理"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_StartMatching"
  content: "开始匹配"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_Matching"
  content: "匹配中"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_MatchSuccess"
  content: "匹配成功"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PredictMatchTime"
  content: "预计{0}秒"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PVP_PvpLoadingTimeOut"
  content: "单局加载超时，无法开战！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Transfer_PointActive"
  content: "{0}传送点已激活"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Transfer_CanOnlyUseLimit"
  content: "快速传送仅可通过传送点和据点中枢进行"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Transfer_PointNonactivated"
  content: "无法传送到未激活的传送点"
  switch: 1
}
rows {
  stringId: "UI_SPGame_UpgradeTerminal"
  content: "前往中枢升级"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TechTreeNotUnlocked"
  content: "尚未解锁科技树"
  switch: 1
}
rows {
  stringId: "UI_SPGame_RoomDetails"
  content: "世界详情"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Cant_Work_PowerOff"
  content: "据点中聚雷鼎的电量供应不足，无法进行工作"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Network_Unstable_Retry_Tip"
  content: "当前网络不稳定，请稍后再试！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Eat_IsAlmostDie_Tips"
  content: "该啾灵处于濒死状态中，无法喂食！"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Backpack_Tip_NoFood"
  content: "背包中没有食物"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Backpack_Tip_NoMedicine"
  content: "背包中没有药品"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TRightMenuGrowthText"
  content: "已加速"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TRightMenuGrowthText_Common"
  content: "<MenuYellow>每日经验加速</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_ApplyManager"
  content: "您所在{0}星球管理员位置空缺，可进入房间内申请成为管理员哦~"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TRightMenuGrowthNumText"
  content: "{0}万"
  switch: 1
}
rows {
  stringId: "UI_SPGame_TRightGrowthRichEndText"
  content: "<MenuYellow>每日经验加速</>"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Num_Symbol_Text "
  content: "×"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Pet_Star_Level_Max "
  content: "该啾灵星级已满"
  switch: 1
}
rows {
  stringId: "UI_SPGame_Guild_NoPermission"
  content: "当前您并无此操作权限"
  switch: 1
}
rows {
  stringId: "UI_SPGame_30DegradeToOpenCarear"
  content: "角色30级开启流派选择功能"
  switch: 1
}
rows {
  stringId: "UI_SPGame_RoleCurrentlyDidNotOpenCareer"
  content: "角色暂未转职"
  switch: 1
}
rows {
  stringId: "UI_SPGame_ChangeProfessionGemTips"
  content: "给你2颗职业宝石，可激活职业武器效果"
  switch: 1
}
rows {
  stringId: "UI_SPGame_PlayerNotExist"
  content: "玩家不存在"
  switch: 1
}
rows {
  stringId: "UI_SPGame_InvalidProfessionId"
  content: "非常的职业Id"
  switch: 1
}
rows {
  stringId: "UI_SPGame_BackpackDataError"
  content: "背包数据错误"
  switch: 1
}
rows {
  stringId: "UI_SPGame_GoldNotEnough"
  content: "金币不足"
  switch: 1
}
