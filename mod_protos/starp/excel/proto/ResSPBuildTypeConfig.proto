syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";


message SPBuildTypeConfigData{// @noSvr
  option (resKey) = "typeId";
  optional int64 typeId = 1;
  optional string name = 2;
  optional string iconPath = 3;
  optional int32 displayOrder = 4;
}


message table_SPBuildTypeConfigData {// @noSvr
  repeated SPBuildTypeConfigData rows = 1;
}