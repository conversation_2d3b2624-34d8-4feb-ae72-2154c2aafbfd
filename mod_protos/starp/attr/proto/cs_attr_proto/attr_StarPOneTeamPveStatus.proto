syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.starp.protocol;

import "attr_base.proto";

message proto_StarPOneTeamPveStatus {
    option (wea_attr_cls) = "com.tencent.wea.starp.attr.StarPOneTeamPveStatus";
    option (wea_attr_key) = "pWorldId";
    // 关卡id，对应组队PVE配置表的层id
    optional int32 pWorldId = 1;
    // bit位表示的各种状态，第一个bit位代表是否已领取首通奖励
    optional int32 bitStatus = 2;
    // 最后一次开宝箱的时间
    optional int64 lastTreasureBoxOpenTimeStamp = 3;
}