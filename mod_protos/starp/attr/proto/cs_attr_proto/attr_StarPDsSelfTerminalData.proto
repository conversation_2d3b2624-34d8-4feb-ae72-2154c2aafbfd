syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.starp.protocol;

import "attr_base.proto";
import "attr_StarPDsSelfTerminalKey.proto";

message proto_StarPDsSelfTerminalData {
    option (wea_attr_cls) = "com.tencent.wea.starp.attr.StarPDsSelfTerminalData";
    // 记录的个人终端数据
    repeated proto_StarPDsSelfTerminalKey terminals = 1;
    repeated int64 terminals_deleted = 2001;
    optional bool terminals_is_cleared = 4001;
    // 主终端UID
    optional int64 mainTerminalUId = 2;
    // 虚拟终端UID
    optional int64 virtualTerminalUId = 3;
}