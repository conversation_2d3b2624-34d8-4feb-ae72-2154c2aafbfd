syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_CocPrisonWorkInfo.proto";

message proto_CocPrisonExtraInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.CocPrisonExtraInfo";
    // 指派建筑唯一Id
    optional int32 buildingUniqueId = 1;
    // 指派开始时间-毫秒
    optional int64 assignStartTime = 2;
    // 指派结束时间-毫秒
    optional int64 assignEndTime = 3;
    // 下一次可指派时间-毫秒
    optional int64 nextCanAssignTime = 4;
    // 自增id
    optional int64 seqId = 5;
    // 工作记录
    repeated proto_CocPrisonWorkInfo workInfos = 6;
    repeated int64 workInfos_deleted = 2006;
    optional bool workInfos_is_cleared = 4006;
    // 指派建筑原始结束时间-毫秒
    optional int64 originalEndTime = 7;
}