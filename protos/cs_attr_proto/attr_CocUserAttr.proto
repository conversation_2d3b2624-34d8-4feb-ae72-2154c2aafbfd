syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_CocClientNumPropCacheInfo.proto";
import "attr_CocCurrentBattleInfo.proto";
import "attr_CocDefenceInfo.proto";
import "attr_CocDialogInfo.proto";
import "attr_CocFeatureUnlock.proto";
import "attr_CocFriendInfo.proto";
import "attr_CocImpression.proto";
import "attr_CocItemInfo.proto";
import "attr_CocLevelDefenseInfo.proto";
import "attr_CocMatchInfo.proto";
import "attr_CocMonthCardInfo.proto";
import "attr_CocOfflineEventInfo.proto";
import "attr_CocPVEGameInfo.proto";
import "attr_CocPlayerCityBuildingInfo.proto";
import "attr_CocPlayerMapRegionInfo.proto";
import "attr_CocProsperityInfo.proto";
import "attr_CocRankingScore.proto";
import "attr_CocRecoverPropInfo.proto";
import "attr_CocResourceInfo.proto";
import "attr_CocScienceInfo.proto";
import "attr_CocSoldierInfo.proto";
import "attr_CocTaskInfo.proto";
import "attr_CocTreasureBoxInfo.proto";
import "attr_CocUserBasicInfo.proto";
import "attr_CocVillagerInfo.proto";
import "attr_CocXingbaoInfo.proto";
import "attr_XiaoWoDsInfo.proto";
import "attr_XiaoWoVisitorInfo.proto";

message proto_CocUserAttr {
    option (wea_attr_cls) = "com.tencent.wea.attr.CocUserAttr";
    // 玩家id
    optional int64 uid = 1;
    // 城建信息
    optional proto_CocPlayerCityBuildingInfo cityBuildingInfo = 2;
    // 匹配信息
    optional proto_CocMatchInfo cocMatchInfo = 3;
    // 段位信息
    optional proto_CocRankingScore rankingScore = 4;
    // 客户端缓存numProp数值
    optional proto_CocClientNumPropCacheInfo clientNumPropCacheInfo = 5;
    optional bool clientNumPropCacheInfo_deleted = 2005;
    // 背包信息
    optional proto_CocItemInfo itemInfo = 6;
    // 客户端版本号
    optional string version = 7;
    // 战斗防御数据
    optional proto_CocDefenceInfo defenceInfo = 8;
    // 研究所数据
    optional proto_CocScienceInfo scienceInfo = 9;
    // 部队数据
    optional proto_CocSoldierInfo soldierInfo = 10;
    // 资源数据
    optional proto_CocResourceInfo resourceInfo = 11;
    // 繁荣度信息
    optional proto_CocProsperityInfo prosperityInfo = 12;
    // 印记（时效buff）系统
    optional proto_CocImpression impression = 13;
    // 宝箱信息
    optional proto_CocTreasureBoxInfo treasureBoxInfo = 14;
    // 地图区域信息
    optional proto_CocPlayerMapRegionInfo mapRegionInfo = 15;
    optional bool mapRegionInfo_deleted = 2015;
    // 对话系统
    optional proto_CocDialogInfo dialogInfo = 16;
    // PVE玩法
    optional proto_CocPVEGameInfo pveGameInfo = 17;
    // 星宝信息
    optional proto_CocXingbaoInfo xingbaoInfo = 18;
    // 离线期间发生的事件
    optional proto_CocOfflineEventInfo offlineEventInfo = 19;
    optional bool offlineEventInfo_deleted = 2019;
    optional proto_CocFeatureUnlock featureUnlock = 20;
    // 村民信息
    optional proto_CocVillagerInfo villagerInfo = 21;
    // 月卡信息
    repeated proto_CocMonthCardInfo monthCardInfo = 22;
    repeated int32 monthCardInfo_deleted = 2022;
    optional bool monthCardInfo_is_cleared = 4022;
    // 任务信息
    optional proto_CocTaskInfo taskInfo = 23;
    // 记录当前战斗摘要信息，不下发客户端(开局时进行设置，后续客户端通过ds拉取战斗信息时，依赖这里的数据为客户端填充数据)
    optional proto_CocCurrentBattleInfo currentBattleInfo = 25;
    optional bool currentBattleInfo_deleted = 2025;
    // 好友相关信息
    optional proto_CocFriendInfo friendInfo = 26;
    // 杂项基础信息
    optional proto_CocUserBasicInfo basicInfo = 27;
    optional bool basicInfo_deleted = 2027;
    // 防守关卡信息
    optional proto_CocLevelDefenseInfo levelDefenseInfo = 28;
    // 可以自动恢复的属性
    optional proto_CocRecoverPropInfo recoverPropInfo = 29;
    // 家园访客信息，这里复用了xiaowo的结构，很遗憾，名字不容易改了
    optional proto_XiaoWoVisitorInfo homeVisitorInfo = 30;
    // 家园DS信息，这里复用了xiaowo的结构，很遗憾，名字不容易改了
    optional proto_XiaoWoDsInfo homeDsInfo = 31;
    optional bool homeDsInfo_deleted = 2031;
}