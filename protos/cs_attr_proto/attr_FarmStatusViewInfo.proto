syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_FarmStatusViewInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.FarmStatusViewInfo";
    // [作物]最近成熟-时间
    optional int64 cropRipeTime = 1;
    // [作物]最近成熟-类型
    optional int32 cropRipeType = 2;
    // [作物]最近成熟常规-时间
    optional int64 normalCropRipeTime = 3;
    // [作物]最近成熟常规-类型
    optional int32 normalCropRipeType = 4;
    // [作物]最近干涸-时间
    optional int64 cropDryTime = 5;
    // [作物]最近干涸-类型
    optional int32 cropDryType = 6;
    // [动物]最近成熟-时间
    optional int64 animalRipeTime = 7;
    // [动物]最近成熟-类型
    optional int32 animalRipeType = 8;
    // [动物]最近饥饿-时间
    optional int64 animalHungryTime = 9;
    // [动物]最近饥饿-类型
    optional int32 animalHungryType = 10;
    // [动物]最近待产-时间
    optional int64 animalCanEncourageTime = 11;
    // [动物]最近待产-类型
    optional int32 animalCanEncourageType = 12;
    // [鱼塘]成熟时间
    optional int64 fishRipeTime = 13;
    // [鱼塘]保护结束时间
    optional int64 fishProtectTime = 14;
    // [鱼塘]层数
    optional int32 fishPoolLayer = 15;
    // [加工器]最近成熟时间
    optional int64 machineRipeTime = 16;
    // [加工器]最近成熟类型
    optional int32 machineRipeType = 17;
    // [水族箱]攒满时间
    optional int64 aquariumRipeTime = 18;
    // [收藏品]掉落时间
    optional int64 collectionDropTime = 19;
    // [收藏品]掉落时间(仅农场内)
    optional int64 collectionDropInFarmTime = 20;
    // [神像]可祈福时间
    optional int64 godFigureCanPrayTime = 21;
    // [仙术]蓝条满时间
    optional int64 magicSkillMpFullTime = 22;
    // [麒麟]孵化完成时间
    optional int64 kirinIncubateFinishTime = 23;
    // [麒麟]可采集仙力时间
    optional int64 kirinCanCollectManaTime = 24;
    // [餐厅]贵宾可邀请时间
    optional int64 cookVisitantCanServeTime = 25;
    // [餐厅]贵宾可预约时间
    optional int64 cookVisitantCanPrebookTime = 26;
    // [餐厅]可备菜时间
    optional int64 cookCanOpenTime = 27;
    // [餐厅]贵宾保护到期时间
    optional int64 cookVisitantProtectEndTime = 28;
}