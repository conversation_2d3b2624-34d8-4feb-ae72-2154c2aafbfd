syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_AchievementStageCompleteInfo.proto";

message proto_AchievementCompleteInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.AchievementCompleteInfo";
    option (wea_attr_key) = "id";
    // 成就id
    optional int32 id = 1;
    // 阶段信息
    repeated proto_AchievementStageCompleteInfo stageInfo = 4;
    repeated int32 stageInfo_deleted = 2004;
    optional bool stageInfo_is_cleared = 4004;
}