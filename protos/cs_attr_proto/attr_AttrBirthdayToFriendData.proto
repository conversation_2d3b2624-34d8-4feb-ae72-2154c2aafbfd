syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_AttrBirthdayToFriendData {
    option (wea_attr_cls) = "com.tencent.wea.attr.AttrBirthdayToFriendData";
    option (wea_attr_key) = "friendUid";
    // 好友uid
    optional int64 friendUid = 1;
    // 生日月*100+生日日
    optional int32 birthdayMonthDay = 2;
    // 上一次提醒好友生日的时间戳秒
    optional int64 lastRemindEpochSecs = 3;
    // 上上次
    optional int64 twoTimesAgoRemindEpochSecs = 4;
    // 上一次赠送好友时间戳毫秒
    optional int64 lastSendEpochMillis = 5;
    // 已经赠送的时间戳毫秒
    repeated int64 sentEpochMillis = 7;
    optional bool sentEpochMillis_is_cleared = 2007;
}