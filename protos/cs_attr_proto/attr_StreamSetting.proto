syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_StreamSetting {
    option (wea_attr_cls) = "com.tencent.wea.attr.StreamSetting";
    option (wea_attr_key) = "streamPlatType";
    // 开播权利
    optional bool streamRight = 1;
    // 是否在开播
    optional bool onStream = 2;
    // 上次开播时间
    optional int64 lastStreamTimestamp = 3;
    // 开播token
    optional string streamToken = 4;
    // 直播平台类型 取值参考StreamPlatType
    optional int32 streamPlatType = 5;
    // 直播玩法id
    optional string streamPlayInfoId = 6;
    // 玩法状态
    optional int32 playState = 7;
}