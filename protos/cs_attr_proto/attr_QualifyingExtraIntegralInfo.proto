syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_QualifyingExtraIntegralInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.QualifyingExtraIntegralInfo";
    option (wea_attr_key) = "id";
    // id
    optional int32 id = 1;
    // 完成次数
    optional int32 finishNum = 2;
    // 更新时间
    optional int64 updateTimeMs = 3;
}