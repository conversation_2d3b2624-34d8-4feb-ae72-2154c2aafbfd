syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_StarPBreedEggsParentsHistory.proto";

message proto_StarPBreedEggsParentsHistoryInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.StarPBreedEggsParentsHistoryInfo";
    // 孵蛋历史记录
    repeated proto_StarPBreedEggsParentsHistory historys = 1;
    repeated int32 historys_deleted = 2001;
    optional bool historys_is_cleared = 4001;
}