syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_LobbyChangeColorInfo.proto";
import "attr_LobbyNpcInfo.proto";
import "attr_PlayerUgcLobbyInfo.proto";
import "attr_UgcMapInfo.proto";

message proto_LobbyInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.LobbyInfo";
    // 大厅id
    optional int64 lobbyId = 1;
    // ds地址信息
    optional string dsAddr = 2;
    // DS认证Token
    optional string dsAuthToken = 3;
    // ds所在dsa实例
    optional int64 dsaInstanceID = 4;
    // 大厅地图mapId
    optional int32 mapId = 5;
    // npc信息列表
    repeated proto_LobbyNpcInfo npcInfos = 6;
    repeated int32 npcInfos_deleted = 2006;
    optional bool npcInfos_is_cleared = 4006;
    // 大厅换色信息
    optional proto_LobbyChangeColorInfo changeColorInfo = 7;
    // 地图类型
    optional int32 gameType = 8;
    // ugc地图数据 弃用
    optional proto_UgcMapInfo ugcMapInfo = 9;
    optional bool ugcMapInfo_deleted = 2009;
    // 地图名字
    optional string name = 10;
    // 上次大厅id
    optional int64 lastLobbyId = 11;
    // 上次大厅地图id
    optional int32 lastMapId = 12;
    // 上次大厅ugcId
    optional int64 lastUgcId = 13;
    // 未发布的自测UGC大厅ID,用于重回该大厅
    optional proto_PlayerUgcLobbyInfo lastTestUgcLobby = 14;
    optional bool lastTestUgcLobby_deleted = 2014;
    // 上次未发布的自测UGC大厅ID,用于重回该大厅
    optional proto_PlayerUgcLobbyInfo lastInspectorUgcLobby = 15;
    optional bool lastInspectorUgcLobby_deleted = 2015;
    // 大厅的创建者信息，用于UGC自测大厅邀请的时候做创建人校验
    optional int64 lobbyCreatorUid = 16;
    // 当前大厅的ugcId
    optional int64 ugcId = 17;
    // 当前地图的状态
    optional int32 ugcSafeStatus = 18;
}