syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_FarmBuffEffect.proto";
import "attr_FarmBuffList.proto";

message proto_FarmBuffInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.FarmBuffInfo";
    // 玩家自己的农场Buff
    repeated proto_FarmBuffList selfBuffList = 1;
    repeated string selfBuffList_deleted = 2001;
    optional bool selfBuffList_is_cleared = 4001;
    // 玩家自己的农场buff效果（缓存用）
    repeated proto_FarmBuffEffect selfBuffEffectList = 2;
    repeated int32 selfBuffEffectList_deleted = 2002;
    optional bool selfBuffEffectList_is_cleared = 4002;
    // buff效果下一次刷新时间（秒）
    optional int64 selfBuffEffectNextFreshTimeSec = 3;
    // 玩家自己的农场Buff刷新时间戳（支持热更）
    optional int64 selfBuffHotUpdateTimeMs = 4;
}