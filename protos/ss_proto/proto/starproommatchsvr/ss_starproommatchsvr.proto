syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "plat_common.proto";
import "ss_head.proto";
import "ResKeywords.proto";
//import "attr_MatchSuccData.proto";
import "attr_AttrRoomInfo.proto";
import "attr_BattleInfo.proto";
import "server_interaction.proto";
import "attr_MultiPlayerSquadInfo.proto";
import "competition_common.proto";

message RpcTestStarpRoomMatchReq {
  optional int64 roomid = 1 [(field_hash_key) = true];   // 房间id
  optional int64 uid = 2;   // 玩家id
}

message RpcTestStarpRoomMatchRes {
  optional int32 result = 2;
}