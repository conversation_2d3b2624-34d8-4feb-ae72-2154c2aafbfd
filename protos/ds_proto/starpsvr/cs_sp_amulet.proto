syntax = "proto2";
package com.tencent.wea.protocol.sp;

message SPAddPetAmulet_Ntf{
  optional uint64 PlayerUID = 1; // 玩家UID
  optional uint64 AmuletID = 2; // 信物Id
  optional int32 AmuletCount = 3; // 获得的信物数量
}

message SPAddPetAmuletDetail_Ntf{
  optional uint64 PlayerUID = 1; // 玩家UID
  optional uint64 AmuletID = 2; // 信物Id
  optional int32 ExpAmuletCount = 3; // 提供经验信物数量
  optional int32 CurrencyAmuletCount = 4; // 提供货币信物数量
  optional int32 ObtainExp = 5; // 当次获得信物经验值
  optional int32 ObtainCurrency = 6; // 当次获得信物货币数量
}