syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "attr_BattleInfo.proto";
import "ResKeywords.proto";
import "competition_common.proto";

// 房间创建
message RoomCreate_C2S_Msg {
  optional MatchRuleInfo rule_info = 1;
  //optional TeamType team_type = 2;
  optional RoomType roomType = 2; // 房间类型
  optional string name = 3;  //房间名称
  optional string pwd = 4;   //房间密码
  optional int32 memberLimit = 5;  //房间人数
  optional int32 robotLevel = 6; // 机器人难度等级 1-5 AI能力配置表
  repeated RoomLevelSetting levelSetting = 7; // 关卡设置
  repeated RoomSpecialSetting specialSettings = 8; // 特殊设置
  optional RoomSetting setting = 9; // 整体设置
}

message RoomCreate_S2C_Msg {
  optional int32 retCode = 1;
  optional UniversalRoom roomInfo = 2; // 创建的房间信息
}

message RoomInvite_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  required int64 invitor_uid = 2; // 邀请人uid
  repeated int64 uid_list = 3; // 被邀请用户角色的uid列表
  optional InvitationSourceType ist = 4; // 邀请来源
  optional MatchRuleInfo rule_info = 5; // 玩家在非组队状态下选中的游戏模式
  optional int32 in_room_role_type = 6;   // 参考MemberBaseInfoRoomRoleType
}

message RoomInvite_S2C_Msg {
  optional int64 failureTime = 2;// 按钮失效的时间戳 当前时间+配置时间
}

message RoomInvitationReplyCheck_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  optional int64 inviter_uid = 2; // 邀请人uid
  required InvitationType resType = 3; // 回应类型
  optional TeamInvitationType invite_type = 4; // 是否需要跳转到队伍场景
  optional int32 in_room_role_type = 5;   // 这里需要客户端上行告知 参考MemberBaseInfoRoomRoleType
  optional InvitationSourceType ist = 6; // 邀请来源
  optional int64 starPWorldId = 7;   // 选择哪个啾灵世界加入队伍
  optional int64 starPRoleId = 8; // starP角色Id-StarP角色跨世界转移
  optional int64 sendTimestampMs = 9;// 发送时间戳
}

message RoomInvitationReplyCheck_S2C_Msg {
  optional int32 reserved = 1;
}

message RoomLeave_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  optional int64 uid = 2; // 离开房间的用户uid
  optional RoomExitType exitType = 3; // 退出方式
}

message RoomLeave_S2C_Msg {
  optional int32 reserved = 1;
}

message RoomKick_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  optional int64 kicker_uid = 2; // 被踢人的用户uid
  optional int32 position = 3; // 指定位置的玩家或机器人
}

message RoomKick_S2C_Msg {
  optional int32 reserved = 1;
}

// 匹配指定
message RoomStart_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  optional MatchRuleInfo rule_info = 2;

  optional MatchContentInfo matchContentInfo = 4; // 其他匹配参数
}

message RoomStart_S2C_Msg {
  optional int32 reserved = 1;
  optional MatchRuleInfo rule_info = 2; // 真正匹配的rule, 目前仅用于新手引导温暖局
}

// 队长开始匹配后的队员收到的ntf
message RoomStartNtf {
  optional int64 room_id = 1;
  optional int64 unique_id = 2; // 用于区别每一次匹配
}

// 队员点击确认匹配，避免过程中的模式改变，与队长开始匹配时参数一致，便于校验
message RoomConfirmStart_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  optional MatchRuleInfo rule_info = 2;

  //room已存在时(传递room_id!=0), 忽略以下参数
  optional TeamType team_type = 3; // TODO 替换
  optional MatchContentInfo matchContentInfo = 4; // 其他匹配参数

  optional int64 unique_id = 5; // 回传唯一标志符

  optional bool confirm = 6; // true 表示准备， false 表示取消准备

  optional MatchCancelReason cancelReason = 7; // 取消的原因
}

message RoomStartMatchCancelNtf {
  optional int64 room_id = 1;
  optional int64 unique_id = 2;
  optional int64 canceler_uid = 3; // 取消的玩家id
  optional MatchCancelType cancelType = 4; // 1:取消开始 2:取消匹配
  optional MatchCancelReason cancelReason = 5; // 取消的原因 在MatchCancelType为2时读取
}

message RoomConfirmStart_S2C_Msg {
}

message RoomCancelStart_C2S_Msg { //队伍取消匹配
  optional int64 room_id = 1; // 房间id
  optional com.tencent.wea.xlsRes.MatchReqType type = 2;  //匹配请求类型
}

message RoomCancelStart_S2C_Msg {
  optional int32 reserved = 1;
}

//队长不需要准备
// message RoomReadyStart_C2S_Msg {
//   optional int64 room_id = 1;   //房间id
//   optional int32 cancel_ready = 2; // 0 表示准备， 1 表示取消准备
// }

// message RoomReadyStart_S2C_Msg {
//   optional int32 reserved = 1;
// }

message MatchSuccNtf {
  optional proto_BattleInfo battleInfo = 1;
  optional int32 res_code = 2; // 错误码 0 成功  其他失败
  optional UgcBriefInfo ugcBriefInfo = 3; // ugc对局的附加简要信息
}

message MatchProcessNtf {
  optional int32 curMatchedPlayerNumber = 1;  // 当前已匹配到玩家数量
  optional int32 targetMatchPlayerNumber = 2; // 目标匹配玩家数量
}
message RoomCancelPreStart_C2S_Msg { //房间取消PreStart
    optional int64 room_id = 1; // 房间id
}
message RoomCancelPreStart_S2C_Msg {
    
}

message RoomInvitationNtf {// 组队邀请的发送
  optional MemberBaseInfo inviterInfo = 1;
  optional int64 roomID = 2;
  optional int64 roomType = 3;
  optional MatchRuleInfo rule_info = 4;
  optional ChatGroupKey chatGroupKey = 5;
  optional TeamInvitationType invite_type = 6; // TODO 存疑 场景跳转时一并调整
  optional int32 intimacy = 7; // 亲密度
  //optional proto_DressItemInfo headFrame = 8; // 头像框
  optional RoomBriefInfo roomBriefInfo = 8; // 房间简要信息
  optional RoomUgcInfo roomUgcInfo = 9; // ugc相关信息
  optional InvitationSourceType ist = 10; // 邀请来源
  optional int32 targetRoleType = 11;   // 邀请玩家加入的类型 参考MemberBaseInfoRoomRoleType
  optional int64 battleID = 12;// 战斗id
  optional int64 sendTimestampMs = 13;// 发送时间戳
}

message RoomMemberModifyNtf {// 组队中成员 增删改
  repeated MemberBaseInfo memberList = 1;
  optional int64 roomID = 2;
  optional int64 roomType = 3; // common.proto RoomType
  optional ModifyType modify = 4;
  optional MatchRuleInfo rule_info = 5; // 仅主玩法有效
  optional int64 leaderID = 6;
  optional TeamType teamType = 7; // 废弃
  optional int32 roomState = 8;   // 废弃
  optional ChatGroupKey chatGroupKey = 9;
  optional int32 maxMemberNum = 10;       // 人数上限
  optional com.tencent.wea.xlsRes.RoomStateType state = 11; // 房间状态
  optional com.tencent.wea.xlsRes.PlayerStateType curPlayerState = 12; // 当前玩家状态，避免客户端频繁遍历列表查询
  optional UgcBriefInfo ugcBriefInfo = 13; // ugc多人房间，地图相关信息
  optional int64 stateStartTime = 14; // 状态开始时间
  optional string pwd = 15; // 密码，可能有外显需求，为空就是无密码
  optional string roomNo = 16; // 房间号
  optional RoomBriefInfo roomBriefInfo = 17; // 房间简要信息
  optional int64 matchID = 18;
  optional bool autoSwitchMatchType = 21; // 自动切换玩法
  optional RoomSetting roomSetting = 22; // 房间设置
  optional CompetitionElimRoomInfo compElimRoomInfo = 23; // 赛事-淘汰赛房间信息
  repeated MapInfoForRoomMapVote mapInfosForRoomMapVote = 24; // UGC结伴同玩，投票用的地图信息
  repeated RoomMapVoteInfo voteInfos = 25; // UGC结伴同玩，投票信息
  optional int64 firstUserVoteTime = 26; // UGC结伴同玩，首次投票玩家的时间
  optional int32 battleCount = 27; // 战斗次数，目前只用在Ugc结伴同玩
  optional AlgoInfo recommendMapAlgoInfo = 28; // UGC结伴同玩，推荐地图的标识
  optional int32 maxObserverNum = 29;                // 最大房间ob人数
  repeated MemberBaseInfo observerMemberList = 30;   // ob的玩家列表
  optional UgcMatchRoomExtraInfo ugcMatchRoomExtra = 31;
  optional RoomGeoInfo roomGeoInfo = 32; // 房间的位置短号信息
  optional RoomCoMatchInfo roomCoMatchInfo = 33; // coMatch信息
  optional ModifyExtraInfo modifyExtraInfo = 34; // 修改额外补充说明内容
  repeated UgcBriefInfo ugcBriefInfos = 35; // ugc房间，多轮次地图列表信息
}

message RoomModifyModMode_C2S_Msg {
  optional int64 room_id = 1;   //房间id
  optional MatchRuleInfo rule_info = 2;
}

message RoomModifyModMode_S2C_Msg {
  optional int32 reserved = 1;
}

message RoomCancelResultNtf {
 required int64 room_id = 1; // 房间id
}

message BattleReConnectNtf {// 发现未结束房间，发送重连通知
  optional int64 battle_id = 1;   //房间id
}

message RoomWantToJoin_C2S_Msg {
  //optional int64 tag_player = 1;    // 房间中玩家的ID
  optional int64 targetPlayerUid = 1; // 目标玩家uid
  optional int32 ist = 2; // 邀请来源 参考InvitationSourceType取值
}

message RoomWantToJoin_S2C_Msg {
  optional int32 errorCode = 1;
}

message RoomWantToJoinNtf{
  //optional int64 req_player = 1; // 等待邀请的玩家id, refuse为空 member表示请求玩家信息
  //optional int64 refuse_id = 2;// 不为空，表示该id玩家拒绝加入请求，member为拒绝玩家信息
  //optional MemberBaseInfo req_Info = 3;
  optional MemberBaseInfo proposalInfo = 4; // 请求者信息
  optional int64 uniqueId = 5; // 唯一id，校验后续的同意逻辑有效性
  optional int32 ist = 6; // 邀请来源
  optional RoomBriefInfo roomBriefInfo = 7; // 房间基础信息
}

message RoomReplayWantToJoin_C2S_Msg{
  //optional int64 join_player = 1;    // 想要加入的玩家id
  //optional bool is_censent = 2;    // 是否同意 true 同意
  //optional MemberBaseInfo req_Info = 3;
  //optional TeamInvitationType invite_type = 4; // 是否需要同意后跳转场景
  optional int64 uniqueId = 5; // 唯一id，校验同意逻辑有效性
  optional int64 targetUid = 6; // 目标玩家uid
  optional bool agreeOrNot = 7; // 同意与否，true同意，false拒绝
}

message RoomReplayWantToJoin_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败
}

message RoomStartBattleNtf {
  required int64 battle_id = 1;
  optional ChatGroupKey globalGroupKey = 2; // 所有人聊天
  optional ChatGroupKey sideGroupKey = 3;   // 所属阵营聊天
  optional MatchRuleInfo ruleInfo = 4; // 房间规则数据
  optional bool enable_sec_tlog = 5;  // 是否上报安全反外挂日志
  repeated BattlePlayerClientInfo battlePlayerClientInfos = 6; //走gamesvr同步给客户端的局内玩家数据，这些数据在battlesvr维护，不在ds侧维护
}

message RoomCommonNtf {
  optional int32 type = 1;                              // 1: 加入队伍成功; 2: 修改房间主题
  optional string title = 2;                            // 房间主题
}

// 废弃
message RoomJoinFromQQ_C2S_Msg{
  optional int64 join_room = 1;    // 想要加入的roomid
  optional int64 inviter_uid = 2;   // 邀请人uid
  optional TeamInvitationType invite_type = 3;
}

message RoomJoinFromQQ_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败
}

message RoomJoinFromShare_C2S_Msg{
  optional int64 join_room = 1;    // 想要加入的roomid
  optional int64 inviter_uid = 2;   // 邀请人uid
  optional RoomShareType share_type = 3; // 分享的类型
}

message RoomJoinFromShare_S2C_Msg{

}

// 移交队长
message RoomLeaderTransit_C2S_Msg {
  optional int64 target_uid = 1; // 目标玩家uid
  optional int64 room_id = 2; // 队伍/房间id
}

message RoomLeaderTransit_S2C_Msg {
  optional int32 result = 1;         //0成功 其他失败
}

// 队伍解散
message RoomDisband_C2S_Msg {
  optional int64 room_id = 1;
}

message RoomDisband_S2C_Msg {
  optional int32 result = 1;         //0成功 其他失败
}

// 召集队员
message RoomLobbyGather_C2S_Msg {
  optional int64 room_id = 1; // 当前房间id
}

message RoomLobbyGather_S2C_Msg {
  optional int32 result = 1;         //0成功 其他失败
}

// 队员收到的房间场景召集通知
message RoomLobbyGatherNtf {
  optional int64 proposer_id = 1; // 召集发起者Uid
  optional int64 lobby_id = 2; // 目标场景（岛屿）id
}

// 房间场景输送逻辑
message RoomLobbyTransit_C2S_Msg {
  optional int64 target_uid = 1; // 目标玩家id
  optional int64 lobby_id = 2; // 目标场景（岛屿）id
  optional int64 room_id = 3; // 队伍/房间id
}

message RoomLobbyTransit_S2C_Msg {
  optional int32 result = 1;         //0成功 其他失败
}

// 组队招募
// 发布
message TeamRecruitPublish_C2S_Msg {
  optional int64 room_id = 1; // 房间Id
  optional int32 mode_id = 2; // 模式Id
  optional int32 play_id = 3; // 玩法Id
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifying_low_limit = 4; // 最低的段位限制
  optional int32 topic_id = 5; // 避免字符串被篡改
}

message TeamRecruitPublish_S2C_Msg {
}

// 发布（新模式分类）
message TeamRecruitPublishWithModeType_C2S_Msg {
  optional int64 room_id = 1; // 房间Id
  optional int32 mode_type = 2; // 一级筛选类型
  optional int32 category_type = 3; // 二级筛选类型
  optional int32 play_id = 4; // 具体玩法Id
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifying_low_limit = 5; // 最低的段位限制
  optional int32 topic_id = 6; // 避免字符串被篡改
}

message TeamRecruitPublishWithModeType_S2C_Msg {
}

// 筛选查询
message TeamRecruitQuery_C2S_Msg {
  optional int32 mode_id = 1; // 模式id
  optional int32 play_id = 2; // 玩法Id
  optional int32 targetRecruitType = 3; // 参考RecruitQuickJoinTargetType的取值
}

message TeamRecruitQuery_S2C_Msg {
  optional RoomRecruitArray recruit_array = 1;
}

// 筛选查询（新模式分类）
message TeamRecruitQueryByModeType_C2S_Msg {
  optional int32 mode_type = 1; // 一级筛选类型
  optional int32 category_type = 2; // 二级筛选类型
  optional int32 play_id = 3; // 玩法Id
  optional int32 targetRecruitType = 4; // 参考RecruitQuickJoinTargetType的取值
}

message TeamRecruitQueryByModeType_S2C_Msg {
  optional RoomRecruitArray recruit_array = 1;
}

message TeamRecruitQueryByPlayGroup_C2S_Msg {
  optional int32 playGroupId = 1; // 玩法组id
}

message TeamRecruitQueryByPlayGroup_S2C_Msg {
  optional RoomRecruitArray recruit_array = 1;
}

message PlayDetailPageRoomQueryByPlayGroup_C2S_Msg {
  optional int32 playGroupId = 1; // 玩法组id
}

message PlayDetailPageRoomQueryByPlayGroup_S2C_Msg {
  optional RoomRecruitArray recruit_array = 1; // 废弃
  optional UniversalRoomArray room_array = 2;
}

// 加入
message TeamRecruitJoin_C2S_Msg {
  optional RoomRecruit recruit = 1;
  optional int32 joinType = 2; // 参考RoomJoinType的取值
}

message TeamRecruitJoin_S2C_Msg {

}

// 目标招募类型
enum RecruitQuickJoinTargetType {
  RQJTT_All = 0; // 都可以
  RQJTT_Team = 1; // 队伍
  RQJTT_Room = 2; // 房间
}

// 快速加入
message TeamRecruitQuickJoin_C2S_Msg {
  repeated int64 priorityIdcZoneId = 1; // 优先idcZoneId 列表
  optional RecruitQuickJoinTargetType targetType = 2; // 目标类型
}

message TeamRecruitQuickJoin_S2C_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int64 targetIdcZoneId = 2;  // idcZoneId
}

message TeamRecruitQuickJoinByPlayGroup_C2S_Msg {
  optional int32 playGroupId = 1; // 玩法组id
  optional int32 targetType = 2; // 参考RecruitQuickJoinTargetType的取值
}

message TeamRecruitQuickJoinByPlayGroup_S2C_Msg {
}

message MatchSceneCreateSuccNtf {
  optional int32 result = 1; // 错误码 0 成功  其他失败
  optional SceneAddrInfo scene_addr = 2;
  optional int64 roundId = 3;
}

message MatchJoinSuccNtf {
  optional int32 result = 1; // 错误码 0 成功  其他失败
}

// 友情小队
message SquadGetInfo_C2S_Msg {
  optional int64 squadId = 1; // 查询的小队id
}

message SquadGetInfo_S2C_Msg {
  optional int32 result = 1; // 错误码 0 成功  其他失败
  optional ActivitySquad squadInfo = 2; // 小队信息
}

message SquadJoin_C2S_Msg {
  optional int64 squadId = 1; // 查询的小队id
}

message SquadJoin_S2C_Msg {
  optional int32 result = 1; // 错误码 0 成功  其他失败
  optional ActivitySquad squadInfo = 2; // 小队信息
}

message SquadTaskInfo_C2S_Msg {
  optional int64 squadId = 1; // 查询的小队id
}

message SquadTaskInfo_S2C_Msg {
  optional int32 result = 1; // 错误码 0 成功  其他失败
  optional SquadTaskGroupInfo squadTaskInfo = 2; // 小队任务完成详情
}

message SquadMultiPlayerGetInfo_C2S_Msg {
  optional int64 squadId = 1; // 查询的小队id
}

message SquadMultiPlayerGetInfo_S2C_Msg {
  optional int32 result = 1; // 错误码 0 成功  其他失败
  optional LongArray memberUidArray = 2; // 小队成员uid列表
}

message SquadMultiPlayerJoin_C2S_Msg {
  optional int64 squadId = 1; // 待加入的小队uid
}

message SquadMultiPlayerJoin_S2C_Msg {

}

// 房间推荐列表
message RoomRecommendList_C2S_Msg {
  optional RoomType roomType = 1; // 房间类型
  optional int32 num = 2; // 数量
  optional int32 modeId = 3; // 模式id
  optional int32 playId = 4; // 玩法id
}

message RoomRecommendList_S2C_Msg {
  optional UniversalRoomArray roomList = 1; // 房间列表
}

// 房间推荐列表（新招募模式）
message RoomRecommendListWithModeType_C2S_Msg {
  optional RoomType roomType = 1; // 房间类型
  optional int32 num = 2; // 数量
  optional int32 modeType = 3; // 一级筛选类型
  optional int32 categoryType = 4; // 二级筛选类型
  optional int32 playId = 5; // 玩法Id
}

message RoomRecommendListWithModeType_S2C_Msg {
  optional UniversalRoomArray roomList = 1; // 房间列表
}

// 快速加入房间
message RoomQuickJoin_C2S_Msg {
  optional RoomType roomType = 1; // 房间类型
  repeated int64 priorityIdcZoneId = 2; // 优先idcZoneId 列表
}

message RoomQuickJoin_S2C_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int64 targetIdcZoneId = 2;  // targetIdcZoneId
}

enum RoomNoType {
  RNT_No = 0;
  RNT_Id = 1;
}

// 通过房间短号获取房间信息
message RoomInfoQueryByRoomNo_C2S_Msg {
  optional int64 roomNo = 1; // 房间号
  optional RoomNoType rnt = 2; // 号码类型
}

message RoomInfoQueryByRoomNo_S2C_Msg {
  optional UniversalRoom room = 1;
}

message RoomJoin_C2S_Msg {
  optional int64 roomId = 1; // 房间ID
  optional RoomJoinType joinType = 2; // 加入类型
  optional string pwd = 3; // 密码
}

message RoomJoin_S2C_Msg {

}

message RoomPositionExchange_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int32 targetPosition = 2; // 目标位置
  optional int32 targetRoleType = 3;    // 目标房间角色
}

message RoomPositionExchange_S2C_Msg {
}

message RoomPositionExchangeNtf {
  optional MemberBaseInfo proposal = 1;
  optional int64 roomId = 2;
  optional int64 uniqueId = 3; // 换座请求id，用于后续同意或取消
}

message RoomPositionExchangeReply_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int64 uniqueId = 2; // 前序换座请求id
  optional bool acceptOrNot = 3; // 是否接受
}

message RoomPositionExchangeReply_S2C_Msg {
}

message RoomInfoChange_C2S_Msg {
  optional int64 roomId = 1;
  optional string title = 2;
  optional MatchRuleInfo ruleInfo = 3; //自定义房间能修改，UGC房间不能修改，但需要带过来
  optional string pwd = 4;   //房间密码
  optional int32 memberLimit = 5;  //自定义房间能修改，UGC房间不能修改，但需要带过来
  optional int32 robotLevel = 6; // 机器人难度等级 1-5 AI能力配置表，自定义房间能修改，UGC房间不能修改，但需要带过来
  repeated RoomLevelSetting levelSetting = 7; // 关卡设置，自定义房间能修改，UGC房间不能修改，但需要带过来
  repeated RoomSpecialSetting specialSettings = 8; // 特殊设置
  optional RoomSetting setting = 9; //房间设置，也包含了robotLevel，levelSetting，specialSettings，如果有设置，就覆盖setting里的
}

message RoomInfoChange_S2C_Msg {

}

message RoomReady_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional bool readyOrNot = 2; // 是否准备 true 准备；false 取消准备
}

message RoomReady_S2C_Msg {

}

message RoomStateCheck_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional com.tencent.wea.xlsRes.RoomStateType state = 2; // 房间状态
  optional com.tencent.wea.xlsRes.PlayerStateType curPlayerState = 3; // 玩家状态
}

message RoomStateCheck_S2C_Msg {
  optional int64 roomId = 1; // 当前房间id
  optional com.tencent.wea.xlsRes.RoomStateType state = 2; // 当前房间状态
  optional com.tencent.wea.xlsRes.PlayerStateType curPlayerState = 3; // 当前玩家状态
}

message RoomMapDownloadStat_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int64 mapId = 2;
  optional int32 percent = 3; // 下载进度
}

message RoomMapDownloadStat_S2C_Msg {

}

message RoomMapDownloadStatNtf {
  optional int64 roomId = 1;
  optional int64 uid = 2; // 废弃
  optional int64 mapId = 3;
  optional int32 percent = 4; // 废弃
  repeated PlayerMapDownloadProcessInfo playerProcessInfoList = 5; // 考虑组播
}

message RoomMapDownloadReminder_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int64 mapId = 2;
}

message RoomMapDownloadReminder_S2C_Msg {

}

message RoomMapDownloadReminderNtf {
  optional int64 roomId = 1;
  optional int64 mapId = 2;
}

message RoomAddRobot_C2S_Msg {
  optional int64 roomId = 1;
  optional bool oneOrAll = 2; // 一个或补满
  optional int32 position = 3; // 指定位置
  optional int32 robotLevel = 4; // 预留拓展，添加指定难度的机器人，暂时不填
}

message RoomAddRobot_S2C_Msg {

}

message RoomRemoveRobot_C2S_Msg {
  optional int64 roomId = 1;
  optional bool oneOrAll = 2; // 一个或全部删除
  optional int32 position = 3; // 指定位置
}

message RoomRemoveRobot_S2C_Msg {
}

message TeamJoinRoomConfirmNtf {
  optional RoomBriefInfo roomBriefInfo = 1; // 要加入的房间简要信息
  optional int64 uniqueId = 2; // 唯一id
  optional TeamJoinRoomExtraInfo extraInfo = 3;  // 扩展信息
}

message TeamJoinRoomCancelNtf {
  optional string notice = 1; // 提示文案
}

message RoomTeamConfirmJoin_C2S_Msg {
  optional int64 clientRoomId = 1; // 当前玩家的队伍id
  optional RoomBriefInfo targetRoom = 2; // 要加入的房间
  optional int64 uniqueId = 3; // 唯一id
  optional bool confirmOrNot = 4; // true 确认；false 取消
  optional TeamJoinRoomExtraInfo extraInfo = 5;  // 扩展信息
}

message RoomTeamConfirmJoin_S2C_Msg {

}

message RoomPlayerReadyReminder_C2S_Msg {
  optional int64 clientRoomId = 1; // 当前玩家的房间id
}

message RoomPlayerReadyReminder_S2C_Msg {

}

message RoomPlayerReadyReminderNtf {
  optional int64 roomId = 1; // 房间id
  optional int64 remindStartTimeMs = 2; // 提醒开始时间
  optional RoomReadyReminderType type = 3; // 提醒类型
}

message ReputationScoreNotEnoughNtf {
  repeated string nameList = 1;
}

message RoomMapStateSync_C2S_Msg {
  optional int64 clientRoomId = 1; // 当前玩家的房间id
  optional int64 mapId = 2;
  optional int32 type = 3;
  optional int32 process = 4;
}

message RoomMapStateSync_S2C_Msg {

}

message RoomMapStateSyncNtf {
  optional int64 roomId = 1; // 房间id
  optional int64 mapId = 2;
  repeated PlayerMapStateSyncInfo playerMapStateSyncInfo = 3;
}


message RoomReservation_C2S_Msg {
  required int64 uid = 1; // 要预约的玩家
}

message RoomReservation_S2C_Msg {
}

message RoomReservationNtf {
  optional int64 uid = 1; // 发起预约的玩家
}

message RoomReservationResponse_C2S_Msg {
  required int64 uid = 1; // 发起预约的玩家
  required int32 response = 2; // 响应
}

message RoomReservationResponse_S2C_Msg {
}

message RoomReservationResponseNtf {
  optional int64 uid = 1; // 要预约的玩家
  optional int32 response = 2; // 响应
}

message RoomMapVote_C2S_Msg {
  optional int64 roomId = 1; // 房间id
  optional int64 mapId = 2; // 选择的地图id
}

message RoomMapVote_S2C_Msg {

}

// 结束结算状态
message RoomEndSettlement_C2S_Msg {
  optional int64 roomId = 1; // 房间id
}

message RoomEndSettlement_S2C_Msg {

}

message RoomNeedNtfWhenAllMemberReadyForMatch_C2S_Msg {
  optional int64 roomId = 1; // 队伍/房间id
}

message RoomNeedNtfWhenAllMemberReadyForMatch_S2C_Msg {

}

message RoomAllMemberReadyForMatchNtf {
  optional int64 roomId = 1;
  optional int64 readyTimestampMs = 2;
}

message RoomOperationNtf {
  optional int64 roomId = 1;
  optional int32 operationType = 2; // 操作类型，1:重置玩法选择 2:重置关卡设置
}

message RoomCommonBroadcast_C2S_Msg {
  optional int64 roomId = 1;
  optional RoomBroadcastInfo broadcastInfo = 2;
}

message RoomCommonBroadcast_S2C_Msg {

}

message RoomBroadcastInfoNtf {
  optional int64 roomId = 1;
  optional RoomBroadcastInfo broadcastInfo = 2;
}
message DSInvitationReplyCheck_C2S_Msg {
  optional int64 inviter_uid = 1; // 邀请人uid
  optional int64 room_id = 2; // 房间id
  optional int64 battle_id = 3; // 战斗id
  required InvitationType resType = 4; // 回应类型
  optional MatchRuleInfo ruleInfo = 5; // ds的玩法匹配
}

message DSInvitationReplyCheck_S2C_Msg {
  optional int32 result = 1;
}

// 中途加入对局失败（只是邀请加入对局之后才会发）
message RoomMidJoinBattleFailNtf {
  optional int64 roomId = 1;
}

message RoomMiniGameInvitationNtf {
  optional int64 roomId = 1;
  optional int64 invitor = 2; // 邀请者
  optional PlatMiniGameNoticeInfo noticeInfo = 3; // 邀请的小游戏信息
}

message RoomLbsQuickJoinByPin_C2S_Msg {
  optional int32 pin = 1;           // 4位pin码
  optional double latitude = 2;     // 纬度
  optional double longitude = 3;    // 经度
  optional int32 nation = 4;        // 国家
  optional int32 province = 5;      // 省份
  optional int32 city = 6;          // 城市
  optional int32 town = 7;          // 区县
  optional string pinStr = 8;       // pin字符串
}

message RoomLbsQuickJoinByPin_S2C_Msg {

}

message TeamSetBackgroundTheme_C2S_Msg {
  optional int64 teamId = 1; // 组队id
  optional int32 backgroundTheme = 2; // 背景主题id
}

message TeamSetBackgroundTheme_S2C_Msg {
}

// 再来一局一起匹配的确认或取消
message RoomCoMatchReady_C2S_Msg {
  optional int64 coMatchId = 1;
  optional bool readyOrNot = 2; // 准备或者取消准备
}

message RoomCoMatchReady_S2C_Msg {
}


message RoomCoMatchInfoNtf {
  optional int64 battleId = 1; // 对局id
  repeated BattlePlayerCoMatchInfo coMatchInfoList = 2; // 所有同阵营玩家的同游匹配信息
  optional int64 proposedEndTimeMs = 3; // 提议结束时间
}

message RoomCoMatchPropose_C2S_Msg {
  optional int64 roomId = 1;
  optional int64 battleId = 2; // 对局id
}

message RoomCoMatchPropose_S2C_Msg {

}

message RoomCoMatchConfirm_C2S_Msg {
  optional int64 roomId = 1;
  optional int64 battleId = 2; // 对局id
  optional int32 agreeFlag = 3; // 参考CoMatchAgreeFlag的取值
}

message RoomCoMatchConfirm_S2C_Msg {

}

message PlaySwitch_C2S_Msg {

}

message PlaySwitch_S2C_Msg {
}


message LobbyMatchResultNtf {
  repeated MemberBaseInfo info = 1;
  optional int32 result = 2;         //0成功 其他失败
}

message LobbyMatchCancelResultNtf {
  optional int32 result = 1;         //0成功 其他失败
}

message GetLobbyMatchHistoryList_C2S_Msg {
}

message LobbyLobbyMatchHistoryList {
  optional int64 timeStampMS = 1;
  optional int64 uid = 2;
}

message GetLobbyMatchHistoryList_S2C_Msg {
  repeated LobbyLobbyMatchHistoryList list = 1;
}

// 获取当前房间轮次信息
message RoomGetCurRoundInfo_C2S_Msg {
  optional int64 roomId = 1;
}

message RoomGetCurRoundInfo_S2C_Msg {
  optional RoomRoundInfo roundInfo = 1;
}

// 当前房间轮次信息通知
message RoomRoundInfoNtf {
  optional int64 roomId = 1;
  optional RoomRoundInfo roundInfo = 2;
}

// 使用金币
message RoomUgcMultiRoundCoinUse_C2S_Msg {
  optional int64 roomId = 1;
  optional int32 coinNum = 2; // 使用的数量
}

message RoomUgcMultiRoundCoinUse_S2C_Msg {
  optional RoomRoundInfo roundInfo = 1; // 返回最新的轮次信息
}
// 队伍成员匹配成功后收到确认进入对局的ntf
message RoomConfirmEnterNtf {
  optional int64 room_id = 1;
  optional int64 unique_id = 2; // 用于区别每一次确认对局
  repeated MemberBaseInfo matchMember = 3 ; // 匹配的全部成员
  optional int32 resultType = 4 ; // 1: 显示匹配成功 2: 显示准备开战
}

// 队员点击确认进入对局，避免过程中的模式改变，与队长开始匹配时参数一致，便于校验
message RoomConfirmEnter_C2S_Msg {
  optional int64 room_id = 1; // 房间id
  optional MatchRuleInfo rule_info = 2;
  optional MatchContentInfo matchContentInfo = 4; // 其他匹配参数
  optional int64 unique_id = 5; // 回传唯一标志符
  optional bool confirm = 6; // true 表示确认进入， false 表示取消进入
}

message RoomConfirmEnter_S2C_Msg {
}

// 取消进入对局 NTF
message RoomConfirmEnterCancelNtf {
  optional int64 room_id = 1;
  optional int64 unique_id = 2;
  optional int64 canceler_uid = 3; // 取消的玩家id
  optional MatchCancelType cancelType = 4; // 1:取消开始 2:取消匹配
  optional MatchCancelReason cancelReason = 5; // 取消的原因 在MatchCancelType为2时读取
}

// 通知玩家进入pve副本的通知
message RoomStarPEnterGameNtf {
  optional StarPRoomPveInfo pveInfo = 1;
}

// 确认/取消 进入啾灵副本 进度 通知
message RoomStarPConfirmEnterProgressNtf {
  optional bool  cancel       = 2;   // 取消or确认
  optional int64 opUid        = 3;   // 操作玩家
  optional int64 room_id      = 4;
  optional int64 confirmCnt   = 5;   // 已确认玩家数量(只需确认时需要)
  optional int64 playerCnt    = 6;   // 总玩家数量
}

// SP 队伍广播(客户端发起cs,服务器转发到每个成员)
message RoomSPBroadcastInfoNtf {
  optional int64 roomId = 1;
  optional RoomSPBroadcastData broadcastData = 2;
}

// SP 招募列表
message StarPRecruitQuery_C2S_Msg {
  optional int32 mode    = 1 ; // 模式
  optional int32 subMode = 2 ; // 子模式
  optional int64 starPId = 3 ; // SP 世界ID (若玩家世界DS在线必填)
  optional int64 roleId  = 4 ; // roleId SP角色id
}

message StarPRecruitQuery_S2C_Msg {
  optional RoomRecruitArray recruit_array = 1;
}

// SP发布组队招募
message StarPRecruitPublish_C2S_Msg {
  optional int64 teamId = 1 ; // 队伍ID
  optional int64 level  = 2 ; // 加入等级要求
}

message StarPRecruitPublish_S2C_Msg {
}

// SP加入招募(该操作包含 三个步骤(队伍预占位/拜访预占位/发起拜访 三合一))
message StarPJoinRecruit_C2S_Msg {
  optional int64 teamId     = 1; // 队伍ID
  optional int64 starPId    = 2; // SP世界ID (若玩家世界DS在线必填)
  optional int64 roleId     = 3; // SP角色ID
  optional int32 matchType  = 4; // 队伍目标
}

message StarPJoinRecruit_S2C_Msg {
  optional int64 friendUid       = 1; // 队长uid
  optional int64 visitStarPId    = 2; // 队长SP世界ID(当前访问的房间)
  optional int64 visitRoleId     = 3; // 队长roleId
  optional int32 identity        = 4; // 我的身份标记,参考枚举 EnmStarPMemeberIdentity

  optional int64  enterWorldId   = 5; // 当该参数大于0时,客户端需要发起离开当前DS(若有),发起拜访请求(StarPEnter_C2S_Msg)
  optional int64  teamId         = 6; // 队伍ID
  optional int64  roleId         = 7; // SP角色ID
}

// 在匹配过程中,客户端发起修改队伍信息C2S消息
message RoomModifyMatchingTeamInfo_C2S_Msg {
  optional int64 room_id = 1;                                   // 房间id
  repeated MatchTeamInfoModificationField fields = 2;           // 修改字段 目前只有side
}

// 在匹配过程中,服务器返回修改队伍信息S2C消息
message RoomModifyMatchingTeamInfo_S2C_Msg {
  optional int32 result = 1;                                   // 0成功 其他失败
}

// 在匹配过程中,服务器返回修改队伍信息结果通知
message RoomModifyMatchingTeamInfoResultNtf {
  optional int64 room_id = 1;                           // 房间id 参考取消匹配和取消的cs 需要客户端提供房间id
  optional int64 requester_id = 2;                      // 请求修改的玩家id
}