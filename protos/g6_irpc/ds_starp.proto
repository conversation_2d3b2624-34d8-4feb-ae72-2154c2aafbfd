syntax = "proto3";
package ds_starp;
import "irpc_field_option.proto";
option java_package = "com.tencent.wea.g6.irpc.proto.ds_starp";

import "g6_common.proto";
import "common.proto";
import "attr_StarPUserPet.proto";
import "attr_PlayerPublicProfileInfo.proto";
import "attr_StarpDsGuildOneCopied.proto";
import "attr_StarPPublicInfo.proto";
import "player_info.proto";


// 发送 离线消息 消费请求 (ds->starpsvr->ds)
message StarPSendOfflineMsgRequest {
  int64 starPWorldId = 1 [(field_ds_session_id) = true];
  int32 type = 2; //  com.tencent.wea.protocol.StarPInteractionType
  int64 typeId = 3;
  repeated int64 msgId = 4;  // 需要显示消费的 消息id
}

message StarPSendOfflineMsgReply {
  int32 result = 1;
}

// 更新 玩家公开数据 db.PlayerPublic.StarPPublicUserInfo
message StarPUpdatePublicInfoRequest {
  int64 uid = 1 [(field_ds_session_id) = true]; // 玩家uid
  int64 roleId = 2;  // 角色id
  com.tencent.wea.protocol.StarPPublicUserInfo userInfo = 3;  // 数据
  bool increment = 4;  // 是否增量,false=全量
}

message StarPUpdatePublicInfoReply {
  int32 result = 1;
}

// 更新 队伍数据
message StarPUpdateTeamInfoRequest {
  int64 uid = 1 [(field_ds_session_id) = true]; // 玩家uid
  int64 roleId = 2;  // 角色id
  com.tencent.wea.protocol.StarPTeamUserInfo userInfo = 3;  // 数据
}

message StarPUpdateTeamInfoReply {
  int32 result = 1;
}

// 在房间内，更新部落信息（心跳对账保持一致）
message StarPUpdateGuildInfoRequest {
  int64 starPId = 1[(field_ds_session_id) = true];
  repeated com.tencent.wea.protocol.proto_StarpDsGuildOneCopied allGuildCopied = 2;
}

message StarPUpdateGuildInfoReply {
  int64 result = 1;
}

// 拉取ds离线时，删除玩家信息的通知，可能的情况：
// 1.玩家终端大厅退出世界删除自己。
// 2.管理员删除玩家，待踢出48h后变为删除，此时ds可能不在线。
message StarPGetDelUserInfoToGuildRequest {
  int64 starPId = 1 [(field_ds_session_id) = true];
}

message StarPGetDelUserInfoToGuildReply {
  int32 result = 1;
}

message DsAddPlayerFinishedRequest {
  int64 ds_id             = 1[(field_ds_session_id) = true];//ds id
  int64 uuid              = 2;
  int64 roleId            = 3; // SP角色ID
  int32 identity          = 4; // 玩家身份 参考@EnmStarPMemeberIdentity
  int32 result            = 5;
}

message DsAddPlayerFinishedReply {
}

// DS侧询问SP请求关闭自己
message DsAskStarPStopGameRequest {
  int64 ds_id             = 1[(field_ds_session_id) = true];//ds id
  string game_session_id  = 2;
  int64 dsa_inst_id       = 3;
}

// DS侧询问SP请求关闭自己
message DsAskStarPStopGameReply {
}

// DS侧已经完成SP的StopGame请求
message DsStopGameFinishedRequest {
  int64 ds_id             = 1[(field_ds_session_id) = true];//ds id
  string game_session_id  = 2;
  int32 result            = 3;
}

// DS侧已经完成SP的StopGame请求
message DsStopGameFinishedReply {
}

// DS->StarpSvr，批量获取玩家基础信息
message BatchGetPlayerInfoRequest {
  int64 starPId         = 1[(field_ds_session_id) = true];
  repeated int64 uids   = 2;
  repeated int32 fields = 3; // 获取字段：参考player_info.proto中的 PlayerPublicInfoField
}

message BatchGetPlayerInfoReply {
  repeated com.tencent.wea.protocol.PlayerPublicInfo playerPublicInfoList = 1;
}

// 请求房间基础信息
message StarPGetRoomPublicInfoRequest {
  int64 starPId = 1 [(field_ds_session_id) = true];
}

message StarPGetRoomPublicInfoReply {
  int32 result = 1;
  com.tencent.wea.protocol.proto_StarPPublicInfo starPPubInfo = 2;
}

//更新世界等级信息
message UpdateWorldLevelInfoRequest {
    int64 starPWorldId = 1 [(field_ds_session_id) = true];
	int32 newWorldLevel     = 2;    //新的世界等级
}

message UpdateWorldLevelInfoReply {
}

// DS侧 批量玩家广播请求
message BatchBroadcastRequest {
  int64 starPId           = 1[(field_ds_session_id) = true];
  repeated int64 uids     = 2;
  repeated int64 roleIds  = 3;
  int32 templateId        = 4;	// 模板id
  repeated string params  = 5;  // 格式化参数
  int32 type              = 6;  // 广播类型
  int32 canReplace        = 7;  // 能否顶替当前广播
  int32 canQueue          = 8;  // 是否进入当前广播队列
}

// DS侧 批量玩家广播响应
message BatchBroadcastReply {
  int32 result        = 1;  // 0成功 其他失败
  repeated int64 uids = 2;  // 处理成功uid列表
}

// idip修改上锁进入世界
message IdipModifyLockWroldRequest {
  int64 starPId              = 1[(field_ds_session_id) = true];
  int32 opType         = 3;               // 0:开始修改 1:结束修改
}

message IdipModifyLockWroldReply {
  int32 result = 1;
}

// DS侧 公会频道：系统消息发送
message ChatGroupSendSystemMessageRequest {
  int64 starPId              = 1[(field_ds_session_id) = true];
  string textFormat          = 2;	// 文案格式
  repeated string params     = 3;  // 格式化参数
  int32 msgType              = 4;
  com.tencent.wea.protocol.ChatGroupKey chatGroupKey  = 5;
}

// DS侧 批量玩家广播响应
message ChatGroupSendSystemMessageReply {
}

// DS侧 Ds到Gs的心跳包
message DsHeartbeatToStarpsvrRequest {
  int64 ds_id                 = 1[(field_ds_session_id) = true];//ds id
  string game_session_id      = 2;
  repeated int64 uuid         = 3; // 普通成员列表
  repeated int64 visitor_uuid = 4; // 访客成员列表
  int32 real_mem = 5; // ds占用的实际内存(预留)
  int32 physical_mem = 6; // 物理内存
  int32 total_cpu = 7; // 总cpu
  repeated int64 ruid         = 8; // roleIdList，不再需要uid
  repeated int64 visitor_ruid = 9; // roleIdList，不再需要uid
}

message DsHeartbeatToStarpsvrReply {
  int32 result = 1;
}

// ds通知服务器玩家准备离线
message PlayerDsPrepareOfflineRequest {
  int64 ds_id             = 1[(field_ds_session_id) = true]; // ds对应的id，如大厅id 战场id
  int64 uuid              = 2; // 玩家uid
  int32 quit_code         = 3; // 参考QuitBattleCode
  int32 identity          = 4; // 玩家身份 参考@EnmStarPMemeberIdentity
  int64 ruid              = 5; // roleId
}

message PlayerDsPrepareOfflineReply {

}


// ds通知服务器玩家正式离线
message PlayerDsOfflineRequest {
  int64 ds_id             = 1[(field_ds_session_id) = true]; // ds对应的id，如大厅id 战场id
  int64 uuid              = 2;  // 玩家uid
  int64 ds_session_id     = 3; //ds实例id
  int32 quit_code         = 4;  // 参考QuitBattleCode
  int32 identity          = 5; // 玩家身份 参考@EnmStarPMemeberIdentity
  map<string, string> callback_data = 6;  // 回调上下文, server调用ds的PlayerExitDsRequest请求时带入的
  int64 ruid              = 7; // ruid
}

message PlayerDsOfflineReply {

}

// ds通知服务器玩家进入
message PlayerDsEnterRequest {
  int64 ds_id             = 1[(field_ds_session_id) = true]; // ds对应的id，如大厅id 战场id
  int64 uuid              = 2; // 玩家uid
  int64 ds_session_id     = 3; //ds实例id
  int32 identity          = 4; // 玩家身份 参考@EnmStarPMemeberIdentity
  int64 ruid              = 5; //roleId
  int32 callbackType      = 6; // 回调类型,0:默认为原回调类型, 新增1:表示玩家真成功进入DS回调
}

message PlayerDsEnterReply {
  int64 expireTime    = 1; // 访客体验结束时间戳
}

message GMAddRobotInfoRequest{
  int64 starPId = 1 [(field_ds_session_id) = true];
  int64 uid = 2 [(field_ds_session_id) = true]; // 玩家uid
  int32 num = 3;
}

message GMAddRobotInfoReply{
  repeated int64 robotUidList = 1;
  int32 result = 2;
}

// ds通知服务器玩家玩家进入完毕
message PlayerDsEnterFinishRequest {
  option (irpc_one_way) = true;
  int64 starPId    = 1[(field_ds_session_id) = true];
  int64 uid        = 2; // 玩家uid
  int32 identity   = 3; // 玩家身份 参考@EnmStarPMemeberIdentity
  int64 ruid       = 4; //roleId
}

message PlayerDsEnterFinishReply {
}

// 1.ds侧请求玩家的预退出 ds->gamesvr

service StarPServer {
  rpc SendOfflineMsg (StarPSendOfflineMsgRequest) returns (StarPSendOfflineMsgReply) {}
  rpc UpdatePublicInfo (StarPUpdatePublicInfoRequest) returns (StarPUpdatePublicInfoReply) {}
  rpc StarPGetDelUserInfoToGuild (StarPGetDelUserInfoToGuildRequest) returns (StarPGetDelUserInfoToGuildReply) {}
  // DS到starpsvr，玩家数据载入结果返回
  rpc DsAddPlayerFinished (DsAddPlayerFinishedRequest) returns (DsAddPlayerFinishedReply) {}
  // DS到Starpsvr, 请求向自己发送StopGame
  rpc DsAskStarPStopGame (DsAskStarPStopGameRequest) returns (DsAskStarPStopGameReply) {}
  // DS到Starpsvr, 已经完成SP的StopGame请求
  rpc DsStopGameFinished (DsStopGameFinishedRequest) returns (DsStopGameFinishedReply) {}
  rpc UpdateTeamInfo (StarPUpdateTeamInfoRequest) returns (StarPUpdateTeamInfoReply) {}
  // 更新部落信息
  rpc StarPUpdateGuildInfo (StarPUpdateGuildInfoRequest) returns (StarPUpdateGuildInfoReply) {}
  // 请求房间基础信息
  rpc StarPGetRoomPublicInfo (StarPGetRoomPublicInfoRequest) returns (StarPGetRoomPublicInfoReply) {}

  // DS->StarpSvr，批量获取玩家基础信息
  rpc StarPBatchGetPlayerInfo (BatchGetPlayerInfoRequest) returns (BatchGetPlayerInfoReply) {}
  
  // DS->StarpSvr  更新世界等级信息
  rpc UpdateWorldLevelInfo (UpdateWorldLevelInfoRequest) returns (UpdateWorldLevelInfoReply) {}
  // DS到Starpsvr, 批量玩家广播
  rpc BatchBroadcast (BatchBroadcastRequest) returns (BatchBroadcastReply) {}
  // DS到Starpsvr, 聊天频道发送系统消息
  rpc ChatGroupSendSystemMessage (ChatGroupSendSystemMessageRequest) returns (ChatGroupSendSystemMessageReply) {}
  // DS到Starpsvr，idip修改上锁进入世界
  rpc IdipModifyLockWrold (IdipModifyLockWroldRequest) returns (IdipModifyLockWroldReply) {}
  // DS到starpsvr心跳
  rpc DsHeartbeatToStarpsvr (DsHeartbeatToStarpsvrRequest) returns (DsHeartbeatToStarpsvrReply) {}
  // ds通知服务器玩家准备离线
  rpc PlayerDsPrepareOffline (PlayerDsPrepareOfflineRequest) returns (PlayerDsPrepareOfflineReply) {}
  // ds通知服务器玩家正式离线
  rpc PlayerDsOffline (PlayerDsOfflineRequest) returns (PlayerDsOfflineReply) {}
  // ds通知服务器玩家进入
  rpc PlayerDsEnter (PlayerDsEnterRequest) returns (PlayerDsEnterReply) {}
  rpc GMAddRobotInfo(GMAddRobotInfoRequest) returns (GMAddRobotInfoReply) {}
  rpc PlayerDsEnterFinish(PlayerDsEnterFinishRequest) returns (PlayerDsEnterFinishReply) {}
}

