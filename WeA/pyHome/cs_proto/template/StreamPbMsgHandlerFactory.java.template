// generated by tools, DO NOT MODIFY ANYTHING
package com.tencent.wea.cshandler;

import java.util.HashMap;
import java.util.Map;
import com.tencent.wea.protocol.MsgTypes;

%for i in sorted(msgTypedesc.msgId2Msgs):
    %if msgTypedesc.msgId2Msgs[i].getServerName()==servername:
    %if msgTypedesc.msgId2Msgs[i].getMsgName() in protodesc.globalmsgdict:
        %if msgTypedesc.msgId2Msgs[i].msgType == "C2S":
    <%
           msgid=i
           msgName=msgTypedesc.msgId2Msgs[i].getMsgName()
           msgTypeName=msgTypedesc.msgId2Msgs[i].getMsgTypeName()
           msgfileclassname=protodesc.globalmsgdict[msgName].fileclassname
           parentDirName=msgfileclassname.replace("Cs","").lower()
           msgHandlerName=msgName.replace('C2S_Msg', '').replace('_', '') + "MsgHandler"
    %>
${"import com.tencent.wea.cshandler.handler." + parentDirName + "." + msgHandlerName + ";"}
        %endif
    %endif
    %endif
%endfor

/**
 * maintain the msg handlers
 */
public class ${servername.capitalize()}PbMsgHandlerFactory {
    public static final Map<Integer, Class<? extends AbstractStreamClientRequestHandler>> MSG_HANDLERS = new HashMap<>();
    static {

%for i in sorted(msgTypedesc.msgId2Msgs):
    %if msgTypedesc.msgId2Msgs[i].getServerName()==servername:
    %if msgTypedesc.msgId2Msgs[i].getMsgName() in protodesc.globalmsgdict:
        %if msgTypedesc.msgId2Msgs[i].msgType =="C2S":
    <%
           msgid=i
           msgName=msgTypedesc.msgId2Msgs[i].getMsgName()
           msgTypeName=msgTypedesc.msgId2Msgs[i].getMsgTypeName()
           msgfileclassname=protodesc.globalmsgdict[msgName].fileclassname
           parentDirName=msgfileclassname.replace("Cs","").lower()
           msgHandlerName=msgName.replace('C2S_Msg', '').replace('_', '') + "MsgHandler"
    %>
            ${"MSG_HANDLERS.put(MsgTypes." + msgTypeName + ", " + msgHandlerName + ".class);"}
        %endif
    %endif
    %endif
%endfor

    }

    /**
     *  get msg handler
     * @param type
     * @return
     */
    public static Class<? extends AbstractStreamClientRequestHandler> getMsgHandler(int type) {
        return MSG_HANDLERS.get(type);
    }
}
