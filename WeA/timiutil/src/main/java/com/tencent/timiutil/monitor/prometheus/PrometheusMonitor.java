package com.tencent.timiutil.monitor.prometheus;

import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorCountType;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.monitor.MonitorMethod;
import com.tencent.timiutil.monitor.MonitorModule;
import com.tencent.timiutil.property.PropertyFileReader;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

public class PrometheusMonitor extends Monitor {

    private static final Logger LOGGER = LogManager.getLogger("zhiyanMonitor");
    private static final String SAMPLE_TAG_DELIMITER = "|";
    private static boolean histogramCmdFlag = false;
    private String busid = "0.0.0.0";
    private String svrName = "{svrName}";
    private String shardName = "";
    private int ret = -1;

    private static final ThreadLocal<StringBuilder> sbPool = ThreadLocal.withInitial(() -> new StringBuilder(1024));

    PrometheusMonitor() {
    }

    public static boolean isHistogramCmdFlag() {
        return histogramCmdFlag;
    }

    public static void setHistogramCmdFlag(boolean histogramCmdFlag) {
        PrometheusMonitor.histogramCmdFlag = histogramCmdFlag;
    }

    private static ExporterSample.CountType translateCountType(MonitorCountType countType) {
        switch (countType) {
            case TOTAL:
                return ExporterSample.CountType.LT_TOTAL;
            case SUCC:
                return ExporterSample.CountType.LT_SUCC;
            case FAIL:
                return ExporterSample.CountType.LT_FAIL;
            case TIMEOUT:
                return ExporterSample.CountType.LT_TIMEOUT;
            default:
                return ExporterSample.CountType.LT_UNKNOWN;
        }
    }

    private static ExporterSample.SampleType translateMethodType(MonitorMethod method) {
        switch (method) {
            case ADD:
                return ExporterSample.SampleType.ST_COUNTER;
            case SET:
                return ExporterSample.SampleType.ST_GAUGE;
            case LESS:
                return ExporterSample.SampleType.ST_HISTOGRAM;
            default:
                return ExporterSample.SampleType.ST_UNKNOWN;
        }
    }

    public static PrometheusMonitor getInstance() {
        return PrometheusMonitor.InstanceHolder.instance;
    }

    private static StringBuilder borrowSB() {
        var sb = sbPool.get();
        sb.setLength(0);
        return sb;
    }

    public static String getSampleId(ExporterSample.Sample.Builder sample) {
        StringBuilder ret = borrowSB();
        ret.append(sample.getMonitorid())
                .append(SAMPLE_TAG_DELIMITER)
                .append(sample.getCtype().getNumber());
        ret.append(SAMPLE_TAG_DELIMITER);
        if (sample.hasRpcMethod()) {
            ret.append(sample.getRpcMethod());
        }
        ret.append(SAMPLE_TAG_DELIMITER);
        if (sample.hasRpcDest()) {
            ret.append(sample.getRpcDest());
        }
        ret.append(SAMPLE_TAG_DELIMITER);
        if (sample.hasDb()) {
            ret.append(sample.getDb());
        }
        ret.append(SAMPLE_TAG_DELIMITER);
        if (sample.hasP1()) {
            ret.append(sample.getP1());
        }
        ret.append(SAMPLE_TAG_DELIMITER);
        if (sample.hasP2()) {
            ret.append(sample.getP2());
        }
        ret.append(SAMPLE_TAG_DELIMITER);
        if (sample.hasP3()) {
            ret.append(sample.getP3());
        }
        return ret.toString();
    }

    private static void setSampleParamsArray(ExporterSample.Sample.Builder sample, String[] params) {
        if (params.length >= 1) {
            sample.setP1(params[0]);
        }
        if (params.length >= 2) {
            sample.setP2(params[1]);
        }
        if (params.length >= 3) {
            sample.setP3(params[2]);
        }
    }

    @Override
    public void init(String busid, String svrName, String worldId, boolean isClose) {
        if (ret == 0) {
            long lastUpdateTime = 0;
            LOGGER.error("inited already on {}, nop", lastUpdateTime);
            return;
        }
        WeAExporterCnf.loadCfg();
        LOGGER.info("initializing prometheus monitor");
        this.svrName = svrName;
        this.busid = busid;
        ret = WeAExporter.init(new String[]{});
    }

    @Override
    public void setShard(String shard) {
        LOGGER.info("setShard {}", shard);
        shardName = shard.isEmpty() ? "shard" : shard;
    }

    @Override
    public void observe(MonitorId metrics, double value) {
        observerInternal(metrics, value, null, null);
    }

    @Override
    public void observe(MonitorId metrics, double value, String[] labels) {
        observerInternal(metrics, value, labels, null);
    }

    @Override
    public void observe(MonitorId metrics, double value, String[] labels, float[] buckets) {
        observerInternal(metrics, value, labels, buckets);
    }

    private void observerInternal(MonitorId metrics, double value, String[] labels, float[] allBuckets) {
        boolean isOpen = PropertyFileReader.getRealTimeBooleanItem("monitor_histogram_open_flag", false);
        if (!isOpen && !histogramCmdFlag) {
            return;
        }

        String monitorIdStr = getMonitorIDStr(metrics);
        if (monitorIdStr == null) {
            LOGGER.error("observerInternal error metrics={}", metrics);
            return;
        }

        ExporterSample.Sample.Builder sample = ExporterSample.Sample.newBuilder()
                .setType(translateMethodType(MonitorMethod.LESS))
                .setCtype(translateCountType(MonitorCountType.TOTAL))
                .setBusid(busid)
                .setMonitorid(monitorIdStr)
                .setModuleName(metrics.getMonitorModule().name())
                .setInstance("V1")
                .setValue(value)
                .addValues(value)
                .setServer(WeAExporterCnf.getServerType(svrName));

        if (allBuckets != null) {
            for (float allBucket : allBuckets) {
                sample.addBuckets(allBucket);
            }
        }

        if (labels != null) {
            for (int i = 0; i < labels.length && i <= 3; i++) {
                if (i == 0) {
                    sample.setP1(labels[i]);
                } else if (i == 1) {
                    sample.setP2(labels[i]);
                } else if (i == 2) {
                    sample.setP3(labels[i]);
                }
            }
        }

        WeAExporter.addSample(sample);
    }

    @Nullable
    private String getMonitorIDStr(MonitorId metrics) {
        if (metrics == null) {
            return null;
        }

        if (ret != 0) {
            return null;
        }

        String monitorIdStr = metrics.name();
        return monitorIdStr;
    }

    @Override
    public void reload() {
        WeAExporterCnf.loadCfg();
    }

    @Override
    public void report(MonitorId metrics, MonitorCountType countType, double value, MonitorMethod method) {
        report(metrics, countType, value, method, new String[]{});
    }

    @Override
    public void report(MonitorId metrics, MonitorCountType countType, double value, MonitorMethod method,
            String[] params) {
        if (metrics == null) {
            return;
        }
        if (ret != 0) {
            return;
        }
        String monitorIdStr = metrics.name();
        ExporterSample.Sample.Builder sample = ExporterSample.Sample.newBuilder()
                .setType(translateMethodType(method))
                .setCtype(translateCountType(countType))
                .setBusid(busid)
                .setMonitorid(monitorIdStr)
                .setModuleName(metrics.getMonitorModule().name())
                .setInstance("V1")
                .setValue(value)
                .setServer(WeAExporterCnf.getServerType(svrName));
        if (metrics.getMonitorModule() == MonitorModule.Rpc && params.length >= 2) {
            sample.setRpcMethod(params[0]);
            sample.setRpcDest(WeAExporterCnf.getServerType(params[1]));
            if(params.length >= 3) {
                sample.setP3(params[2]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.IRpc && params.length >= 2) {
            sample.setRpcMethod(params[0]);
            sample.setRpcDest(WeAExporterCnf.getServerType(params[1]));
        } else if (metrics.getMonitorModule() == MonitorModule.Tcaplus && params.length >= 1) {
            sample.setDb(params[0]);
            if (params.length > 1) {
                sample.setP1(params[1]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.C2sReq && params.length >= 1) {
            sample.setRpcMethod(params[0]);
            if (params.length >= 2) {
                sample.setP1(params[1]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.Statistics && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.Activity && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.Lobby && params.length >= 1) {
            sample.setDb(params[0]);
            if (params.length >= 2) {
                sample.setRpcMethod(params[1]);
            }
            sample.setP1(params[0]);
            if (params.length >= 2) {
                sample.setP2(params[1]);
            }
            if (params.length >= 3) {
                sample.setP3(params[2]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.OutputControl && params.length >= 1) {
            sample.setDb(params[0]);
            if (params.length >= 2) {
                sample.setP1(params[1]);
            }
            if (params.length >= 3) {
                sample.setP2(params[2]);
            }
            if (params.length >= 4) {
                sample.setP3(params[3]);
            }
            if (params.length >= 5) {
                sample.setRpcMethod(params[4]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.BattleSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.DscAllocSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.UgcSvr && params.length >= 1) {
            sample.setDb(params[0]);
            sample.setP1(params[0]);
            if (params.length >= 2) {
                sample.setP2(params[1]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.AsyncSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.UgcPlatSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.MatchSvr && params.length >= 1) {
            sample.setDb(params[0]);
            sample.setP1(params[0]);
            if (params.length >= 2) {
                sample.setP2(params[1]);
            }
            if (params.length >= 3) {
                sample.setP3(params[2]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.MatchAllocSvr && params.length >= 1) {
            sample.setDb(params[0]);
            if (params.length >= 2) {
                sample.setRpcMethod(params[1]);
            }
        } else if (metrics.getMonitorModule() == MonitorModule.IdipSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.XiaoWoSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.FarmSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.SampleRoomSvr && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.Tss && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.Apollo && params.length >= 1) {
            sample.setP1(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.Midas && params.length >= 1) {
            sample.setDb(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.Redis && params.length >= 2) {
            sample.setP1(params[0]);
            sample.setP2(params[1]);
        } else if (metrics.getMonitorModule() == MonitorModule.Raffle && params.length >= 1) {
            sample.setP1(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.TbusppOnlineInstance && params.length >= 1) {
            sample.setP1(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.DanMuSvr && params.length >= 1) {
            sample.setP1(params[0]);
        } else if (metrics.getMonitorModule() == MonitorModule.HotRes && params.length >= 1) {
            sample.setDb(params[0]);
        } else if(metrics.getMonitorModule() == MonitorModule.Shard && params.length >= 1) {
            sample.setP1(params[0]);
        }  else if(metrics.getMonitorModule() == MonitorModule.Proxy && params.length >= 1) {
            sample.setP1(params[0]);
        }  else {
            setSampleParamsArray(sample, params);
        }
        WeAExporter.addSample(sample);
    }

    @Override
    public void report(MonitorModule module, String monitorId, MonitorCountType countType, double value,
            MonitorMethod method) {
        if (module == null || monitorId.isEmpty()) {
            return;
        }
        if (ret != 0) {
            return;
        }
        WeAExporter.addSample(ExporterSample.Sample.newBuilder()
                .setType(translateMethodType(method))
                .setCtype(translateCountType(countType))
                .setBusid(busid)
                .setMonitorid(monitorId)
                .setModuleName(module.toString())
                .setInstance("V1")
                .setValue(value)
                .setServer(WeAExporterCnf.getServerType(svrName))
        );
    }

    @Override
    protected void reportCustom(String monitorId, MonitorCountType countType, double value,
            MonitorMethod method) {
        reportCustom(monitorId, countType, value, method, new String[] {});
    }

    @Override
    protected void reportCustom(String monitorId, MonitorCountType countType, double value, MonitorMethod method,
                                String[] params) {
        if (StringUtils.isBlank(monitorId)) {
            return;
        }
        if (ret != 0) {
            return;
        }

        ExporterSample.Sample.Builder sample = ExporterSample.Sample.newBuilder()
                .setType(translateMethodType(method))
                .setCtype(translateCountType(countType))
                .setBusid(busid)
                .setMonitorid(monitorId)
                .setModuleName("other")
                .setInstance("V1")
                .setValue(value)
                .setServer(WeAExporterCnf.getServerType(svrName));
        setSampleParamsArray(sample, params);

        WeAExporter.addSample(sample);
    }

    private static class InstanceHolder {

        public static PrometheusMonitor instance = new PrometheusMonitor();
    }

}
