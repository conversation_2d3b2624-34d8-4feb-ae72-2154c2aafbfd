package com.tencent.coHttp;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.http.HttpResponse;
import org.apache.http.nio.protocol.HttpAsyncResponseConsumer;
import org.apache.http.protocol.HttpContext;

abstract class CustomHttpAsyncResponseConsumer implements HttpAsyncResponseConsumer<HttpResponse> {
    private final AtomicBoolean completed;
    private HttpResponse response;
    protected ByteBuffer buffer;
    private volatile Exception ex;
    private int bufferCapacity = 1024;

    public CustomHttpAsyncResponseConsumer() {
        this.completed = new AtomicBoolean(false);
    }

    @Override
    public void responseReceived(HttpResponse response) {
        this.response = response;
        // 初始化缓冲区
        this.buffer = ByteBuffer.allocate(bufferCapacity);
    }

    @Override
    public void responseCompleted(HttpContext context) {
        if (this.completed.compareAndSet(false, true)) {
            releaseResources();
        }
    }

    @Override
    public void failed(Exception ex) {
        if (this.completed.compareAndSet(false, true)) {
            this.ex = ex;
            releaseResources();
        }
    }

    @Override
    public Exception getException() {
        return this.ex;
    }

    @Override
    public HttpResponse getResult() {
        return this.response;
    }

    @Override
    public boolean isDone() {
        return this.completed.get();
    }

    @Override
    public void close() throws IOException {
        if (this.completed.compareAndSet(false, true)) {
            releaseResources();
        }
    }

    @Override
    public boolean cancel() {
        if (this.completed.compareAndSet(false, true)) {
            releaseResources();
            return true;
        }
        return false;
    }

    protected void releaseResources() {
    }

    public void setBufferCapacity(int bufferCapacity) {
        this.bufferCapacity = bufferCapacity;
    }
}
