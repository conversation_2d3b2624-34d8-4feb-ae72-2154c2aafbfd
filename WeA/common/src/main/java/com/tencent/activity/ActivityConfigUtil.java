package com.tencent.activity;

import com.google.protobuf.Timestamp;
import com.tencent.nk.commonframework.Framework;
import com.tencent.resourceloader.resclass.ActivityMainConfig;
import com.tencent.resourceloader.resclass.ActivityMianGanData;
import com.tencent.resourceloader.resclass.TaskConfData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.protocol.common.ReceiveInfo;
import com.tencent.wea.xlsRes.ResActivityFunPara;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/03/29
 * @desc 活动配置管理工具, 用于管理和查询活动归属于ActivitySvr或GameSvr上
 */

public class ActivityConfigUtil {
    private static final Logger LOGGER = LogManager.getLogger(ActivityConfigUtil.class);

    private static ConcurrentHashMap<Integer, ActivityConfigObject> allActivityObj = new ConcurrentHashMap<>();

    static {
        // activitySvr上的活动,新增的活动都添加到ActivitySvr
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATWish, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATTaskRunOnActivitySvr, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATTrainingCamp, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATMonopoly, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATMinesweeper, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATThemeAdventure, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATBookOfFriends, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATGroupReturning, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATWishingTree, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATLanternRiddles, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFishingHallOfFame, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATDanceOutfit, false, true));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATConanWarmup, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATMOBANewHeroTrial, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATCaptureShadow, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATTraining, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATWolfLinearExchange, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATTravelingDog, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFactionBattle, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATSpringLobby2025, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFashionFund, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFarmFoodFestival, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATScoreActivity, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATMobaRandomVote, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATAmusementPark, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATMoboChallenge, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFarmAnswer, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATWolfLinearExchangeTwo, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATConanIpActive, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATWolfReturn, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATQingShuangTrial, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFeatureIntegrationTemplate, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATTreasureLevelUp, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATRestaurantThemed, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFlashRaceCheering, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATFarmDragonBoatFestival, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATWolfTeamChest, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATSummerNavigationBar, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATSummerVacationBP, false, false));
        addActivitySvrObj(new ActivityConfigObject(ActivityType.ATSummerFlashMob, false, false));


        // gameSvr上的活动,原则上不再新增;
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTradingCardBounds, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATPrayerCard, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATPlatShareActive, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLuckyMoney, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATDreamNewStar, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATWealthBank, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFriendSquad, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATGuide, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTaskAndShop, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATChapterTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATDailyColorPaint, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLinearRedeem, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATCheckInPlan, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATMonthlySpecial, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATInterServerGift, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSquad, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTakeaway, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATDeposit, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATRecruitRights, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSingleTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSuperLinearRedeem, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLuckyBalloon, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTeamRank, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATRecruitOrder, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTimeLimitedCheckIn, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSpringRedPacket, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATScratchOffTickets, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATAccumulateBlessings, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATUltramanTheme, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLuckyStar, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSingelTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATNNWelfare, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSpringFestivalCeremony, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSprintBlessingCollection, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSpringPray, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATReturningTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATUseItemShare, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATUltramanThemeTeam, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATUpgradeCheckInManual, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLotteryDraw, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATKungFuPanda, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATMultiPlayerGroup, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATRelatedDisplay, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSticker, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATActivityNavigation, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATActivityPermanentExchange, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATActivityCompetitionWarmUp, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATIntelligenceStation, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFlyingChess, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATMusicOrder, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATHalfYearNavigation, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATReturningDailyGroupTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATHalfYearWarmUp, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATKuromiTechou, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATUpdateForesight, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATAnimalHandbook, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATCallWerewolf, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATPaidUnlock, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATEaseBurden, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATEaseBurdenEasyTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATEaseBurdenTeamTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATCommonAppointment, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLuckyTurntable, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATWolfKillSquadTrophy, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATWeekendIceBroken, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATUGCChaseDream, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATNationalDayHundredDraws, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSuperCoreRanking, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLuckyRebate, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATWerewolfFullReduce, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTaskShopRaffle, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATConanCeremony, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATArenaWeeklyActivity, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATDoubleDiamond, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTradingCardShow, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATAnniversaryMoaba, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATReturningDiffers, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATArenaSevenDaysLogin, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATMobaSquadDrawRedPacket, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATNewYearSign, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSpringFestivalRaffle, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFindPartnerActivity, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTwoPeopleSquad, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATInflateRedPacket, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATScoreGuide, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFarmSquadActivity, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATBirthday, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFarmReturn, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFarmBuffWish, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFarmSevenDayTask, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATSpringSlip, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATCOCEightDays, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATPuzzle, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATFarmDailyAward, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATTreasureHunt, false, false));
        addGameSvrObj(new ActivityConfigObject(ActivityType.ATLevelUpChallenge, false, false));

        LOGGER.info("ActivityConfigUtil init success! size[{}]", allActivityObj.size());
    }

    private static boolean check(ActivityConfigObject obj) {
        if (obj.isActivitySvrRun == obj.isGameSvrRun) {
            // 不可能出现的情况,一个活动要么在ActvitySvr上,要么在GameSvr上!
            LOGGER.error("activityType[{}] is invalid! isActivitySvrRun[{}] isGameSvrRun[{}]",
                    obj.activityType, obj.isActivitySvrRun, obj.isGameSvrRun);
            return false;
        }

        if (!isTypeExistsEnum(obj.activityType)) {
            // 活动类型未在 ActivityType枚举 中定义
            LOGGER.error("activityType[{}] is not defined in ActivityType enum!", obj.activityType);
            return false;
        }

        if (isTypeExists(obj.activityType)) {
            // 配置已经存在,检查是否重复添加
            LOGGER.error("activityType[{}] already exists!", obj.activityType);
            return false;
        }

        return true;
    }

    // 活动配置是否已存在
    private static boolean isTypeExists(int type) {
        return allActivityObj.containsKey(type);
    }

    // 检查活动类型是否存在于 ActivityType 中
    private static boolean isTypeExistsEnum(int type) {
        for (ActivityType activityType : ActivityType.values()) {
            if (activityType.getNumber() == type) {
                return true;
            }
        }

        LOGGER.error("type[{}] not in ActivityType!", type);
        return false;
    }

    // 添加活动配置管理对象
    private static void addObj(ActivityConfigObject obj) {
        if (!check(obj)) {
            LOGGER.error("activityType[{}] is invalid!", obj.activityType);
            return;
        }

        allActivityObj.put(obj.activityType, obj);
        LOGGER.info("addObj ok! activityType[{}]", obj.activityType);
    }

    // 添加ActivitySvr运行的活动配置
    private static void addActivitySvrObj(ActivityConfigObject obj) {
        obj.isGameSvrRun = false;
        obj.isActivitySvrRun = true;
        addObj(obj);
    }

    // 添加GameSvr运行的活动配置
    private static void addGameSvrObj(ActivityConfigObject obj) {
        obj.isGameSvrRun = true;
        obj.isActivitySvrRun = false;
        addObj(obj);
    }

    // 获取活动管理对象
    public static ActivityConfigObject getActivityConfigObject(ActivityType activityType) {
        ActivityConfigObject obj = allActivityObj.get(activityType.getNumber());
        if (null == obj) {
            LOGGER.error("obj[{}] is null!", activityType);
            return null;
        }

        return obj;
    }

    // 活动是否运行在活动服务上
    public static boolean isActivitySvrRun(ActivityType activityType) {
        ActivityConfigObject obj = allActivityObj.get(activityType.getNumber());
        if (null == obj) {
            // LOGGER.error("obj[{}] is null!", activityType);
            return false;
        }

        return obj.isActivitySvrRun;
    }

    /*
     * 活动是否运行在GameSvr服务上
     * */
    public static boolean isGameSvrRun(ActivityType activityType) {
        ActivityConfigObject obj = allActivityObj.get(activityType.getNumber());
        if (null == obj) {
            LOGGER.error("obj[{}] is null!", activityType);
            return false;
        }

        return obj.isGameSvrRun;
    }

    // 活动是否全局止可生效一个? true 全局唯一, false 非全局唯一
    public static boolean isGloballyUnique(ActivityType activityType) {
        ActivityConfigObject obj = allActivityObj.get(activityType.getNumber());
        if (null == obj) {
            LOGGER.error("obj[{}] is null!", activityType);
            return false;
        }

        return obj.isGloballyUnique;
    }

    /*
     * DS是否关注活动数据
     * return true:是 false:否
     * */
    public static boolean isDsAttention(ActivityType activityType) {
        ActivityConfigObject obj = allActivityObj.get(activityType.getNumber());
        if (null == obj) {
            LOGGER.error("obj[{}] is null!", activityType);
            return false;
        }

        return obj.isDSAttention;
    }

    /**
     * 检测任务是否能被快速使用
     *
     * @param rewardInfoList
     * @return
     */
    public static boolean checkTaskQuitFinishConfig(List<ReceiveInfo> rewardInfoList) {
        ResActivityFunPara.ActivityMianGanConfig mianganConfig = ActivityMianGanData.getInstance().getConfigByTime(DateUtils.currentTimeMillis());
        if (mianganConfig == null) {
            LOGGER.error("quit finish task  error: time range");
            return false;
        }

        Timestamp begin = mianganConfig.getBeginTime();
        Timestamp end = mianganConfig.getEndTime();
        long nowSec = Framework.currentTimeSec();
        if (nowSec < begin.getSeconds() || nowSec > end.getSeconds()) {
            LOGGER.error("quit finish task  error: time range");
            return false;
        }
        for (ReceiveInfo receiveInfo : rewardInfoList) {
            ResTask.TaskConf taskConf = TaskConfData.getInstance().getTaskConf(receiveInfo.getIndex());
            if (taskConf == null) {
                LOGGER.error("quit finish task error: task conf is null {}", receiveInfo.getIndex());
                return false;
            }
            if (!taskConf.getCanUseItemFinish()) {
                LOGGER.error("quit finish task error: task conf is not quit finish {}", receiveInfo.getIndex());
                return false;
            }
            if (taskConf.getFinishNeedItemCount() <= 0) {
                LOGGER.error("quit finish task error:task conf is use item is zero{}", receiveInfo.getIndex());
                return false;
            }
        }
        return true;
    }

    /*
     * 根据活动ID来判断活动是否运行在ActivitySvr
     */
    public static boolean isActivitySvrRunById(int activityId) {
        var actCfg = ActivityMainConfig.getInstance().get(activityId);
        if (null == actCfg) {
            // 配置未找到,无法确定,返回false
            return false;
        }

        ActivityType activityType = actCfg.getActivityType();
        return ActivityConfigUtil.isActivitySvrRun(activityType);
    }

    public static boolean isSelfControlActivityType(ActivityType activityType) {
        return activityType == ActivityType.ATReturningTask
                || activityType == ActivityType.ATReturningDailyGroupTask
                || activityType == ActivityType.ATArenaSevenDaysLogin
                || activityType == ActivityType.ATFarmReturn
                || activityType == ActivityType.ATFarmBuffWish
                || activityType == ActivityType.ATFarmSevenDayTask
                || activityType == ActivityType.ATCOCEightDays
                || activityType == ActivityType.ATLevelUpChallenge
                ;
    }
}
