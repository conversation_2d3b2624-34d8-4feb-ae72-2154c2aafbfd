package com.tencent.coJdbc.internal;



import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.DriverPropertyInfo;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.Properties;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class FiberDriver implements Driver {

    @Override
    public FiberConnection connect(final String url, final Properties info) throws SQLException {
        final String dbURL = url.replaceFirst("fiber:", "");
        final int threadCount = Integer.parseInt(info.getProperty(THREADS_COUNT, "10"));
        info.remove(THREADS_COUNT);
        final ExecutorService es = Executors.newFixedThreadPool(threadCount, new ThreadFactoryBuilder().setNameFormat("jdbc-worker-%d").setDaemon(true).build());
        final Connection con = JDBCFiberAsync.exec(es, new Callable<Connection>() {
            @Override
            public Connection call() throws SQLException {
                return DriverManager.getConnection(dbURL, info);
            }
        });
        return new FiberConnection(con, MoreExecutors.listeningDecorator(es));
    }
    private static final String THREADS_COUNT = "threadsCount";
    private static final String RAW_DATA_SOURCE_URL = "rawDataSourceURL";

    @Override
    public boolean acceptsURL(String url) throws SQLException {
        return url.startsWith("jdbc:fiber:");
    }

    @Override
    public DriverPropertyInfo[] getPropertyInfo(String url, Properties info) throws SQLException {
        String dbURL = info.getProperty(RAW_DATA_SOURCE_URL);
        if (dbURL == null)
            throw new SQLException("no rawDataSourceURL parameter");
        info.remove(RAW_DATA_SOURCE_URL);
        return DriverManager.getDriver(url).getPropertyInfo(url, info);
    }

    @Override
    public int getMajorVersion() {
        return 2;
    }

    @Override
    public int getMinorVersion() {
        return 0;
    }

    @Override
    public boolean jdbcCompliant() {
        return false;
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        throw new SQLFeatureNotSupportedException("Not supported yet.");
    }
}
