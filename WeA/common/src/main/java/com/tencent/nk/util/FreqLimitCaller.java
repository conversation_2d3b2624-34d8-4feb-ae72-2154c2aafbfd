package com.tencent.nk.util;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.timer.TimerHandle;
import com.tencent.nk.timer.TimerManager;
import com.tencent.nk.timer.TimerType;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.wea.xlsRes.keywords.FreqLimitCallType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2020/12/7
 */
public class FreqLimitCaller {
    private static final Logger LOGGER = LogManager.getLogger(FreqLimitCaller.class);
    private static final Map<TimerType, Boolean> REGISTERED_TIMER_MAP = new ConcurrentHashMap<>();
    private final TimerType timerType;
    private final long intervalMs;
    private final int maxCount;
    private final Runnable runnable;
    private long lastRunTimeMs = 0;
    private long currentCount = 0;
    private TimerHandle runFuture = null;

    private boolean needNextRun = false;
    private boolean isRunning = false;

    private static synchronized void tryRegisterTimer(TimerType timerType) {
        if (REGISTERED_TIMER_MAP.containsKey(timerType)) {
            return;
        }
        TimerManager.registerTimer(timerType, FreqLimitCaller::onTimeout, FreqLimitCaller.class);
        REGISTERED_TIMER_MAP.put(timerType, true);
    }

    //用于静态注册的Timer回调方法，回调之前getWeakRef().get()为空会直接取消，不会回调进来
    private static void onTimeout(FreqLimitCaller obj) {
        //回调前已经判空，可以直接使用对象
        obj.runFuture = null;
        obj.run();
    }

    /**
     * @param timerType  : 使用timer实现限频调用，所以需要注册一个唯一的timerType
     * @param intervalMs : 一段时间内
     * @param maxCount   ：   允许执行的次数
     * @param runnable   : 可重复调用
     */
    public FreqLimitCaller(TimerType timerType, long intervalMs, int maxCount, Runnable runnable) {
        this.timerType = timerType;
        this.intervalMs = intervalMs;
        this.maxCount = maxCount;
        this.runnable = runnable;
        tryRegisterTimer(timerType);
    }

    public void run() {
        if (runFuture != null) {
            return;
        }
        long timenow = Framework.currentTimeMillis();
        if (checkRunImmediate(timenow)) {
            incRunCount(timenow);
            runImmediate();
            return;
        }

        long initDelay = intervalMs + lastRunTimeMs - timenow;

        runFuture = CurrentExecutorUtil.addTimerWithExpireMs(timerType, initDelay, true, this);
    }

    public void asyncRun() {
        if (runFuture != null) {
            return;
        }

        long timeNow = Framework.currentTimeMillis();
        if (checkRunImmediate(timeNow)) {
            if (isRunning) {
                needNextRun = true;
            } else {
                runFuture = CurrentExecutorUtil.addSpecialTimer(timerType, 1, true,
                        () -> onTimeout(this));
            }
        } else {
            long initDelay = intervalMs + lastRunTimeMs - timeNow;
            runFuture = CurrentExecutorUtil.addSpecialTimer(timerType, initDelay, true,
                    () -> onTimeout(this));
        }
    }

    public void runImmediate() {
        if (runFuture != null) {
            runFuture.cancel();
            runFuture = null;
        }
        //正在运行中，排队
        if (isRunning) {
            needNextRun = true;
            return;
        }
        try {
            isRunning = true;
            runnable.run();
        } finally {
            isRunning = false;
            if (needNextRun) {
                needNextRun = false;
                runImmediate();
            }
        }
    }

    public void run(FreqLimitCallType type) {
        switch (type) {
            case FLCT_Delay:
                run();
                return;
            case FLCT_Immediate:
                runImmediate();
                return;
            default:
                return;
        }
    }

    public void cancel() {
        if (runFuture != null) {
            runFuture.cancel();
            runFuture = null;
        }
    }

    private boolean checkRunImmediate(long timeNow) {
        if (timeNow - lastRunTimeMs >= intervalMs) {
            return true;
        }
        if (currentCount < maxCount) {
            return true;
        }
        return false;
    }

    private void incRunCount(long timeNow) {
        if (timeNow - lastRunTimeMs >= intervalMs) {
            lastRunTimeMs = timeNow;
            currentCount = 0;
        }
        ++currentCount;
    }

}
