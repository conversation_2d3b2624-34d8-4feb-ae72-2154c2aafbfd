package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.wea.xlsRes.ResUgcMgr;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.resourceloader.ResHolder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Optional;

public class UgcCosMapping extends ResTable<ResUgcMgr.T_UgcCosMapping> {

    private static final Logger LOGGER = LogManager.getLogger(UgcCosMapping.class);

    Map<Long, ResUgcMgr.T_UgcCosMapping> internalDataMap;

    public UgcCosMapping() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResUgcMgr.T_UgcCosMapping.newBuilder());
    }

    public static UgcCosMapping getInstance() {
        return (UgcCosMapping) ResLoader.getResHolder().getResTableInstance("UgcCosMapping");
    }

    public static UgcCosMapping getInLoadingInstance(ResHolder resHolder) {
        return (UgcCosMapping) resHolder.allResMap.get("UgcCosMapping");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public Class getMessageClass() {
        return ResUgcMgr.T_UgcCosMapping.class;
    }

    public ResUgcMgr.T_UgcCosMapping get(Long id)
    {
        return Optional.ofNullable(internalDataMap.get(id))
        .orElse(null);
    }
}
