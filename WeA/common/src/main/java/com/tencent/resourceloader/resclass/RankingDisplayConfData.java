package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.wea.xlsRes.ResRanking;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RankingDisplayConfData extends ResTable<ResRanking.RankingDisplayConf> {

    private static final Logger LOGGER = LogManager.getLogger(RankingDisplayConfData.class);

    Map<Integer, ResRanking.RankingDisplayConf> internalDataMap;
    Map<Integer, ResRanking.RankingDisplayConf> rankIdMap;

    public RankingDisplayConfData() {
        internalDataMap = dataMap;
        rankIdMap = new HashMap<>();
        generateKeyMetaData(ResRanking.RankingDisplayConf.newBuilder());
    }

    public static RankingDisplayConfData getInstance() {
        return (RankingDisplayConfData) ResLoader.getResHolder().getResTableInstance("RankingDisplayConfData");
    }

    public static RankingDisplayConfData getInLoadingInstance(ResHolder resHolder) {
        return (RankingDisplayConfData) resHolder.allResMap.get("RankingDisplayConfData");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        rankIdMap.clear();
        for (Map.Entry<Integer, ResRanking.RankingDisplayConf> entry : internalDataMap.entrySet()) {
            ResRanking.RankingDisplayConf displayConf = entry.getValue();
            displayConf.getRankIdsList().forEach(rankId -> rankIdMap.put(rankId, displayConf));
        }
    }

    @Override
    public Class getMessageClass() {
        return ResRanking.RankingDisplayConf.class;
    }

    public ResRanking.RankingDisplayConf get(Integer labelId) {
        return Optional.ofNullable(internalDataMap.get(labelId))
                .orElse(null);
    }

    public ResRanking.RankingDisplayConf getByRankId(Integer rankId) {
        return Optional.ofNullable(rankIdMap.get(rankId))
                .orElse(null);
    }

    public int getDelayDaysByRankId(Integer rankId) {
        ResRanking.RankingDisplayConf conf = rankIdMap.get(rankId);
        if (conf != null) {
            return conf.getDelayDays();
        }
        return 0;
    }
}

