
package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.wea.xlsRes.ResFarmBuff;
import java.util.HashMap;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class FarmBuffConf extends ResTable<ResFarmBuff.FarmBuffConf> {

    private static final Logger LOGGER = LogManager.getLogger(FarmBuffConf.class);

    Map<Integer, ResFarmBuff.FarmBuffConf> internalDataMap;

    Map<Integer, Map<Integer, ResFarmBuff.FarmBuff>> confMap;

    public FarmBuffConf() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResFarmBuff.FarmBuffConf.newBuilder());
    }

    public static FarmBuffConf getInstance() {
        return (FarmBuffConf) ResLoader.getResHolder().getResTableInstance("FarmBuffConf");
    }

    public static FarmBuffConf getInLoadingInstance(ResHolder resHolder) {
        return (FarmBuffConf) resHolder.allResMap.get("FarmBuffConf");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        confMap = new HashMap<>();
        for (var conf : internalDataMap.values()) {
            var buffMap = confMap.computeIfAbsent(conf.getId(), k-> new HashMap<>());
            for(var buff : conf.getFarmBuffsList()) {
                buffMap.put(buff.getEffectId(), buff);
                LOGGER.debug("load farmBuff, buffId:{}, buffEffectId:{}, buff:{}", conf.getId(), buff.getEffectId(), buff);
            }
        }
    }

    @Override
    public Class getMessageClass() {
        return ResFarmBuff.FarmBuffConf.class;
    }

    public static ResFarmBuff.FarmBuff getBuff(Integer buffId, ResFarmBuff.FarmBuffEffectId buffEffectId)
    {
        return getInstance().getBuff_(buffId, buffEffectId);
    }

    public ResFarmBuff.FarmBuff getBuff_(Integer buffId, ResFarmBuff.FarmBuffEffectId buffEffectId)
    {
        LOGGER.debug("try get farmBuff, buffId:{}, buffEffectId:{}", buffId, buffEffectId);
        var buffMap = confMap.get(buffId);
        if(buffMap == null){
            LOGGER.error("null buffId {}", buffId);
            return null;
        }
        var buff = buffMap.get(buffEffectId);
        if(buff == null){
            LOGGER.error("null buffEffectId {}", buffEffectId);
            return null;
        }
        LOGGER.debug("get farmBuff, buffId:{}, buffEffectId:{}, buff:{}", buffId, buffEffectId, buff);
        return buff;
    }

    public static Map<Integer, ResFarmBuff.FarmBuff> get(Integer buffId)
    {
        return getInstance().get_(buffId);
    }

    public Map<Integer, ResFarmBuff.FarmBuff> get_(Integer buffId)
    {
        LOGGER.debug("try get farmBuff, buffId:{}", buffId);
        var buffMap = confMap.get(buffId);
        if(buffMap == null){
            LOGGER.error("null buffId {}", buffId);
            return null;
        }
        return buffMap;
    }

    public enum Source {
        Unknown(0),
        BuildingSkin(1), // 建筑皮肤
        RainBow(2), // 七彩石
        FishHandbook(3), // 钓鱼图鉴
        GM(4), // GM
        MonthCard(5), // 月卡
        PlayerReturnActivity(6), // 玩家回流活动
        Collection(7), // 宝典
        Talent(8), // 天赋
        HotSpring(9), // 温泉
        PlayerFarmReturnActivity(10), // 玩家农场回流活动
        BuffAddItem(11), // BuffAddItem
        PlayerFarmSquadActivity(12), // 玩家农场小队活动
        Kirin(13), // 仙麒麟
        Pet(14), // 宠物
        ;

        private final int value;

        Source(int i) {
            value = i;
        }
    }
}

