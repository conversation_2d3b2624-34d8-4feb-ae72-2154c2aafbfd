
package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.wea.xlsRes.ResWhiteList;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.resourceloader.ResHolder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Optional;

public class StreamOpenIdWhitelistData extends ResTable<ResWhiteList.StreamOpenIdWhitelist> {
    
    private static final Logger LOGGER = LogManager.getLogger(StreamOpenIdWhitelistData.class);
    
    Map<String, ResWhiteList.StreamOpenIdWhitelist> internalDataMap;
   
    public StreamOpenIdWhitelistData() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResWhiteList.StreamOpenIdWhitelist.newBuilder());
    }
    
    public static StreamOpenIdWhitelistData getInstance() {
        return (StreamOpenIdWhitelistData) ResLoader.getResHolder().getResTableInstance("StreamOpenIdWhitelistData");
    }
    
    public static StreamOpenIdWhitelistData getInLoadingInstance(ResHolder resHolder) {
        return (StreamOpenIdWhitelistData) resHolder.allResMap.get("StreamOpenIdWhitelistData");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public Class getMessageClass() {
        return ResWhiteList.StreamOpenIdWhitelist.class;
    }
    
    public ResWhiteList.StreamOpenIdWhitelist get(String openId)
    {
        return Optional.ofNullable(internalDataMap.get(openId))
        .orElse(null);
    }
}

