package com.tencent.cos;

import com.qcloud.cos.model.CopyObjectRequest;
import com.qcloud.cos.model.CopyObjectResult;
import com.qcloud.cos.model.ObjectMetadata;
import com.tencent.cache.SingleFlight;
import com.tencent.cloud.Response;
import com.tencent.cos.CosAbstractApi.CosObjectInfo;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.UgcBucketConfig;
import com.tencent.resourceloader.resclass.UgcCommonConfig;
import com.tencent.resourceloader.resclass.UgcUploadConfig;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.protocol.common.ApplyReason;
import com.tencent.wea.protocol.common.MdType;
import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.protocol.common.UgcKeyInfo;
import com.tencent.wea.protocol.common.UgcMapMetaInfo;
import com.tencent.wea.protocol.common.UgcMdList;
import com.tencent.wea.protocol.common.UgcResType;
import com.tencent.wea.xlsRes.ResUgcMgr.T_UgcUploadConfig;
import com.tencent.wea.xlsRes.keywords.CosOperate;
import com.tencent.wea.xlsRes.keywords.CosOperateScene;
import com.tencent.wea.xlsRes.keywords.FuncType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * willwizhang
 */
public class CosManager {

    public static final String UgcBasePath = "ugc/";
    public static final String PubBasePath = "pub/";
    public static final String UgcCoverBasePath = "mulcover/";
    private static final Logger LOGGER = LogManager.getLogger(CosManager.class);
    private static final int DurationSeconds = 10;
    private static final int LimitSize = 1048576;
    private static final String UpdateKey = "UpdateKey";
    private static List<String> cosList = new ArrayList<>();
    private static List<String> groupCosList = new ArrayList<>();

    private static Map<Integer, String> cosTypeMap = new HashMap<>();
    private static Map<Integer, String> groupCosTypeMap = new HashMap<>();

    public static final String UgcCoverPath = "mulcover/";

    static {
        cosList.add("LevelData.pbin");
        cosList.add("Cover.png");
        cosList.add("EditorData.pbin");

        cosTypeMap.put(MdType.LevelData_VALUE, "LevelData.pbin");
        cosTypeMap.put(MdType.CoverPng_VALUE, "Cover.png");
        cosTypeMap.put(MdType.EditorData_VALUE, "EditorData.pbin");
        cosTypeMap.put(MdType.CoverASTC_VALUE, "Cover.astc");
        cosTypeMap.put(MdType.LayerData_VALUE, "LayerData.pbin");
        cosTypeMap.put(MdType.CoverASTCLarge_VALUE, "CoverLarge.astc");
        cosTypeMap.put(MdType.LayerDataByZip_VALUE, "LayerData.dat");
        cosTypeMap.put(MdType.LevelDataByZip_VALUE, "LevelData.dat");
        cosTypeMap.put(MdType.LevelResByZip_VALUE, "LevelRes.dat");
    }

    static {
        groupCosList.add("{}.pbin");
        groupCosList.add("{}.png");

        groupCosTypeMap.put(MdType.GroupData_VALUE, "{}.pbin");
        groupCosTypeMap.put(MdType.GroupPng_VALUE, "{}.png");
        groupCosTypeMap.put(MdType.ResourceZip_VALUE, "{}.pack");
        groupCosTypeMap.put(MdType.ResourceResZip_VALUE, "{}.rpack");
    }

    //临时过期时间,此处时间要比过期时间小才行
    private CosAbstractApi cosApi = new CosApi();
    private SingleFlight callFlight = new SingleFlight();
    //    private CoLoadingCache<Key, SecretInfo> allSecret = null;
    private Map<String, SecretInfo> allSecret = new ConcurrentHashMap<>();

    public static CosManager getInstance() {
        return InstanceHolder.instance;
    }

    public int init() {
//        int cosCacheCap = PropertyFileReader.getIntItem("cos_cache_cap", 18000);
//        allSecret = new CoLoadingCache.Builder<Key, SecretInfo>()
//                .setCapacity(cosCacheCap)
//                .setLoader(key -> this.updateSecretInfo(key.reason, key.paramList))
//                .setLockKeyBuilder(new CoLoadingCache.LockKeyBuilder<>("SecretInfo", Objects::toString))
//                .build();

        return 0;
    }

    private CopyObjectResult copyObjectReal(int mapType, long fromCreatorId, long fromUgcId, String fromBucket,
            long toCreatorId, long toUgcId, String toBucket, String filePath, boolean publish, UgcResType resType) {
        String sourceKey = GetCosPath(UgcInstanceType.forNumber(mapType), fromCreatorId, fromUgcId, filePath, publish,
                resType);
        String destinationKey = GetCosPath(UgcInstanceType.forNumber(mapType), toCreatorId, toUgcId, filePath, publish,
                resType);
        if (sourceKey == null || sourceKey.isEmpty() || destinationKey == null || destinationKey.isEmpty()) {
            return null;
        }
        boolean existObject = isExistObject(sourceKey, fromBucket);
        if (existObject) {
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(fromBucket, sourceKey,
                    toBucket, destinationKey);
            LOGGER.info("copy cos ugc id:{} success, {}:{} -> {}:{} publish:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, destinationKey, publish);
            return this.cosApi.copyObject(copyObjectRequest);
        } else {
            LOGGER.info("copy cos not exist ugc id:{}, {}:{} -> {}:{} publish:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, destinationKey, publish);
        }
        return null;
    }

    public CopyObjectResult copyObjectCover(long fromUgcId, String fromBucket,long toUgcId, String toBucket,String coverName ,String filePath,long creatorId,boolean isPublish) {
        String destinationKey = "";
        String sourceKey = UgcCoverPath + coverName;
        if (isPublish) {
            destinationKey = PubBasePath + toUgcId + "/" + filePath;
        }else {
            destinationKey = UgcBasePath + creatorId + "/"  + toUgcId + "/" + filePath;
        }
        boolean existObject = isExistObject(sourceKey, fromBucket);
        if (existObject) {
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(fromBucket, sourceKey,
                    toBucket, destinationKey);
            LOGGER.info("copy cos ugc id:{} success, {}:{} -> {}:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, destinationKey);
            return this.cosApi.copyObject(copyObjectRequest);
        } else {
            LOGGER.info("copy cos not exist ugc id:{}, {}:{} -> {}:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, destinationKey);
        }
        return null;
    }

    public CopyObjectResult copyObjectCover(long fromUgcId, String fromBucket, String toBucket,String filePath) {
        String sourceKey = UgcCoverBasePath + filePath;;
        boolean existObject = isExistObject(sourceKey, fromBucket);
        if (existObject) {
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(fromBucket, sourceKey,
                    toBucket, sourceKey);
            LOGGER.info("copy cos ugc cover id:{} success, {}:{} -> {}:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, sourceKey);
            return this.cosApi.copyObject(copyObjectRequest);
        } else {
            LOGGER.info("copy cos not exist cover ugc id:{}, {}:{} -> {}:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, sourceKey);
        }
        return null;
    }

    public CopyObjectResult copyObjectMdList(long fromUgcId, String fromBucket,long fromCreatorId,long toUgcId, String toBucket,long toCreatorId ,String filePath) {
        String sourceKey = UgcBasePath  + fromCreatorId + "/" + fromUgcId + "/"+ filePath;;
        String destinationKey = UgcBasePath + toCreatorId + "/" + toUgcId + "/" + filePath;
        boolean existObject = isExistObject(sourceKey, fromBucket);
        if (existObject) {
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(fromBucket, sourceKey,
                    toBucket, destinationKey);
            LOGGER.info("copy cos ugc id:{} success, {}:{} -> {}:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, destinationKey);
            return this.cosApi.copyObject(copyObjectRequest);
        } else {
            LOGGER.info("copy cos not exist ugc id:{}, {}:{} -> {}:{}",
                    fromUgcId, fromBucket, sourceKey, toBucket, destinationKey);
        }
        return null;
    }

    public NKErrorCode copyObjectNew(int mapType, long fromCreatorId, long fromUgcId, String fromBucket,
            long toCreatorId, long toUgcId, String toBucket, boolean isPublish, UgcMdList.Builder ugcMdListBuilder,
            UgcResType resType) {
        if (toBucket.isEmpty()) {
            NKErrorCode.InvalidParams.throwError("copy cos dest bucket:{} is invalid, ugc id:{}",
                    toBucket, fromUgcId);
            return NKErrorCode.InvalidParams;
        }
        Map<Integer, String> cosRealList = GetCosList(mapType);
        if (cosRealList == null || cosRealList.isEmpty()) {
            return NKErrorCode.InvalidParams;
        }
        // 进行数据拷贝
        if (isPublish && fromUgcId == toUgcId && fromBucket.equals(toBucket)) {
            // 获取数据并更新
            List<UgcMapMetaInfo> ugcMdList = new ArrayList<>();
            for (UgcMapMetaInfo info : ugcMdListBuilder.getInfoList()) {
                UgcMapMetaInfo.Builder ugcMapMetaInfoBuilder = info.toBuilder();
                String filePath = cosRealList.get(info.getMsgType());
                if (filePath != null) {
                    if (info.getMsgType() == MdType.LayerData_VALUE
                            || info.getMsgType() == MdType.LayerDataByZip_VALUE) {
                        filePath = info.getLayerId() + "/" + filePath;
                    }
                    filePath = GetCosPath(UgcInstanceType.forNumber(mapType), fromCreatorId, fromUgcId, filePath,
                            isPublish, resType);
                    ObjectMetadata objectMetadata = getObjectMetadata(filePath, fromBucket);
                    if (objectMetadata != null) {
                        ugcMapMetaInfoBuilder.setVersion(objectMetadata.getVersionId());
                    }
                }
                ugcMdList.add(ugcMapMetaInfoBuilder.build());
            }
            ugcMdListBuilder.clearInfo().addAllInfo(ugcMdList);
            return NKErrorCode.OK;
        }
        // 进行cos拷贝
        List<UgcMapMetaInfo> ugcMdList = new ArrayList<>();
        for (UgcMapMetaInfo info : ugcMdListBuilder.getInfoList()) {
            UgcMapMetaInfo.Builder ugcMapMetaInfoBuilder = info.toBuilder();
            String filePath = cosRealList.get(info.getMsgType());
            if (filePath != null) {
                if (info.getMsgType() == MdType.LayerData_VALUE || info.getMsgType() == MdType.LayerDataByZip_VALUE) {
                    filePath = info.getLayerId() + "/" + filePath;
                }
                CopyObjectResult objectMetadata = copyObjectReal(mapType, fromCreatorId, fromUgcId, fromBucket,
                        toCreatorId, toUgcId, toBucket, filePath, isPublish, resType);
                if (objectMetadata != null) {
                    ugcMapMetaInfoBuilder.setVersion(objectMetadata.getVersionId());
                }
            }
            ugcMdList.add(ugcMapMetaInfoBuilder.build());
        }
        ugcMdListBuilder.clearInfo().addAllInfo(ugcMdList);
        return NKErrorCode.OK;
    }

    private String getCosKey(int reason) {
        return UpdateKey + "_" + reason;
    }

    public SecretInfo getSecretInfo(int reason, ApplyKeyParam... paramList) {

        String[] tagParams = new String[]{String.valueOf(reason)};
        Monitor.getInstance().add.total(MonitorId.attr_ugc_apply_key_info, 1, tagParams);
        SecretInfo secretInfo = globalCache(reason, paramList);
        if (secretInfo != null) {
            return secretInfo;
        }

        return this.updateSecretInfo(reason, paramList);
    }

    private void checkParam(ApplyKeyParam... paramList) {
        // 这个范围的参数要符合全部桶，内部设定，外部传入无效进行报错处理
        List<String> allBucket = UgcBucketConfig.getInstance().getAllBucket();
        Set<String> filter = new HashSet<>();
        for (ApplyKeyParam applyKeyParam : paramList) {
            filter.add(applyKeyParam.bucket);
        }

        for (String v : allBucket) {
            if (filter.add(v)) {
                NKErrorCode.UgcApplyKeyBucketError.throwError("global cache bucket error");
            }
        }
    }

    private SecretInfo globalCache(int reason, ApplyKeyParam... paramList) {
        if (reason == ApplyReason.AR_StaticGet_VALUE || reason == ApplyReason.AR_ServerStaticPut_VALUE) {
            // global cache must set all bucket.
            checkParam(paramList);
            List<T_UgcUploadConfig> sceneData = UgcUploadConfig.getInstance().getReasonData(reason);
            if (sceneData == null || sceneData.isEmpty()) {
                LOGGER.error("getSecretInfo error scene={}", reason);
                return new SecretInfo(NKErrorCode.UgcApplyKeySceneFailed);
            }

            long cacheDuration = 0;
            long duration = 0;
            for (T_UgcUploadConfig data : sceneData) {
                cacheDuration = data.getCacheDuration();
                duration = data.getDuration() - cacheDuration;
                break;
            }

            long now = Framework.currentTimeSec();
            String keys = getCosKey(reason);
            SecretInfo info = allSecret.computeIfAbsent(keys, k -> new SecretInfo(NKErrorCode.UgcApplySecretInit));
            long res = info.endTime - now;
            String[] tagParams = new String[]{String.valueOf(reason)};

            try {
                if (res < 0) {
                    info = callFlight.doCall(keys, () -> updateSecretInfo(reason, paramList));
                } else if (res < duration) {
                    callFlight.tryCallOnce(keys, () -> updateSecretInfo(reason, paramList));
                }

                return info;
            } catch (Throwable e) {
                LOGGER.error("getReadSecretInfo tryCallOnce error ", e);
                Monitor.getInstance().add.fail(MonitorId.attr_ugc_apply_key_info, 1, tagParams);
                NKErrorCode.UgcApplyKeyError.throwError("apply key error.please check");
            }
        }
        return null;
    }

    public ApplyKeyParam[] getApplyKeyParamByReason100() {
        List<String> allBucket = UgcBucketConfig.getInstance().getAllBucket();

        ApplyKeyParam[] applyKeyParams = allBucket.stream().map(k -> new ApplyKeyParam(k))
                .toArray(ApplyKeyParam[]::new);

        return applyKeyParams;
    }

    /**
     * 注意此接口的数据不能给到客户端
     *
     * @return
     */
    public NKPair<String, String> getServerSecretImportInfo() {
        return this.cosApi.getServerSecretImportInfo();
    }

    public NKPair<NKErrorCode, UgcKeyInfo.Builder> getUgcKeyInfo(int reason, ApplyKeyParam... paramList) {
        UgcKeyInfo.Builder info = UgcKeyInfo.newBuilder();

        if (reason == ApplyReason.AR_StaticGet_VALUE) {
            paramList = getApplyKeyParamByReason100();
        }

        SecretInfo secretInfo = getSecretInfo(reason, paramList);
        info.setKey(secretInfo.secretKey);
        info.setId(secretInfo.secretId);
        info.setToken(secretInfo.token);
        info.setStartTime(secretInfo.startTime);
        info.setEndTime(secretInfo.endTime);
        return new NKPair<>(secretInfo.errorCode, info);
    }

    private SecretInfo updateSecretInfo(int reason, ApplyKeyParam... paramList) {
        List<T_UgcUploadConfig> sceneData = UgcUploadConfig.getInstance().getReasonData(reason);
        if (sceneData == null || sceneData.isEmpty()) {
            LOGGER.error("getSecretInfo error reason={} paramList={}", reason, paramList);
            return new SecretInfo(NKErrorCode.UgcApplyKeySceneFailed);
        }

        int duration = DurationSeconds;
        long expireGap = 0;

        List<CosObjectInfo> list = new ArrayList<>();
        for (ApplyKeyParam param : paramList) {
            for (T_UgcUploadConfig data : sceneData) {
                // 最小为10s
                if (data.getDuration() > duration) {
                    duration = data.getDuration();
                }

                if (data.getCacheDuration() > 0) {
                    expireGap = data.getDuration() - data.getCacheDuration();
                }

                String path = "";
                if (data.getOperate() == CosOperate.Co_Upload) {
                    path = seqByFuncType(data.getFuncType(), data.getFilePath(), param);
                } else if (data.getOperate() == CosOperate.Co_Download) {
                    path = seqByFuncType(data.getFuncType(), data.getFilePath(), param);
                }

                if (!path.isEmpty()) {
                    CosObjectInfo cosObjectInfo = new CosObjectInfo();
                    cosObjectInfo.sizeLimit = data.getSizeLimit();
                    cosObjectInfo.bucket = param.bucket;
                    cosObjectInfo.operate = data.getOperate();
                    cosObjectInfo.path = path;
                    list.add(cosObjectInfo);
                } else {
                    LOGGER.warn("updateSecretInfo path empty reason={} path={}", reason, path);
                }
            }
        }

        if (list.isEmpty()) {
            LOGGER.error("getSecretInfo error putMap isEmpty() && getMap isEmpty() reason={} paramList={}", reason,
                    paramList);
            return new SecretInfo(NKErrorCode.UgcApplyKeyCfgError);
        }

        NKPair<NKErrorCode, Response> secRes = this.cosApi.applyBucketsSecret(list, duration);
        if (secRes.getKey() != NKErrorCode.OK) {
            return new SecretInfo(secRes.getKey());
        }

        Response response = secRes.getValue();
        if (response == null) {
            return new SecretInfo(NKErrorCode.UgcCosKeyException);
        }

        SecretInfo secretInfo = new SecretInfo(NKErrorCode.OK, response.credentials.tmpSecretId,
                response.credentials.tmpSecretKey,
                response.credentials.sessionToken, response.startTime, response.expiredTime, expireGap);

        if (reason == ApplyReason.AR_StaticGet_VALUE || reason == ApplyReason.AR_ServerStaticPut_VALUE) {
            allSecret.put(getCosKey(reason), secretInfo);
        }

        String[] tagParams = new String[]{String.valueOf(reason)};
        Monitor.getInstance().add.succ(MonitorId.attr_ugc_apply_key_info, 1, tagParams);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("applyBucketsSecret info {}", secretInfo);
        }

        return secretInfo;
    }

    private String seqByFuncType(FuncType ft, String path, ApplyKeyParam param) {
        if (ft == FuncType.Init) {
            return path;
        } else if (ft == FuncType.Uid_Func) {
            return String.format(path, param.getUid());
        } else if (ft == FuncType.UgcId_Func) {
            return String.format(path, param.getUgcId());
        } else if (ft == FuncType.Uid_UgcIdFunc) {
            return String.format(path, param.getUid(), param.getUgcId());
        } else if (ft == FuncType.Uid_DestIdFunc) {
            return String.format(path, param.getUid(), param.getPubId());
        } else if (ft == FuncType.DestIdFunc) {
            return String.format(path, param.getPubId());
        } else if (ft == FuncType.Uid_DestIdDestIdFunc) {
            return String.format(path, param.getUid(), param.getPubId(), param.getPubId());
        } else if (ft == FuncType.CreatorId_Func) {
            return String.format(path, param.getCreatorId());
        } else if (ft == FuncType.Env_World_CreatorId_UgcIdFunc) {
            return String.format(path, param.envFlag, param.worldId, param.getCreatorId(), param.getUgcId());
        } else if (ft == FuncType.Env_WorldFunc) {
            return String.format(path, param.envFlag, param.worldId);
        } else if (ft == FuncType.ResType_DestIdFunc) {
            return String.format(path, param.ugcResType, param.pubId);
        } else if (ft == FuncType.UgcId_DestIdFunc) {
            return String.format(path, param.getUgcId(), param.getPubId());
        } else if (ft == FuncType.CreatorId_DestIdFunc) {
            return String.format(path, param.getCreatorId(), param.getPubId());
        } else if (ft == FuncType.ResType_UgcId_UgcIdFunc) {
            return String.format(path, param.ugcResType, param.getUgcId(),param.getUgcId());
        }

        return "";
    }

    public ObjectMetadata getObjectMetadata(String key, String bucket) {
        return this.cosApi.getObjectMetadata(key, bucket);
    }

    public byte[] downloadRes(String bucketName, String key) {
        return this.cosApi.downloadObject(bucketName, key);
    }

    public String getRegion(String bucketName) {
        return this.cosApi.getRegion(bucketName);
    }

    public String getRandomHashBucket(long uid) {

        List<String> allBucket = UgcBucketConfig.getInstance().getAllBucket();
        int pos = (int) (uid % allBucket.size());
        Monitor.getInstance().set.total(MonitorId.attr_ugc_Apply_Bucket, pos);

        return allBucket.get(pos);
    }

    public String getRandomHashCommonBucket(long uid) {

        //will 测试了下，这样会比较均衡
        String[] allBucket = this.cosApi.getAllCommonBucket();
        int pos = (int) (uid % allBucket.length);
        Monitor.getInstance().set.total(MonitorId.attr_ugc_Apply_Common_Bucket, pos);
        return allBucket[pos];
    }

    public String getBucketCdn(String bucket) {
        if (bucket == null || bucket.isEmpty()) {
            return "";
        }

        String[] splitList = bucket.split("-");
        if (splitList.length == 0) {
            return "";
        }
        // 先读表格
        String cdn = UgcCommonConfig.getInstance().getCdnHost(splitList[0]);
        // 没有命中读服务器配置
        if (cdn == null || cdn.isEmpty()) {
            cdn = this.cosApi.getServerConfBucketCdn(bucket);
        }
        return cdn;
    }

    public String getCosObjectUrl(String key, String bucket) {
        return this.cosApi.getObjectUrl(key, bucket, Framework.currentTimeMillis() + 1000_000);
    }

    public String getCosObjectUrl(String key, String bucket, long expire) {
        return this.cosApi.getObjectUrl(key, bucket, Framework.currentTimeMillis() + expire);
    }

    public boolean isExistObject(String key, String bucket) {
        return this.cosApi.isExistObject(key, bucket);
    }

    public boolean isExistObjectOnCoroEnv(String key, String bucket) {
        return this.cosApi.isExistObjectOnCoroEnv(key, bucket);
    }

    public CopyObjectResult copyObject(String sourceBucketName, String sourceKey,
            String destinationBucketName, String destinationKey) {
        CopyObjectRequest copyObjectRequest = new CopyObjectRequest(sourceBucketName, sourceKey, destinationBucketName,
                destinationKey);
        CopyObjectResult copyObjectResult = this.cosApi.copyObject(copyObjectRequest);
        return copyObjectResult;
    }

    public List<String> GetCosList(UgcInstanceType mapType) {
        if (mapType == UgcInstanceType.CommonInstance) {
            return cosList;
        } else if (mapType == UgcInstanceType.GroupInstance || mapType == UgcInstanceType.ResInstance) {
            return groupCosList;
        } else {
            return null;
        }
    }

    public Map<Integer, String> GetCosList(int mapType) {
        if (mapType == UgcInstanceType.CommonInstance_VALUE) {
            return cosTypeMap;
        } else if (mapType == UgcInstanceType.GroupInstance_VALUE || mapType == UgcInstanceType.ResInstance_VALUE) {
            return groupCosTypeMap;
        } else {
            return null;
        }
    }

    public String GetCosPath(UgcInstanceType mapType, long uid, long ugcId, String fileName, boolean isPublish,
            UgcResType resType) {
        String cosPath = null;
        if (mapType == UgcInstanceType.CommonInstance) {
            if (isPublish) {
                cosPath = PubBasePath + ugcId + "/" + fileName;
            } else {
                cosPath = UgcBasePath + uid + "/" + ugcId + "/" + fileName;
            }
        } else if (mapType == UgcInstanceType.GroupInstance) {
            String realFileName = NKStringFormater.format(fileName, ugcId);
            if (realFileName != null) {
                if (isPublish) {
                    cosPath = PubBasePath + "GroupData/" + ugcId + "/" + realFileName;
                } else {
                    cosPath = UgcBasePath + uid + "/GroupData/" + ugcId + "/" + realFileName;
                }
            }
        } else if (mapType == UgcInstanceType.ResInstance && resType != UgcResType.EURT_Group) {
            String realFileName = NKStringFormater.format(fileName, ugcId);
            if (realFileName != null) {
                if (isPublish) {
                    cosPath = PubBasePath + "ResourceLibrary/" + resType.getNumber() + "/" + ugcId + "/" + realFileName;
                } else {
                    cosPath = UgcBasePath + uid + "/ResourceLibrary/" + resType.getNumber() + "/" + ugcId + "/"
                            + realFileName;
                }
            }
        }
        return cosPath;
    }

    public CopyObjectResult copyObject(long destUid, long sourceUid, String destBucket, String sourceBucket,
            Long destUgcId, Long sourceUgcId, boolean isPublish, UgcInstanceType mapType)
            throws NKCheckedException {
        CopyObjectResult copyObjectResult = null;
        if (destBucket == null || destBucket.isEmpty() || destBucket.isBlank()) {
            NKErrorCode.InvalidParams.throwError("copy cos dest bucket:{} is invalid, ugc id:{}",
                    destBucket, sourceUgcId);
            return null;
        }
        List<String> cosRealList = GetCosList(mapType);
        if (cosRealList == null || cosRealList.isEmpty()) {
            return null;
        }
        if (isPublish) {
            if (destBucket.equals(sourceBucket) && destUgcId.equals(sourceUgcId)) {
                return null;
            }
            for (String s : cosRealList) {
                String sourceKey = GetCosPath(mapType, sourceUid, sourceUgcId, s, true, UgcResType.EURT_Group);
                String destinationKey = GetCosPath(mapType, destUid, destUgcId, s, true, UgcResType.EURT_Group);
                LOGGER.info("sourceKey:{} destinationKey:{}", sourceKey, destinationKey);
                if (sourceKey == null || sourceKey.isEmpty() || destinationKey == null || destinationKey.isEmpty()) {
                    LOGGER.error("copy cos of ugc id:{} error, sourceKey:{}, destKey:{}",
                            sourceUgcId, sourceKey, destinationKey);
                    continue;
                }
                boolean existObject = isExistObject(sourceKey, sourceBucket);
                if (existObject) {
                    CopyObjectRequest copyObjectRequest = new CopyObjectRequest(sourceBucket, sourceKey,
                            destBucket, destinationKey);
                    copyObjectResult = this.cosApi.copyObject(copyObjectRequest);
                } else {
                    LOGGER.warn("copy cos of published ugc id:{} error, key:{}, bucket:{} not exist",
                            sourceUgcId, sourceKey, sourceBucket);
                }
            }
        } else {
            for (String s : cosRealList) {
                String sourceKey = GetCosPath(mapType, sourceUid, sourceUgcId, s, false, UgcResType.EURT_Group);
                String destinationKey = GetCosPath(mapType, destUid, destUgcId, s, false, UgcResType.EURT_Group);
                LOGGER.info("sourceKey:{} destinationKey:{}", sourceKey, destinationKey);
                if (sourceKey == null || sourceKey.isEmpty() || destinationKey == null || destinationKey.isEmpty()) {
                    continue;
                }
                CopyObjectRequest copyObjectRequest = new CopyObjectRequest(sourceBucket, sourceKey,
                        destBucket, destinationKey);
                copyObjectResult = this.cosApi.copyObject(copyObjectRequest);
            }
        }
        return copyObjectResult;
    }

    private static class InstanceHolder {

        public static CosManager instance = new CosManager();
    }

    public static class Key {

        private long key = 0;
        private int reason = 0;
        private ApplyKeyParam[] paramList;

        public Key(long key, int reason, ApplyKeyParam... param) {
            this.key = key;
            this.reason = reason;
            this.paramList = param;
        }

        @Override
        public int hashCode() {
            return Long.hashCode(this.key);
        }

        @Override
        public boolean equals(Object object) {
            if (object instanceof Key) {
                CosManager.Key other = (CosManager.Key) object;
                return this.key == other.key && this.reason == other.reason;
            }
            return false;
        }
    }

    public static class ApplyKeyParam {

        private String bucket;
        private long uid = 0;
        private long ugcId = 0;
        private long pubId = 0;
        private List<Long> pubList;
        private long creatorId = 0;
        private int worldId = 0;
        private String envFlag = "idc_test";
        private int ugcResType = 0;

        public ApplyKeyParam(String bucket, long uid, long ugcId) {
            this.bucket = bucket;
            this.uid = uid;
            this.ugcId = ugcId;
        }

        public ApplyKeyParam(String bucket, long ugcId) {
            this.bucket = bucket;
            this.ugcId = ugcId;
        }

        public ApplyKeyParam(String bucket) {
            this.bucket = bucket;
        }

        public ApplyKeyParam() {
        }

        public long getCreatorId() {
            return creatorId;
        }

        public void setCreatorId(long creatorId) {
            this.creatorId = creatorId;
        }

        public List<Long> getGetList() {
            return pubList;
        }

        public void setGetList(List<Long> getList) {
            this.pubList = getList;
        }

        public long getPubId() {
            return pubId;
        }

        public void setPubId(long pubId) {
            this.pubId = pubId;
        }

        public String getBucket() {
            return bucket;
        }

        public void setBucket(String bucket) {
            this.bucket = bucket;
        }

        public long getUid() {
            return uid;
        }

        public void setUid(long uid) {
            this.uid = uid;
        }

        public long getUgcId() {
            return ugcId;
        }

        public void setUgcId(long ugcId) {
            this.ugcId = ugcId;
        }

        public String getEnvFlag() {
            return envFlag;
        }

        public void setEnvFlag(String flag) {
            this.envFlag = flag;
        }

        public int getWorldId() {
            return worldId;
        }

        public void setWorldId(int worldId) {
            this.worldId = worldId;
        }

        public int getUgcResType() {
            return ugcResType;
        }

        public void setUgcResType(int ugcResType) {
            this.ugcResType = ugcResType;
        }

        @Override
        public String toString() {
            return "ApplyKeyParam{" +
                    "bucket='" + bucket + '\'' +
                    ", uid=" + uid +
                    ", ugcId=" + ugcId +
                    ", pubId=" + pubId +
                    ", pubList=" + pubList +
                    ", creatorId=" + creatorId +
                    ", worldId=" + worldId +
                    ", envFlag='" + envFlag + '\'' +
                    ", ugcResType=" + ugcResType +
                    '}';
        }
    }

    public static class SecretInfo {

        public String secretId = "";
        public String secretKey = "";
        public String token = "";
        public long startTime = 0;
        public long endTime = 0;
        public long expireGap = 0;

        public NKErrorCode errorCode;

        public String cacheParam = "";

        public SecretInfo(NKErrorCode code, String secretId, String secretKey, String token, long start, long end,
                long expireGap) {
            this.errorCode = code;
            this.secretId = secretId;
            this.secretKey = secretKey;
            this.token = token;
            this.startTime = start;
            this.endTime = end;
            this.expireGap = expireGap;
        }

        public SecretInfo(NKErrorCode code) {
            this.errorCode = code;
        }

        public SecretInfo() {
        }

        @Override
        public String toString() {
            return "SecretInfo{" +
                    "secretId='" + secretId + '\'' +
                    ", secretKey='" + secretKey + '\'' +
                    ", token='" + token + '\'' +
                    ", startTime=" + startTime +
                    ", endTime=" + endTime +
                    ", errorCode=" + errorCode +
                    '}';
        }
    }
}
