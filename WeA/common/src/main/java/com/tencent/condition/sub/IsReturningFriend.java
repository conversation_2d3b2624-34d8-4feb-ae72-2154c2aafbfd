package com.tencent.condition.sub;

import com.tencent.eventcenter.BaseEvent;
import com.tencent.wea.protocol.common.EventDataId;
import com.tencent.wea.xlsRes.keywords.SubConditionType;

import java.util.List;

public class IsReturningFriend extends BaseSubCondition {

    @Override
    public int getType() {
        return SubConditionType.SCT_IsReturningFriend.getNumber();
    }

    @Override
    public boolean isOk(List<Long> valueList, BaseEvent event) {
        return this.checkValueEqual(valueList, event.getIntData(EventDataId.EDT_FriendType.getNumber()));
    }
}
