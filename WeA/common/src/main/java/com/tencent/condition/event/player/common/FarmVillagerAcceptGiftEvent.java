package com.tencent.condition.event.player.common;

import com.tencent.condition.ConditionManager;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.wea.protocol.common.EventTypeId;

public class FarmVillagerAcceptGiftEvent extends BaseConditionEvent {

    /**
     * 只能用单个参数(ConditionManager)的构造函数, 参见: LocalActivityServiceImpl#handlePlayerEventImpl
     */

    public FarmVillagerAcceptGiftEvent(ConditionManager conditionManager) {
        super(conditionManager);
    }

    @Override
    public int getEventType() {
        return EventTypeId.ET_FarmVillagerAcceptGift.getNumber();
    }
}
