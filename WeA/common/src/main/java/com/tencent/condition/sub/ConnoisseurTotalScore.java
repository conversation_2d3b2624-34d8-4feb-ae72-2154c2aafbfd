package com.tencent.condition.sub;

import com.tencent.condition.event.player.common.ClientUiJump;
import com.tencent.condition.event.player.common.ConnoisseurEvent;
import com.tencent.eventcenter.BaseEvent;
import com.tencent.wea.xlsRes.keywords.SubConditionType;
import java.util.List;

public class ConnoisseurTotalScore extends BaseSubCondition {

    public ConnoisseurTotalScore() {

    }

    @Override
    public int getType() {
        return SubConditionType.SCT_ConnoisseurTotalScore.getNumber();
    }

    @Override
    public boolean isOk(List<Long> valueList, BaseEvent event) {
        if (event instanceof ConnoisseurEvent) {
            ConnoisseurEvent specEvt = (ConnoisseurEvent) event;
            return this.checkValueMoreOrEqual(valueList, specEvt.getScore());
        }
        return false;
    }
}
