package com.tencent.condition.event.player.common;

import com.tencent.condition.ConditionManager;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.wea.protocol.SsCommon.FarmObtainFishCardPackEventData;
import com.tencent.wea.protocol.common.EventDataId;
import com.tencent.wea.protocol.common.EventTypeId;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class FarmObtainFishCardPackEvent extends BaseConditionEvent {

    private static final Logger LOGGER = LogManager.getLogger(FarmObtainFishCardPackEvent.class);

    public FarmObtainFishCardPackEvent(ConditionManager conditionManager) {
        super(conditionManager);
    }

    @Override
    public int getEventType() {
        return EventTypeId.ET_PlayerFarmObtainFishCardPack.getNumber();
    }

    public FarmObtainFishCardPackEvent setEventData(FarmObtainFishCardPackEventData data) {
        setProtoData(EventDataId.EDT_FarmEventData.getNumber(), data);
        LOGGER.debug("FarmObtainFishCardPackEvent: {}", () -> data);
        return this;
    }

    public FarmObtainFishCardPackEventData getEventData() {
        return getProtoData(EventDataId.EDT_FarmEventData.getNumber(), FarmObtainFishCardPackEventData.newBuilder());
    }
}
