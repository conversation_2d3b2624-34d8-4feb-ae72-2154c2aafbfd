package com.tencent.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.condition.event.player.common.ChaseIdentityMasteryScoreChangeEvent;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ChaseGetIdentityMasteryScore extends BaseCondition {

    private static final Logger LOGGER = LogManager.getLogger(ChaseGetIdentityMasteryScore.class);

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerChaseGetIdentityMastery_VALUE;
    }

    @SubscribeEvent
    private void onEvent(ChaseIdentityMasteryScoreChangeEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    protected long getAddValue(BaseConditionEvent event, ConditionOperation condition) {
        ChaseIdentityMasteryScoreChangeEvent identityMasteryScoreChangeEvent = (ChaseIdentityMasteryScoreChangeEvent) event;
        return Math.max(identityMasteryScoreChangeEvent.getAddScore(), 0);
    }
}
