package com.tencent.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.condition.event.player.common.FarmCookScoreEvent;
import com.tencent.condition.event.player.common.FarmCropFertilizeEvent;
import com.tencent.condition.event.player.common.FarmPetNumEvent;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.xlsRes.keywords.ConditionType;

public class FarmCookScore extends BaseCondition {

	@Override
	public int getType() {
		return ConditionType.ConditionType_FarmCookScore.getNumber();
	}

	@SubscribeEvent
	private void onEvent(FarmCookScoreEvent event) throws NKRuntimeException {
		handleEvent(event);
	}

	@Override
	protected void handleCondition(BaseConditionEvent event, ConditionOperation condition) {
		var specEvent = (FarmCookScoreEvent) event;
		if (specEvent == null) {
			return;
		}
		var score = specEvent.getScore();
		if (score > condition.getValue()) {
			if (checkSubCondition(specEvent, condition.getSubConditionList()))  {
				condition.setValue(score);
			}
		}
	}
}