package com.tencent.apollo;

import java.util.ArrayList;
import java.util.Arrays;

public class ApolloTopNextResult {

    public static class GetTopSubRankResult {

        public ArrayList<RankEntry> ranks;
    }

    public static class GetSubRanksResult {

        public ArrayList<ApolloTopNextRank> subs;
    }

    public static class GetSubRankImageResult {

        public ArrayList<RankImageStatus> images;
    }

    public static class RankEntry {

        public String openId;
        public int score;
        public int extraScore0;
        public int extraScore1;

        public long timestamp;

        public byte[] userData;
        public int result;

        public int rankNo;
        public int templateId;
        public int instanceId;
        public int totalSize;

        @Override
        public String toString() {
            return String.format("[%d][%d,%d][name=%s, No.%d, score=[%d,%d,%d] ts=%d, userData=%s]", result, templateId,
                    instanceId, openId, rankNo, score, extraScore0, extraScore1, timestamp, Arrays.toString(userData));
        }
    }

    public static class RankImageStatus {

        public int platformId;
        public int zoneId;
        public int result;
        public int templateId;
        public int instanceId;
        public long seqId;
        public int status;
        public int web;
        public long ts;
    }
}
