package com.tencent.tcaplus.dao;

import com.tencent.tcaplus.TcaplusErrorCode.TcaplusErrorException;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusManager.TcaplusRecordData;
import com.tencent.tcaplus.TcaplusManager.TcaplusRecordGroup;
import com.tencent.tcaplus.TcaplusManager.TcaplusRsp;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcCollect;
import java.util.Collection;
import java.util.HashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @program: letsgo_server
 * @description: ugc地图收藏db接口
 * @author: digoldzhang
 * @create: 2023-07-14 20:19
 **/

public class UgcCollectDao {

    private static final Logger LOGGER = LogManager.getLogger(UgcCollectDao.class);

    public static HashMap<Long, PlayerUgcCollect> getCollectData(long creatorId, Collection<Long> ugcIds,
            String... fields) throws TcaplusErrorException {
        HashMap<Long, PlayerUgcCollect> collectMap = new HashMap<>();
        if (ugcIds.isEmpty()) {
            return collectMap;
        }

        TcaplusManager.TcaplusReq reqBuilder = null;
        for (Long ugcId : ugcIds) {
            TcaplusDb.PlayerUgcCollect.Builder builder = TcaplusDb.PlayerUgcCollect.newBuilder().setCreatorId(creatorId)
                    .setUgcId(ugcId);
            if (reqBuilder == null) {
                reqBuilder = TcaplusUtil.newBatchGetReq(builder);
            } else {
                reqBuilder.addRecord(builder);
            }
        }

        TcaplusRsp rsp = TcaplusManager.getInstance().tcaplusSend(reqBuilder.setChangeField(fields));
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("getCollectData err,  ret:{}", rsp.getResult().getValue());
            return collectMap;
        }
        for (TcaplusRecordGroup group : rsp.getRspDataList()) {
            for (TcaplusRecordData data : group.getRecordList()) {
                TcaplusDb.PlayerUgcCollect collectData = (TcaplusDb.PlayerUgcCollect) data.msg;
                collectMap.put(collectData.getUgcId(), collectData);
            }
        }
        return collectMap;
    }

}
