package com.tencent.wea.notice;

import com.tencent.mq.MQClient;
import com.tencent.mq.MQEvent;
import com.tencent.mq.impl.*;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;

import com.tencent.wea.protocol.SsCommon.MQEventBroadcast;
import com.google.protobuf.Message;
import com.google.protobuf.ByteString;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.function.Consumer;

public class NoticeMQClient {
    private static final Logger logger = LogManager.getLogger(NoticeMQClient.class);

    MQClientTcaplus cli = new MQClientTcaplus();
    
    public NoticeMQClient() {

    }

    boolean load() {
        var opts = new MQClientTcaplus.Options()
            .setSwitch(PropertyFileReader.getRealTimeBooleanItem("notice_mq_switch", true))
            .setPollInterval(PropertyFileReader.getRealTimeLongItem("notice_mq_poll_interval", DateUtils.ONE_SECOND_MILLIS))
            .setPollLimit(PropertyFileReader.getRealTimeIntItem("notice_mq_poll_limit", 1024));
        logger.info("notice mq load options:{}", opts);
        return this.cli.load(opts);
    }

    boolean getSwitch() {
        return this.cli.isSwitchOn();
    }

    int proc() {
        return this.cli.proc();
    }

    NKErrorCode subscribe(String topic, long id, Consumer<Event> consumer) {
        return this.cli.subscribe(this.new Event(topic), id, (MQEvent e) -> {
            if (!(e instanceof Event)) {
                logger.error("notice mq got an illegal event:{}", e);
                return;
            }

            consumer.accept((Event)e);
        });
    }

    NKErrorCode publish(String topic, int msgType, Message body, int nice, long src) {
        if (!getSwitch()) {
            return NKErrorCode.MessageQueueSwitchOff;
        }

        if (StringUtils.isBlank(topic) || msgType <= 0) {
            return NKErrorCode.MessageQueueBadParams;
        }

        var event = this.new Event(topic)
            .setMsgType(msgType).setBody(body)
            .setNice(nice).setSrc(src);
        return this.cli.publish(event);
    }

    public class Event implements MQEvent {
        MQEventBroadcast.Builder data = MQEventBroadcast.newBuilder();

        Event(String topic) {
            data.setTopic(topic);
            data.setCreateTime(DateUtils.currentTimeMillis());
        }

        Event setSrc(long src) {
            this.data.setSrc(src);
            return this;
        }

        public int getMsgType() {
            return this.data.getMsgType();
        }

        Event setMsgType(int msgType) {
            this.data.setMsgType(msgType);
            return this;
        }

        public ByteString getBody() {
            return this.data.getBody();
        }
        
        Event setBody(Message body) {
            if (null == body) {
                return this;
            }
            this.data.setBody(body.toByteString());
            return this;
        }

        public int getNice() {
            return this.data.getNice();
        }
        
        Event setNice(int nice) {
            this.data.setNice(nice);
            return this;
        }

        @Override
        public String getKey() {
            return data.getTopic();
        }

        @Override
        public String getTopic() {
            return this.data.getTopic();
        }

        @Override
        public long getCreateTime() {
            return this.data.getCreateTime();
        }

        @Override
        public long getSrc() {
            return this.data.getSrc();
        }

        @Override
        public Event newObject() {
            return new Event(getTopic());
        }

        @Override
        public ByteString marshal() {
            return this.data.build().toByteString();
        }

        @Override
        public boolean unmarshal(ByteString bytes) {
            try {
                this.data.mergeFrom(bytes);
            } catch (Exception e) {
                return false;
            }
            return true;
        }

        @Override
        public String toString() {
            return this.data.toString();
        }
    }
}
