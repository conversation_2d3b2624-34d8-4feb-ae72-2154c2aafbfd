package com.tencent.wea.redis;

import com.google.protobuf.Timestamp;
import com.tencent.cache.Cache;
import com.tencent.cache.Cache.CacheResult;
import com.tencent.cache.CacheScript;
import com.tencent.cache.CacheUtil;
import com.tencent.coLoadingCache.CoLoadingCache;
import com.tencent.coLoadingCache.CoLoadingCache.LockKeyBuilder;
import com.tencent.coRedis.CoRedisCmd;
import com.tencent.localcache.LocalCacheItem;
import com.tencent.nk.util.Tuple;
import com.tencent.nk.util.Tuple.T2;
import com.tencent.resourceloader.resclass.ActivityFunParaConfig;
import com.tencent.resourceloader.resclass.ActivityFunParaConfig.ConfigNotFoundException;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.protocol.AttrAlbumPicInfo.proto_AlbumPicInfo;
import com.tencent.wea.protocol.CsAlbum.PicWallPicInfo;
import com.tencent.wea.xlsRes.ResActivityFunPara.ActivityFunParaType;
import com.tencent.wea.xlsRes.keywords.AlbumPicLabelType;
import io.lettuce.core.KeyValue;
import io.lettuce.core.ScoredValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 图片点赞计数
 **/

public class AlbumPicLikeNumericDao {

    private static final Logger LOGGER = LogManager.getLogger(AlbumPicLikeNumericDao.class);

    /**
     * 照片墙排行榜缓存
     */
    private static final ConcurrentHashMap<AlbumPicLabelType, LocalCacheItem<List<ScoredValue<String>>>> picWallLikeNumRankCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<AlbumPicLabelType, LocalCacheItem<List<ScoredValue<String>>>> picWallAddTimeRankCache = new ConcurrentHashMap<>();

    /**
     * 照片墙照片信息缓存
     */
    public static final CoLoadingCache<String, proto_AlbumPicInfo> wallPicInfoCache = new CoLoadingCache.Builder<String, proto_AlbumPicInfo>()
            .setLoader(AlbumPicLikeNumericDao::loadWallPicInfo)
            .setBatchLoader(AlbumPicLikeNumericDao::loadWallPicInfoBatch)
            .setLockKeyBuilder(new LockKeyBuilder<>("wall_pic_info", k -> k))
            .setCapacity(500)
            .expireAfterWrite(DateUtils.ONE_MINUTE_MILLIS)
            .build();

    /**
     * 照片墙照片点赞缓存
     */
    public static final CoLoadingCache<String, Integer> wallPicLikeNumCache = new CoLoadingCache.Builder<String, Integer>()
            .setLoader(AlbumPicLikeNumericDao::loadWallPicLikeNum)
            .setBatchLoader(AlbumPicLikeNumericDao::loadWallPicLikeNumBatch)
            .setLockKeyBuilder(new LockKeyBuilder<>("wall_pic_like_num", k -> k))
            .setCapacity(500)
            .expireAfterWrite(DateUtils.ONE_MINUTE_MILLIS)
            .build();

    public static Long incr(long picUid, String picKey, long incrNum) {
        if (picUid == 0 || incrNum == 0) {
            return null;
        }
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.AlbumPicLikeNum.getKey(picUid);
        try {
            return coRedisCmd.hincrby(redisKey, picKey, incrNum);
        } catch (Exception e) {
            LOGGER.error("incr redis:{}:{} fail,{}", redisKey, picKey, e.getMessage());
        }
        return null;
    }

    public static long hget(long uid, String picKey) {
        if (uid == 0) {
            return 0;
        }
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.AlbumPicLikeNum.getKey(uid);
        try {
            String valueStr = coRedisCmd.hget(redisKey, picKey);
            return valueStr == null ? 0 : Long.parseLong(valueStr);
        } catch (Exception e) {
            LOGGER.error("get redis:{}:{} fail,{}", redisKey, picKey, e.getMessage());
        }
        return 0;
    }


    public static void hset(long uid, String picKey, long num) {
        if (uid == 0) {
            return;
        }
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.AlbumPicLikeNum.getKey(uid);
        try {
            coRedisCmd.hset(redisKey, String.valueOf(picKey), String.valueOf(num));
        } catch (Exception e) {
            LOGGER.error("set redis:{}:{} fail,{}", redisKey, picKey, e.getMessage());
        }
    }

    public static String getWallPicKey(long uid, String picKey) {
        return uid + "-" + picKey;
    }

    public static T2<Long, String> parseWallPicKey(String wallPicKey) {
        String[] split = wallPicKey.split("-");
        if (split.length != 2) {
            return null;
        }
        return Tuple.New(Long.parseLong(split[0]), split[1]);
    }

    private static int getPicWallRankMaxLimit() {
        return PropertyFileReader.getRealTimeIntItem("album_wall_rank_max_limit", 200);
    }

    private static long getPicWallRankCacheTimeSec() {
        return PropertyFileReader.getRealTimeIntItem("album_wall_rank_cache_time_sec", 60);
    }

    private static long getPicWallExpireTime() {
        long endTime;
        try {
            Timestamp endTimeStamp = ActivityFunParaConfig.getInstance().getTimeValue(ActivityFunParaType.AFPT_PIC_WALL_END_TIME);
            endTime = endTimeStamp.getSeconds();
        } catch (ConfigNotFoundException e) {
            endTime = DateUtils.currentTimeSec() + 30 * DateUtils.ONE_DAY_SECONDS;
        }
        return endTime + 30 * DateUtils.ONE_DAY_SECONDS;
    }

    private static int getPicWallLikeNumRankQueryNum() {
        try {
            return ActivityFunParaConfig.getInstance().getIntValue(ActivityFunParaType.AFPT_PIC_WALL_LIKE_NUM_RANK_DISPLAY_NUM);
        } catch (ConfigNotFoundException e) {
            return 50;
        }
    }

    private static int getPicWallAddTimeRankQueryNum() {
        try {
            return ActivityFunParaConfig.getInstance().getIntValue(ActivityFunParaType.AFPT_PIC_WALL_ADD_TIME_RANK_DISPLAY_NUM);
        } catch (ConfigNotFoundException e) {
            return 50;
        }
    }

    /**
     * 保存照片墙照片数据
     * @param uid 玩家id
     * @param picInfo 照片信息
     * @return boolean
     */
    public static boolean picWallSavePicInfo(long uid, proto_AlbumPicInfo picInfo) {
        // 保存图片信息
        String wallPicKey = getWallPicKey(uid, picInfo.getPicKey());
        CacheResult<String> picInfoResult = CacheUtil.AlbumWallPicInfo.setCacheByteArray(wallPicKey, picInfo.toByteArray());
        if (!picInfoResult.checkCode()) {
            LOGGER.error("picWallSavePicInfo set redis:{} fail: {}", wallPicKey, picInfoResult.errCode);
            return false;
        }

        // 更新排行榜
        int maxLimit = getPicWallRankMaxLimit();
        long nowTimeSec = DateUtils.currentTimeSec();
        long expireTimeSec = getPicWallExpireTime();

        CacheResult<Long> latestResult = Cache.evalAndOutputInteger(CacheScript.setAndUpdateZsetScript,
                new String[]{
                        CacheUtil.AlbumWallPicAddTimeRank.getKey(picInfo.getLabelType()),
                },
                wallPicKey, String.valueOf(nowTimeSec), String.valueOf(maxLimit), String.valueOf(expireTimeSec));
        if (!latestResult.checkCode() || latestResult.val < 0) {
            LOGGER.error("picWallSavePicInfo set AlbumWallPicAddTimeRank wallPicKey {} error:{}", wallPicKey, latestResult.errCode);
            return false;
        }
        CacheResult<Long> likeResult = Cache.evalAndOutputInteger(CacheScript.setAndUpdateZsetScript,
                new String[]{
                        CacheUtil.AlbumWallPicLikeNumRank.getKey(picInfo.getLabelType()),
                },
                wallPicKey, "0", String.valueOf(maxLimit), String.valueOf(expireTimeSec));
        if (!likeResult.checkCode() || likeResult.val < 0) {
            LOGGER.error("picWallSavePicInfo set AlbumWallPicLikeNumRank wallPicKey {} error:{}", wallPicKey, likeResult.errCode);
            return false;
        }
        return true;
    }

    /**
     * 删除照片墙照片数据
     * @param uid 玩家id
     * @param labelType 照片标签类型
     * @param picKey 照片key
     * @return boolean
     */
    public static boolean picWallDeletePicInfo(long uid, AlbumPicLabelType labelType, String picKey) {
        String wallPicKey = getWallPicKey(uid, picKey);
        CacheResult<Long> picInfoResult = CacheUtil.AlbumWallPicInfo.delCacheForByteArray(picKey);
        if (!picInfoResult.checkCode()) {
            LOGGER.error("picWallDeletePicInfo del redis:{} fail: {}", picKey, picInfoResult.errCode);
            return false;
        }

        // 删除排行榜中的数据
        CacheResult<Long> timeRankResult = CacheUtil.AlbumWallPicAddTimeRank.zremCacheForString(labelType, getWallPicKey(uid, picKey));
        if (!timeRankResult.checkCode()) {
            LOGGER.error("picWallDeletePicInfo del time rank:{}:{} fail: {}", labelType, picKey, timeRankResult.errCode);
            return false;
        }
        CacheResult<Long> likeRankResult = CacheUtil.AlbumWallPicLikeNumRank.zremCacheForString(labelType, getWallPicKey(uid, picKey));
        if (!likeRankResult.checkCode()) {
            LOGGER.error("picWallDeletePicInfo del like rank:{}:{} fail: {}", labelType, picKey, likeRankResult.errCode);
            return false;
        }

        return true;
    }

    /**
     * 点赞照片墙照片
     * @param wallPicKey 照片墙照片key
     * @return int
     */
    public static int picWallLikePic(AlbumPicLabelType labelType, String wallPicKey) {
        int maxLimit = getPicWallRankMaxLimit();
        long expireTimeSec = getPicWallExpireTime();

        // 更新点赞数
        CacheResult<Long> likeNumResult = CacheUtil.AlbumWallPicLikeNum.incrCache(wallPicKey);
        if (!likeNumResult.isOk()) {
            LOGGER.error("picWallLikePic incr AlbumWallPicLikeNum:{} fail:{}", wallPicKey, likeNumResult.errCode);
            return -1;
        }

        // 更新排行榜
        CacheResult<Long> result = Cache.evalAndOutputInteger(CacheScript.incrAndUpdateZsetScript,
                new String[]{
                        CacheUtil.AlbumWallPicLikeNumRank.getKey(labelType),
                },
                wallPicKey, String.valueOf(maxLimit), String.valueOf(expireTimeSec));
        if (!result.checkCode() || result.val < 0) {
            LOGGER.error("picWallLikePic wallPicKey {} error:{}", wallPicKey, result.errCode);
            return -2;
        }

        return result.val.intValue();
    }

    /**
     * 获取照片墙点赞最多照片，分页
     * @param labelType 标签类型
     * @param page 页数
     * @param pageSize 每页数量
     * @return T2<List<PicWallPicInfo>, Integer> 照片详细信息和总照片数
     */
    public static T2<List<PicWallPicInfo>, Integer> picWallGetPicTopLikeNum(AlbumPicLabelType labelType, int page, int pageSize) {
        List<PicWallPicInfo> picList = new ArrayList<>();

        List<ScoredValue<String>> list = getPicWallLikeNumRankCache(labelType);
        // 分页查key列表
        int startIndex = (page - 1) * pageSize;
        if (startIndex >= list.size()) {
            return Tuple.New(picList, 0);
        }
        int endIndex = Math.min(startIndex + pageSize, list.size());

        List<String> wallPicKeyList = new ArrayList<>();
        Map<String, Integer> picLikeNumMap = new HashMap<>();
        for (ScoredValue<String> node : list.subList(startIndex, endIndex)) {
            String wallPicKey = node.getValue();
            int likeNum = (int) node.getScore();
            wallPicKeyList.add(wallPicKey);
            picLikeNumMap.put(wallPicKey, likeNum);
            wallPicLikeNumCache.put(wallPicKey, likeNum);
        }

        // 批量查key的照片数据
        Map<String, proto_AlbumPicInfo> picInfoMap = getWallPicInfoMap(wallPicKeyList);

        for (String key : wallPicKeyList) {
            long uid = 0;
            T2<Long, String> picKeyInfo = parseWallPicKey(key);
            if (picKeyInfo != null) {
                uid = picKeyInfo._1;
            }
            picList.add(PicWallPicInfo.newBuilder()
                    .setUid(uid)
                    .setPicInfo(picInfoMap.getOrDefault(key, proto_AlbumPicInfo.newBuilder().setPicKey(key).build()))
                    .setLikeNum(picLikeNumMap.getOrDefault(key, 0))
                    .build());
        }

        return Tuple.New(picList, list.size());
    }

    /**
     * @param labelType 标签类型
     * @param page 页数
     * @param pageSize 每页数量
     * @return T2<List<PicWallPicInfo>, Integer> 照片详细信息和总照片数
     */
    public static T2<List<PicWallPicInfo>, Integer> picWallGetPicLatest(AlbumPicLabelType labelType, int page, int pageSize) {
        List<PicWallPicInfo> picList = new ArrayList<>();

        List<ScoredValue<String>> list = getPicWallAddTimeRankCache(labelType);

        // 分页查key列表
        int startIndex = (page - 1) * pageSize;
        if (startIndex >= list.size()) {
            return Tuple.New(picList, 0);
        }
        int endIndex = Math.min(startIndex + pageSize, list.size());

        List<String> wallPicKeyList = new ArrayList<>();
        for (ScoredValue<String> node : list.subList(startIndex, endIndex)) {
            wallPicKeyList.add(node.getValue());
        }

        // 批量查key的照片数据
        Map<String, proto_AlbumPicInfo> picInfoMap = getWallPicInfoMap(wallPicKeyList);

        // 批量查照片点赞数
        Map<String, Integer> picLikeNumMap = getWallPicLikeNumMap(wallPicKeyList);

        for (String key : wallPicKeyList) {
            long uid = 0;
            T2<Long, String> picKeyInfo = parseWallPicKey(key);
            if (picKeyInfo != null) {
                uid = picKeyInfo._1;
            }
            picList.add(PicWallPicInfo.newBuilder()
                    .setUid(uid)
                    .setPicInfo(picInfoMap.getOrDefault(key, proto_AlbumPicInfo.newBuilder().setPicKey(key).build()))
                    .setLikeNum(picLikeNumMap.getOrDefault(key, 0))
                    .build());
        }

        return Tuple.New(picList, list.size());
    }

    /**
     * 获取照片墙点赞数
     * @param wallPicKeyList 照片墙照片key列表
     * @return Map<String, Integer>
     */
    public static Map<String, Integer> getWallPicLikeNumMap(List<String> wallPicKeyList) {
        return wallPicLikeNumCache.getAll(wallPicKeyList);
    }

    /**
     * 获取照片墙数据
     * @param wallPicKeyList 照片墙照片key列表
     * @return Map<String, proto_AlbumPicInfo>
     */
    public static Map<String, proto_AlbumPicInfo> getWallPicInfoMap(List<String> wallPicKeyList) {
        return wallPicInfoCache.getAll(wallPicKeyList);
    }

    private static Integer loadWallPicLikeNum(String wallPicKey) {
        CacheResult<String> result = CacheUtil.AlbumWallPicLikeNum.getCacheString(wallPicKey);
        if (!result.checkCode()) {
            LOGGER.error("loadWallPicLikeNum get pic {} fail,{}", wallPicKey, result.errCode);
            return 0;
        }
        try {
            int num = 0;
            if (result.val != null) {
                num = Integer.parseInt(result.val);
            }
            return num;
        } catch (Exception e) {
            LOGGER.error("loadWallPicLikeNum {} parse num {} error: ", wallPicKey, result.val, e);
            return 0;
        }
    }

    private static proto_AlbumPicInfo loadWallPicInfo(String wallPicKey) {
        CacheResult<byte[]> picInfoResult = CacheUtil.AlbumWallPicInfo.getCacheByteArray(wallPicKey);
        if (!picInfoResult.isOk()) {
            LOGGER.error("loadWallPicInfo get pic {} fail,{}", wallPicKey, picInfoResult.errCode);
            return null;
        }

        try {
            proto_AlbumPicInfo picInfo = proto_AlbumPicInfo.parseFrom(picInfoResult.val);
            LOGGER.info("cache loadWallPicInfo load wallPicKey: {}, picInfo: {}", wallPicKey, picInfo);
            return picInfo;
        } catch (Exception e) {
            LOGGER.error("loadWallPicInfo parse picInfo {} error: ", wallPicKey, e);
            return null;
        }
    }

    private static Map<String, Integer> loadWallPicLikeNumBatch(Collection<String> wallPicKeyList) {
        Map<String, Integer> picLikeNumMap = new HashMap<>();
        CacheResult<List<KeyValue<String, String>>> picLikeNumResult = CacheUtil.AlbumWallPicLikeNum.mget(Arrays.asList(wallPicKeyList.toArray()));
        if (!picLikeNumResult.isOk()) {
            LOGGER.error("loadWallPicLikeNumBatch get pic list {} fail,{}", wallPicKeyList, picLikeNumResult.errCode);
            return picLikeNumMap;
        }
        for (KeyValue<String, String> kv : picLikeNumResult.val) {
            String wallPicKey = CacheUtil.AlbumPicLikeNum.parseKey(kv.getKey());
            try {
                int num = 0;
                if (kv.hasValue()) {
                    num = Integer.parseInt(kv.getValue());
                }
                picLikeNumMap.put(wallPicKey, num);
            } catch (Exception e) {
                LOGGER.error("loadWallPicLikeNumBatch parse picInfo {} error: ", wallPicKey, e);
            }
        }
        return picLikeNumMap;
    }

    private static Map<String, proto_AlbumPicInfo> loadWallPicInfoBatch(Collection<String> wallPicKeyList) {
        Map<String, proto_AlbumPicInfo> picInfoMap = new HashMap<>();
        CacheResult<List<KeyValue<String, byte[]>>> picInfoResult = CacheUtil.AlbumWallPicInfo.mgetCacheByteArray(Arrays.asList(wallPicKeyList.toArray()));
        if (!picInfoResult.isOk()) {
            LOGGER.error("get pic list {} fail,{}", wallPicKeyList, picInfoResult.errCode);
            return picInfoMap;
        }
        for (KeyValue<String, byte[]> kv : picInfoResult.val) {
            String wallPicKey = CacheUtil.AlbumWallPicInfo.parseKey(kv.getKey());
            try {
                proto_AlbumPicInfo picInfo = proto_AlbumPicInfo.parseFrom(kv.getValue());
                picInfoMap.put(wallPicKey, picInfo);
                LOGGER.info("cache loadWallPicInfoBatch load wallPicKey: {}, picInfo: {}", wallPicKey, picInfo);
            } catch (Exception e) {
                LOGGER.error("getPicInfoMap parse picInfo {} error: ", wallPicKey, e);
            }
        }
        return picInfoMap;
    }

    private static List<ScoredValue<String>> getPicWallLikeNumRankCache(AlbumPicLabelType labelType) {
        LocalCacheItem<List<ScoredValue<String>>> cacheItem = picWallLikeNumRankCache.compute(labelType, (key, val) -> {
            if (val == null || val.isExpired()) {
                int maxNum = getPicWallLikeNumRankQueryNum();
                // 查询排行
                CacheResult<List<ScoredValue<String>>> rankResult = CacheUtil.AlbumWallPicLikeNumRank.zrevrangeWithScoreCacheForString(
                        labelType.toString(), 0, maxNum);
                if (!rankResult.isOk()) {
                    LOGGER.error("getPicWallLikeNumRankCache get rank labelType {} fail,{}", labelType, rankResult.errCode);
                    return new LocalCacheItem<>(new ArrayList<>(), getPicWallRankCacheTimeSec());
                }
                LOGGER.info("getPicWallLikeNumRankCache update: get rank labelType {} val:{}", labelType, rankResult.val);
                return new LocalCacheItem<>(rankResult.val, getPicWallRankCacheTimeSec());
            }
            return val;
        });
        LOGGER.info("getPicWallLikeNumRankCache update: get rank labelType {} val:{}", labelType, cacheItem.getVal());
        return cacheItem.getVal();
    }

    private static List<ScoredValue<String>> getPicWallAddTimeRankCache(AlbumPicLabelType labelType) {
        LocalCacheItem<List<ScoredValue<String>>> cacheItem = picWallAddTimeRankCache.compute(labelType, (key, val) -> {
            if (val == null || val.isExpired()) {
                int maxNum = getPicWallAddTimeRankQueryNum();
                // 查询排行
                CacheResult<List<ScoredValue<String>>> rankResult = CacheUtil.AlbumWallPicAddTimeRank.zrangeWithScoreCacheForString(
                        labelType.toString(), 0, maxNum);
                if (!rankResult.isOk()) {
                    LOGGER.error("getPicWallAddTimeRankCache get rank labelType {} fail,{}", labelType, rankResult.errCode);
                    return new LocalCacheItem<>(new ArrayList<>(), getPicWallRankCacheTimeSec());
                }
                LOGGER.info("getPicWallAddTimeRankCache update: get rank labelType {} val:{}", labelType, rankResult.val);
                return new LocalCacheItem<>(rankResult.val, getPicWallRankCacheTimeSec());
            }
            return val;
        });
        LOGGER.info("getPicWallAddTimeRankCache update: get rank labelType {} val:{}", labelType, cacheItem.getVal());
        return cacheItem.getVal();
    }
}
