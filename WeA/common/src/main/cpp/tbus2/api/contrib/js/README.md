tbus2 Nodejs module
======================

# 概述

tbus2 api Nodejs 封装

# 安装

1. 准备好nodejs环境，以及npm包管理工具、node-gyp编译工具（开发用版本：nodejs-v18.5.0, npm-8.12.1, node-gyp-v9.1.0）
```shell
# 查看环境 
node --version
npm --version
node-gyp --version
```
详情可查看：[Node.js](https://github.com/nodejs), [node-gyp](https://github.com/nodejs/node-gyp)

2. 安装依赖
```shell
# api转接库（开发用版本：node-addon-api 5.0.0)
npm install node-addon-api -s

# demo.js 日志打印库
npm install winston 
# 安装成功后新建有目录node_modules，存放依赖文件
```

3. 安装后执行如下命令编译
```shell
node-gyp configure rebuild
```
编译后的库文件在 /build/Release/tbus2.node

如demo.js所示，可用如下命令引用该库
```js
var tbus2 = require('./build/Release/tbus2');
```

4. 试运行
```shell
# 按本地所配置的agent参数设置agents
node demo.js
```

5. 使用文档
```js
// Endpoint类
class Endpoint() {
  member:
    busid,      // BigInt，终端自身id
    agent_id,   // BigInt，挂载的agent的id
    is_ready,   // BigInt，终端是否挂载agent

  method:
  // -------------------------------所有附带初始值的参数表示可缺省-------------------------------
  // ------------------------不支持按名填参，缺省参数必须全部位于参数表末端------------------------
  // ---------------------------Open(busid, mod_version = 1)   wrong---------------------------
  // ---------------------------Open(busid, "", false, 1)      yes---------------------------

    // 序列化Endpoint信息
    // return: String, include endpoint and agent
    to_String();

    // open endpoint
    // busid: String, Number or BigInt, 本地端点id(1 ~ 2^64-1)
    // agent_url: String, 本机agent配置的endpoint_url
    // return: number, 0 is success, otherwise is error code
    Open(busid, agent_url = nullptr, start_standby = False, mod_version = 0, cookie = 0, wait_ms = 1000);

    // close endpoint
    Close();

    // check endpoint is opened
    // return Boolean
    IsOpened();

    // write msg to output mq
    // dest: String, Number or BigInt, 目标端点id(1 ~ 2^64-1)
    // data: String, msg data
    // return: number, 0 is success, otherwise is error code
    Write(dest, data);

    // write msg to output mq, require hash routing
    // dest: String, Number or BigInt, 目标端点id(1 ~ 2^64-1)
    // data: String, msg data
    // hash_key:  Number or BigInt, 哈希路由的键值(0 ~ 2^64-1)
    // return: number, 0 is success, otherwise is error code
    WriteHash(dest, data, hash_key, conf = {min_require_version:0, max_require_version:0, msg_type:0, usr_flags:0});

    // write groupcast msg
    // dest: String, Number or BigInt, 目标组gid掩码
    // data: String, msg data
    // with_multi_group: Boolean, 多组广播
    // return: number, 0 is success, otherwise is error code
    WriteGroupcast(dest, data, with_multi_group = false, 
    conf = {min_require_version:0, max_require_version:0, msg_type:0, hash_key:0});

    // write msg to outpur mq, full function
    // dest: String, Number or BigInt
    // data: String, msg data
    // return: number, 0 is success, otherwise is error code
    WriteEx(dest, data, conf = {min_require_version:0, max_require_version:0, msg_type:0, usr_flags:0, hash_key:0});

    // read msg, msg pop from mq after readed
    // buf: Buffer, msg buffer
    // return: Object, {err=0, size, desc} if success, else {err}
    // err表示错误码，为0时成功；size表示消息长度；desc表示消息基本信息, 包括：源端id，目标端id，代理，写入队列的时间，消息类型，序列化字符串
    // desc = {src, dest, proxy, ctime, msg_type, str}
    Read(buf);

    // peek msg from input queue, msg stay in mq
    // return: Object, {msg, size, desc} if success, else {size=0}
    // msg表示消息内容，为String类型；size表示消息长度，Peek失败时为0；desc同Read()
    Peek();

    // pop top msg from input queue
    Pop();

    // keep endpoint alive
    // 周期性调用，可用于agent重启后重新恢复信令通道
    // return: Number, 0 if success, otherwise is error code
    Update(wait_ms = 0);

    // join group, when joined, would recv group-oriented msg (msg.dest is gid)
    // return: Number, 0 if success, otherwise is error code
    JoinGroup(wait_ms = 1000);

    // exit group
    // return: Number, 0 if success, otherwise is error code
    ExitGroup(wait_ms = 1000);

    // query endpoint status, if busid = 0, then query self status(ready or standby, ready is joined group)
    // if busid = 0, then query self status(ready or standby, ready is joined group)
    // return: Object, {status, err}, if err = 0, then query success, otherwise failure and status = 0
    QueryEndpointStatus(busid = 0, wait_ms = 1000);

    // set hold share memory queue time when gamesvr exit, usually not called
    // reserve_secs: unit seconds
    // return: Number, 0 if success, otherwise is error code
    SetRestartReserveTime(reserve_secs, wait_ms = 1000);

    // require agent subscribe group change
    // gid: Number, String or BigInt
    // return: Number, 0 if success, otherwise is error code
    SubscribeGroup(gid, wait_ms = 1000);

    // require agent unsubscribe group change
    // gid: Number, String or BigInt
    // return: Number, 0 if success, otherwise is error code
    UnsubscribeGroup(gid, wait_ms = 1000);
    
    // set event callback function
    // callback: function(Endpoint, Event)
    SetEventCallback(callback = nullptr);
}
// Endpoint类 end

// Event类
class Event {
  member:
    event_id,     // event_id for tbuspp_event_t
    data,         // {gid, version, str}, 组id，版本号，序列化信息
  method:
    toString();   // return String, 序列化成员信息
}
// Event类 end

// busid 操作

// tmpl_str: String, 点分式模板字符串
// gid_mask: String, Number or BigInt, 组id掩码
// return: Number, 0 if success, otherwise is error code
busid_template_init(tmpl_str, gid_mask);

// convert string busid to number busid, use it after open endpoint
// busid_str: String
// return: BigInt
busid_aton(busid_str);

// convert number busid to string busid, use it after open endpoint
// busid: BigInt or Number
// return: String
busid_ntoa(busid);

// get gid with busid
// busid: BigInt or Number
// return: BigInt
busid_get_gid(busid);

// check the busid is gid
// busid: BigInt or Number
busid_is_gid(busid);

// parse error code
// err: Number, error code
// return: String, error infomation
error_string(err)
```
