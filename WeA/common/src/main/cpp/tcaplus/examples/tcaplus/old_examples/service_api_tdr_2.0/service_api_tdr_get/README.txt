/* This file is generated by tdr. */
/* No manual modification is permitted. */

/* creation time: Thu Jan 15 20:05:44 2015 */
/* tdr version: 2.7.4, build at 20150115 */

Some explanation about TDR feature that generates C++ source files:

1,  For each .xml file, TDR generates one .h source file

2,  If .xml file contains <struct></struct> or <union></union>,
    TDR generates one .cpp source file for this .xml file.

3,  All source files refered by 1 and 2 are protected by a namespace,
    with the same name as value of 'name' attribute of xml tag <metalib></metalib>.

4,  For the same release of TDR, all assistant source files(Tdr*.h Tdr*.cpp) are independent of xml files.
    That means these assistant source files can be shared by many metalibs.
    For the same reason, one project SHOULD contains only one set of assistant source files.
    Otherwise, linker will complains like 'symbol redefined' or 'multiple definition'.

5,  In order to make the feature easy to use,
    'visualize' and 'defaultvalue' for entry with type="wstring" are not supported.
    Otherwise, some third party library will be needed.

6,  Avoid defining entry with count="0".
    In other words, avoid using variable-length array.

7,  Avoid defining entry with type="float" or type="double".

8,  For XML related function, only ASCII / UTF-8 / GBK are well supported.

9,  If XML related function is needed, avoid defining entry with type="wchar" or type="wstring".

10,  If XML related function is needed for WINDOWS, VC 7.0 (VS 2003) or higher version compiler is needed.

11,  macros in TdrPal.h are NOT suggested to use directly.

12,  set_has* and clear_has* are NOT supposed to call directly at present.

For more help information, please refer to URL:
    http://ied.oa.com/tsf4g/tsf4gonline/lib/tdr.htm
