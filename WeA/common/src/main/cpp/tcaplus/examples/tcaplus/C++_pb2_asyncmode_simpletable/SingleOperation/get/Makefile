# ================================================================
#Makefile for tcaplus example
#
# Date:   2016-09-14
#
# Copyright (C) 2016 Architechure IRED TENCENT
#
# ================================================================

CPPFILE=$(wildcard *.cpp)
CCFILE=$(wildcard *.cc)

#GENERATE_FILE=$(shell ./conv.sh )

LIBS += -L $(TSF4G_HOME)/lib -L$(TCAPLUS_HOME)/lib -Wl,-Bstatic -ltcaplusprotobufapi -ltcaplusserviceapi -lprotobuf -ltsf4g_r -lscew -lexpat -Wl,-Bdynamic -lpthread
 
INC = -I$(TSF4G_HOME)/include -I $(TCAPLUS_HOME)/include/tcaplus_service -I $(TCAPLUS_HOME)/include/tcaplus_service/protobuf -I../../../C++_common_for_pb2

.PHONY: all clean 

all:
	g++ -o mytest $(CCFILE) $(CPPFILE) $(INC) ${LIBS}  

clean:
	rm -f mytest     mytest.log*
