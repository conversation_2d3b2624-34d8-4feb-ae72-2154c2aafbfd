package com.tencent.wea.action.ugc;

import com.google.protobuf.Message;
import com.tencent.cos.CosManager;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.UgcCosMapping;
import com.tencent.ugc.CommonUtil;
import com.tencent.wea.action.BaseAction;
import com.tencent.wea.data.MapData.CreateData;
import com.tencent.wea.data.UgcPlayerData;
import com.tencent.wea.manager.UgcPlayerMgr;
import com.tencent.wea.manager.UgcResManager;
import com.tencent.wea.manager.WorkShopMgr;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.common.ApplyReason;
import com.tencent.wea.protocol.common.MapLoadingInfo;
import com.tencent.wea.protocol.common.MapLobbyCoverInfo;
import com.tencent.wea.protocol.common.UgcCreateInfo;
import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.protocol.common.UgcKeyInfo;
import com.tencent.wea.xlsRes.ResUgcMgr.T_UgcCosMapping;
import com.tencent.wea.xlsRes.keywords.SavePosition;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class CreateUgcAction extends BaseAction<SsUgcsvr.RpcCreateUgcEntityReq.Builder> {

    private static final Logger LOGGER = LogManager.getLogger(CreateUgcAction.class);

    @Override
    public Message.Builder execute(SsUgcsvr.RpcCreateUgcEntityReq.Builder builder) {
        SsUgcsvr.RpcCreateUgcEntityRes.Builder res = SsUgcsvr.RpcCreateUgcEntityRes.newBuilder();

        // 一些前置检查
        int checkRet = check(builder);
        if (checkRet != 0) {
            return res.setResult(checkRet);
        }

        UgcPlayerData ugcPlayerData = UgcPlayerMgr.getInstance().getUgcPlayerData(builder.getCreatorId());
        if (ugcPlayerData == null) {
            return res.setResult(NKErrorCode.UgcPlayerInfoFail.getValue());
        }

        long curTime = Framework.currentTimeMillis();
        UgcCreateInfo info = builder.getInfo();
        CreateData createData = new CreateData();
        createData.creatorId = builder.getCreatorId();
        createData.ugcId = info.getUgcId();
        createData.name = info.getName();
        createData.desc = info.getDesc();
        createData.tags = info.getTags();
        createData.bucket = info.getBucket();
        createData.templateId = info.getTemplateId();
        createData.openId = info.getOpenId();
        createData.saveType = SavePosition.Draft_VALUE;
        createData.mdList = info.getMdList();
        createData.instanceType = info.getInstanceType();
        createData.extraInfo = info.getExtraInfo();
        createData.layerInfos = info.getLayersList();
        createData.ugcResparam = info.getUgcResParam();
        createData.createTime = curTime;
        createData.updateTime = curTime;
        createData.editorName = ugcPlayerData.getNickName();
        createData.isAiGen = builder.getIsAiGen();
        createData.coverInfos = info.getCoversList();
        createData.isLuaCoding = info.getIsLuaCoding();
        createData.achievementMap = CommonUtil.convertUgcAchievementList(info.getAchievementIndexListList());
        createData.extraConfigIndexMap = CommonUtil.convertExtraConfigIndexList(info.getExtraConfigIndexListList());
        createData.resIdList = info.getResIdsList();
        createData.instanceSource = info.getInstanceSource();
        createData.mapLoadingInfo = MapLoadingInfo.newBuilder().mergeFrom(info.getMapLoading()).build();
        createData.lobbyCoverInfo = MapLobbyCoverInfo.newBuilder().mergeFrom(info.getLobbyCover()).build();
        createData.traceStr = StringUtils.isNotBlank(builder.getTraceStr())?builder.getTraceStr():"";
        int code = WorkShopMgr.getInstance().createBriefTableData(createData, createData.mdList);
        if (code != 0) {
            return res.setResult(code);
        }
        // M10 私有资源创建时存一份到 publish
        if (UgcResManager.isUgcResType(createData.instanceType)) {
            code = UgcResManager.createPrivateResPubTableData(createData, builder.getCreatorId(), builder.getIdipArea());
        }

        if (builder.getIsApplyCosKey()) {
            // 设置cos key数据
            setUgcKeyInfo(builder, res);
        }

        return res.setResult(code);
    }

    @Override
    public void executeRun(SsUgcsvr.RpcCreateUgcEntityReq.Builder builder) {
    }

    @Override
    public Message.Builder errorExecute() {
        SsUgcsvr.RpcCreateUgcEntityRes.Builder res = SsUgcsvr.RpcCreateUgcEntityRes.newBuilder();
        return res.setResult(NKErrorCode.UnknownError.getValue());
    }

    public int check(SsUgcsvr.RpcCreateUgcEntityReq.Builder req) {
        int ret = 0;
        // 检查一下非模组资源的上限
        if (!req.getToolGenFlag() && UgcResManager.isUgcResType(req.getInfo().getInstanceType())) {
            ret = UgcResManager.doCreateCheck(req.getCreatorId(), req.getInfo().getUgcResParam());
            if (ret != 0) {
                return ret;
            }
        }

        if (req.getIsApplyCosKey()) {
            if (ApplyReason.forNumber(req.getCosKeyApplyReason()) == null) {
                LOGGER.error("param cosKeyApplyReason:{} is invalid", req.getCosKeyApplyReason());
                return NKErrorCode.InvalidParams.getValue();
            }

            if (ApplyReason.forNumber(req.getCoverCosKeyApplyReason()) == null) {
                LOGGER.error("param coverCosKeyApplyReason:{} is invalid", req.getCoverCosKeyApplyReason());
                return NKErrorCode.InvalidParams.getValue();
            }
        }

        return ret;
    }

    /**
     * 设置ugc key数据
     * @param builder 请求结构体
     * @param res 返回结构体
     */
    private void setUgcKeyInfo(SsUgcsvr.RpcCreateUgcEntityReq.Builder builder, SsUgcsvr.RpcCreateUgcEntityRes.Builder res) {

        if (builder == null || res == null) {
            return;
        }

        if (!builder.getIsApplyCosKey()) {
            return;
        }

        // 获取cos key
        CosManager.ApplyKeyParam param = new CosManager.ApplyKeyParam();
        param.setUgcId(builder.getInfo().getUgcId());
        param.setCreatorId(builder.getCreatorId());
        param.setBucket(builder.getInfo().getBucket());
        param.setUgcResType(builder.getInfo().getUgcResParam().getResType().getNumber());

        int realReason = CommonUtil.getRealReason(builder.getCosKeyApplyReason(), builder.getInfo().getInstanceType());
        // 获取ugc pbin文件cos key
        NKPair<NKErrorCode, UgcKeyInfo.Builder> ugcKeyInfo =
                CosManager.getInstance().getUgcKeyInfo(realReason, param);
        if (ugcKeyInfo.getKey().getValue() == NKErrorCode.OK.getValue()) {
            res.setKeyInfo(ugcKeyInfo.value);
        } else {
            LOGGER.error("getUgcKeyInfo failed, creatorId:{}, ugcId:{}, applyReason:{}, code:{}",
                    builder.getCreatorId(), builder.getInfo().getUgcId(), builder.getCosKeyApplyReason(),
                    ugcKeyInfo.getKey());
        }

        int cosReason = CommonUtil.getRealReason(builder.getCoverCosKeyApplyReason(), builder.getInfo().getInstanceType());
        // 获取ugc cover文件cos key
        NKPair<NKErrorCode, UgcKeyInfo.Builder> coverUgcKeyInfo =
                CosManager.getInstance().getUgcKeyInfo(cosReason, param);
        if (coverUgcKeyInfo.getKey().getValue() == NKErrorCode.OK.getValue()) {
            res.setCoverKeyInfo(coverUgcKeyInfo.value);
        } else {
            LOGGER.error("getUgcKeyInfo failed, creatorId:{}, ugcId:{}, applyReason:{}, code:{}",
                    builder.getCreatorId(), builder.getInfo().getUgcId(), builder.getCoverCosKeyApplyReason(),
                    coverUgcKeyInfo.getKey());
        }
    }
}
