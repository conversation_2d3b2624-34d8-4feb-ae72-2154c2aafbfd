package com.tencent.wea.action.ugc;

import com.google.protobuf.Message;
import com.tencent.cos.CosManager;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.ugc.CommonUtil;
import com.tencent.wea.action.BaseAction;
import com.tencent.wea.protocol.SsUgcsvr.RpcUgcApplyKeyInfoReq;
import com.tencent.wea.protocol.SsUgcsvr.RpcUgcApplyKeyInfoRes;

import com.tencent.wea.protocol.common.ApplyReason;
import com.tencent.wea.protocol.common.Common;
import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.protocol.common.UgcKeyInfo;
import com.tencent.wea.protocol.common.UgcResType;
import com.tencent.wea.xlsRes.keywords.CosOperateScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 *
 */
public class UgcApplyKeyInfoAction extends BaseAction<RpcUgcApplyKeyInfoReq.Builder> {

    private static final Logger LOGGER = LogManager.getLogger(UgcApplyKeyInfoAction.class);

    @Override
    public Message.Builder execute(RpcUgcApplyKeyInfoReq.Builder req) {

        RpcUgcApplyKeyInfoRes.Builder res = RpcUgcApplyKeyInfoRes.newBuilder();

        if (req.getCreatorId() == 0L || req.getUgcId() == 0L || UgcInstanceType.forNumber(req.getInstanceType()) == null ||
                UgcResType.forNumber(req.getUgcResType()) == null || req.getBucket().isEmpty() ||
                req.getApplyReasonCount() == 0) {
            LOGGER.error("param error");
            res.setResult(NKErrorCode.InvalidParams.getValue());
            return res;
        }

        for (Integer applyReason : req.getApplyReasonList()) {
            // 获取cos key
            CosManager.ApplyKeyParam param = new CosManager.ApplyKeyParam();
            param.setUgcId(req.getUgcId());
            param.setCreatorId(req.getCreatorId());
            param.setBucket(req.getBucket());
            param.setUgcResType(req.getUgcResType());

            UgcInstanceType type = UgcInstanceType.forNumber(req.getInstanceType());
            int realReason = CommonUtil.getRealReason(applyReason, type);

            // 获取ugc pbin文件cos key
            NKPair<NKErrorCode, UgcKeyInfo.Builder> ugcKeyInfo = CosManager.getInstance().getUgcKeyInfo(realReason, param);
            if (ugcKeyInfo.getKey().getValue() == NKErrorCode.OK.getValue()) {
                res.addInfos(RpcUgcApplyKeyInfoRes.UgcApplyKeyInfo.newBuilder()
                        .setApplyReason(applyReason)
                        .setInfo(ugcKeyInfo.getValue()));
            } else {
                LOGGER.error("getUgcKeyInfo failed, creatorId:{}, ugcId:{}, applyReason:{}, code:{}",
                        req.getCreatorId(), req.getUgcId(), applyReason, ugcKeyInfo.getKey());
            }
        }

        res.setResult(NKErrorCode.OK.getValue());

        return res;
    }


    @Override
    public void executeRun(RpcUgcApplyKeyInfoReq.Builder builder) {
    }

    @Override
    public Message.Builder errorExecute() {
        return null;
    }
}