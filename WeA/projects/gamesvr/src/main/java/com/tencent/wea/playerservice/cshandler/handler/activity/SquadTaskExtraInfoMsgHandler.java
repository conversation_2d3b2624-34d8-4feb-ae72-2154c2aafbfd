package com.tencent.wea.playerservice.cshandler.handler.activity;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.playerservice.activity.implement.ActivityTwoPeopleSquad;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.CsHead;
import java.util.Objects;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class SquadTaskExtraInfoMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(SquadTaskExtraInfoMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsActivity.SquadTaskExtraInfo_C2S_Msg reqMsg = (CsActivity.SquadTaskExtraInfo_C2S_Msg) request;
        ActivityTwoPeopleSquad runningActivity = player.getActivityManager().getRunningActivity(reqMsg.getActivityId(), ActivityTwoPeopleSquad.class);
        if (Objects.isNull(runningActivity)) {
            LOGGER.error("activity not found, uid:{} activity:{}", player.getUid(), reqMsg.getActivityId());
            NKErrorCode.ActivityNotRunning.throwError("activity not found");
            return CsActivity.ActivityPrayerCardInfo_S2C_Msg.newBuilder();
        }
        return runningActivity.getActivityInfo(reqMsg.getTaskIdList());
    }
}
