package com.tencent.wea.playerservice.bag;

import static com.tencent.wea.playerservice.cshandler.handler.player.CreateRoleMsgHandler.DEFAULT_ITEM_ID_COUNT;
import static com.tencent.wea.wolfKill.Util.getWolfKillDefaultAni;
import static com.tencent.wea.wolfKill.Util.isWolfKillItemType;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.guid.BillNoIdGenerator;
import com.tencent.nk.util.random.util.RandomByWeight;
import com.tencent.resourceloader.resclass.*;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.*;
import com.tencent.wea.playerservice.condition.PlayerConditionInitData;
import com.tencent.wea.playerservice.condition.group.AcquireEquipPopUpEquipSlotConditionGroup;
import com.tencent.wea.playerservice.condition.group.PlayerConditionGroup;
import com.tencent.wea.playerservice.event.common.PlayerChangeClothesEvent;
import com.tencent.wea.playerservice.event.common.PlayerEquipItemChangeEvent;
import com.tencent.wea.playerservice.event.common.PlayerGetItemEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.player.PlayerNr3e8Mgr;
import com.tencent.wea.playerservice.report.PlayerReportUtil;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.ugc.manager.PlayerUgcManager.UgcProfileChangeType;
import com.tencent.wea.protocol.AttrItaBagInfo;
import com.tencent.wea.protocol.AttrPlayerPublicEquipments.proto_PlayerPublicEquipments.Builder;
import com.tencent.wea.protocol.CsDressup;
import com.tencent.wea.protocol.CsPlayer.ChangeDefaultDress_C2S_Msg;
import com.tencent.wea.protocol.common.EHeadPublicInfoType;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.room.RoomUtil.UpdateMemberBaseInfoSource;
import com.tencent.wea.tlog.flow.TlogMacros.JSVEHICLEEQUIPOPTYPE;
import com.tencent.wea.xlsRes.ResBackpack.BackpackSundryConfig;
import com.tencent.wea.xlsRes.ResBackpack.CloakroomUnlock;
import com.tencent.wea.xlsRes.ResBackpackItem;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResBackpackItemEffect.ItemStatusInfo;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResMisc;
import com.tencent.wea.xlsRes.ResMisc.MiscPlayerRegConf;
import com.tencent.wea.xlsRes.ResProfileTheme.ProfileThemeConifg;
import com.tencent.wea.xlsRes.keywords.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Nullable;
import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 道具装备穿戴
 *
 * @author:blitzzhang
 * @date:2022/2/17 11:00
 */
public class ItemEquipMgr {

    private static final Logger LOGGER = LogManager.getLogger(ItemEquipMgr.class);

    private final Player player;
    private static final Set<ItemType> DRESS_ITEM_TYPES = new HashSet<>();
    private static final Set<ItemType> INTERACTION_ITEM_TYPES = new HashSet<>();

    private static final Set<ItemType> REAL_DRESS_ITEM_TYPES = new HashSet<>();

    private static final Set<ItemType> ACCESSORIES_ITEM_TYPES = new HashSet<>(); // 配饰类型

    private HashMap<Long, Integer> interactionItems;
    private HashMap<Long, Integer> wolfKillInteractionItems;

    static {
        init();
    }

    public static void reload() {
        init();
    }

    private static void init() {
        // 判断是否需要加时尚度用
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Suit);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_UpperGarment);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_LowerGarment);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Gloves);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_FaceOrnament);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_HeadWear);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_BackOrnament);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_HandOrnament);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Face);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Shuttle);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Vehicle);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Kart);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_FootPrint);
        DRESS_ITEM_TYPES.add(ItemType.ItemType_Velarium);
        // 判断是否需要加赛季道具 userAttr.seasonInfo.dresses
        INTERACTION_ITEM_TYPES.add(ItemType.ItemType_Emoji);
        INTERACTION_ITEM_TYPES.add(ItemType.ItemType_Action1P);
        INTERACTION_ITEM_TYPES.add(ItemType.ItemType_Action2P);
        INTERACTION_ITEM_TYPES.add(ItemType.ItemType_InteractiveProp);
        // 判断是否要加时装数量
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_Suit);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_UpperGarment);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_LowerGarment);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_Gloves);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_FaceOrnament);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_HeadWear);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_BackOrnament);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_HandOrnament);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_Face);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_FootPrint);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_Velarium);
        REAL_DRESS_ITEM_TYPES.add(ItemType.ItemType_Surroundings);
        // 判断是否是限定外观分类
        ACCESSORIES_ITEM_TYPES.add(ItemType.ItemType_FaceOrnament);
        ACCESSORIES_ITEM_TYPES.add(ItemType.ItemType_HeadWear);
        ACCESSORIES_ITEM_TYPES.add(ItemType.ItemType_BackOrnament);
    }

    public ItemEquipMgr(Player player) {
        this.player = player;
        // 删除自动装备
        // player.getEventSwitch().register(new ItemEquipMgr.OnGetItemDefaultDressForDailyBattle());
    }

    // 时尚分计算、装扮数量、赛季时尚手册
    public static boolean isDress(ItemType itemType) {
        return DRESS_ITEM_TYPES.contains(itemType);
    }

    // 装扮道具 fittingSlot 装配的道具类型
    public static boolean isRealDress(ItemType itemType) {
        return REAL_DRESS_ITEM_TYPES.contains(itemType);
    }

    public static boolean isInteraction(ItemType itemType) {
        return INTERACTION_ITEM_TYPES.contains(itemType);
    }

    // 判断是否是限定外观分类
    private static boolean isAccessories(ItemType itemType) {
        return ACCESSORIES_ITEM_TYPES.contains(itemType);
    }

    // 是否可以穿戴到dressItemInfo
    private static boolean canFittingSingleItem(ItemType itemType) {
        return MiscConf.getInstance().getMiscConf().getDressItemInfoTypeList().contains(itemType.getNumber());
    }


    public NKErrorCode saveFittingSlot(int slotId, List<CsDressup.DressInfo> dresses) {
        if (!checkFittingSlotUnlock(slotId)) {
            LOGGER.error("changeFittingSlot failed, slot:{} locked, uid:{}", slotId, player.getUid());
            return NKErrorCode.FittingSlotLocked;
        }
        NKErrorCode funcUnlocked = isFittingSlotAvailable(slotId);
        if (funcUnlocked.hasError()) {
            LOGGER.error("changeFittingSlot failed, function locked, uid:{}, e:{}",
                    player.getUid(), funcUnlocked);
            return funcUnlocked;
        }

        boolean hasFace = false;
        boolean hasSuit = false;
        int oneOutLookGroupId = 0;
        for (CsDressup.DressInfo dress : dresses) {
            if (dress.getItemUuid() == 0) {
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(dress.getItemUuid());
            if (item == null) {
                LOGGER.error("save fitting slot uid:{}, slot:{}, uuid:{} not exist",
                        player.getUid(), slotId, dress.getItemUuid());
                return NKErrorCode.ItemNotExist;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                return NKErrorCode.ResNotFound;
            }
            if (!ItemEquipMgr.isRealDress(itemConf.getType())) {
                return NKErrorCode.ItemEquipIsNotRealDress;
            }
            // Arena局内皮肤不可以在局外使用
            int arenaHeroSkinId = ArenaSkinEffectData.getInstance().getSkinIdOfItemId(item.getItemId());
            if (0 != arenaHeroSkinId) {
                LOGGER.error("arena hero skin can not dressed up. player({} {}), slot({}) uuid({}) itemId({}) arenaHeroSkinId({})",
                        player.getUid(), player.getName(), slotId, dress.getItemUuid(), item.getItemId(), arenaHeroSkinId);
                return NKErrorCode.ArenaHeroSkinCanNotDressedUp;
            }
            // IP配饰冲突检测
            if (itemConf.getOutlookConf().getGroupId() > 0) {
                if (oneOutLookGroupId > 0 && oneOutLookGroupId != itemConf.getOutlookConf().getGroupId()) {
                    return NKErrorCode.EquipItemOutLookGroupConflict;
                } else {
                    oneOutLookGroupId = itemConf.getOutlookConf().getGroupId();
                }
            }
            if (MiscConf.getInstance().getMiscConf().getFittingSlotIdWithNoOutlookGroup() == slotId
                    && (oneOutLookGroupId > 0 || MatchTypeOutlookReplaceData.getInstance().get(itemConf.getId()) != null)) {
                if (!isAccessories(itemConf.getType())){
                    return NKErrorCode.FittingSlotCannotEquipItemWithOutLookGroup;
                }
            }
            if (player.getItemManager().isExpired(item) || item.getNumber() <= 0) {
                LOGGER.error("save fitting slot uid:{}, slot:{}, uuid:{} expired, expire ms:{}",
                        player.getUid(), slotId, dress.getItemUuid(), item.getExpireMs());
                return NKErrorCode.ItemIsExpire;
            }
            if (itemConf.getType() == ItemType.ItemType_Face) {
                hasFace = true;
            }
            if (itemConf.getType() == ItemType.ItemType_Suit) {
                hasSuit = true;
            }
        }
        if (!hasSuit || !hasFace) {
             return NKErrorCode.SaveFittingSlotFaceOrSuitErr;
        }

        FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (slot == null) {
            tryDressDefault(slotId);
            slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        }
        if (slot == null) {
            return NKErrorCode.UserHasNoDefaultDresses;
        }
        ArrayList<DressInfo> formerDresses = new ArrayList<>(slot.getDresses().values());

        for (CsDressup.DressInfo dress : dresses) {
            ItemType itemType = ItemType.forNumber(dress.getItemType());
            if (itemType == null) {
                LOGGER.error("changeFittingSlot got invalid itemType:{}", dress.getItemType());
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(dress.getItemUuid());
            slot.putDresses(itemType, new DressInfo().setDressType(itemType).setDressUuid(dress.getItemUuid())
                    .setDressItemId(item != null ? item.getItemId() : 0));
        }

        ArrayList<DressInfo> putList = new ArrayList<>(slot.getDresses().values());
        slot.clearDresses();
        player.getUserAttrMgr().collectAndSyncDirtyToClient(); // 客户端需求

        for (DressInfo dressInfo : putList) {
            slot.putDresses(dressInfo.getDressType(), dressInfo);
        }
//        updateDressRandomInfo();
        updateDressUpDetailInfos();
        boolean switchSlot = player.getUserAttr().getFittingSlots().getCurrId() != slotId;
        NKErrorCode ret = this.changeFittingSlot(slotId);
        if (ret.isOk()) {
            TlogFlowMgr.sendAvatarChangeFlow(player, putList, formerDresses, switchSlot, slotId);
            // 上报营地
            PlayerReportUtil.reportPlayerDressUp(player);
        }
        return ret;
    }

    public int getSlotFashionValue(int slotId) {
        FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (slot == null) {
            return 0;
        }
        int ret = 0;
        for (DressInfo dress : slot.getDresses().values()) {
            if (dress.getDressUuid() == 0) {
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(dress.getDressUuid());
            if (item == null) {
                continue;
            }
            Item_BackpackItem conf = BackpackItem.getInstance().get(item.getItemId());
            if (conf == null) {
                continue;
            }
            ret += conf.getOutlookConf().getFashionValue();
            if (conf.getOutlookConf().getBelongTo() != 0) {
                int belongToItemId = conf.getOutlookConf().getBelongTo();
                conf = BackpackItem.getInstance().get(belongToItemId);
                if (conf == null) {
                    LOGGER.error("get item conf err, uid:{} itemId:{}", player.getUid(), belongToItemId);
                    continue;
                }
                ret += conf.getOutlookConf().getFashionValue();
            }
        }
        return ret;
    }



    public void fittingSingleItem(long itemUUID) {
        Item item = player.getItemManager().getItemByUUID(itemUUID);
        if (item == null) {
            NKErrorCode.ItemNotExist.throwError("params error item not exist, uid:{} itemUUID:{}", player.getUid(),
                    itemUUID);
            return;
        }
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            NKErrorCode.ResNotFound.throwError("item config can not found, uid:{} itemUUID:{} itemId:{}",
                    player.getUid(), item.getId(), item.getItemId());
            return;
        }
        if (!canFittingSingleItem(itemConf.getType())) {
            NKErrorCode.ItemUnknownItemType.throwError("item is dress can not equip, uid:{} itemUUID:{} itemId:{}",
                    player.getUid(), item.getId(), item.getItemId());
            return;
        }
        //
        DressItemInfo dressItemInfo = player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo()
                .get(itemConf.getType());
        if (dressItemInfo != null) {
            // 脱掉
            if (dressItemInfo.getItemUUID() == itemUUID) {
                player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo().get(itemConf.getType()).clear();
                sendFittingItemTlog(item, itemConf, 0, item.getItemId());
                player.getPlayerRoomMgr()
                        .updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.DressUpChange); // 道具脱下也需要同步
                player.getPlayerNr3e8Mgr().updatePlayerMemberBase(PlayerNr3e8Mgr.MemberBaseChangeType.DressUpChange);
                dispatchChangeField();
                return;
            }
            sendFittingItemTlog(item, itemConf, item.getItemId(), dressItemInfo.getItemId());
        } else {
            dressItemInfo = new DressItemInfo();
            player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo().put(itemConf.getType(), dressItemInfo);
            sendFittingItemTlog(item, itemConf, item.getItemId(), 0);
        }
        // 穿戴才检测是否过期
        if (player.getItemManager().isExpired(item) || item.getNumber() <= 0) {
            NKErrorCode.ItemIsExpire.throwError("dressUpItem item expire, uid:{} itemUUID:{}",
                    player.getUid(), itemUUID);
            return;
        }

        dressItemInfo.setItemUUID(itemUUID);
        dressItemInfo.setItemId(itemConf.getId());
        if (ItemStatusChangeData.getInstance().get(item.getItemId(),item.getStatus()) != null) {
            changeFittingItemStatus(item.getId(),item.getStatus());
        } else {
            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.DressUpChange);
            player.getPlayerNr3e8Mgr().updatePlayerMemberBase(PlayerNr3e8Mgr.MemberBaseChangeType.DressUpChange);
        }


        // 狼人动画
//        if (itemConf.getType() == ItemType.ItemType_NR3E_ReportAnim || itemConf.getType() == ItemType.ItemType_NR3E_AttackAnim) {
//            LOGGER.debug("woilfkill rpcRoomPlayerUpdateMemberBaseInfo, item_id {}", itemConf.getId());
//            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.Default);
//        }
        dispatchChangeField();
    }

    private void checkChangeTitle() {
        DressItemInfo titleInfo = player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo()
                .get(ItemType.ItemType_Title);
        // 使用称号后自动切换对外展示称号
        if(titleInfo != null && titleInfo.getItemId() != 0){
            player.getPlayerPersonalityMgr().changeHeadPublicInfoType(EHeadPublicInfoType.EHShow_Title);
        }else{
            // 称号到期或者卸载，取消对外展示称号
            if (player.getUserAttr().getPlayerPublicGameSettings().getHeadPublicInfoType()
                    == EHeadPublicInfoType.EHShow_Title_VALUE) {
                player.getPlayerPersonalityMgr().changeHeadPublicInfoType(EHeadPublicInfoType.EHShow_None);
            }
        }
    }

    private void dispatchChangeField() {
        checkChangeTitle();

        player.getPlayerUgcManager().syncPlayerProfileOnChange(UgcProfileChangeType.DressItemInfos);
        player.getFriendManager().addChangeField(PlayerPublicInfoField.DRESS_ITEM_INFOS);
        player.getClubMgr().addChangeField(PlayerPublicInfoField.DRESS_ITEM_INFOS);
    }

    private void changeDefaultWolfKillDressInfo(ItemType itemType){
        Map<ItemType, Integer> mapDefault = getWolfKillDefaultAni();
        if (mapDefault != null && mapDefault.get(itemType) != null) {
            DressItemInfo dressItemInfo = new DressItemInfo();
            int itemId = mapDefault.get(itemType);
            Map<Long, Item> mapDefaultUuid = player.getItemManager().getItemsByItemId(mapDefault.get(itemType));
            if (mapDefaultUuid!=null){
                for (Long itemUUID : mapDefaultUuid.keySet()) {
                    dressItemInfo.setItemUUID(itemUUID);
                    dressItemInfo.setItemId(itemId);
                    player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo().put(itemType, dressItemInfo);
                    return;
                }
            }
        }
    }

    private void fittingSingleItemExpire(Item item) {
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            return;
        }
        DressItemInfo dressItemInfo = player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo()
                .get(itemConf.getType());
        if (dressItemInfo != null) {
            // 脱掉
            if (dressItemInfo.getItemUUID() == item.getId()) {
                player.getUserAttr().getPlayerPublicEquipments().removeDressItemInfo(itemConf.getType());
                sendFittingItemTlog(item, itemConf, 0, item.getItemId());
                LOGGER.debug("fittingSingleItemExpire debug, uid:{} itemUUID:{}", player.getUid(), item.getId());
            }
            // 称号到期，切换对外展示
            if(itemConf.getType() == ItemType.ItemType_Title){
                checkChangeTitle();
            }

            // 狼人玩法的动画，有默认配置，需要切换为默认动画
            if (isWolfKillItemType(itemConf.getType().getNumber())){
                changeDefaultWolfKillDressInfo(itemConf.getType());
            }
        }
    }

    private void sendFittingItemTlog(Item item, Item_BackpackItem itemConf, int newItemId, int oldItemId) {
        switch (itemConf.getType()) {
            case ItemType_Title:
                TlogFlowMgr.sendPlayerTitleChangeFlow(player, newItemId, oldItemId, true);
                break;
            case ItemType_Frame:
                TlogFlowMgr.sendPlayerHeadFrameChangeFlow(player, newItemId, oldItemId,
                        (int) (item.getExpireMs() - DateUtils.currentTimeMillis()) / 1000, true);
                break;
            case ItemType_NamePlate:
                TlogFlowMgr.sendPlayerNameplateChangeFlow(player, newItemId, oldItemId, true);
                break;
            case ItemType_Kart:
                TlogFlowMgr.sendPlayerRacingVehicleOpFlow(player, newItemId, oldItemId, JSVEHICLEEQUIPOPTYPE.JS_EQUIP);
                break;
            case ItemType_Background:
                TlogFlowMgr.sendPlayerBackgroundChangeFlow(player, newItemId, oldItemId, true);
                break;
            case ItemType_FriendInviteBox:
                int expTime = -1;
                if (item.getExpireMs() > 0) {
                    expTime = (int) (item.getExpireMs() - DateUtils.currentTimeMillis()) / 1000;
                }
                TlogFlowMgr.sendPlayerInviteFrameChangeFlow(player, newItemId, oldItemId, expTime, true);
                break;
            default:

        }
    }

    public void onItemRemoved(Item item) {
        removeInteractionsItem(item.getId());
        removeWolfKillInteractionsItem(item.getId());
        removeReadyBattleBagItem(item);
        player.getItemEquipManager().fittingSingleItemExpire(item);
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf != null && isRealDress(itemConf.getType())) {
            List<CloakroomUnlock> slotConfigs = FittingSlotUnlockConf.getInstance().dataArray;
            for (CloakroomUnlock config : slotConfigs) {
                takeOffFromSlot(config.getId(), item.getId());
            }
            if (player.getItemManager().getItemsByItemId(item.getItemId()).isEmpty()) {
                takeOffFromPublicEquipments(item.getItemId());
            }
            fillUnlockedSlotsWithDefaultDresses(1);
            changeFittingSlot(player.getUserAttr().getFittingSlots().getCurrId());
        }
    }

    private void removeReadyBattleBagItem( Item item){
        long uid = player.getUid();
        int itemId = item.getItemId();
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (itemConf == null){
            LOGGER.warn("player {}  current item {} conf is null {}", uid,itemId,item.getId());
            return;
        }
        ItemType type = itemConf.getType();
        MapAttrObj<ItemType, DressItemInfo> readyBattleBagInfo = player.getUserAttr().getPlayerPublicEquipments().getReadyBattleBagInfo();
        DressItemInfo dressItemInfo = readyBattleBagInfo.get(type);
        if (dressItemInfo!=null && dressItemInfo.getItemUUID()==item.getId()){
            // player.getUserAttr().getPlayerPublicEquipments().removeReadyBattleBagInfo(type);
            LOGGER.debug(" player {} current ReadyBattleBag item {}  remove {}",player.getUid(),itemId,item.getId());
            //流水补充
            long removeTime = Framework.currentTimeSec();
            TlogFlowMgr.sendDailyBattleItemOutTimeFlow(player,itemId,item.getId(),dressItemInfo.getDressUpType().toString(),removeTime);

            // int belongToItemId = itemConf.getOutlookConf().getBelongTo();
            // Item_BackpackItem backpackItem = BackpackItem.getInstance().get(belongToItemId);
            // if (Objects.nonNull(backpackItem)) { }

            Item_BackpackItem dailyBattleDefaultItem = BackpackItem.getInstance().getDailyBattleDefaultItemMap().get(itemConf.getType());
            if (Objects.nonNull(dailyBattleDefaultItem)) {
                int defaultItemId = dailyBattleDefaultItem.getId();
                ResBackpackItem.Item_ReadyBattleOrnamentationConf mConf = ReadyBattleOrnamentationDataConfData.getInstance().get(defaultItemId);
                if (Objects.isNull(mConf)) {
                    LOGGER.error("not in Item_ReadyBattleOrnamentationConf uid:{} itemId:{} defaultItemId:{}", uid, itemId, defaultItemId);
                    NKErrorCode.ItemReadyBattleOrnamentBagNotBelongConf.throwError("item not in ReadyBattleOrnamentationDataConfData");
                } else {
                    Map<Long, Item> defaultItems = player.getItemManager().getItemsByItemId(defaultItemId);
                    if (defaultItems.isEmpty()) {
                        LOGGER.error("default daily battle item can not found. uid:{} itemId:{} defaultItemId:{}", uid, itemId, defaultItemId);
                        return;
                    }
                    Item defaultItem = defaultItems.values().iterator().next();
                    DressItemInfo defaultDressItemInfo = new DressItemInfo()
                            .setDressUpType(mConf.getType())
                            .setItemId(defaultItem.getItemId())
                            .setItemUUID(defaultItem.getId());
                    readyBattleBagInfo.put(mConf.getType(), defaultDressItemInfo);
                }
            }
        }
    }

    public void updateItaBagInfo(AttrItaBagInfo.proto_ItaBagInfo itaBagInfo) {
        Item item = player.getItemManager().getItemByUUID(itaBagInfo.getItemUUID());
        if (item == null) {
            NKErrorCode.InvalidParams.throwError("invalid params, itemUUID err, playerUid:{} itemUUID:{}",
                    player.getUid(), itaBagInfo.getItemUUID());
            return;
        }
        if (player.getItemManager().isExpired(item) || item.getNumber() <= 0) {
            NKErrorCode.ItemIsExpire.throwError("dressUpItem item expire, uid:{} itemUUID:{}",
                    player.getUid(), item.getId());
            return;
        }
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            NKErrorCode.ResNotFound.throwError("item config can not found, uid:{} itemUUID:{} itemId:{}",
                    player.getUid(), item.getId(), item.getItemId());
            return;
        }
        if (itemConf.getType() != ItemType.ItemType_InteractiveToy) {
            NKErrorCode.ItemUnknownItemType.throwError("item is not itaBag, uid:{} itemUUID:{} itemId:{}",
                    player.getUid(), item.getId(), item.getItemId());
            return;
        }
        ItaBagInfo itaBagInfoAttr = new ItaBagInfo().copyFromDto(itaBagInfo);
        if (!checkItaBagBadgeItemEnough(itaBagInfoAttr)) {
            NKErrorCode.ItemNumNotEnough.throwError("itaBag badge item not enough, uid:{} itemUUID:{}",
                    player.getUid(), item.getId());
            return;
        }
        player.getUserAttr().getItaBagData().put(itaBagInfo.getItemUUID(), itaBagInfoAttr);
    }

    private boolean checkItaBagBadgeItemEnough(ItaBagInfo newItaBagInfo) {
        HashMap<Long, Long> totalBadgeEquipItem = getTotalItaBagBadgeItem(newItaBagInfo);
        for (Long badgeId : totalBadgeEquipItem.keySet()) {
            if (player.getBagManager().getItemNumByItemId(badgeId.intValue()) < totalBadgeEquipItem.get(badgeId)) {
                return false;
            }
        }
        return true;

    }

    private HashMap<Long, Long> getTotalItaBagBadgeItem(@Nullable ItaBagInfo newItaBagInfo) {
        HashMap<Long, Long> badgeItemMap = new HashMap<Long, Long>();
        MapAttrObj<Long, ItaBagInfo> itaBagData = player.getUserAttr().getItaBagData();
        for (ItaBagInfo itaBagInfo : itaBagData.values()) {
            if (itaBagInfo.getBadgeInfo().isEmpty()) {
                continue;
            }
            if (newItaBagInfo != null && itaBagInfo.getItemUUID() == newItaBagInfo.getItemUUID()) {
                for (KvLL badgeInfo : newItaBagInfo.getBadgeInfo().values()) {
                    badgeItemMap.put(badgeInfo.getK(),
                            badgeItemMap.getOrDefault(badgeInfo.getK(), 0L) + badgeInfo.getValue());
                }
            } else {
                for (KvLL badgeInfo : itaBagInfo.getBadgeInfo().values()) {
                    badgeItemMap.put(badgeInfo.getK(),
                            badgeItemMap.getOrDefault(badgeInfo.getK(), 0L) + badgeInfo.getValue());
                }
            }
        }
        return badgeItemMap;
    }

    private class OnGetItemDefaultDressForDailyBattle implements EventConsumer {
        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerGetItem)
        private NKErrorCode onEvent(PlayerGetItemEvent event) throws NKRuntimeException {
            for (ItemInfo itemInfo : event.getChangeItemInfoList().getItemsList()) {
                long itemUuid = itemInfo.getUuid();
                int itemId = itemInfo.getItemId();
                ResBackpackItem.Item_ReadyBattleOrnamentationConf itemReadyBattleOrnamentationConf = ReadyBattleOrnamentationDataConfData.getInstance().get(itemId);
                if (itemReadyBattleOrnamentationConf != null){
                    try {
                        updateReadyBattleOrnamentationBagInfo(player,itemUuid,itemReadyBattleOrnamentationConf.getType().toString());
                    } catch (Exception e) {
                        LOGGER.error("update ReadyBattleOrnamentation iterm  error, e:", e);
                    }
                }
            }
            return NKErrorCode.OK;
        }
        @Override
        public boolean isDestroyed() {
            return false;
        }
    }

    public void updateReadyBattleOrnamentationBagInfo(Player player, long itemUUID ,String itemType) {
        MapAttrObj<ItemType, DressItemInfo> readyBattleBagInfo = player.getUserAttr().getPlayerPublicEquipments().getReadyBattleBagInfo();
        Item item = player.getBagManager().getItemManager().getItemByUUID(itemUUID);
        if (item == null) {
            //传0 默认都不选
            if(itemUUID == 0){
                // readyBattleBagInfo.remove(ItemType.valueOf(itemType));
                // 禁止卸下
                LOGGER.error("can not unEquip uid:{} itemUUID:{}", player.getUid(), itemUUID);
                NKErrorCode.InvalidParams.throwErrorSmart();
            }
            LOGGER.info("player {} current ReadyBattle OrnamentationBag dont exist itemUUID {} ", player.getUid(), itemUUID);
            NKErrorCode.ItemReadyBattleOrnamentBagNotExist.throwError("ReadyBattle OrnamentationBag  item  dont exist");
        }
        //判断道具是否过期
        if (player.getItemManager().isExpired(item) || item.getNumber() <= 0) {
            //强制刷新
            player.getItemManager().delExpiredItems();
            LOGGER.info("player {} current ReadyBattle OrnamentationBag  item {} out time itemUUID {} ", player.getUid(),item.getItemId(), itemUUID);
            NKErrorCode.ItemReadyBattleOrnamentBagOutTime.throwError("ReadyBattle OrnamentationBag  item out time");
        }
        //检查是否在备战表中
        ResBackpackItem.Item_ReadyBattleOrnamentationConf itemReadyBattleOrnamentationConf = ReadyBattleOrnamentationDataConfData.getInstance().get(item.getItemId());
        if (itemReadyBattleOrnamentationConf == null){
            NKErrorCode.ItemReadyBattleOrnamentBagNotBelongConf.throwError(" item not in ReadyBattleOrnamentationDataConfData ");
        }

        DressItemInfo dressItemInfo = new DressItemInfo().setDressUpType(itemReadyBattleOrnamentationConf.getType()).setItemId(item.getItemId()).setItemUUID(itemUUID);
        readyBattleBagInfo.put(itemReadyBattleOrnamentationConf.getType(),dressItemInfo);
        long dressedTime = Framework.currentTimeSec();
        TlogFlowMgr.sendDailyBattleItemOutTimeFlow(player,dressItemInfo.getItemId(),dressItemInfo.getItemUUID(),dressItemInfo.getDressUpType().toString(),dressedTime);
    }


    private void tryDressDefault(int slotId) {
        Collection<DressInfo> defaultDresses = player.getUserAttr().getFittingSlots().getDefaultDresses().values();
        if (defaultDresses.isEmpty()) {
            if (player.getUserAttr().getPlayerPublicProfileInfo().getCreateRoleProfile()) {
                LOGGER.error("no default dresses found after createRole, uid:{}", player.getUid());
            }
            return;
        }
        LOGGER.debug("tryDressDefault debug, uid:{} slotId:{}", player.getUid(), slotId);
        for (DressInfo defaultDress : defaultDresses) {
            FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
            if (slot == null) {
                LOGGER.info("tryDressDefault err debug, uid:{} slotId:{}", player.getUid(), slotId);
                return;
            }
            if (slot.getDresses(defaultDress.getDressType()) == null ||
                    slot.getDresses(defaultDress.getDressType()).getDressUuid() == 0) {
                slot.putDresses(defaultDress.getDressType(), new DressInfo()
                        .setDressType(defaultDress.getDressType()).setDressUuid(defaultDress.getDressUuid())
                        .setDressItemId(defaultDress.getDressItemId()));
            }
        }
    }

    // 1.改变槽位id(若有变化)
    // 2.改变PublicEquipments中记录的itemId(若有变化)
    public NKErrorCode changeFittingSlot(int slotId) {
        if (!checkFittingSlotUnlock(slotId)) {
            return NKErrorCode.FittingSlotLocked;
        }

        NKErrorCode funcUnlocked = isFittingSlotAvailable(slotId);
        if (funcUnlocked.hasError()) {
            LOGGER.error("changeFittingSlot failed, function locked, uid:{}, e:{}",
                    player.getUid(), funcUnlocked);
            return funcUnlocked;
        }
        FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (slot == null) {
            tryDressDefault(slotId);
            slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        }
        if (slot == null) {
            return NKErrorCode.UserHasNoDefaultDresses;
        }
        SetAttrObj<Integer> formerDresses = player.getUserAttr().getPlayerPublicEquipments().getDressUpInfos();
        boolean change = false;
        SetAttrObj<Item> newDresses = new SetAttrObj<>();
        ArrayList<DressInfo> newDressInfos = new ArrayList<>(slot.getDresses().values());
        ArrayList<DressInfo> formerDressInfos;

        int formerSlotId = player.getUserAttr().getFittingSlots().getCurrId();
        FittingSlot formerSlot = player.getUserAttr().getFittingSlots().getSlots(formerSlotId);
        if (formerSlot != null) {
            formerDressInfos = new ArrayList<>(formerSlot.getDresses().values());
        } else {
            formerDressInfos = new ArrayList<>();
        }

        for (DressInfo dress : slot.getDresses().values()) {
            if (dress.getDressUuid() == 0) {
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(dress.getDressUuid());
            if (item == null) {
                LOGGER.error("changeFittingSlot get item failed, uuid:{}, uid:{}",
                        dress.getDressUuid(), player.getUid());
                continue;
            }
            if (!formerDresses.contains(item.getItemId()) && !change) {
                change = true;
            }
            newDresses.add(item);
        }
        if (player.getUserAttr().getFittingSlots().getCurrId() != slotId) {
            player.getUserAttr().getFittingSlots().setCurrId(slotId);
            if (!player.getUserAttr().getFittingSlots().getShowIds().contains(slotId)) {
                player.getUserAttr().getFittingSlots().addShowIds(slotId);
                player.getUserAttr().getFittingSlots().removeShowIds(formerSlotId);
            }
        }

        if (!change && newDresses.size() != formerDresses.size()) {
            change = true;
        }

        if (change) {
            player.getUserAttr().getPlayerPublicEquipments().clearDressUpInfos();
            player.getUserAttrMgr().collectAndSyncDirtyToClient(); // 客户端需求
            for (Item dressItem : newDresses.getValues()) {
                player.getUserAttr().getPlayerPublicEquipments().addDressUpInfos(dressItem.getItemId());
            }
            onBackupDressUpInfoChange(slotId);
            new PlayerChangeClothesEvent(player).dispatch();
            new PlayerEquipItemChangeEvent(player).dispatch();
        }
        if (formerSlotId != slotId) {
            TlogFlowMgr.sendAvatarChangeFlow(player, newDressInfos, formerDressInfos, true, slotId);
            // 改变啾灵装扮的装扮数据
            player.getPlayerStarPMgr().onChangeFittingSlot();
        }
        player.getPlayerUgcManager().syncPlayerProfileOnChange(UgcProfileChangeType.DressIds);
        return NKErrorCode.OK;
    }

    public void fillUnlockedSlotsWithDefaultDresses(int start) {
        getUnlockedFittingSlotIds(start);
    }

    private void takeOffFromSlot(int slotId, long dressUuid) {
        FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (slot == null) {
            return;
        }
        for (DressInfo dress : slot.getDresses().values()) {
            if (dress.getDressUuid() == dressUuid) {
                dress.clear();
            }
        }
    }

    private void takeOffFromPublicEquipments(int dressItemId) {
        player.getUserAttr().getPlayerPublicEquipments().getDressUpInfos().remove(dressItemId);
    }

    public void updateInteractionsItem() {
        interactionItems = new HashMap<>();
        HashSet<Integer> removePosSet = new HashSet<>();
        for (Interaction interaction : player.getUserAttr().getInteractions().values()) {
            if (interaction.getType() == InteractionType.IT_Combination) {
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(interaction.getItemUuid());
            if (item != null && !player.getItemManager().isExpired(item) && item.getNumber() > 0) {
                interactionItems.put(interaction.getItemUuid(),interaction.getPos());
            } else {
                if (item != null && item.getExpireMs() > DateUtils.currentTimeMillis()) {
                    // 未过期的限时道具不删除
                    interactionItems.put(interaction.getItemUuid(),interaction.getPos());
                } else {
                    removePosSet.add(interaction.getPos());
                }
            }
        }
        for (int pos : removePosSet) {
            player.getUserAttr().getInteractions().remove(pos);
        }
    }

    public void updateWolfKillInteractionsItem() {
        wolfKillInteractionItems = new HashMap<>();
        HashSet<Integer> removePosSet = new HashSet<>();
        for (Interaction interaction : player.getUserAttr().getWolfKillInteractions().values()) {
            if (interaction.getType() == InteractionType.IT_Combination) {
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(interaction.getItemUuid());
            if (item != null && !player.getItemManager().isExpired(item) && item.getNumber() > 0) {
                wolfKillInteractionItems.put(interaction.getItemUuid(),interaction.getPos());
            } else {
                if (item != null && item.getExpireMs() > DateUtils.currentTimeMillis()) {
                    // 未过期的限时道具不删除
                    wolfKillInteractionItems.put(interaction.getItemUuid(),interaction.getPos());
                } else {
                    removePosSet.add(interaction.getPos());
                }
            }
        }
        for (int pos : removePosSet) {
            player.getUserAttr().getWolfKillInteractions().remove(pos);
        }
    }

    private void removeInteractionsItem(long itemUUid) {
        if (interactionItems != null && interactionItems.containsKey(itemUUid)) {
            int pos = interactionItems.get(itemUUid);
            player.getUserAttr().getInteractions().remove(pos);
        }
    }

    private void removeWolfKillInteractionsItem(long itemUUid) {
        if (wolfKillInteractionItems != null && wolfKillInteractionItems.containsKey(itemUUid)) {
            int pos = wolfKillInteractionItems.get(itemUUid);
            player.getUserAttr().getWolfKillInteractions().remove(pos);
        }
    }

    public void afterLogin() {
//        registerFittingSlotUnlockCondition();

        // 填充 DressInfo 中缺失的 ItemConfigId 的信息
        for (FittingSlot fittingSlot : player.getUserAttr().getFittingSlots().getSlots().values()) {
            for (DressInfo dressInfo : fittingSlot.getDresses().values()) {
                if (dressInfo.getDressUuid() != 0 && dressInfo.getDressItemId() == 0) {
                    Item item = player.getItemManager().getItemByUUID(dressInfo.getDressUuid());
                    dressInfo.setDressItemId(item != null ? item.getItemId() : 0);
                }
            }
        }
        for (DressInfo dressInfo : player.getUserAttr().getFittingSlots().getDefaultDresses().values()) {
            if (dressInfo.getDressUuid() != 0 && dressInfo.getDressItemId() == 0) {
                Item item = player.getItemManager().getItemByUUID(dressInfo.getDressUuid());
                dressInfo.setDressItemId(item != null ? item.getItemId() : 0);
            }
        }
        player.getUserAttr().getFittingSlots().addShowIds(player.getUserAttr().getFittingSlots().getCurrId());

        fillUnlockedSlotsWithDefaultDresses(1);
        changeFittingSlot(player.getUserAttr().getFittingSlots().getCurrId());
        updateInteractionsItem();
        updateWolfKillInteractionsItem();
        // 过期删除的道具没脱下容错
        HashSet<ItemType> needRemoveDress = new HashSet<ItemType>();
        for (DressItemInfo dressItemInfo : player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo().values()) {
            if (player.getItemManager().getItemByUUID(dressItemInfo.getItemUUID()) == null) {
                needRemoveDress.add(dressItemInfo.getDressUpType());
            }
        }
        for (ItemType itemType: needRemoveDress) {
            DressItemInfo dressItemInfo = player.getUserAttr().getPlayerPublicEquipments().removeDressItemInfo(itemType);
            LOGGER.error("check item not exist, uid:{} itemType:{} itemUUID:{} itemId:{}",
                    player.getUid(),itemType,dressItemInfo.getItemUUID(),dressItemInfo.getItemId());
        }
        // 竞速飞车未装备情况下随机装备可用车辆
        equipKart();

        tryDressDefaultItems();
        fixDressItemInfo();
    }


    private void tryDressDefaultItems() {
        List<Integer> dressDefaultItems = MiscConf.getInstance().getMiscConf().getDressDefaultItemsList();
        if (dressDefaultItems.isEmpty()) {
            return;
        }

        for (int itemId : dressDefaultItems) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                continue;
            }

            DressItemInfo dressItemInfo = player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo()
                    .get(itemConf.getType());
            if (dressItemInfo != null && dressItemInfo.getItemUUID() != 0) {
                continue;
            }
            HashMap<Long, Item> playerItems = player.getItemManager().getItemsByItemId(itemConf.getId());
            if (playerItems.isEmpty()){
                continue;
            }
            for (Item item : playerItems.values()) {
                if (player.getItemManager().isExpired(item) || item.getNumber() <= 0) {
                    continue;
                }
                fittingSingleItem(item.getId());
                break;
            }
        }
    }

    private void equipKart(){
        DressItemInfo dressItemInfo = player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo()
                .get(ItemType.ItemType_Kart);
        if (dressItemInfo!=null){
            return;
        }
        ResMisc.MiscConf miscConf = MiscConf.getInstance().getMiscConf();
        if (miscConf == null) {
            return;
        }
        MiscPlayerRegConf playerRegConf = miscConf.getPlayerReg();
        if (playerRegConf == null) {
            return;
        }
        List<Long> kartUUIDs=new ArrayList<>();
        for (ResCommon.Item defaultItem : playerRegConf.getRegDefaultItemList()) {
            Item_BackpackItem backpackItem = BackpackItem.getInstance().get(defaultItem.getItemId());
            if (backpackItem.getType()==ItemType.ItemType_Kart){
                HashMap<Long, Item> playerItems = player.getItemManager().getItemsByItemId(backpackItem.getId());
                if (playerItems==null||playerItems.size()==0){
                    continue;
                }
                kartUUIDs.addAll(playerItems.keySet());
            }
        }
        if (kartUUIDs.size()>0){
            player.getItemEquipManager().fittingSingleItem(kartUUIDs.get(RandomUtils.nextInt(0,kartUUIDs.size())));
        }
    }

    private void registerFittingSlotUnlockCondition() {
        HashMap<Integer, ConditionGroup> matchTypeConditionAttr = new HashMap<>();
        for (CloakroomUnlock cloakroomUnlockConfig : FittingSlotUnlockConf.getInstance().dataArray) {
            ConditionGroup conditionGroupAttr = matchTypeConditionAttr.get(cloakroomUnlockConfig.getId());
            if (conditionGroupAttr == null) {
                conditionGroupAttr = new ConditionGroup();
                matchTypeConditionAttr.put(cloakroomUnlockConfig.getId(), conditionGroupAttr);
            }
            PlayerConditionGroup conditionGroup = new PlayerConditionGroup(player,
                    cloakroomUnlockConfig.getUnlockCondition(), conditionGroupAttr);
            conditionGroup.initConditionGroup(new PlayerConditionInitData(player));
        }
    }

    private boolean checkFittingSlotUnlock(int slotId) {
        CloakroomUnlock fittingSlotConfig = FittingSlotUnlockConf.getInstance().get(slotId);
        if (fittingSlotConfig == null) {
            return false;
        }
        FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (slot != null && slot.getUnlock()) {
            return true;
        }
        PlayerConditionGroup conditionGroup = new PlayerConditionGroup(player, fittingSlotConfig.getUnlockCondition(),
                new ConditionGroup());
        conditionGroup.initConditionGroup(new PlayerConditionInitData(player));
        if (conditionGroup.checkComplete()) {
            conditionGroup.unregisterConditionGroup();
            if (slot == null) {
                slot = new FittingSlot().setId(slotId);
                player.getUserAttr().getFittingSlots().putSlots(slotId, slot);
            }
            return true;
        }
        conditionGroup.unregisterConditionGroup();
        return false;
    }

    // 永久解锁
    public void unlockedFittingSlot(int slotId) {
        FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (slot == null) {
            slot = new FittingSlot().setId(slotId).setUnlock(true);
            player.getUserAttr().getFittingSlots().putSlots(slotId, slot);
        } else {
            slot.setUnlock(true);
        }
        tryDressDefault(slotId);
    }

    public List<Integer> getUnlockedFittingSlotIds(int start) {
        ArrayList<Integer> unlockSlots = new ArrayList<>();
        List<CloakroomUnlock> configs = FittingSlotUnlockConf.getInstance().dataArray;
        for (CloakroomUnlock cloakroomUnlockConfig : configs) {
            if (checkFittingSlotUnlock(cloakroomUnlockConfig.getId())) {
                unlockSlots.add(cloakroomUnlockConfig.getId());
                if (cloakroomUnlockConfig.getId() >= start) {
                    tryDressDefault(cloakroomUnlockConfig.getId());
                }
            } else if (cloakroomUnlockConfig.getId() != player.getUserAttr().getFittingSlots().getCurrId()) {
                player.getUserAttr().getFittingSlots().removeShowIds(cloakroomUnlockConfig.getId());
            }
        }
        return unlockSlots;
    }

    public NKErrorCode isFittingSlotAvailable(int slotId) {
        Collection<DressInfo> defaultDresses = player.getUserAttr().getFittingSlots().getDefaultDresses().values();
        if (defaultDresses.size() == 0) {
            LOGGER.error("no default dresses found, uid:{}", player.getUid());
            return NKErrorCode.UserHasNoDefaultDresses;
        }
        if (!checkFittingSlotUnlock(slotId)) {
            return NKErrorCode.FittingSlotLocked;
        }
        return NKErrorCode.OK;
    }

    public void onBackupDressUpInfoChange(int slotId) {
        int defaultSlotId = MiscConf.getInstance().getMiscConf().getFittingSlotIdWithNoOutlookGroup();
        if (slotId != defaultSlotId) {
            return;
        }
        FittingSlot slotInfo = player.getUserAttr().getFittingSlots().getSlots(defaultSlotId);
        if (slotInfo == null) {
            LOGGER.error("onBackupDressUpInfoChange get FittingSlotIdWithNoOutlookGroup id:{} dressInfo error, uid:{}",
                    defaultSlotId, player.getUid());
            return;
        }
        player.getUserAttr().getPlayerPublicEquipments().clearBackupDressUpInfos();
        for (DressInfo dress : slotInfo.getDresses().values()) {
            Item item = player.getItemManager().getItemByUUID(dress.getDressUuid());
            if (item == null) {
                LOGGER.error("onBackupDressUpInfoChange get item failed, uuid:{}, uid:{}",
                        dress.getDressUuid(), player.getUid());
                continue;
            }
            player.getUserAttr().getPlayerPublicEquipments().addBackupDressUpInfos(item.getItemId());
        }
    }

    public NKErrorCode showFittingSlot(int slotId, boolean execOrRevoke) {
        FittingSlots fittingSlots = player.getUserAttr().getFittingSlots();
        if (execOrRevoke) {
            if (fittingSlots.getShowIds().contains(slotId)) {
                return NKErrorCode.OK;
            }
            NKErrorCode checkAvailableRet = isFittingSlotAvailable(slotId);
            if (!checkAvailableRet.isOk()) {
                return checkAvailableRet;
            }
            fittingSlots.addShowIds(slotId);
        } else {
            if (fittingSlots.getCurrId() == slotId) {
                return NKErrorCode.FittingSlotShowRevokeCurrent;
            }
            fittingSlots.removeShowIds(slotId);
        }
        return NKErrorCode.OK;
    }

    public void changeProfileTheme(int id) {
        if (checkProfileThemeActive(id)) {
            int oldItemId = player.getUserAttr().getPlayerPublicEquipments().getProfileTheme();
            if (oldItemId == id) { // 卸下
                id = 0;
            }
            player.getUserAttr().getPlayerPublicEquipments().setProfileTheme(id);
            TlogFlowMgr.sendPlayerBackgroundChangeFlow(player, id, oldItemId, true);
        } else {
            NKErrorCode.UnknownError.throwError("ProfileTheme not Active");
        }
    }

    public boolean checkProfileThemeActive(int id) {
        ProfileThemeConifg profileThemeConfig = ProfileThemeData.getInstance().get(id);
        if (profileThemeConfig == null) {
            return false;
        }
        for (int itemId : profileThemeConfig.getItemsList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                continue;
            }
            if (itemConf.getType() == ItemType.ItemType_Background
                    && player.getBagManager().getItemNumByItemId(itemId) > 0) {
                return true;
            }
            if (player.getBagManager().getItemNumByItemIdIgnoreTemp(itemId) > 0) {
                return true;
            }
        }
        return false;
    }

    public void changeDressUpItemStatus(long itemUUID, int status) {
        int currentId = player.getUserAttr().getFittingSlots().getCurrId();
        FittingSlot fittingSlot = player.getUserAttr().getFittingSlots().getSlots(currentId);
        if (fittingSlot == null) {
            NKErrorCode.InvalidParams.throwError("FittingSlot Info Empty");
            return;
        }
        DressInfo suitDressInfo = fittingSlot.getDresses(ItemType.ItemType_Suit);
        if (suitDressInfo == null) {
            NKErrorCode.InvalidParams.throwError("Suit DressInfo Empty");
            return;
        }
        if (suitDressInfo.getDressUuid() != itemUUID) {
            NKErrorCode.InvalidParams.throwError("itemUUID error");
            return;
        }
        Item item = player.getBagManager().getItemManager().getItemByUUID(itemUUID);
        if (item == null) {
            return;
        }
        ItemStatusInfo itemSuitStatusConfig = ItemStatusChangeData.getInstance().get(suitDressInfo.getDressItemId(), status);
        if (itemSuitStatusConfig == null) {
            NKErrorCode.SuitItemCanNotChangeStatus.throwError("SuitItemCanNotChangeStatus itemId:{}",suitDressInfo.getDressItemId());
            return;
        }
        if (item.getStatus() == status) {
            return;
        }
        item.setStatus(status);
        updateDressUpDetailInfoStatus(item);
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.ItemStatusChange);
    }

    public void changeFittingItemStatus(long itemUUID, int status) {
        Item item = player.getBagManager().getItemManager().getItemByUUID(itemUUID);
        if (item == null) {
            return;
        }
        ItemStatusInfo itemSuitStatusConfig = ItemStatusChangeData.getInstance().get(item.getItemId(), status);
        if (itemSuitStatusConfig == null) {
            NKErrorCode.SuitItemCanNotChangeStatus.throwError("SuitItemCanNotChangeStatus itemId:{}",item.getItemId());
            return;
        }
        if (item.getStatus() == status) {
            return;
        }
        item.setStatus(status);
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            return;
        }
        DressItemInfo dressItemInfo = player.getUserAttr()
                .getPlayerPublicEquipments().getDressItemInfo(itemConf.getType());
        if (itemConf.getType() == ItemType.ItemType_Surroundings // 环绕物不需要 DressItemInfo
                || (dressItemInfo != null && dressItemInfo.getItemUUID() == item.getId())) {
            updateDressUpDetailInfoStatus(item);
            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.ItemStatusChange);
        }
    }


    public void randomSlotId() {
        try {
            if (!player.getUserAttr().getFittingSlots().getRandomOpen()) {
                return;
            }
            RandomByWeight<Integer> randomWeight = new RandomByWeight<>(false);
            for (FittingSlot fittingSlot : player.getUserAttr().getFittingSlots().getSlots().values()) {
                if (!fittingSlot.getRandom()) {
                    continue;
                }
                randomWeight.add(fittingSlot.getId(),100);
            }
            if (randomWeight.size() == 0) {
                return;
            }
            int randomSlotId =  randomWeight.rand();
            int beforeSlotId = player.getUserAttr().getFittingSlots().getCurrId();
            if (randomSlotId != beforeSlotId) {
                player.getUserAttr().getFittingSlots().setTmpRandomSlot(beforeSlotId);
                changeFittingSlot(randomSlotId);
            }
        } finally {
            player.getPlayerBattleMgr().updateBattleDsPlayerPublicInfo();
        }
    }

    public void resetSlotId() {
        int beforeSlotId = player.getUserAttr().getFittingSlots().getTmpRandomSlot();
        if (beforeSlotId > 0) {
            changeFittingSlot(beforeSlotId);
            player.getUserAttr().getFittingSlots().setTmpRandomSlot(0);
        }
    }
    
    public void updateFashionScores(Builder playerEquipmentsBuilder) {
        NKPair<Integer, Integer> fashionScores = BackpackItem.getInstance().getBattleFashionScore(player.getUid(),playerEquipmentsBuilder,player.getClientVersion64());
        playerEquipmentsBuilder.clearFashionScores();
        playerEquipmentsBuilder.addFashionScores(fashionScores.getKey());
        playerEquipmentsBuilder.addFashionScores(fashionScores.getValue());
        LOGGER.debug("uid:{} fashionValue:{} fashionScore:{}",player.getUid(),playerEquipmentsBuilder.getFashionValue(),playerEquipmentsBuilder.getFashionScoresList());
    }

    private void updateDressUpDetailInfos() {
        player.getUserAttr().getPlayerPublicEquipments().clearDressUpDetailInfos();
        for (FittingSlot fittingSlot : player.getUserAttr().getFittingSlots().getSlots().values()) {
            for (DressInfo dressInfo : fittingSlot.getDresses().values()) {
                if (dressInfo.getDressUuid() == 0) {
                    continue;
                }
                Item item = player.getItemManager().getItemByUUID(dressInfo.getDressUuid());
                if (item == null) {
                    LOGGER.error("updateDressUpDetailInfos get item failed, uuid:{}, uid:{}",
                            dressInfo.getDressUuid(), player.getUid());
                    continue;
                }
                if (ItemStatusChangeData.getInstance().get(item.getItemId(),item.getStatus()) == null) {
                    continue;
                }
                updateDressUpDetailInfoStatus(item);
                /*Item_BackpackItem backpackItem = BackpackItem.getInstance().get(item.getItemId());
                if (null != backpackItem && ItemType.ItemType_Surroundings == backpackItem.getType()) {
                    try {
                        player.getItemEquipManager().fittingSingleItem(ItemType.ItemType_Surroundings.getNumber());
                    } catch (Exception e) {
                        LOGGER.error("saveFittingSlot fittingSingleItem error", e);
                    }
                }*/
            }
        }
        for (DressItemInfo dressItemInfo : player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo().values()) {
            if (dressItemInfo.getItemUUID() == 0) {
                continue;
            }
            Item item = player.getItemManager().getItemByUUID(dressItemInfo.getItemUUID());
            if (item == null) {
                LOGGER.error("updateDressUpDetailInfos get item failed, uuid:{}, uid:{}",
                        dressItemInfo.getItemUUID(), player.getUid());
                continue;
            }
            if (ItemStatusChangeData.getInstance().get(item.getItemId(),item.getStatus()) == null) {
                continue;
            }
            updateDressUpDetailInfoStatus(item);
        }
    }

    private void updateDressUpDetailInfoStatus(Item item) {
        player.getUserAttr()
                .getPlayerPublicEquipments()
                .putDressUpDetailInfos(item.getItemId(),
                        new DressUpDetailInfo().setItemId(item.getItemId()).setStatus(item.getStatus()).setShowStatus(item.getStatus()));
    }

    public void changeDefaultDress(ChangeDefaultDress_C2S_Msg reqMsg) {
        if (!player.getUserAttr().getPlayerPublicProfileInfo().getCreateRoleProfile()) {
            NKErrorCode.UnknownError.throwError("changeDefaultDress not create role finished");
            return;
        }
        if (player.getUserAttr().getFittingSlots().getDefaultDressChange()) {
            return;
        }
        if (reqMsg.getIsGiveUp()) {
            player.getUserAttr().getFittingSlots().setDefaultDressChange(true);
            return;
        }

        // 道具id种类合法性检查
        if (reqMsg.getItemIdsCount() != DEFAULT_ITEM_ID_COUNT) {
            NKErrorCode.CreateRoleItemInvalid.throwError(
                    "changeDefaultDress itemIds count is invalid, param value:{}, expect value:{}",
                    reqMsg.getItemIdsCount(), DEFAULT_ITEM_ID_COUNT);
            return;
        }
        int face = -1;
        int suit = -1;
        Map<Integer, Item_BackpackItem> confs = new HashMap<>();
        for (int itemId : reqMsg.getItemIdsList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                NKErrorCode.ResNotFound.throwError("item config err, itemId:{}",itemId);
                return;
            }
            if (itemConf.getType() == ItemType.ItemType_Face) {
                var faceConf = PlayerStarterFaceConf.getInstance().get(itemConf.getId());
                if (faceConf == null || faceConf.getNotDefaultChosen()) {
                    NKErrorCode.CreateRoleItemInvalid
                            .throwError("changeDefaultDress face:{}, not in PlayerStarterFaceConfData", itemConf.getId());
                    return;
                }
                face = itemConf.getId();
            }
            if (itemConf.getType() == ItemType.ItemType_Suit) {
                var suitConf = PlayerStarterDressConf.getInstance().get(itemConf.getId());
                if (suitConf == null || suitConf.getNotDefaultChosen()) {
                    NKErrorCode.CreateRoleItemInvalid
                            .throwError("changeDefaultDress suit:{}, not in PlayerStarterDressConfData", itemConf.getId());
                    return;
                }
                suit = itemConf.getId();
            }
            confs.put(itemId,itemConf);
        }
        if (face == -1 || suit == -1) {
            NKErrorCode.CreateRoleItemInvalid.throwError("changeDefaultDress param lack of face or suit, face:{}, suit:{}, req:{}",
                    face, suit, reqMsg);
            return;
        }
        for (DressInfo dressInfo : player.getUserAttr().getFittingSlots().getDefaultDresses().values()) {
            Item item = player.getItemManager().getItemByUUID(dressInfo.getDressUuid());
            if (item == null) {
                NKErrorCode.UnknownError.throwError("get item err, uuid:{}",dressInfo.getDressUuid());
                return;
            }
        }

        // 是否已有永久的创角道具
        Item faceItem = null;
        Item suitItem = null;
        HashMap<Long, Item> faceItems = player.getItemManager().getItemsByItemId(face);
        if (!faceItems.isEmpty()) {
            for (Item item: faceItems.values()) {
                if (!player.getItemManager().hasExpireTime(item)) {
                    faceItem = item;
                    break;
                }
            }
        }
        HashMap<Long, Item> suitItems = player.getItemManager().getItemsByItemId(suit);
        if (!suitItems.isEmpty()) {
            for (Item item: suitItems.values()) {
                if (!player.getItemManager().hasExpireTime(item)) {
                    suitItem = item;
                    break;
                }
            }
        }

        NKErrorCode profileRet = player.getProfileManager().createRoleProfile(reqMsg.getNickname(), reqMsg.getGender());
        if (profileRet.hasError()) {
            profileRet.throwError("changeDefaultDress create profile failed, uid:{}, nick:{}, gender:{}",
                    player.getUid(), reqMsg.getNickname(), reqMsg.getGender());
            return;
        }

        String before = player.getUserAttr().getFittingSlots().toString();

        HashMap<Long,Integer>  itemChangeInfo = new HashMap<>(); // 道具ID替换
        String billNo = BillNoIdGenerator.getBusinessBillNo("ChangeDefaultDress");
        ChangedItems exchangeItems = new ChangedItems(ItemChangeReason.ICR_ChangeDefaultDress_VALUE,"");
        exchangeItems.setBusBillNo(billNo);
        // 旧的创角道具
        Item beforeFaceItem = null;
        Item beforeSuitItem = null;
        boolean needReload = false;
        for (DressInfo dressInfo : player.getUserAttr().getFittingSlots().getDefaultDresses().values()) {
            Item item = player.getItemManager().getItemByUUID(dressInfo.getDressUuid());
            if (item == null) {
                NKErrorCode.UnknownError.throwError("get item err, uuid:{}",dressInfo.getDressUuid());
                return;
            }
            if (dressInfo.getDressType() == ItemType.ItemType_Suit) {
                if (suitItem == null){
                    item.setItemId(suit);
                    needReload = true;
                    itemChangeInfo.put(dressInfo.getDressUuid(),suit);
                } else {
                    if (suitItem.getId() != dressInfo.getDressUuid()) {
                        beforeSuitItem = item;
                        dressInfo.setDressUuid(suitItem.getId());
                        exchangeItems.mergeItemInfoMulti(confs.get(suit).getExceedReplaceItemList(),1);
                    }
                }
                dressInfo.setDressItemId(suit);
            }
            if (dressInfo.getDressType() == ItemType.ItemType_Face) {
                if (faceItem == null){
                    item.setItemId(face);
                    needReload = true;
                    itemChangeInfo.put(dressInfo.getDressUuid(),face);
                } else {
                    if (faceItem.getId() != dressInfo.getDressUuid()) {
                        beforeFaceItem = item;
                        dressInfo.setDressUuid(faceItem.getId());
                        exchangeItems.mergeItemInfoMulti(confs.get(face).getExceedReplaceItemList(),1);
                    }
                }
                dressInfo.setDressItemId(face);
            }
        }
        // 修改item的itemId itemId2UUIDs 需要重新加载
        if (needReload) {
            player.getItemManager().initItemId2UUIDs();
        }

        for (FittingSlot fittingSlot : player.getUserAttr().getFittingSlots().getSlots().values()) {
            boolean needUpdate = false;
            ArrayList<DressInfo> formerDresses = new ArrayList<>(fittingSlot.clone().getDresses().values());
            for (DressInfo dressInfo : fittingSlot.getDresses().values()) {
                if (itemChangeInfo.containsKey(dressInfo.getDressUuid())) {
                    needUpdate = true;
                    dressInfo.setDressItemId(itemChangeInfo.get(dressInfo.getDressUuid()));
                } else {
                    if (beforeSuitItem != null && dressInfo.getDressUuid() == beforeSuitItem.getId()) {
                        dressInfo.setDressUuid(suitItem.getId()).setDressItemId(suitItem.getItemId());
                        needUpdate = true;
                    }
                    if (beforeFaceItem != null && dressInfo.getDressUuid() == beforeFaceItem.getId()) {
                        dressInfo.setDressUuid(faceItem.getId()).setDressItemId(faceItem.getItemId());
                        needUpdate = true;
                    }
                }
            }
            if (needUpdate) {
                ArrayList<DressInfo> putList = new ArrayList<>(fittingSlot.getDresses().values());
                TlogFlowMgr.sendAvatarChangeFlow(player, putList, formerDresses, false, fittingSlot.getId());
            }
        }
        if (beforeFaceItem != null) {
            player.getItemManager().updateItemNumByUUID(beforeFaceItem.getId(), (int) -beforeFaceItem.getNumber(),
                    exchangeItems.getBusBillNo(), exchangeItems.getChangeReason());
        }
        if (beforeSuitItem != null) {
            player.getItemManager().updateItemNumByUUID(beforeSuitItem.getId(), (int) -beforeSuitItem.getNumber(),
                    exchangeItems.getBusBillNo(), exchangeItems.getChangeReason());
        }
        player.getUserAttr().getFittingSlots().setDefaultDressChange(true);

        // CurrentFittingSlot dressDefault
        FittingSlot currentFittingSlot = player.getUserAttr().getFittingSlots().getSlots(player.getUserAttr().getFittingSlots().getCurrId());
        ArrayList<DressInfo> formerDresses = new ArrayList<>(currentFittingSlot.clone().getDresses().values());
        for (DressInfo defaultDress : player.getUserAttr().getFittingSlots().getDefaultDresses().values()) {
            if (currentFittingSlot.getDresses(defaultDress.getDressType()) == null ||
                    currentFittingSlot.getDresses(defaultDress.getDressType()).getDressUuid() == 0) {
                currentFittingSlot.putDresses(defaultDress.getDressType(), new DressInfo()
                        .setDressType(defaultDress.getDressType()).setDressUuid(defaultDress.getDressUuid())
                        .setDressItemId(defaultDress.getDressItemId()));
            } else {
                currentFittingSlot.getDresses(defaultDress.getDressType()).setDressUuid(defaultDress.getDressUuid())
                        .setDressItemId(defaultDress.getDressItemId());
            }
        }
        changeFittingSlot(player.getUserAttr().getFittingSlots().getCurrId());
        ArrayList<DressInfo> putList = new ArrayList<>(currentFittingSlot.getDresses().values());
        TlogFlowMgr.sendAvatarChangeFlow(player, putList, formerDresses, false, currentFittingSlot.getId());
        if (!exchangeItems.getChangeItems().isEmpty()) {
            player.getBagManager().AddItems2(exchangeItems);
        }
        String after = player.getUserAttr().getFittingSlots().toString();
        StringBuilder log = new StringBuilder();
        log.append("changeDefaultDress debug, uid:").append(player.getUid())
                        .append(" msg:").append(reqMsg)
                        .append(" itemChangeInfo:").append(itemChangeInfo);
        if (faceItem != null) {
            log.append(" faceItem:").append(faceItem.getId()).append(" ").append(faceItem.getItemId());
        }
        if (suitItem != null) {
            log.append(" suitItem:").append(suitItem.getId()).append(" ").append(suitItem.getItemId());
        }
        if (beforeFaceItem != null) {
            log.append(" beforeFaceItem:").append(beforeFaceItem.getId()).append(" ").append(beforeFaceItem.getItemId());
        }
        if (beforeSuitItem != null) {
            log.append(" beforeSuitItem:").append(beforeSuitItem.getId()).append(" ").append(beforeSuitItem.getItemId());
        }
        log.append(" before:").append(before).append(" after:").append(after).append(" exchange:").append(exchangeItems.getChangeItems());
        LOGGER.info(log.toString());
        player.getBagManager().getSuitCount();
    }
    
    private void fixDressItemInfo() {
        MapAttrObj<ItemType, DressItemInfo> dressItemInfos = player.getUserAttr()
                .getPlayerPublicEquipments().getDressItemInfo();

        StringBuilder change = new StringBuilder();
        Set<ItemType> clearDressType = new HashSet<>();
        for (DressItemInfo dressItemInfo : dressItemInfos.values()) {
            if (!canFittingSingleItem(dressItemInfo.getDressUpType())) {
                clearDressType.add(dressItemInfo.getDressUpType());
                change.append(dressItemInfo.getDressUpType()).append(" remove \t");
                continue;
            }
            if (dressItemInfo.getItemUUID() > 0) {
                Item item = player.getItemManager().getItemByUUID(dressItemInfo.getItemUUID());
                if (item == null) {
                    dressItemInfo.setItemUUID(0);
                    dressItemInfo.setItemId(0);
                    change.append(dressItemInfo.getDressUpType()).append(" clear \t");
                    continue;
                }
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
                if (itemConf == null) {
                    dressItemInfo.setItemUUID(0);
                    dressItemInfo.setItemId(0);
                    change.append(dressItemInfo.getDressUpType()).append(" clear \t");
                    continue;
                }
            }
        }
        if (!clearDressType.isEmpty()) {
            for (ItemType itemType : clearDressType) {
                dressItemInfos.remove(itemType);
            }
        }
        if (dressItemInfos.isDirty()) {
            LOGGER.info("fixDressItemInfo debug, uid:{} change:{}", player.getUid(), change.toString());
        }
    }

    public void afterLoad() {
        registerAcquireEquipPopUpEquipSlotCondition();
    }

    private void registerAcquireEquipPopUpEquipSlotCondition() {
        boolean acquireEquipPopUpEquipSlot = player.getUserAttr().getPlayerPublicGameSettings()
                .getAcquireEquipPopUpEquipSlot();
        if (!acquireEquipPopUpEquipSlot) {
            //注册解锁条件
            BackpackSundryConfig backpackSundryConfig = BackpackSundryConfigData.getInstance().getByType("BSC_AcquireEquipPopUpEquipSlot");
            if (backpackSundryConfig == null) {
                LOGGER.error("backpackSundryConfig not found, type:{}", "BSC_AcquireEquipPopUpEquipSlot");
                return;
            }
            CommonConditionGroup commonConditionAttr = player.getUserAttr()
                    .getCommonConditionAttr(ContionGroupType.CGT_AcquireEquipPopUpEquipSlot);
            if (commonConditionAttr == null) {
                commonConditionAttr = new CommonConditionGroup().setContionGroupType(ContionGroupType.CGT_AcquireEquipPopUpEquipSlot);
                player.getUserAttr().getCommonConditionAttr().put(ContionGroupType.CGT_AcquireEquipPopUpEquipSlot,
                        commonConditionAttr);
            }
            ConditionGroupInfo attrConditionGroup = commonConditionAttr.getAttrConditionGroup(backpackSundryConfig.getId());
            if (attrConditionGroup == null) {
                attrConditionGroup = new ConditionGroupInfo().setId(backpackSundryConfig.getId());
                commonConditionAttr.getAttrConditionGroup().put(backpackSundryConfig.getId(), attrConditionGroup);
            }
            AcquireEquipPopUpEquipSlotConditionGroup conditionGroup = new AcquireEquipPopUpEquipSlotConditionGroup(player,
                    backpackSundryConfig.getConditionGroup(), attrConditionGroup.getConditionGroup());
        }
    }
}
