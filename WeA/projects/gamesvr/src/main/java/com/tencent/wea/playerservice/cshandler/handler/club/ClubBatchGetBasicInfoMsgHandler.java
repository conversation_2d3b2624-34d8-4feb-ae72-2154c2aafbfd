package com.tencent.wea.playerservice.cshandler.handler.club;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsClub;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.common.ClubBasicInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ClubBatchGetBasicInfoMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ClubBatchGetBasicInfoMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsClub.ClubBatchGetBasicInfo_C2S_Msg reqMsg = (CsClub.ClubBatchGetBasicInfo_C2S_Msg)request;
        CsClub.ClubBatchGetBasicInfo_S2C_Msg.Builder rspMsg = CsClub.ClubBatchGetBasicInfo_S2C_Msg.newBuilder();

        List<ClubBasicInfo> infoList = player.getClubMgr().batchGetBasicInfo(reqMsg.getClubIdsList());
        rspMsg.addAllBasicInfos(infoList);
        return rspMsg;
    }
}