package com.tencent.wea.scripts

import com.google.protobuf.Message
import com.google.protobuf.util.JsonFormat
import com.tencent.nk.groovy.GroovyScript
import com.tencent.timiCoroutine.LocalService
import com.tencent.wea.framework.GSEngine
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler
import com.tencent.wea.playerservice.player.Player
import com.tencent.wea.playerservice.playerref.PlayerRefMgr
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

import java.util.concurrent.Callable
import java.util.function.Consumer

class Zihaoyuan implements GroovyScript {
    private static final Logger LOGGER = LogManager.getLogger("groovyScripts")

    /******************************************* util *********************************************/
    static <T extends Message.Builder> T merge(T builder, String json) {
        JsonFormat.parser().ignoringUnknownFields().merge(json, builder);
        return builder;
    }

    static String runPlayerScript(String[] args, int uidIndex, Consumer<Player> consumer) {
        LOGGER.error("----------> args={}", args.toString())
        return runPlayerScript(Long.valueOf(args[uidIndex]), consumer);
    }

    static String runPlayerScript(long uid, Consumer<Player> consumer) {
        runOnPlayerService(uid, () -> {
            LOGGER.error("------------------------> runPlayerScript start <------------------------")
            try {
                Player player = PlayerRefMgr.getInstance().groovyGetPlayer(uid);
                consumer.accept(player);
            } catch (Exception e) {
                LOGGER.error("runPlayerScript error", e)
            }
            LOGGER.error("------------------------> runPlayerScript  end  <------------------------")
        })
        return "success";
    }

    static void runOnPlayerService(long uid, Callable<Void> callable) {
        def playerService = GSEngine.getSpecInstance().getPlayerService()
        LocalService.DoNotDirectCall.GroovyTest.runJob(playerService, uid, callable, "GroovyScript", false)
    }

    static void sendC2SMsg(Player player, Class<? extends AbstractGsClientRequestHandler> clazz, Message request) {
        try {
            LOGGER.error("groovy_sendC2SMsg-start, uid={} req={}", player.getUid(), request)
            AbstractGsClientRequestHandler handler = ((AbstractGsClientRequestHandler) clazz.getDeclaredConstructor().newInstance())
            Message.Builder response = handler.handle(player, null, request)
            LOGGER.error("groovy_sendC2SMsg-end, uid={}, rsp={}", player.getUid(), response);
        } catch (Exception e) {
            LOGGER.error("groovy_sendC2SMsg-error, uid={}", player.getUid(), e)
        }
    }

    /******************************************** debug *******************************************/
    static long ownerUid = 1950140737488361328;

    @Override
    String runScript(String[] args) {
        return runPlayerScript(ownerUid, player -> {

        })
    }

    // groovy scripts/Zihaoyuan.groovy run uid
    static String run(String[] args) {
        return runPlayerScript(args, 0,player -> {
            LOGGER.error(player.getUserAttr().getCopyDbBuilder());

        })
    }

    // groovy scripts/Zihaoyuan.groovy test
    static String test(String[] args) {
        LOGGER.error("----------> args={}", args.toString())

        return "success";
    }
}
