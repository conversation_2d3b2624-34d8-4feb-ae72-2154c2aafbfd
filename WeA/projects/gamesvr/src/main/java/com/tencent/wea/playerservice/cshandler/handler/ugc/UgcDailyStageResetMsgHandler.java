package com.tencent.wea.playerservice.cshandler.handler.ugc;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.ugc.manager.UgcDailyStageMgr;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsUgc;
import com.tencent.wea.protocol.common.UgcDailyStageResetInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class UgcDailyStageResetMsgHandler extends AbstractGsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(UgcDailyStageResetMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsUgc.UgcDailyStageReset_C2S_Msg reqMsg = (CsUgc.UgcDailyStageReset_C2S_Msg) request;
        CsUgc.UgcDailyStageReset_S2C_Msg.Builder res = CsUgc.UgcDailyStageReset_S2C_Msg.newBuilder();
        LOGGER.debug("UgcDailyStageReset_C2S_Msg req:{}", request.toString());
        UgcDailyStageMgr.DailyStageResetInfoResult stageResult = player.getUgcDailyStageMgr().resetDailyStage();
        if (stageResult.result != 0) {
            LOGGER.error("resetDailyStage is error:{} uid:{}", stageResult.result, player.getUid());
            NKErrorCode.forNumber(stageResult.result).throwError("");
        }
        UgcDailyStageResetInfo.Builder resetInfo = UgcDailyStageResetInfo.newBuilder();
        resetInfo.setResetCount(stageResult.resetCount);
        resetInfo.setResetTotalCount(stageResult.resetTotalCount);
        res.setResetInfo(resetInfo);
        return res;
    }
}
