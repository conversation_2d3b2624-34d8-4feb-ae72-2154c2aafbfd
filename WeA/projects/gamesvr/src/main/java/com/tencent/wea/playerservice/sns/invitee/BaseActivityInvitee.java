package com.tencent.wea.playerservice.sns.invitee;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.IEnumedException;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.wea.attr.PlayerSnsInvitationData;
import com.tencent.wea.playerservice.activity.implement.BaseActivity;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.AttrPlayerSnsInvitationData.proto_PlayerSnsInvitationDataOrBuilder;
import com.tencent.wea.protocol.common.SnsInvitationData;
import com.tencent.wea.protocol.common.SnsInvitationParam;
import com.tencent.wea.tcaplus.TcaplusDb.SnsInvitationTableOrBuilder;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public abstract class BaseActivityInvitee<T extends BaseActivity> implements SnsInvitee, SnsInviteeGmHandler {

    private static final Logger LOGGER = LogManager.getLogger(BaseActivityInvitee.class);

    @Override
    @SuppressWarnings("unchecked")
    public NKErrorCode check(Player player, SnsInvitationTableOrBuilder invitation) {
        int activityId = invitation.getData().getActivity().getActivityId();
        BaseActivity activity = player.getActivityManager().getRunningActivity(activityId);
        if (activity == null) {
            LOGGER.error("failed to find activity, uid:{} activity:{}", player.getUid(), activityId);
            return NKErrorCode.ActivityNotExist;
        }

        if (activity.getType() != getActivityType()) {
            LOGGER.error("failed to find activity, uid:{} activity:{} expect:{} current:{}", player.getUid(),
                    activityId, getActivityType(), activity.getType());
            return NKErrorCode.ActivityUnitNotExist;
        }

        try {
            NKErrorCode errorCode = check(player, activityId, (T) activity, invitation);
            if (!errorCode.isOk()) {
                LOGGER.error("failed to check activity, uid:{} activity:{} invitation:{} err:{}", player.getUid(),
                        activityId, Pb2JsonUtil.getPbMsg(invitation), errorCode);
                return errorCode;
            }

            return NKErrorCode.OK;
        } catch (Exception e) {
            NKErrorCode errorCode =
                    (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                            : NKErrorCode.UnknownError;
            LOGGER.error("failed to check activity, uid:{} activity:{} invitation:{} err:{}", player.getUid(),
                    activityId, Pb2JsonUtil.getPbMsg(invitation), e);
            return errorCode;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public void accept(Player player, SnsInvitationTableOrBuilder acceptedInvitation) {
        int activityId = acceptedInvitation.getData().getActivity().getActivityId();
        BaseActivity activity = player.getActivityManager().getRunningActivity(activityId);
        if (activity == null) {
            LOGGER.error("failed to find activity, uid:{} activity:{}", player.getUid(), activityId);
            return;
        }

        if (activity.getType() != getActivityType()) {
            LOGGER.error("failed to find activity, uid:{} activity:{} expect:{} current:{}", player.getUid(),
                    activityId, getActivityType(), activity.getType());
            return;
        }

        try {
            accept(player, activityId, (T) activity, acceptedInvitation);
        } catch (Exception e) {
            LOGGER.error("failed to check activity, uid:{} activity:{} invitation:{} err:{}", player.getUid(),
                    activityId, Pb2JsonUtil.getPbMsg(acceptedInvitation), e);
        }
    }

    @Override
    public boolean isExpired(Player player, long currentMs, int cfgId,
            proto_PlayerSnsInvitationDataOrBuilder data) {
        int activityId = data.getActivity().getActivityId();
        BaseActivity activity = player.getActivityManager().getRunningActivity(activityId);
        if (activity == null) {
            LOGGER.error("failed to find activity, uid:{} activity:{}", player.getUid(), activityId);
            return true;
        }

        if (activity.getType() != getActivityType()) {
            LOGGER.error("failed to find activity, uid:{} activity:{} expect:{} current:{}", player.getUid(),
                    activityId, getActivityType(), activity.getType());
            return true;
        }

        return false;
    }

    @Override
    public void gmDeleteAcceptRelatedData(Player player, int cfg, PlayerSnsInvitationData param) {
        int activityId = param.getActivity().getActivityId();
        BaseActivity activity = player.getActivityManager().getRunningActivity(activityId);
        if (activity == null) {
            LOGGER.error("failed to find activity, uid:{} activity:{}", player.getUid(), activityId);
            return;
        }

        if (activity.getType() != getActivityType()) {
            LOGGER.error("failed to find activity, uid:{} activity:{} expect:{} current:{}", player.getUid(),
                    activityId, getActivityType(), activity.getType());
            return;
        }


    }

    protected abstract ActivityType getActivityType();

    protected abstract NKErrorCode check(Player player, int activityId, T activity, SnsInvitationTableOrBuilder invitation);

    protected abstract void accept(Player player, int activityId,
            T activity, SnsInvitationTableOrBuilder acceptedInvitation);

    protected abstract void gmDeleteAcceptRelatedData(Player player, int activityId, T activity, int cfg, PlayerSnsInvitationData data);
}
