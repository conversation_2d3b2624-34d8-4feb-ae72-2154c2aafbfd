package com.tencent.wea.playerservice.bag;

import static com.tencent.wea.wolfKill.Util.getWolfKillRealTreasureLevel;
import static com.tencent.wea.wolfKill.Util.isWolfKillAniItem;
import static com.tencent.wea.wolfKill.Util.isWolfKillItemType;

import com.tencent.cl5.PolarisUtil;
import com.tencent.condition.event.player.common.CostCurrencyEvent;
import com.tencent.condition.event.player.common.FashionLevelChangeEvent;
import com.tencent.condition.event.player.common.GetItemEvent;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.guid.BillNoIdGenerator;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.resclass.AppearanceRoadData;
import com.tencent.resourceloader.resclass.AppearanceRoadLevelDataConfig;
import com.tencent.resourceloader.resclass.ArenaSkinEffectData;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.BackpackItemDressUpValueConfData;
import com.tencent.resourceloader.resclass.FittingSlotUnlockConf;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.NR3E3Treasure;
import com.tencent.resourceloader.resclass.PlayerLevelConfData;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.resourceloader.resclass.SuitInformation;
import com.tencent.resourceloader.resclass.SuitInformation.SuitContent;
import com.tencent.resourceloader.resclass.WolfKillVocation;
import com.tencent.rpc.RpcResult;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.tool.FunctionUtil;
import com.tencent.util.JsonUtil;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.wea.attr.*;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.PlayerInteractionInvoker;
import com.tencent.wea.outputcontrol.OutputModuleCtx;
import com.tencent.wea.playerservice.bag.ChangedItems.ChangeItem;
import com.tencent.wea.playerservice.event.common.PlayerChangeCoinEvent;
import com.tencent.wea.playerservice.event.common.PlayerCostCurrencyEvent;
import com.tencent.wea.playerservice.event.common.PlayerGetItemEvent;
import com.tencent.wea.playerservice.event.common.PlayerItemExpireRemoveEvent;
import com.tencent.wea.playerservice.event.consumer.PlayerChangeCoinConsumer;
import com.tencent.wea.playerservice.event.consumer.PlayerEquipItemChangeConsumer;
import com.tencent.wea.playerservice.event.consumer.PlayerGetItemEventConsumer;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.outputcontrol.GamesvrOutputMgr;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.returning.PlayerReturnActivityManager;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.welfare.TencentWelfareManager;
import com.tencent.wea.protocol.AttrDSItem;
import com.tencent.wea.protocol.CsBag;
import com.tencent.wea.protocol.CsBag.BagCommonGetItemsNtf;
import com.tencent.wea.protocol.CsBag.BagGetItem;
import com.tencent.wea.protocol.CsBag.ItemAutoReplaceReason;
import com.tencent.wea.protocol.CsNotice.ChangedItemInfo;
import com.tencent.wea.protocol.CsWolfkill.WolfKillTreasureShareResult;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsBattlesvr;
import com.tencent.wea.protocol.SsCommon;
import com.tencent.wea.protocol.SsDsdbsvr;
import com.tencent.wea.protocol.common.DSItem;
import com.tencent.wea.protocol.common.DSItemsChange;
import com.tencent.wea.protocol.common.ItemArray;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MetaDataReason;
import com.tencent.wea.room.RoomUtil;
import com.tencent.wea.rpc.service.BattleService;
import com.tencent.wea.rpc.service.DsdbService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionInstruction;
import com.tencent.wea.tlog.flow.TlogMacros.ADDORREDUCE;
import com.tencent.wea.xlsRes.ResBackpack.CloakroomUnlock;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResMisc;
import com.tencent.wea.xlsRes.ResNR3E3Treasure;
import com.tencent.wea.xlsRes.ResNR3E3Treasure.NR3E3TreasureUnlockItem;
import com.tencent.wea.xlsRes.ResNR3E3Vocation;
import com.tencent.wea.xlsRes.keywords.CoinType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.PlayerGameTimeType;
import com.tencent.wea.xlsRes.keywords.RankRule;
import com.tencent.wea.zplan.HttpClient;
import java.util.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.tencent.wea.protocol.common.BagVehicleType;

/**
 * 背包管理器
 *
 * @author:blitzzhang
 * @date:2021/8/18 18:38
 */
public class BagManager extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(BagManager.class);
    private final com.tencent.wea.attr.Money playerMoney = player.getUserAttr().getMoney();
    private final ItemManager itemManager;
    private final ItemEquipMgr itemEquipMgr;
    private int lastAddItemLogSeqId = 0;
    private int lastMinItemLogSeqId = 0;
    private long lastAttrVersionForAdd = 0;
    private long lastAttrVersionForMin = 0;
    private long bagCommonGetItemsNtfSeqId = 0;
    private Map<Long, BagCommonGetItemsNtfMsg> msgMap = new HashMap<>();

    private static int expireMailTempateId = 5;
    private static long deltaTimeMs = 10 * 1000; // ms

    private static final String PRIV_REPORT_URL = "http://{}:{}/ide/?sIdeToken=ezGkTK&iChartId=246301";
    private long lastPrivReportSec = 0;
    private int privSeqId = 0;

    /**
     * 不需要发道具通知的类型
     */
    private static final List<ItemType> noNtfItemTypes = Arrays.asList(
            ItemType.ItemType_Card,
            ItemType.ItemType_CardBag,
            ItemType.ItemType_WildCard);

    private long getPrivReportMs() {
        long lastRefreshMs = player.getUserAttr().getBasicInfo().getLastMidnightRefreshTimeMs();
        if (lastRefreshMs == 0) {
            return Framework.currentTimeMillis();
        }
        return Math.min(Framework.currentTimeMillis(), DateUtils.getDayEndTimeMs(lastRefreshMs) - 1);
    }

    private static class PrivReportData {

        public long addNum = 0;
        public long afterNum = 0;
        public long nowMs = 0;
    }

    private PrivReportData privReportData = null;

    public static class BagCommonGetItemsNtfMsg {

        private long sendTimeMs;
        private CsBag.BagCommonGetItemsNtf.Builder body;

        public BagCommonGetItemsNtfMsg(long sendTimeMs, CsBag.BagCommonGetItemsNtf.Builder body) {
            this.sendTimeMs = sendTimeMs;
            this.body = body;
        }

        public long getSendTimeMs() {
            return sendTimeMs;
        }

        public CsBag.BagCommonGetItemsNtf.Builder getBody() {
            return body;
        }
    }

    public BagManager(Player player) {
        super(GameModuleId.GMI_BagManager, player);
        itemManager = new ItemManager(player);
        itemEquipMgr = new ItemEquipMgr(player);
        player.getEventSwitch().register(new PlayerEquipItemChangeConsumer(player));
        player.getEventSwitch().register(new PlayerChangeCoinConsumer(player));
        player.getEventSwitch().register(new PlayerGetItemEventConsumer(player));
        player.getEventSwitch().register(itemManager);
    }

     // 包含临时道具
    public long getItemNumByItemId(int itemId) {
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (itemConf == null) {
            return 0;
        }
        if (itemConf.getType().equals(ItemType.ItemType_Currency)) {
            return getMoneyNum(itemId);
        } else if (itemConf.getType().equals(ItemType.ItemType_NR3E_Treasure)){
            //  狼人珍宝道具另外计算，因为没有实际物品存在
            if (itemId==240900){
                return itemManager.GetItemNumByItemId(itemId);
            }
            int treasureItemId = MiscConf.getInstance().getMiscConf().getWolfKillTreasureItemId();
            if (treasureItemId==0){
                return 0;
            }
            long ownedNum = getMoneyNum(treasureItemId);
            List<Long> curLevelAndNum =  getWolfKillRealTreasureLevel(ownedNum, player.getClientVersion64());
            int curLevel = curLevelAndNum.get(0).intValue();
            for (int i=1; i<=curLevel; i++) {
                ResNR3E3Treasure.NR3E3TreasureItem treasureItem = NR3E3Treasure.getInstance().get(i);
                if (treasureItem == null) {
                    return 0;
                }
                for (NR3E3TreasureUnlockItem treasureUnlock : treasureItem.getTreasureList()) {
                    if (itemId == treasureUnlock.getId()){
                        return 1;
                    }
                }
            }
            return 0;
        }else {
            return itemManager.GetItemNumByItemId(itemId);
        }
    }

    // 不包含临时道具
    public long getItemNumByItemIdIgnoreTemp(int itemId) {
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (itemConf == null) {
            return 0;
        }
        if (itemConf.getType().equals(ItemType.ItemType_Currency)) {
            return getMoneyNum(itemId);
        } else {
            return itemManager.getItemNumByItemIdIgnoreTemp(itemId);
        }
    }

    private void init() {
        ResMisc.MiscConf miscConf = MiscConf.getInstance().getMiscConf();
        if (miscConf == null) {
            return;
        }

        ResMisc.MiscPlayerRegConf playerRegConf = miscConf.getPlayerReg();
        playerRegConf.getRegDefaultItemList().forEach(item -> {
            ChangedItems changeItem = new ChangedItems(ItemChangeReason.ICR_PlayerRegisterItem.getNumber(), "");
            if (player.getUserAttr().getBagInfo().getCreateRoleItem().contains(item.getItemId())) {
                return;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                return;
            }
            if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
                return;
            }
            changeItem.mergeItemInfo(item.getItemId(), item.getItemNum());
            NKPair<NKErrorCode, ItemChangeDetails> ret = AddItems2(changeItem, false);
            if (ret.getKey().isOk()) {
                player.getUserAttr().getBagInfo().addCreateRoleItem(item.getItemId());
            } else {
                LOGGER.error("add RegDefaultItemList error, uid:{}, itemId:{}, itemNum:{}",
                        player.getUid(), item.getItemId(), item.getItemNum());
            }
        });
    }

    /** 天天晋级赛默认道具 */
    private void tryAddDailyBattleDefaultItems() {
        // BackpackItem.getInstance().getDailyBattleDefaultItemMap().values().stream().map(e -> e.getId()).collect(Collectors.toList());
        List<Integer> dailyBattleDefaultItems = new ArrayList<>();
        for (Item_BackpackItem item : BackpackItem.getInstance().getDailyBattleDefaultItemMap().values()) {
            if (player.getUserAttr().getBagInfo().getCreateRoleItem().contains(item.getId())) {
                continue;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getId());
            if (Objects.isNull(itemConf)) {
                continue;
            }
            if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
                continue;
            }
            dailyBattleDefaultItems.add(item.getId());
        }

        if (!dailyBattleDefaultItems.isEmpty()) {
            ChangedItems changeItem = new ChangedItems(ItemChangeReason.ICR_DailyBattleDefaultItem.getNumber(), "");
            for (int dailyBattleDefaultItem : dailyBattleDefaultItems) {
                changeItem.mergeItemInfo(dailyBattleDefaultItem, 1);
            }
            NKPair<NKErrorCode, ItemChangeDetails> ret = AddItems2(changeItem, false);
            if (ret.getKey().isOk()) {
                player.getUserAttr().getBagInfo().getCreateRoleItemList().addAll(dailyBattleDefaultItems);
                for (ItemInfo itemInfo : ret.value.getActualChangeItems().getItemsList()) {
                    if (0L == itemInfo.getUuid()) {
                        continue;
                    }
                    String itemType = Optional.ofNullable(ItemType.forNumber(itemInfo.getItemType())).map(Enum::toString).orElse("");
                    player.getItemEquipManager().updateReadyBattleOrnamentationBagInfo(player, itemInfo.getUuid(), itemType);
                }
                LOGGER.info("add dailyBattleDefaultItems success. uid:{} itemIds:{}", player.getUid(), dailyBattleDefaultItems);
            } else {
                LOGGER.error("add dailyBattleDefaultItems error. uid:{} itemIds:{}", player.getUid(), dailyBattleDefaultItems);
            }
        }
    }

    public NKErrorCode preCheckAddItemsVersion(ChangedItems changeItems) {
        for (ChangeItem addItem : changeItems.getChangeItems()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(addItem.getSrcItemId());
            if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
                LOGGER.error("preCheckAddItems item check item version err, UID:{} addItem:{}",
                        player.getUid(), addItem.toString());
                return NKErrorCode.CanNotGetItemVersionBeyond;
            }
        }
        return NKErrorCode.OK;
    }

    //

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Collection<ItemInfo> itemInfos, int reason) {
        return AddItems2(itemInfos, reason, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Collection<ItemInfo> itemInfos, int reason,
            boolean withNtf) {
        ChangedItems changeItems = new ChangedItems(reason, "");
        for (ItemInfo itemInfo : itemInfos) {
            changeItems.mergeItemInfo(itemInfo);
        }
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Collection<ItemInfo> itemInfos, int reason,
            boolean withNtf, String busBillNo) {
        ChangedItems changeItems = new ChangedItems(reason, "");
        for (ItemInfo itemInfo : itemInfos) {
            changeItems.mergeItemInfo(itemInfo);
            changeItems.setBusBillNo(busBillNo);
        }
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Map<Integer, Long> itemInfos, int reason) {
        return AddItems2(itemInfos, reason, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Map<Integer, Long> itemInfos, int reason,
            boolean withNtf) {
        ChangedItems changeItems = new ChangedItems(reason, "");
        for (Map.Entry<Integer, Long> itemInfo : itemInfos.entrySet()) {
            changeItems.mergeItemInfo(itemInfo.getKey(), itemInfo.getValue());
        }
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(int itemId, int itemNum, int reason) {
        return AddItems2(itemId, itemNum, reason, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(int itemId, int itemNum, int reason,
            boolean withNtf) {
        ChangedItems changeItems = new ChangedItems(itemId, itemNum, reason, "");
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(ChangedItems changeItems, int reason,
            int subReason) {
        return AddItems2(changeItems, true, subReason);
    }

    //

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Collection<ItemInfo> itemInfos, ItemChangeReason reason) {
        return AddItems2(itemInfos, reason, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Collection<ItemInfo> itemInfos, ItemChangeReason reason,
            boolean withNtf) {
        ChangedItems changeItems = new ChangedItems(reason.getNumber(), "");
        for (ItemInfo itemInfo : itemInfos) {
            changeItems.mergeItemInfo(itemInfo);
        }
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Collection<ItemInfo> itemInfos, ItemChangeReason reason,
            boolean withNtf, String busBillNo) {
        ChangedItems changeItems = new ChangedItems(reason.getNumber(), "");
        for (ItemInfo itemInfo : itemInfos) {
            changeItems.mergeItemInfo(itemInfo);
            changeItems.setBusBillNo(busBillNo);
        }
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Map<Integer, Long> itemInfos, ItemChangeReason reason) {
        return AddItems2(itemInfos, reason, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(Map<Integer, Long> itemInfos, ItemChangeReason reason,
            boolean withNtf) {
        ChangedItems changeItems = new ChangedItems(reason.getNumber(), "");
        for (Map.Entry<Integer, Long> itemInfo : itemInfos.entrySet()) {
            changeItems.mergeItemInfo(itemInfo.getKey(), itemInfo.getValue());
        }
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(int itemId, int itemNum, ItemChangeReason reason) {
        return AddItems2(itemId, itemNum, reason, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(int itemId, int itemNum, ItemChangeReason reason,
            boolean withNtf) {
        ChangedItems changeItems = new ChangedItems(itemId, itemNum, reason.getNumber(), "");
        return AddItems2(changeItems, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(ChangedItems changeItems) {
        return AddItems2(changeItems, true);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(ChangedItems changeItems, int subReason) {
        return AddItems2(changeItems, true, subReason);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(ChangedItems changeItems, boolean withNtf) {
        return AddItems2(changeItems, withNtf, 0);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> AddItems2(ChangedItems changeItems,
            boolean withNtf, long subReason) {
        if (changeItems.getChangeItems().size() == 0) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("addItems empty, uid:{} trace:{}", player.getUid(), FunctionUtil.getStackTrace());
            }
            return new NKPair<>(NKErrorCode.OK, null);
        }
        changeItems.getTlogData().setChangeSubReason(subReason);

        NKPair<NKErrorCode, Map<Integer, Item_BackpackItem>> checkConfRet = batchGetItemConf(changeItems);
        if (!checkConfRet.getKey().isOk()) {
            LOGGER.error("player changeItems batchGetItemConf err, uid:{}", player.getUid());
            return new NKPair<>(checkConfRet.getKey(), null);
        }

        // 租借道具检查逻辑
        NKErrorCode checkRentItemCode = changeItems.checkRentItem(player);
        if (!checkRentItemCode.isOk()) {
            return new NKPair<>(checkRentItemCode, null);
        }

        // 自动打开普通和随机礼包
        ChangedItems itemsAfterOpen = changeItems.autoOpenPackage(player);

        // 自动打开农场宝箱
        itemsAfterOpen = itemsAfterOpen.autoOpenFarmChest(player);

        // 自动打开狼人装备
        itemsAfterOpen = itemsAfterOpen.autoOpenWolfKillItems(player);

        NKPair<Integer, List<Integer>> needAddFashionItems = player.getSeasonMgr()
                .getSeasonDressesFashionValue(itemsAfterOpen);

        BagCommonGetItemsNtf.Builder getItemsNtfMsg = BagCommonGetItemsNtf.newBuilder();
        // 狼人的某些珍宝道具不弹窗提示，在autoOpenWolfKillItems方法里处理过了，所以用itemsAfterOpen
//        getItemsNtfMsg.setReason(changeItems.getChangeReason());
        getItemsNtfMsg.setReason(itemsAfterOpen.getChangeReason());

        // 发送过期道具处理
        itemsAfterOpen.replaceExpiredItems();

        // 将超上限的道具替换掉
        itemsAfterOpen.replaceExceedMaxItems(player);

        NKPair<NKErrorCode, Map<Integer, Item_BackpackItem>> getConfRet = batchGetItemConf(itemsAfterOpen);
        if (!getConfRet.getKey().isOk()) {
            LOGGER.error("player itemsAfterOpen batchGetItemConf err, uid:{}", player.getUid());
            return new NKPair<>(getConfRet.getKey(), null);
        }
        Map<Integer, Item_BackpackItem> confMap = getConfRet.getValue();

        if (itemManager.isBagGirdsExceedLimit(itemsAfterOpen, confMap)) {
            LOGGER.error("player backpack grids exceed limit, uid:{}", player.getUid());
            return new NKPair<>(NKErrorCode.CheckBagGridNotEnough, null);
        }

        int sequenceId;
        if (withNtf) {
            sequenceId = getSequenceIdForAddItem();
        } else {
            sequenceId = keepSequenceId();
        }
        changeItems.getTlogData().setSequenceId(sequenceId);

        //先处理钻石赠送
        long totalAddDiamond = 0L;
        for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
            if (itemInfo.getReplaceReason().equals(ItemAutoReplaceReason.IARR_None)) {
                if (CoinType.CT_Diamond == CoinType.forNumber(itemInfo.getSrcItemId())) {
                    totalAddDiamond += itemInfo.getSrcItemNum();
                }
            } else {
                for (ItemInfo actItemInfo : itemInfo.getActItems().getItemsList()) {
                    if (CoinType.CT_Diamond == CoinType.forNumber(actItemInfo.getItemId())) {
                        totalAddDiamond += actItemInfo.getItemNum();
                    }
                }
            }
        }
        if (totalAddDiamond > 0) {
            try {
                MetaDataReason.Builder metaDataReasonBuilder = MetaDataReason.newBuilder();
                metaDataReasonBuilder.setSubReason(subReason);
                List<Long> changeReservedParams = changeItems.getChangeReservedParams();
                if (null != changeReservedParams) {
                    metaDataReasonBuilder.addAllChangeReservedParams(changeReservedParams);
                }
                player.getPlayerMoneyMgr()
                        .presentAsync((int) totalAddDiamond, changeItems.getChangeReason(), changeItems.getBusBillNo(), metaDataReasonBuilder.build());
            } catch (Exception e) {
                LOGGER.error("AddItems add diamond failed, num:{}, uid:{}",
                        totalAddDiamond, player.getUid());
            }
        }

        // 小红花道具逻辑
        for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
            long totalAddXHH = 0L;
            if (itemInfo.getReplaceReason().equals(ItemAutoReplaceReason.IARR_None)) {
                Item_BackpackItem itemConf = confMap.get(itemInfo.getSrcItemId());
                if (itemConf.getType() == ItemType.ItemType_GongyiXHH) {
                    totalAddXHH += itemInfo.getSrcItemNum();
                }
            } else {
                for (ItemInfo actItemInfo : itemInfo.getActItems().getItemsList()) {
                    Item_BackpackItem itemConf = confMap.get(actItemInfo.getItemId());
                    if (itemConf.getType() == ItemType.ItemType_GongyiXHH) {
                        totalAddXHH += actItemInfo.getItemNum();
                    }
                }
            }
            if (totalAddXHH > 0) {
                TencentWelfareManager.getInstance().produceXHH(player, player.getUid(),
                        player.getUserAttr().getPlayerPublicProfileInfo().getPlatOpenId(),
                        player.getAccountType().getNumber(), player.getAccessToken(), (int) totalAddXHH, "");
            }
        }
        for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
            long totalAddMedal = 0L;
            if (itemInfo.getReplaceReason().equals(ItemAutoReplaceReason.IARR_None)) {
                Item_BackpackItem itemConf = confMap.get(itemInfo.getSrcItemId());
                if (itemConf.getType() == ItemType.ItemType_GongyiMedal) {
                    totalAddMedal += itemInfo.getSrcItemNum();
                }
            } else {
                for (ItemInfo actItemInfo : itemInfo.getActItems().getItemsList()) {
                    Item_BackpackItem itemConf = confMap.get(actItemInfo.getItemId());
                    if (itemConf.getType() == ItemType.ItemType_GongyiMedal) {
                        totalAddMedal += actItemInfo.getItemNum();
                    }
                }
            }
            if (totalAddMedal > 0) {
                TencentWelfareManager.getInstance().awardMedal(player, player.getUid(),
                        player.getUserAttr().getPlayerPublicProfileInfo().getPlatOpenId(),
                        player.getAccountType().getNumber(), player.getAccessToken(), (int) totalAddMedal, "");
            }
        }

        for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
            List<Long> changeReservedParams = changeItems.getChangeReservedParams();
            if (itemInfo.getGiftPackageId() != 0) {
                changeReservedParams = setChangeReservedParams(changeReservedParams, 3, itemInfo.getGiftPackageId());
            }
            // 狼人珍宝系统特别的上报而已
            if (!itemsAfterOpen.getOpenWolfKillItemMap().isEmpty()){
                int reason = changeItems.getChangeReason();
                for (Map.Entry<Integer, Long> entry : itemsAfterOpen.getOpenWolfKillItemMap().entrySet()){
                    if (itemInfo.getSrcItemId() == entry.getKey()) {
                        LOGGER.debug("wolfKillTreasure reward item:{}", itemInfo.getSrcItemId());
                        reason = ItemChangeReason.ICR_WolfKill_Treasure_VALUE;
                        subReason = itemsAfterOpen.getWolfKillTreasureLevel();
                        break;
                    }
                }
                Item_BackpackItem itemConf = confMap.get(itemInfo.getSrcItem().getItemId());
                ItemArray.Builder actual = addItem(itemConf, itemInfo.getSrcItem(), changeItems.getTlogData());
                for (ItemInfo actItemInfo : actual.getItemsList()) {
                    CsBag.BagGetItem.Builder bagGetItem = CsBag.BagGetItem.newBuilder();
                    bagGetItem.setSrcItem(actItemInfo);
                    getItemsNtfMsg.addItems(bagGetItem);
                }
            } else if (itemInfo.getReplaceReason().equals(ItemAutoReplaceReason.IARR_None)) {
                Item_BackpackItem itemConf = confMap.get(itemInfo.getSrcItem().getItemId());
                ItemArray.Builder actual = addItem(itemConf, itemInfo.getSrcItem(), changeItems.getTlogData());
                for (ItemInfo actItemInfo : actual.getItemsList()) {
                    CsBag.BagGetItem.Builder bagGetItem = CsBag.BagGetItem.newBuilder();
                    bagGetItem.setSrcItem(actItemInfo);
                    getItemsNtfMsg.addItems(bagGetItem);
                }
            } else {
                CsBag.BagGetItem.Builder bagGetItem = CsBag.BagGetItem.newBuilder();
                bagGetItem.setSrcItem(itemInfo.getSrcItem());
                bagGetItem.setReason(itemInfo.getReplaceReason());
                for (ItemInfo actItemInfo : itemInfo.getActItems().getItemsList()) {
                    Item_BackpackItem itemConf = confMap.get(actItemInfo.getItemId());
                    ItemArray.Builder actual = addItem(itemConf, actItemInfo, changeItems.getTlogData());
                    bagGetItem.addAllActItem(actual.getItemsList());
                }
                getItemsNtfMsg.addItems(bagGetItem);
            }
        }

        // 等所有道具真实入包之后再加时尚度
        player.getSeasonMgr().addFashionValue(needAddFashionItems);
        player.getSeasonMgr().updateSeasonFashionEquipBook(itemsAfterOpen);

        // 更新装扮分
        updateDressUpScore(itemsAfterOpen);

        ItemChangeDetails itemRealChanges = new ItemChangeDetails(getItemsNtfMsg);
        // 保持RootAttrNtf 在BagCommonGetItemsNtfMsg 之前发送
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
        if (withNtf) {
            sendGetItemsNtf(itemRealChanges);
        }
        // 道具分解日志流水
        for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
            if (!itemInfo.getReplaceReason().equals(ItemAutoReplaceReason.IARR_None)) {
                for (ItemInfo actItemInfo : itemInfo.getActItems().getItemsList()) {
                    TlogFlowMgr.sendItemDecomposeFlow(player, 0, itemInfo.getSrcItemId(), itemInfo.getSrcItemNum(),
                            itemsAfterOpen.getChangeReason(), subReason, actItemInfo.getItemId(),
                            actItemInfo.getItemNum(), itemsAfterOpen.getBusBillNo());
                }
            }
        }

        ItemArray actualChangeItems = itemRealChanges.getActualChangeItems();

        // register ams ntf waits
        player.getAmsItemMgr()
                .registerAmsItemsForAsyncAutoUse(actualChangeItems, changeItems.getChangeReason(), (int) subReason,
                        changeItems.getBusBillNo());

        // 事件
        new PlayerGetItemEvent(player).setFashionValueDirty(needAddFashionItems.getKey() != 0).
                setActualChangedItems(actualChangeItems)
                .setItemChangeReason(changeItems.getChangeReason())
                .setItemChangeSubReason(subReason).dispatch();

        Map<Integer, Long> afterMap = new HashMap<>();
        // 如果需要通过BattleSvr通知DS
        ntfToDSItemsChangeAsync(changeItems.getChangeReason(), actualChangeItems);

        PlayerChangeCoinEvent changeCoinEvent = new PlayerChangeCoinEvent(player);
        for (Map.Entry<Integer, Long> entry : itemRealChanges.getChangedTotalItemsMap().entrySet()) {
            int itemId = entry.getKey();
            long changeNum = entry.getValue();
            Item_BackpackItem itemConf = confMap.get(itemId);
            long afterNum;
            if (itemConf.getType() == ItemType.ItemType_Currency) {
                afterNum = getMoneyNum(itemId);
                long beforeNum = afterNum - changeNum;
                ChangedItemInfo.Builder changeItem = ChangedItemInfo.newBuilder();
                changeItem.setItemId(itemId);
                changeItem.setCountBeforeChange(beforeNum);
                changeItem.setCountAfterChange(afterNum);
                changeCoinEvent.addChangeCoinInfo(changeItem);
            } else {
                afterNum = getItemNumByItemId(itemId);
            }
            afterMap.put(itemId, afterNum);
            if (itemId == CoinType.CT_DayActivePoint_VALUE && entry.getValue() > 0) {
                long dailyActivePointAdd = entry.getValue();
                if (privReportData == null) {
                    privReportData = new PrivReportData();
                }
                privReportData.addNum += dailyActivePointAdd;
                privReportData.afterNum = playerMoney.getCoin(CoinType.CT_DayActivePoint_VALUE).getCoinNum();
                privReportData.nowMs = getPrivReportMs();
            }
        }

        player.getPlayerEventManager().dispatch(
                new GetItemEvent(player.getConditionMgr()).
                        setChangeItem(actualChangeItems).
                        setAfterItemInfo(ItemChangeDetails.covetEasyItemArray(afterMap)));

        if (!changeCoinEvent.getChangeCoinMap().isEmpty()) {
            changeCoinEvent.dispatch();
        }

        // 狼人身份解锁需要另外上报
        sendTlogToWolfKillVocation(getConfRet);
        // 狼人身份的添加
        addWolfKillTreasureShare(confMap,itemsAfterOpen);

        // 处理Arena皮肤解锁
        player.getArenaMgr().checkUnlockSkin(itemsAfterOpen);
        // 检查，如果购买了Arena局外皮肤，自动解锁Arena局内皮肤
        autoUnlockArenaInterSkin(itemsAfterOpen);

        return new NKPair<>(NKErrorCode.OK, itemRealChanges);
    }


    // 狼人珍宝值统一在这里维护，升级后如果有奖励，会返回一个奖励物品列表。执行完了才会改变珍宝值
    public Map<Integer, Long> addWolfKillTreasure(int addTreasureNum){
        Map<Integer, Long> changeItemMap = new HashMap<>();
        if (addTreasureNum <= 0) {
            return changeItemMap;
        }
        int treasureItemId = MiscConf.getInstance().getMiscConf().getWolfKillTreasureItemId();
        if (treasureItemId==0){
            return changeItemMap;
        }
        // 是珍宝道具，需要根据等级解锁
        // 道具用下面
//        long ownedNum = getItemManager()
//                .getItemNumByItemIdIgnoreTemp(treasureItemId);
        // 货币用其他的
        long ownedNum = getMoneyNum(treasureItemId);
        List<Long> curLevelAndNum =  getWolfKillRealTreasureLevel(ownedNum, player.getClientVersion64());
        List<Long> levelAndNum = getWolfKillRealTreasureLevel(ownedNum+addTreasureNum, player.getClientVersion64());
        LOGGER.debug("wolfkill is treasureItemId, item_id {}, now:{} , add:{}, curLevel:{},curNum:{}, afterLevel:{},afterNum:{} clVer:{}",
                treasureItemId,
                ownedNum,addTreasureNum, curLevelAndNum.get(0),curLevelAndNum.get(1), levelAndNum.get(0),
                levelAndNum.get(1), player.getClientVersion64());

        // 计算一个显示值
        long realAddNum = 0;
        // 上次的显示值
        long lastDisplayNum = player.getUserAttr().getWolfKillInfo().getTreasureNum();
        int curLevel = curLevelAndNum.get(0).intValue();
        int afterLevel = levelAndNum.get(0).intValue();
        long curNum = curLevelAndNum.get(1);
        long afterNum = levelAndNum.get(1);
        long curRealMaxNum = curLevelAndNum.get(2);
        long afterRealMaxNum = levelAndNum.get(2);
        if (curLevel < afterLevel){
            // 升级了，增加珍宝功能
            for (int i=curLevel+1; i<=afterLevel; i++ ){
                ResNR3E3Treasure.NR3E3TreasureItem treasureItem = NR3E3Treasure.getInstance().get(i);
                if (treasureItem==null){
                    LOGGER.error("wolfkill is treasureItemId, config is null, level:{}", i);
                }else{
                    // 两次的差异
                    if (i==curLevel+1){
                        realAddNum += (treasureItem.getUpgradeNum() - curNum);
                    }else if (i==afterLevel){
                        realAddNum += afterNum;
                        // 每次累加最后一次的值
                        realAddNum+=lastDisplayNum;
                    }else{
                        realAddNum += treasureItem.getUpgradeNum();
                    }

                    // 判断等级和开启时间
                    for (NR3E3TreasureUnlockItem treasureUnlock : treasureItem.getTreasureList()) {
                        WolfKillTreasureEquipInfo wolfKillTreasureEquipInfo = new WolfKillTreasureEquipInfo();
                        wolfKillTreasureEquipInfo.setId(treasureUnlock.getId());
                        wolfKillTreasureEquipInfo.setInUse(treasureUnlock.getAutoUse() == 1);
                        wolfKillTreasureEquipInfo.setIsNew(true);
                        player.getUserAttr().getWolfKillInfo()
                                .putTreasureEquipInfo(treasureUnlock.getId(), wolfKillTreasureEquipInfo);
                        if (treasureUnlock.getCanUse() == 1 || treasureUnlock.getCanDayUse() == 1){

                        }else{
                            if (treasureUnlock.getItemNumsCount() != treasureUnlock.getItemsCount()){
                                LOGGER.error("wolfkill is treasureItemId, config is error");
                                return changeItemMap;
                            }
                            for (int iUnlock = 0; iUnlock < treasureUnlock.getItemsCount(); iUnlock++){
                                long itemNum = treasureUnlock.getItemNums(iUnlock);
                                changeItemMap.put(treasureUnlock.getItems(iUnlock), itemNum);
                            }
                        }
                        LOGGER.debug("wolfkill is treasureItemId,level:{} unlock {}",i, treasureUnlock.getId()
                        );
                    }
                }
            }
            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.Default);
        }else{
            // 不升级
            realAddNum = afterNum-curNum+lastDisplayNum;
        }
        long realAddNum2 = afterRealMaxNum-player.getUserAttr().getWolfKillInfo().getTreasureNumAllLast();


        LOGGER.debug("wolfkill is treasureItemId, item_id {}, now:{} , add:{}, curLevel:{}, afterLevel:{},realAddNum:{},realAddNum2:{}, changeItemMap:{}, getWolfKillInfo:{}", treasureItemId,
                ownedNum,addTreasureNum, curLevelAndNum.get(0), levelAndNum.get(0), realAddNum, afterRealMaxNum-player.getUserAttr().getWolfKillInfo().getTreasureNumAllLast(),
                changeItemMap,player.getUserAttr().getWolfKillInfo());
        // 记录
        player.getUserAttr().getWolfKillInfo().setLv(levelAndNum.get(0).intValue());
        player.getUserAttr().getWolfKillInfo().setTreasureNum(levelAndNum.get(1));
        player.getUserAttr().getWolfKillInfo().setTreasureNumAll(afterRealMaxNum);
        // 不是实际增加的珍宝值，而是上限值
        player.getUserAttr().getWolfKillInfo().setTreasureNumAdd(realAddNum2);

        return changeItemMap;
    }

    private void addWolfKillTreasureShare(Map<Integer, Item_BackpackItem> confMap, ChangedItems itemsAfterOpen){

        List<Integer> listAni = new ArrayList<>();
        List<Integer> listVocation = new ArrayList<>();
        for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
            if (confMap.get(itemInfo.getSrcItemId()).getType() == ItemType.ItemType_NR3E_Identity){
                if (itemInfo.getSrcItem().getExpireTimeMs()>0){
                    LOGGER.debug("wolfkill ItemType_NR3E_Identity, item_id {}, temp item", itemInfo.getSrcItemId());
                    continue;
                }

                LOGGER.debug("wolfkill ItemType_NR3E_Identity, item_id {}", itemInfo.getSrcItemId());

                listVocation.add(itemInfo.getSrcItemId());


            }else if (isWolfKillAniItem(confMap.get(itemInfo.getSrcItemId()).getType().getNumber())){
                // 动画需要上报
                if (itemInfo.getSrcItem().getExpireTimeMs()>0){
                    LOGGER.debug("wolfkill ItemType_NR3E_Identity, item_id {}, temp item", itemInfo.getSrcItemId());
                    continue;
                }
                LOGGER.debug("wolfkill isAni, item_id {}", itemInfo.getSrcItemId());
                listAni.add(itemInfo.getSrcItemId());

            }
        }

        if (!listVocation.isEmpty()){
            if (!player.getUserAttr().getWolfKillInfo().getRentVocation().isEmpty()){
                for ( WolfKillTreasureRentInfo rentInfo : player.getUserAttr().getWolfKillInfo().getRentVocation().values()){
                    PlayerInteraction.PlayerInteractionData.Builder interactionData = PlayerInteraction.PlayerInteractionData.newBuilder();
                    PlayerInteraction.PiiWolfKillTreasureParams.Builder params = PlayerInteraction.PiiWolfKillTreasureParams.newBuilder();
                    params.setPlayerUid(player.getUid());
                    params.setExpiredTime(rentInfo.getRentTime());
                    interactionData.setInstruction(PlayerInteractionInstruction.PII_WOLFKILL_VOCATION_SHARE);
                    for (Integer itemInfo:listVocation){
                        PlayerInteraction.PiiWolfKillTreasureItemInfo.Builder rentItemInfo = PlayerInteraction.PiiWolfKillTreasureItemInfo.newBuilder();
                        rentItemInfo.setItemId(itemInfo);
                        rentItemInfo.setItemNum(1);
                        rentItemInfo.setExpiredTime(rentInfo.getRentTime());
                        rentItemInfo.setSharePlayerUid(player.getUid());
                        params.addItems(rentItemInfo);
                    }

                    interactionData.setWolfKillTreasureParams(params);
                    PlayerInteractionInvoker.interact(rentInfo.getRentUid(), interactionData);
                    // tlog
                    int relation = player.getFriendManager().getFriendTypeTlog(rentInfo.getRentUid());
                    String sendTlogItem = "";
                    int sendTlogItemCnt = 0;
                    for (PlayerInteraction.PiiWolfKillTreasureItemInfo piiItem : params.getItemsList()){
                        sendTlogItem += piiItem.getItemId() + ",";
                        sendTlogItemCnt++;
                    }
                    TlogFlowMgr.sendSuspectTreasureShareFlow(player,1, NR3E3Treasure.getInstance().getShareVocationId(),  rentInfo.getRentUid(),
                            relation, sendTlogItem, sendTlogItemCnt);
                }
            }
        }

        if (!listAni.isEmpty()){
            if (!player.getUserAttr().getWolfKillInfo().getRentAni().isEmpty()){
                for ( WolfKillTreasureRentInfo rentInfo : player.getUserAttr().getWolfKillInfo().getRentAni().values()){
                    PlayerInteraction.PlayerInteractionData.Builder interactionData = PlayerInteraction.PlayerInteractionData.newBuilder();
                    PlayerInteraction.PiiWolfKillTreasureParams.Builder params = PlayerInteraction.PiiWolfKillTreasureParams.newBuilder();
                    params.setPlayerUid(player.getUid());
                    params.setExpiredTime(rentInfo.getRentTime());
                    interactionData.setInstruction(PlayerInteractionInstruction.PII_WOLFKILL_ANI_SHARE);
                    for (Integer itemInfo:listAni){
                        PlayerInteraction.PiiWolfKillTreasureItemInfo.Builder rentItemInfo = PlayerInteraction.PiiWolfKillTreasureItemInfo.newBuilder();
                        rentItemInfo.setItemId(itemInfo);
                        rentItemInfo.setItemNum(1);
                        rentItemInfo.setExpiredTime(rentInfo.getRentTime());
                        rentItemInfo.setSharePlayerUid(player.getUid());
                        params.addItems(rentItemInfo);
                    }
                    interactionData.setWolfKillTreasureParams(params);
                    PlayerInteractionInvoker.interact(rentInfo.getRentUid(), interactionData);

                    // tlog
                    int relation = player.getFriendManager().getFriendTypeTlog(rentInfo.getRentUid());
                    String sendTlogItem = "";
                    int sendTlogItemCnt = 0;
                    for (PlayerInteraction.PiiWolfKillTreasureItemInfo piiItem : params.getItemsList()){
                        sendTlogItem += piiItem.getItemId() + ",";
                        sendTlogItemCnt++;
                    }
                    TlogFlowMgr.sendSuspectTreasureShareFlow(player,1, NR3E3Treasure.getInstance().getShareAniId(),  rentInfo.getRentUid(),
                            relation, sendTlogItem, sendTlogItemCnt);
                }
            }
        }
    }

    private void sendTlogToWolfKillVocation(NKPair<NKErrorCode, Map<Integer, Item_BackpackItem>> checkConfRet){
        Map<Integer, Item_BackpackItem> itemList = checkConfRet.getValue();
        boolean needNtfRoom = false;
        for (Map.Entry<Integer, Item_BackpackItem> itemInfo : itemList.entrySet()) {
            if (itemInfo.getValue().getType() == ItemType.ItemType_NR3E_Identity) {
                LOGGER.debug("wolfkill rpcRoomPlayerUpdateMemberBaseInfo, item_id {}", itemInfo.getValue().getId());
//                player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.Default);
                needNtfRoom = true;

                int roleType = 0;
                Map<Integer, ResNR3E3Vocation.NR3E3VocationData> vocationMapData = WolfKillVocation.getInstance()
                        .getLockMap();
                for (Map.Entry<Integer, ResNR3E3Vocation.NR3E3VocationData> vocationMapItem : vocationMapData.entrySet()) {
                    ResNR3E3Vocation.NR3E3VocationData vocationData = vocationMapItem.getValue();
                    if (vocationData.getIdentityItemUnlock() == itemInfo.getValue().getId()) {
                        roleType = vocationMapItem.getKey();
                        break;
                    }
                }
                if (roleType != 0) {
                    TlogFlowMgr.sendSuspectPerformFlow(player, "", "", "", roleType, 0,
                            0, 0, 0, 0);
                } else {
                    LOGGER.error("wolfkill sendSuspectPerformFlow error, item_id {}", itemInfo.getValue().getId());
                }
            }else if (isWolfKillAniItem(itemInfo.getValue().getType().getNumber())){
                // 动画需要上报
                needNtfRoom = true;
            }
        }

        if ( needNtfRoom ){
            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.Default);
        }
    }

    private static List<Long> setChangeReservedParams(List<Long> changeReservedParams, int index,
            long changeReservedParam) {
        if (changeReservedParams == null) {
            changeReservedParams = new ArrayList<>();
        }
        // 补齐List
        for (int i = changeReservedParams.size(); i <= index; i++) {
            changeReservedParams.add(i, 0L);
        }
        changeReservedParams.set(index, changeReservedParam);
        return changeReservedParams;
    }

    private ItemArray.Builder addItem(Item_BackpackItem itemConf, ItemInfo itemInfo, TlogData tlogData) {
        ItemArray.Builder changeItem = ItemArray.newBuilder();
        if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
            LOGGER.error("add item check item version err, UID:{} itemId:{} num:{}",
                    player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("add item check item version err, UID:{} itemId:{} num:{} trace:{}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), FunctionUtil.getStackTrace());
            }
            String[] monitorParams = new String[]{
                    String.valueOf(itemConf.getId()),
                    itemConf.getName(),
            };
            Monitor.getInstance().add.total(MonitorId.attr_add_item_check_version_err, 1, monitorParams);
//            NKErrorCode.CanNotGetItemVersionBeyond.throwError("CanNotGetItemVersionBeyond Exception");
            return changeItem;
        }

        try {
            // 通用产出系统检查。道具产出使用的阈值是模糊值而不是确切限制，所以这里不应阻碍道具发放！！！
            boolean informal = (ServerEngine.getInstance() != null && !ServerEngine.getInstance().isBusiness());
            List<OutputModuleCtx> outputCtxs = GamesvrOutputMgr.makePlayerItemCtx(player, itemInfo.getItemId(),
                    itemInfo.getItemNum(), tlogData.getChangeReason(), tlogData.getChangeSubReason(),
                    DateUtils.currentTimeSec());
            for (OutputModuleCtx outputCtx : outputCtxs) {
                if (!outputCtx.canOutput()) {
                    LOGGER.error("player {} addItem: output control check fail. item:{}-{}, reason:{}-{}, moduleKey:{}",
                            player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), tlogData.getChangeReason(),
                            tlogData.getChangeSubReason(), Pb2JsonUtil.getPbMsg(outputCtx.moduleKey));
                    // 非正式环境可结合七彩石开关(output_control_test_flag)拦截；正式环境绝不拦截
                    if (informal) {
                        return changeItem;
                    }
                }
            }
            for (OutputModuleCtx outputCtx : outputCtxs) {
                outputCtx.recordOutput();   // 产出系统计数
            }
        } catch (Exception e) {
            LOGGER.error("player {} addItem: output control exception. item:{}-{}, reason:{}-{}, exception:{}",
                    player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), tlogData.getChangeReason(),
                    tlogData.getChangeSubReason(), e);
        }

        long beforeNum;
        if (itemConf.getType() != ItemType.ItemType_Currency) {
            if (itemConf.getType() == ItemType.ItemType_GongyiXHH
                    || itemConf.getType() == ItemType.ItemType_GongyiMedal) {
                changeItem.addItems(itemInfo.toBuilder().setItemType(itemConf.getType().getNumber()));
            } else {
                beforeNum = itemManager.GetItemNumByItemId(itemInfo.getItemId());
                changeItem = itemManager.addItem(itemConf, itemInfo);
                TlogFlowMgr.sendItemFlow(player, itemConf.getType().getNumber(), itemConf.getId(),
                        itemInfo.getItemNum(), itemInfo.getExpireTimeMs(), beforeNum,
                        itemManager.GetItemNumByItemId(itemInfo.getItemId()),
                        ADDORREDUCE.ADD, itemInfo.getLessorUid(), tlogData);
            }
        } else {
            int formerLv = player.getUserAttr().getPlayerPublicProfileInfo().getLevel();
            if (playerMoney.getCoin(itemConf.getId()) == null) {
                playerMoney.putCoin(itemConf.getId(), new com.tencent.wea.attr.CoinInfo());
            }
            beforeNum = playerMoney.getCoin(itemConf.getId()).getCoinNum();
            CoinType coinType = CoinType.forNumber(itemConf.getId());
            if (coinType == CoinType.CT_Diamond) {
                // 钻石已经处理过了,这边只添加changeItem
                changeItem.addItems(itemInfo.toBuilder().setItemType(itemConf.getType().getNumber()));
                return changeItem; // present中调用了setMoneyNum, 不能再往下走event和flow的流程了
            } else if (coinType == CoinType.CT_SeasonCoin) {
                long fixNum = player.getSeasonMgr().addSeasonCoin(coinType, itemInfo.getItemNum());
                playerMoney.getCoin(itemConf.getId()).addCoinNum(fixNum);
                itemInfo = itemInfo.toBuilder().setItemNum(fixNum).build();
            } else if (itemConf.getId() == PlayerReturnActivityManager.getFriendShipFireItemId()) {
                long fixNum = player.getReturnActivityManager().fixFriendShipFireItemNum(itemInfo.getItemNum());
                playerMoney.getCoin(itemConf.getId()).addCoinNum(fixNum);
                itemInfo = itemInfo.toBuilder().setItemNum(fixNum).build();
            } else {
                playerMoney.getCoin(itemConf.getId()).addCoinNum(itemInfo.getItemNum());
            }
            changeItem.addItems(itemInfo.toBuilder().setItemType(itemConf.getType().getNumber()));
            long afterNum = playerMoney.getCoin(itemConf.getId()).getCoinNum();
            TlogFlowMgr.sendMoneyFlow(player, itemConf.getId(), beforeNum, afterNum, itemInfo.getItemNum(),
                    ADDORREDUCE.ADD, formerLv, tlogData);
        }
        return changeItem;
    }

    // 获得道具拥有的数量
    @Deprecated
    public long getItemOwnedNum(Item_BackpackItem itemConf) {
        return itemManager.getItemNumByItemIdIgnoreTemp(itemConf.getId());
    }

    private long fixExpIncrement(long incr) {
        long fix = incr;
        int max = PlayerLevelConfData.getInstance().getExpUpperLimit();
        if (player.getPlayerMoneyMgr().getCoinNum(CoinType.CT_Exp.getNumber()) + incr > max) {
            fix = max - player.getPlayerMoneyMgr().getCoinNum(CoinType.CT_Exp.getNumber());
        }
        return fix;
    }

    // 用于游戏内扣除道具场景
    public NKErrorCode minItemsAsPossible(ChangedItems minItems) {
        // 先付一级货币
        for (ChangeItem itemInfo : minItems.getChangeItems()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getSrcItemId());
            if (itemConf == null) {
                LOGGER.debug("player minItemsAsPossible get item config null, player Uid:{} itemID:{} itemNum:{}",
                        player.getUid(), itemInfo.getSrcItemId(), itemInfo.getSrcItemNum());
                return NKErrorCode.ResNotFound;
            }
            if (itemConf.getId() == CoinType.CT_Diamond_VALUE) {
                NKErrorCode.CostDiamondInBagManagerError
                        .throwError("can't cost diamond in bag manager, reason:{}, uid:{}", minItems.getChangeReason(),
                                player.getUid());
                return NKErrorCode.CostDiamondInBagManagerError;
            }
        }
        PlayerChangeCoinEvent changeCoinEvent = new PlayerChangeCoinEvent(player);
        for (ChangeItem changeItem : minItems.getChangeItems()) {
            ItemInfo itemInfo = changeItem.getSrcItem();
            if (itemInfo.getItemNum() < 0) {
                LOGGER.error("player minItemsAsPossible itemNum < 0 error, player Uid:{} itemID:{} itemNum:{}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum());
                return NKErrorCode.InvalidParams;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemConf == null) {
                LOGGER.error("player minItemsAsPossible get item config null, player Uid:{} itemID:{} itemNum:{}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum());
                return NKErrorCode.ResNotFound;
            }
            long beforeNum;
            if (!itemConf.getType().equals(ItemType.ItemType_Currency)) {
                beforeNum = itemManager.GetItemNumByItemId(itemInfo.getItemId());
                if (!itemManager.minItem(itemConf, itemInfo.getItemNum())) {
                    LOGGER.error("player minItems MinBagItem error, player Uid:{} itemConf:{} targetBagType:{}",
                            player.getUid(), itemConf, itemConf.getType());
                    return NKErrorCode.MinBagItemError;
                }
                TlogFlowMgr.sendItemFlow(player, itemConf.getType().getNumber(), itemConf.getId(),
                        itemInfo.getItemNum(), itemInfo.getExpireTimeMs(), beforeNum,
                        itemManager.GetItemNumByItemId(itemInfo.getItemId()), ADDORREDUCE.REDUCE,
                        itemInfo.getLessorUid(), minItems.getTlogData());
            } else {
                CoinType coinType = CoinType.forNumber(itemConf.getId());
                if (coinType == CoinType.CT_Diamond) { // 函数开头已经付过了
                    continue;
                }
                if (playerMoney.getCoin() == null) {
                    LOGGER.error("player minItemsAsPossible playerMoney getCoin null, uid:{} coinType:{}",
                            player.getUid(), itemConf.getType());
                    return NKErrorCode.ItemNumNotEnough;
                }

                if (playerMoney.getCoin(itemConf.getId()) == null) {
                    CoinInfo coinInfo = new CoinInfo();
                    coinInfo.setCoinType(itemConf.getId());
                    playerMoney.putCoin(itemConf.getId(), coinInfo);
                }

                beforeNum = playerMoney.getCoin(itemConf.getId()).getCoinNum();
                if (beforeNum < itemInfo.getItemNum()) {
                    return NKErrorCode.ItemNumNotEnough;
                }
                playerMoney.getCoin(itemConf.getId()).addCoinNum(-itemInfo.getItemNum());
                long afterNum = playerMoney.getCoin(itemConf.getId()).getCoinNum();
                if (afterNum < 0) {
                    playerMoney.getCoin(itemConf.getId()).setCoinNum(0);
                    LOGGER.warn("minItemsAsPossible afterNum < 0, uid:{} itemId:{} itemInfo:{}",
                            player.getUid(), itemConf.getId(), itemInfo.toString());
                }
                changeCoinEvent.addChangeCoinInfo(
                        ChangedItemInfo.newBuilder().
                                setItemId(itemConf.getId()).
                                setCountAfterChange(afterNum).
                                setCountBeforeChange(beforeNum)
                );
                new PlayerCostCurrencyEvent(player)
                        .setCurrencyInfo(
                                ItemInfo.newBuilder()
                                        .setItemId(itemConf.getId())
                                        .setItemNum(itemInfo.getItemNum())
                                        .build())
                        .dispatch();
                player.getPlayerEventManager().dispatch(new CostCurrencyEvent(player.getConditionMgr())
                        .setCurrencyInfo(
                                ItemInfo.newBuilder()
                                        .setItemNum(itemInfo.getItemNum())
                                        .setItemId(itemConf.getId())
                                        .build()));
                minItems.getTlogData().setSequenceId(this.getSequenceIdForMinItem());
                TlogFlowMgr.sendMoneyFlow(player, itemConf.getId(), beforeNum,
                        playerMoney.getCoin(itemConf.getId()).getCoinNum(), itemInfo.getItemNum(),
                        ADDORREDUCE.REDUCE, 0, minItems.getTlogData());
            }

        }
        if (!changeCoinEvent.getChangeCoinMap().isEmpty()) {
            changeCoinEvent.dispatch();
        }
        ntfToDSItemsChangeAsync(minItems.getChangeReason(), minItems.getChangeItems());
        return NKErrorCode.OK;
    }

    // 尽最大努力扣除道具, 直到该道具扣除到0为止, 用于idip扣除道具场景
    public NKErrorCode minItemsAsPossibleForIdip(ChangedItems minItems) {
        for (ChangeItem changeItem : minItems.getChangeItems()) {
            ItemInfo itemInfo = changeItem.getSrcItem();
            if (itemInfo.getItemNum() < 0) {
                LOGGER.error("player minItemsAsPossible itemNum < 0 error, player Uid:{} itemID:{} itemNum:{}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum());
                continue;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemConf == null) {
                LOGGER.error("player minItemsAsPossible get item config null, player Uid:{} itemID:{} itemNum:{}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum());
                continue;
            }
            long beforeNum;
            if (!itemConf.getType().equals(ItemType.ItemType_Currency)) {
                beforeNum = itemManager.GetItemNumByItemId(itemInfo.getItemId());
                // 尽最大努力去扣除道具, 最差情况下是道具数量已经扣除到0, 但待扣除数量非0
                itemManager.minItemForIdip(itemConf, itemInfo.getItemNum());
                TlogFlowMgr.sendItemFlow(player, itemConf.getType().getNumber(), itemConf.getId(),
                        itemInfo.getItemNum(), itemInfo.getExpireTimeMs(), beforeNum,
                        itemManager.GetItemNumByItemId(itemInfo.getItemId()),
                        ADDORREDUCE.REDUCE, itemInfo.getLessorUid(), minItems.getTlogData());
            } else {
                CoinType coinType = CoinType.forNumber(itemConf.getId());
                if (coinType == CoinType.CT_Diamond) { // 该函数不支持钻石操作, 钻石需要另行处理
                    continue;
                }
                if (playerMoney.getCoin() == null) {
                    LOGGER.error("player minItemsAsPossible playerMoney getCoin null, uid:{} coinType:{}",
                            player.getUid(), itemConf.getType());
                    continue;
                }
                if (playerMoney.getCoin(itemConf.getId()) == null) {
                    playerMoney.putCoin(itemConf.getId(), new CoinInfo().setCoinType(itemConf.getId()));
                }

                beforeNum = playerMoney.getCoin(itemConf.getId()).getCoinNum();
                long operateNum = itemInfo.getItemNum();
//                if (beforeNum < itemInfo.getItemNum()) {
//                    operateNum = beforeNum;
//                }
                playerMoney.getCoin(itemConf.getId()).addCoinNum(-operateNum);
//                long afterNum = playerMoney.getCoin(itemConf.getId()).getCoinNum();
//                if (afterNum < 0) {
//                    playerMoney.getCoin(itemConf.getId()).setCoinNum(0);
//                    LOGGER.warn("minItemsAsPossible afterNum < 0, uid:{} itemId:{} itemInfo:{}",
//                            player.getUid(), itemConf.getId(), itemInfo.toString());
//                }
                TlogFlowMgr.sendMoneyFlow(player, itemConf.getId(), beforeNum,
                        playerMoney.getCoin(itemConf.getId()).getCoinNum(), itemInfo.getItemNum(),
                        ADDORREDUCE.REDUCE, 0, minItems.getTlogData());
            }
        }
        return NKErrorCode.OK;
    }

    /**
     * 离线用户扣除道具(idip操作场景)
     *
     * @param openId
     * @param platId
     * @param uid
     * @param businessBillNo
     * @param itemMap
     * @param coinMap
     * @param playerBuilder
     * @param retMap
     */
    public static void offlineMinItemsForIdip(String openId, int platId, long uid, String businessBillNo,
            HashMap<Integer, Long> itemMap, HashMap<Integer, Long> coinMap,
            TcaplusDb.Player.Builder playerBuilder, HashMap<Integer, Long> retMap) {

        if (itemMap == null || coinMap == null) {
            return;
        }

        if (!itemMap.isEmpty()) {
            // 处理道具
            ItemInfoDb itemInfoDb = new ItemInfoDb();
            itemInfoDb.mergeFromDto(playerBuilder.getUserAttr().getItemInfo());

            if (playerBuilder.getChangedUserAttrBuilder().hasItemInfo()) {
                // 合并changedUserAttr里的数据
                itemInfoDb.mergeFromDto(playerBuilder.getChangedUserAttr().getItemInfo());
            }

            // 获取道具id->道具结构体id之间的映射关系
            Map<Integer, Long> itemMapTmp = new HashMap<>();
            for (Item item : itemInfoDb.getItem().values()) {
                if (!itemMapTmp.containsKey(item.getItemId())) {
                    itemMapTmp.put(item.getItemId(), item.getId());
                }
            }

            itemMap.forEach((itemId, operateNum) -> {
                long afterNum = 0L;

                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
                if (itemConf != null && itemMapTmp.containsKey(itemId)) {
                    long id = itemMapTmp.get(itemId);
                    if (itemInfoDb.getItem(id) != null) {
                        Item item = itemInfoDb.getItem(id);
                        long beforeNum = item.getNumber();
                        afterNum = beforeNum - operateNum;
                        if (afterNum == 0L) {
                            itemInfoDb.removeItem(id);
                        } else {
                            item = item.setNumber(afterNum);
                            itemInfoDb.putItem(id, item);
                        }
                        if (LOGGER.isDebugEnabled()) {
                            LOGGER.debug(
                                    "openId:{}, platId:{}, uid:{}, itemId:{}, operateNum:{}, beforeNum:{}, afterNum:{}",
                                    openId, platId, uid, itemId, operateNum, beforeNum, afterNum);
                        }

                        TlogFlowMgr.offlineSendItemFlow(openId, platId, uid, playerBuilder,
                                itemConf.getType().getNumber(),
                                itemConf.getId(), operateNum, 0L, beforeNum, afterNum, ADDORREDUCE.REDUCE,
                                ItemChangeReason.ICR_IdipModify.getNumber(), 0, 0, businessBillNo);
                    }
                }

                retMap.put(itemId, afterNum);
            });

            playerBuilder.getChangedUserAttrBuilder().clearItemInfo();
            itemInfoDb.copyToDb(playerBuilder.getUserAttrBuilder().getItemInfoBuilder());
        }

        if (!coinMap.isEmpty()) {
            // 处理普通货币
            Money money = new Money();
            money.mergeFromDto(playerBuilder.getUserAttr().getMoney());

            if (playerBuilder.getChangedUserAttrBuilder().hasMoney()) {
                // 合并changedUserAttr里的数据
                money.mergeFromDto(playerBuilder.getChangedUserAttr().getMoney());
            }

            coinMap.forEach((coinType, operateNum) -> {
                long afterNum = 0L;
                if (money.getCoin(coinType) != null) {
                    CoinInfo coinInfo = money.getCoin(coinType);
                    long beforeNum = coinInfo.getCoinNum();
                    afterNum = beforeNum - operateNum;
                    coinInfo = coinInfo.setCoinNum(afterNum);
                    money.putCoin(coinType, coinInfo);
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug(
                                "openId:{}, platId:{}, uid:{}, coinType:{}, operateNum:{}, beforeNum:{}, afterNum:{}",
                                openId, platId, uid, coinType, operateNum, beforeNum, afterNum);
                    }

                    TlogFlowMgr.offlineSendMoneyFlow(openId, platId, uid, playerBuilder, coinType, beforeNum, afterNum,
                            operateNum, ADDORREDUCE.REDUCE, ItemChangeReason.ICR_IdipModify.getNumber(), 0,
                            0, 0, businessBillNo);
                }
                retMap.put(coinType, afterNum);
            });

            playerBuilder.getChangedUserAttrBuilder().clearMoney();
            money.copyToDb(playerBuilder.getUserAttrBuilder().getMoneyBuilder());
        }
    }

    public NKErrorCode MinItems(ChangedItems minItems) {
        if (!isItemsEnough(minItems)) {
            return NKErrorCode.ItemNumNotEnough;
        }

        return minItemsAsPossible(minItems);
    }

    public NKErrorCode MinItems(ChangedItems minItems, long subReason) {
        if (!isItemsEnough(minItems)) {
            return NKErrorCode.ItemNumNotEnough;
        }
        minItems.getTlogData().setChangeSubReason(subReason);
        return minItemsAsPossible(minItems);
    }

    public NKErrorCode MinItem(int itemId, Integer itemNum, ItemChangeReason reason, long subReason, String billNo) {
        ChangedItems costItems = new ChangedItems(reason.getNumber(),
                String.format("%d:%d", itemId, itemNum));
        costItems.setBusBillNo(billNo);
        costItems.mergeItemInfo(itemId, itemNum);

        return MinItems(costItems, subReason);
    }

    public NKErrorCode MinItem(int itemId, Integer itemNum, ItemChangeReason reason, long subReason, String billNo, List<Long> changeReservedParams) {
        ChangedItems costItems = new ChangedItems(reason.getNumber(),
                String.format("%d:%d", itemId, itemNum));
        costItems.setBusBillNo(billNo);
        costItems.mergeItemInfo(itemId, itemNum);
        costItems.addChangeReservedParams(changeReservedParams);
        return MinItems(costItems, subReason);
    }

    private NKPair<NKErrorCode, Map<Integer, Item_BackpackItem>> batchGetItemConf(ChangedItems changeItems) {
        HashMap<Integer, Item_BackpackItem> ret = new HashMap<>();
        for (ChangeItem itemInfo : changeItems.getChangeItems()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getSrcItemId());
            if (itemConf == null) {
                LOGGER.error("item {} config not found", itemInfo.getSrcItemId());
                return new NKPair<>(NKErrorCode.ResNotFound, null);
            }
            ret.putIfAbsent(itemConf.getId(), itemConf);
            for (ItemInfo actItemInfo : itemInfo.getActItems().getItemsList()) {
                Item_BackpackItem actItemConf = BackpackItem.getInstance().get(actItemInfo.getItemId());
                if (actItemConf == null) {
                    LOGGER.error("item {} config not found", actItemInfo.getItemId());
                    return new NKPair<>(NKErrorCode.ResNotFound, null);
                }
                ret.putIfAbsent(actItemConf.getId(), actItemConf);
            }
        }
        return new NKPair<>(NKErrorCode.OK, ret);
    }

    public boolean isItemsEnough(int itemId, Integer itemNum, ItemChangeReason reason) {
        ChangedItems costItems = new ChangedItems(reason.getNumber(), "");
        costItems.mergeItemInfo(itemId, itemNum);
        return isItemsEnough(costItems);
    }

    public boolean isItemsEnough(ChangedItems checkItems) {
        for ( Map.Entry<Integer, Long> item : checkItems.getMinItemsTotal().entrySet()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getKey());
            if (itemConf == null) {
                LOGGER.debug("player CheckItemsEnough get item config null, player Uid:{} itemID:{} itemNum:{}",
                        player.getUid(), item.getKey(), item.getValue());
                return false;
            }
            if (!itemConf.getType().equals(ItemType.ItemType_Currency)) {
                long itemTotalNum = itemManager.GetItemNumByItemId(item.getKey());
                if (itemTotalNum < item.getValue()) {
                    return false;
                }
            } else {
                if (itemConf.getId() == CoinType.CT_Diamond_VALUE) {
                    // nothing to do 钻石直接走米大师 不做预判断
                } else {
                    if (getMoneyNum(itemConf.getId()) < item.getValue()) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public NKPair<NKErrorCode, ItemChangeDetails> openGiftPackage(Item_BackpackItem itemConf, List<Long> params,
            int multi, int reason, String billNo, boolean withNtf) {
        ChangedItems reward = GiftPackage.GeneratePackageReward(player, itemConf, params, multi, reason);
        if (reward == null) {
            LOGGER.error("openGiftPackage null, uid:{} item:{} params:{}", player.getUid(), itemConf.getId(), params);
            return new NKPair<>(NKErrorCode.UnknownError, null);
        }
        reward.setChangeReason(reason);
        reward.setChangeParams(params == null ? "" : params.toString());
        reward.setBusBillNo(billNo);
        return player.getBagManager().AddItems2(reward, withNtf);
    }

    public NKPair<NKErrorCode, ItemChangeDetails> openGiftPackage(Item_BackpackItem itemConf, List<Long> params,
            int multi, int reason, String billNo) {
        return openGiftPackage(itemConf, params, multi, reason, billNo, true);
    }

    //清空背包道具
    public boolean clearBagItem(int itemId) {
        itemManager.clearBagItem(itemId);
        return true;
    }

    // 赛季道具过期
    public void seasonExpire(int beforeSeasonId, int newSeasonId) {
        String billNo = BillNoIdGenerator.getBusinessBillNo(String.format("SettleSeason:%d", newSeasonId));
        PlayerItemExpireRemoveEvent event = new PlayerItemExpireRemoveEvent(player);
        List<Integer> currencyItemIdList = MiscConf.getInstance().getMiscConf().getSeasonExpireCurrencyList();
        currencyItemIdList.forEach((itemId) -> {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                LOGGER.error("seasonMoneyExpire get itemConf err, uid:{} itemId:{}", player.getUid(), itemId);
                return;
            }
            if (itemConf.getType() != ItemType.ItemType_Currency) {
                HashMap<Long, Item> items = getItemManager().getItemsByItemId(itemId);
                if (!items.isEmpty()) {
                    return;
                }
                getItemManager().delExpiredItems(items.values());
            } else {
                long coinNum = getMoneyNum(itemConf.getId());
                if (coinNum > 0) {
                    event.addExpireItem(itemConf.getId(), coinNum);
                }
                setMoneyNum(itemConf.getId(), 0, ItemChangeReason.ICR_SettleSeason.getNumber(), billNo);
                LOGGER.info("settleSeason debug, uid:{}, itemId:{} coinNum:{} beforeSeasonId:{} current:{}",
                        player.getUid(), itemId, coinNum, beforeSeasonId, newSeasonId);
            }
        });
        event.dispatch();
    }

    // 货币过期需要分解道具的调用这个接口
    public void moneyExpire(int itemId) {
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (itemConf == null || itemConf.getType() != ItemType.ItemType_Currency) {
            LOGGER.error("moneyExpire error, uid:{} itemId:{}", player.getUid(), itemId);
            return;
        }
        String billNo = BillNoIdGenerator.getBusinessBillNo(String.format("moneyExpire:%d", itemId));
        long coinNum = getMoneyNum(itemId);
        if (coinNum > 0) {
            setMoneyNum(itemId, 0, ItemChangeReason.ICR_ItemExpire.getNumber(), billNo);
            new PlayerItemExpireRemoveEvent(player).addExpireItem(itemId, coinNum).dispatch();
            LOGGER.info("moneyExpire debug, uid:{}, coinNum:{} ", player.getUid(), coinNum);
        }
    }

    public void setMoneyNumWithSubReason(int coinType, long num, TlogData tlogData) {
        long beforeNum = 0;
        com.tencent.wea.attr.CoinInfo coinInfo = playerMoney.getCoin(coinType);
        if (coinInfo == null) {
            coinInfo = new com.tencent.wea.attr.CoinInfo().setCoinType(coinType);
            playerMoney.putCoin(coinType, coinInfo);
        } else {
            beforeNum = coinInfo.getCoinNum();
        }
        coinInfo.setCoinNum(num);
        long change = num - beforeNum;
        LOGGER.info("money change, coinType:{} before:{} after:{} reason:{} no:{}", coinType, beforeNum, num,
                tlogData.getChangeReason(), tlogData.getBusBillNo());
        if (change == 0) {
            return;
        }
        int changeType;
        if (change > 0) {
            changeType = ADDORREDUCE.ADD;
        } else {
            changeType = ADDORREDUCE.REDUCE;
        }
        long afterNum = playerMoney.getCoin(coinInfo.getCoinType()).getCoinNum();
        TlogFlowMgr.sendMoneyFlow(player, coinInfo.getCoinType(), beforeNum,
                afterNum, change, changeType, 0, tlogData);
        if (changeType == ADDORREDUCE.ADD) {
            ItemInfo itemInfo = ItemInfo.newBuilder()
                    .setItemId(coinInfo.getCoinType())
                    .setItemNum(coinInfo.getCoinNum())
                    .build();
            ItemArray changedItems = ItemArray.newBuilder().addItems(itemInfo).build();
            new PlayerGetItemEvent(player).setActualChangedItems(changedItems)
                    .setItemChangeReason(tlogData.getChangeReason())
                    .setItemChangeSubReason(tlogData.getChangeSubReason()).dispatch();

            ItemInfo afterItemInfo = ItemInfo.newBuilder()
                    .setItemId(coinInfo.getCoinType())
                    .setItemNum(afterNum)
                    .build();
            ItemArray afterItems = ItemArray.newBuilder().addItems(afterItemInfo).build();
            player.getPlayerEventManager().dispatch(
                    new GetItemEvent(player.getConditionMgr()).setChangeItem(changedItems)
                            .setAfterItemInfo(afterItems));
        }
        new PlayerChangeCoinEvent(player).addChangeCoinInfo(
                ChangedItemInfo.newBuilder().
                        setItemId(coinInfo.getCoinType()).
                        setCountAfterChange(afterNum).
                        setCountBeforeChange(beforeNum)
        ).dispatch();
        if (change < 0) {
            new PlayerCostCurrencyEvent(player)
                    .setCurrencyInfo(
                            ItemInfo.newBuilder()
                                    .setItemNum(-change)
                                    .setItemId(coinInfo.getCoinType())
                                    .build())
                    .dispatch();
            player.getPlayerEventManager().dispatch(new CostCurrencyEvent(player.getConditionMgr())
                    .setCurrencyInfo(
                            ItemInfo.newBuilder()
                                    .setItemNum(-change)
                                    .setItemId(coinInfo.getCoinType())
                                    .build()));
        }
    }

    /**
     * 设置货币数量
     *
     * @param coinType 货币类型
     * @param num 数量
     */
    public void setMoneyNum(int coinType, long num, int reason, String busBillNo) {
        var tlogData = new TlogData();
        tlogData.setChangeReason(reason);
        tlogData.setBusBillNo(busBillNo);
        setMoneyNumWithSubReason(coinType, num, tlogData);
    }

    public void addMoneyNum(int coinType, long addNum, TlogData tlogData) {
        long beforeNum = 0;
        com.tencent.wea.attr.CoinInfo coinInfo = playerMoney.getCoin(coinType);
        if (coinInfo == null) {
            coinInfo = new com.tencent.wea.attr.CoinInfo().setCoinType(coinType);
            playerMoney.putCoin(coinType, coinInfo);
        } else {
            beforeNum = coinInfo.getCoinNum();
        }
        coinInfo.addCoinNum(addNum);
        LOGGER.info("addMoneyNum change, coinType:{} before:{} change:{} reason:{} no:{}", coinType, beforeNum, addNum,
                tlogData.getChangeReason(), tlogData.getBusBillNo());
        if (addNum == 0) {
            return;
        }
        int changeType;
        if (addNum > 0) {
            changeType = ADDORREDUCE.ADD;
        } else {
            changeType = ADDORREDUCE.REDUCE;
        }
        long afterNum = playerMoney.getCoin(coinInfo.getCoinType()).getCoinNum();
        TlogFlowMgr.sendMoneyFlow(player, coinInfo.getCoinType(), beforeNum, afterNum, addNum, changeType, 0, tlogData);
        if (changeType == ADDORREDUCE.ADD) {
            ItemInfo itemInfo = ItemInfo.newBuilder()
                    .setItemId(coinInfo.getCoinType())
                    .setItemNum(coinInfo.getCoinNum())
                    .build();
            ItemArray changedItems = ItemArray.newBuilder().addItems(itemInfo).build();
            new PlayerGetItemEvent(player).setActualChangedItems(changedItems)
                    .setItemChangeReason(tlogData.getChangeReason())
                    .setItemChangeSubReason(tlogData.getChangeSubReason()).dispatch();

            ItemInfo afterItemInfo = ItemInfo.newBuilder()
                    .setItemId(coinInfo.getCoinType())
                    .setItemNum(afterNum)
                    .build();
            ItemArray afterItems = ItemArray.newBuilder().addItems(afterItemInfo).build();
            player.getPlayerEventManager().dispatch(
                    new GetItemEvent(player.getConditionMgr()).setChangeItem(changedItems)
                            .setAfterItemInfo(afterItems));
        }
        new PlayerChangeCoinEvent(player).addChangeCoinInfo(
                ChangedItemInfo.newBuilder().
                        setItemId(coinInfo.getCoinType()).
                        setCountAfterChange(afterNum).
                        setCountBeforeChange(beforeNum)
        ).dispatch();
        if (addNum < 0) {
            new PlayerCostCurrencyEvent(player)
                    .setCurrencyInfo(
                            ItemInfo.newBuilder()
                                    .setItemNum(-addNum)
                                    .setItemId(coinInfo.getCoinType())
                                    .build())
                    .dispatch();
            player.getPlayerEventManager().dispatch(new CostCurrencyEvent(player.getConditionMgr())
                    .setCurrencyInfo(
                            ItemInfo.newBuilder()
                                    .setItemNum(-addNum)
                                    .setItemId(coinInfo.getCoinType())
                                    .build()));
        }
    }

    public long getMoneyNum(int coinType) {
        com.tencent.wea.attr.CoinInfo coinInfo = playerMoney.getCoin(coinType);
        if (coinInfo == null) {
            return 0;
        }
        return playerMoney.getCoin(coinType).getCoinNum();
    }

    private void filterItemNtf(CsBag.BagCommonGetItemsNtf.Builder msg) {
        // 过滤不需要下发通知的道具
        List<BagGetItem> itemList = new ArrayList<>();
        for (BagGetItem bagGetItem : msg.getItemsList()) {
            Item_BackpackItem itemConfig = BackpackItem.getInstance().get(bagGetItem.getSrcItem().getItemId());
            if (itemConfig != null && noNtfItemTypes.contains(itemConfig.getType())) {
                continue;
            }
            itemList.add(bagGetItem);
        }
        msg.clearItems().addAllItems(itemList);
    }

    public void sendGetItemsNtf(ItemChangeDetails realChanges) {
        this.lastAddItemLogSeqId += 1;
        this.bagCommonGetItemsNtfSeqId += 1;
        CsBag.BagCommonGetItemsNtf.Builder msg = realChanges.genBagGetItemsNtf();
        CsBag.BagCommonGetItemsNtf.Builder sendMsg = CsBag.BagCommonGetItemsNtf.newBuilder().mergeFrom(msg.build());
        filterItemNtf(sendMsg);
        if (sendMsg.getItemsList().isEmpty()) {
            return;
        }
        sendMsg.setSeqId(this.bagCommonGetItemsNtfSeqId);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_BAGCOMMONGETITEMSNTF, sendMsg);
        msgMap.put(sendMsg.getSeqId(),
                new BagCommonGetItemsNtfMsg(DateUtils.currentTimeMillis(), sendMsg));
    }

    public void commitBagCommonGetItemsMsg(long seqId, List<Long> seqIds) {
        msgMap.remove(seqId);
        seqIds.forEach((removeId) -> {
            msgMap.remove(removeId);
        });
    }

    private void dispatchBagCommonGetItems() {
        if (msgMap.size() > 0) {
            msgMap.forEach((seqId, msgInfo) -> {
                // send mail
                sendBagCommonGetItemMail(msgInfo.getBody());
            });
            msgMap.clear();
        }
    }

    private void sendBagCommonGetItemMail(CsBag.BagCommonGetItemsNtf.Builder msg) {
        ItemChangeDetails changeDetails = new ItemChangeDetails(msg);
        MailInteraction.sendTemplateMail(player.getUid(), expireMailTempateId,
                MailInteraction.TlogSendReason.bagGetItemNtfMail, changeDetails.getChangeReasonText(),
                changeDetails.getChangedItemsText());
    }

    public void refresh() {
        long currentTimeMs = DateUtils.currentTimeMillis();
        int beforeNum = msgMap.size();
        HashMap<Long, BagCommonGetItemsNtfMsg> needRemoveSeqMap = new HashMap<>();
        msgMap.forEach((seqId, msgInfo) -> {
            if (currentTimeMs - msgInfo.getSendTimeMs() >= deltaTimeMs) {
                // need remove
                needRemoveSeqMap.put(seqId, msgInfo);
            }
        });
        if (!needRemoveSeqMap.isEmpty()) {
            for (long seqId : needRemoveSeqMap.keySet()) {
                msgMap.remove(seqId);
            }
            needRemoveSeqMap.forEach((seqId, msgInfo) -> {
                if (currentTimeMs - msgInfo.getSendTimeMs() >= deltaTimeMs) {
                    // send mail
                    sendBagCommonGetItemMail(msgInfo.getBody());
                }
            });
            LOGGER.debug("check bagCommonGetItemNtf msg pool, uid:{} beforeNum:{} removeNum:{} afterNum:{}",
                    player.getUid(), beforeNum, needRemoveSeqMap.keySet().size(), msgMap.size());
        }
        if (privReportData != null) {
            sendPrivReport();
            privReportData = null;
        }
    }

    public Collection<Integer> getSuitCount() {
        fixAppearanceRoadReward();
        HashMap<Integer, HashSet<Integer>> suitActiveType = new HashMap<>();
        Collection<SuitContent> suitsVal = SuitInformation.getInstance().getSuitsVal();
        String before = player.getUserAttr().getPlayerPublicEquipments().getActiveSuitBookList().toString();
        for (SuitInformation.SuitContent suitInfoConf : suitsVal) {
            if (checkSuitActive(suitInfoConf)) {
//                LOGGER.debug("suitbook active suit-{}", suitInfoConf.toString());
                player.getUserAttr().getPlayerPublicEquipments().addActiveSuitBook(suitInfoConf.getId());
                if (SuitInformation.getInstance().isSuitValid(suitInfoConf,player.getClientVersion64())) {
                    if (!suitActiveType.containsKey(suitInfoConf.getAppearanceRoadType())) {
                        suitActiveType.put(suitInfoConf.getAppearanceRoadType(), new HashSet<>());
                    }
                    suitActiveType.get(suitInfoConf.getAppearanceRoadType()).add(suitInfoConf.getId());
                } else if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("suitInfoConf not valid ignore, uid:{} suitConf:{} playerVersion:{}",
                            player.getUid(),suitInfoConf.toString(),player.getClientVersion64());
                }
            } else {
                if (player.getUserAttr().getPlayerPublicEquipments().getActiveSuitBook()
                        .contains(suitInfoConf.getId())) {
                    player.getUserAttr().getPlayerPublicEquipments().removeActiveSuitBook(suitInfoConf.getId());
                    LOGGER.info("suitbook remove suit-{}", suitInfoConf.toString());
                }
            }
        }
        if (LOGGER.isDebugEnabled() && player.getUserAttr().getPlayerPublicEquipments().getActiveSuitBook().isDirty()) {
            String after = player.getUserAttr().getPlayerPublicEquipments().getActiveSuitBookList().toString();
            LOGGER.debug("checkSuitActive change, uid:{} before:{} after:{}", player.getUid(), before, after);
        }
        player.getBagManager().updateAppearanceByType(suitActiveType);
        return player.getUserAttr().getPlayerPublicEquipments().getActiveSuitBookList();
    }

    private boolean checkSuitActive(SuitInformation.SuitContent suitInfoConf) {
//        LOGGER.debug("check suit active, uid:{} suitId:{} items:{}",
//                player.getUid(), suitInfoConf.getId(), suitInfoConf.getItems());
        if (suitInfoConf.isInvalid()) {
            return false;
        }
        for (int suitItemId : suitInfoConf.getItems()) {
            if (suitItemId == 0) {
                continue;
            }
            if (itemManager.getItemNumByItemIdIgnoreTemp(suitItemId) == 0) {
//                LOGGER.debug("check suit active item not exist, uid:{} suitId:{} suitItemId:{}",
//                        player.getUid(), suitInfoConf.getId(), suitItemId);
                return false;
            }
        }
        return true;
    }

    public List<Item> delExpiredItems() {
        return itemManager.delExpiredItems();
    }


    public NKErrorCode isAddedItemsValid(ChangedItems changeItems) {
        if (changeItems.getChangeItems().size() == 0) {
            return NKErrorCode.OK;
        }

        NKPair<NKErrorCode, Map<Integer, Item_BackpackItem>> checkConfRet = batchGetItemConf(changeItems);
        if (!checkConfRet.getKey().isOk()) {
            return checkConfRet.getKey();
        }

        // 自动打开普通和随机礼包
        ChangedItems itemsAfterOpen = changeItems.autoOpenPackage(player);

        // 将超上限的道具替换掉
        itemsAfterOpen.replaceExceedMaxItems(player);

        NKPair<NKErrorCode, Map<Integer, Item_BackpackItem>> getConfRet = batchGetItemConf(itemsAfterOpen);
        if (!getConfRet.getKey().isOk()) {
            return getConfRet.getKey();
        }
        Map<Integer, Item_BackpackItem> confMap = getConfRet.getValue();

        if (itemManager.isBagGirdsExceedLimit(itemsAfterOpen, confMap)) {
            LOGGER.error("player backpack grids exceed limit, uid:{}", player.getUid());
            return NKErrorCode.CheckBagGridNotEnough;
        }
        return NKErrorCode.OK;
    }

    public NKPair<String, Integer> getPrivReportHostInfo() {
        String l5addr = PropertyFileReader.getRealTimeItem("priv_report_l5", "");
        String l5ns = PropertyFileReader.getRealTimeItem("priv_report_l5_ns", "Production");
        if (l5addr.isEmpty()) {
            LOGGER.error("getPrivReportHostInfo error, priv_report_l5 is empty");
            return null;
        }
        NKPair<String, Integer> hostInfo = PolarisUtil.discover(l5addr, l5ns);
        if (hostInfo == null) {
            LOGGER.error("privReport discover l5addr {} l5ns {} fail", l5addr, l5ns);
            return null;
        }
        return hostInfo;
    }

    private void sendPrivReport() {
        if (privReportData == null) {
            return;
        }
        PrivReportData reportData = privReportData;
        long addNum = reportData.addNum;
        long afterNum = reportData.afterNum;
        if (addNum <= 0) {
            return;
        }
        if (!PropertyFileReader.getRealTimeBooleanItem("priv_report_enable", false)) {
            return;
        }
        long nowMs = reportData.nowMs;
        long nowSec = nowMs / 1000;
        int seqId = 0;
        if (nowSec == lastPrivReportSec) {
            seqId = ++privSeqId;
        } else {
            lastPrivReportSec = nowSec;
            privSeqId = 0;
        }
        String serialNo = NKStringFormater.format("{}-{}-{}", player.getUid(), nowSec, seqId);
        String urlParam = NKStringFormater.format(
                "&sRoleId={}&sOpenId={}" +
                        "&iNowNum={}&iTodayNum={}&iTimestamp={}&sSerial={}",
                player.getRoleId(), player.getOpenId(),
                addNum, afterNum, nowMs, serialNo);
        try {
            CurrentExecutorUtil.runJob(() -> {
                LOGGER.debug("addNum {} afterNum {} serialNo {}", addNum, afterNum, serialNo);
                NKPair<String, Integer> hostInfo = null;
                String failReason = null;
                String ret = "";
                try {
                    hostInfo = getPrivReportHostInfo();
                    if (hostInfo == null) {
                        throw new NullPointerException("getPrivReportHostInfo null");
                    }
                    String url = NKStringFormater.format(
                            PRIV_REPORT_URL,
                            hostInfo.getKey(), hostInfo.getValue()) + urlParam;
                    ret = HttpClient.getInstance().doGet(url, new HashMap<>());
                    LOGGER.debug("url {} ret {}", url, ret);
                    if (ret.isEmpty()) {
                        failReason = "empty response";
                    } else {
                        SsCommon.PrivReportSyncActivePointParam.Builder retPb = SsCommon.PrivReportSyncActivePointParam.newBuilder();
                        JsonUtil.fromJsonIgnoreUnknownFields(ret, retPb);
                        if (retPb.getRet() == 0 && retPb.getIRet() == 0) {
                            LOGGER.debug("success");
                        } else {
                            LOGGER.error("url {} ret {} iRet {} fullJson {}", url, retPb.getRet(), retPb.getIRet(),
                                    ret);
                            failReason = NKStringFormater.format("ret {} iRet {}", retPb.getRet(), retPb.getIRet());
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("sendPrivReport error", e);
                    failReason = NKStringFormater.format("send fail: {}", e.getMessage());
                } finally {
                    if (failReason != null) {
                        TlogFlowMgr.sendPrivReportFailFlow(
                                player, addNum, afterNum, serialNo,
                                hostInfo == null ? "" : hostInfo.getKey(),
                                hostInfo == null ? 0 : hostInfo.getValue(),
                                failReason, ret);
                    }
                }
                return null;
            }, "sendPrivReport", false);
        } catch (NKCheckedException e) {
            LOGGER.error("sendPrivReport error", e);
            TlogFlowMgr.sendPrivReportFailFlow(player, addNum, afterNum, serialNo, "", 0,
                    NKStringFormater.format("runJob fail: {}", e.getMessage()), "");
        }
    }

    public ItemManager getItemManager() {
        return itemManager;
    }

    public ItemEquipMgr getItemEquipMgr() {
        return itemEquipMgr;
    }

    private int getSequenceIdForAddItem() {
        if (this.lastAttrVersionForAdd != player.getUserAttr().getAttrVersion()) {
            this.lastAttrVersionForAdd = player.getUserAttr().getAttrVersion();
            this.lastAddItemLogSeqId = 0;
        }
        this.lastAddItemLogSeqId += 1;
        return (int) this.lastAttrVersionForAdd * 1000 + (this.lastAddItemLogSeqId);
    }

    private int getSequenceIdForMinItem() {
        if (this.lastAttrVersionForMin != player.getUserAttr().getAttrVersion()) {
            this.lastAttrVersionForMin = player.getUserAttr().getAttrVersion();
            this.lastMinItemLogSeqId = 0;
        }
        this.lastMinItemLogSeqId += 1;
        return (int) this.lastAttrVersionForMin * 1000 + (this.lastMinItemLogSeqId);
    }

    private int keepSequenceId() {
        if (this.lastAttrVersionForAdd == 0) {
            this.lastAttrVersionForAdd = player.getUserAttr().getAttrVersion();
        }
        return (int) this.lastAttrVersionForAdd * 1000 + (this.lastAddItemLogSeqId);
    }

    public List<Integer> getUnlockedFittingSlot() {
        return itemEquipMgr.getUnlockedFittingSlotIds(1);
    }

    public void unlockedFittingSlot(int slotId) {
        FittingSlot fittingSlot = player.getUserAttr().getFittingSlots().getSlots(slotId);
        if (fittingSlot != null) {
            NKErrorCode.FittingSlotUnlocked.throwError("FittingSlot already unlocked,slotId:{}", slotId);
            return;
        }
        CloakroomUnlock fittingSlotConfig = FittingSlotUnlockConf.getInstance().get(slotId);
        if (fittingSlotConfig == null) {
            NKErrorCode.InvalidParams.throwError("CloakroomUnlockConfig not found");
            return;
        }
        if (fittingSlotConfig.getCostCoinType() > 0 && fittingSlotConfig.getCostCoinNum() > 0) {
            NKErrorCode ret = MinItem(fittingSlotConfig.getCostCoinType(), fittingSlotConfig.getCostCoinNum(),
                    ItemChangeReason.ICR_UnlockFittingSlot, 0, "");
            if (ret == NKErrorCode.OK) {
                itemEquipMgr.unlockedFittingSlot(slotId);
            } else {
                ret.throwErrorIfNotOk("minItems err");
            }
        }
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {
    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {
        itemEquipMgr.afterLoad();
    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {
        try {
            init();
            tryAddDailyBattleDefaultItems();
        } catch (Exception e) {
            LOGGER.error(" onLogin error, uid:{} e:{}", player.getUid(), e);
        }
    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        delExpiredItems();
        getItemManager().afterLogin();
        getItemEquipMgr().afterLogin();
        getSuitCount();
        if (todayFirstLogin) {
            String[] monitorParams = new String[]{
                    String.valueOf(itemManager.getItmSize()/1000),
                    String.valueOf(itemManager.getItmSize()/100),
                    String.valueOf(itemManager.getItmSize()/10),
            };
            Monitor.getInstance().add.total(MonitorId.attr_player_item_size, 1, monitorParams);
        }
        //player.xhhRetryFailureBill(); // 小红花重试逻辑


        boolean isUpdateOnline =player.getUserAttr().getSafetyCheck().getIsUpdateOnline();
        if(isUpdateOnline) {
            return;
        }

        MapAttrObj<Integer, Interaction> interactions = player.getUserAttr().getInteractions();
        if(interactions == null || interactions.size()<=0) {
            return;
        }


        // Map<Integer, Interaction> newInteractions = new HashMap<>();
        List<Integer> keys = new ArrayList<>();

        for (Interaction interaction : interactions.values()) {

            Item item =  player.getItemManager().getItemByUUID(interaction.getItemUuid());
            if (item == null) {
                continue;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                continue;
            }
            int vehicleValue=BagVehicleType.FACE_VEHICLE_VALUE;
            if (itemConf.getType().equals(ItemType.ItemType_Vehicle )) {
                if(interaction.getPos()>=vehicleValue){
                    continue;
                }

                //int lastkey=interaction.getPos();
                //int interactionKey=lastkey+ vehicleValue;
                //   interaction.setPos(interactionKey);
                //newInteractions.put(interactionKey, new Interaction().setPos(interactionKey) interaction);

                keys.add(interaction.getPos());
            }
        }

        for(int needRemoveKey : keys){
            if (interactions.containsKey(needRemoveKey)) {
                var interaction=interactions.remove(needRemoveKey);
                int targetPos=needRemoveKey+BagVehicleType.FACE_VEHICLE_VALUE;;
                interaction.setPos(targetPos);
                interactions.put(targetPos,interaction);
            }
        }

        player.getUserAttr().getSafetyCheck().setIsUpdateOnline(true);
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {
        updateDressUpScore();
        itemManager.calcGatherUpIds();
        updateDressCount();
    }

    @Override
    public void onLogout() {
        dispatchBagCommonGetItems();
    }

    @Override
    public void onMidNight() {

    }

    @Override
    public void onWeekStart() {

    }

    /**
     * 异步通知ds物品变化
     *
     * @param reason
     * @param actualChangeItems
     * @return
     */
    public boolean ntfToDSItemsChangeAsync(int reason, ItemArray actualChangeItems) {
        boolean needNtf = false;
        for (ItemInfo itemInfo : actualChangeItems.getItemsList()) {
            if (needNtfToDS(itemInfo.getItemId())) {
                needNtf = true;
                break;
            }
        }

        if (needNtf) {

            ItemChangeReason itemChangeReason = ItemChangeReason.forNumber(reason);
            long uid = player.getUid();
            SsBattlesvr.RpcGSItemChangeReq.Builder rpcGSItemChangeReqBuilder = getGSItemChangeReqBuilder(uid,
                    itemChangeReason);
            if (rpcGSItemChangeReqBuilder == null) {
                return false;
            }
            for (ItemInfo itemInfo : actualChangeItems.getItemsList()) {
                long newNum = getItemNumByItemId(itemInfo.getItemId());
                LOGGER.debug("ntfToDSItemsChangeAsync  itemId:{},itemNum:{} ", itemInfo.getItemId(), newNum);
                rpcGSItemChangeReqBuilder.getItemsChangeBuilder()
                        .addChangedItems(DSItem.newBuilder()
                                .setItemId(itemInfo.getItemId())
                                .setItemChangeNum(itemInfo.getItemNum())
                                .setNewItemNum(newNum)
                        );
            }

            sendRpcGSItemChange(rpcGSItemChangeReqBuilder, true);
        }

        return needNtf;
    }

    public boolean ntfToDSItemsChangeAsync(int reason, List<ChangeItem> changeItemList) {
        boolean needNtf = false;
        for (ChangeItem changeItem : changeItemList) {
            if (changeItem.getSrcItem() != null && needNtfToDS(changeItem.getSrcItem().getItemId())) {
                needNtf = true;
                break;
            }
        }
        if (needNtf) {
            ItemChangeReason itemChangeReason = ItemChangeReason.forNumber(reason);
            long uid = player.getUid();
            SsBattlesvr.RpcGSItemChangeReq.Builder rpcGSItemChangeReqBuilder = getGSItemChangeReqBuilder(uid,
                    itemChangeReason);
            if (rpcGSItemChangeReqBuilder == null) {
                return false;
            }
            for (ChangeItem changeItem : changeItemList) {
                ItemInfo itemInfo = changeItem.getSrcItem();
                if (itemInfo != null) {
                    long newNum = getItemNumByItemId(itemInfo.getItemId());
                    rpcGSItemChangeReqBuilder.getItemsChangeBuilder()
                            .addChangedItems(DSItem.newBuilder()
                                    .setItemId(itemInfo.getItemId())
                                    .setItemChangeNum(-itemInfo.getItemNum())
                                    .setNewItemNum(newNum)
                            );
                }
            }

            sendRpcGSItemChange(rpcGSItemChangeReqBuilder, true);
        }

        return needNtf;
    }

    /**
     * DS物品修改
     *
     * @param dsItemsChange
     */
    public DSItemsChange dsUpdateItem(DSItemsChange dsItemsChange) {
        ItemChangeReason reason = dsItemsChange.getReason();
        //说明是ds更新后第一次将物品转换成gs的物品
        if (reason == ItemChangeReason.ICR_TYC_Init) {
            ChangedItems minItems = new ChangedItems(reason.getNumber(), "min");
            ChangedItems addItems = new ChangedItems(reason.getNumber(), "add");
            boolean haveCrystal = false;
            for (DSItem dsItem : dsItemsChange.getChangedItemsList()) {
                int itemId = dsItem.getItemId();
                int changeNum = (int) dsItem.getItemChangeNum();
                if (!isTYCItem(itemId) || changeNum < 0) {
                    NKErrorCode.ItemCanNotChangeByDS.throwError(
                            NKStringFormater.format("DsBag not tyc item itemId:{} changeNum:{} reason:{}"
                                    , itemId, dsItem.getItemChangeNum(), reason));
                }
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
                if (itemConf == null) {
                    NKErrorCode.ItemCanNotBuy.throwError(
                            NKStringFormater.format("DsBag conf is null itemId:{} changeNum:{} reason:{}"
                                    , itemId, changeNum, reason));
                }
                // 先扣除身上的拥有的这个物品
                long curNum = getItemNumByItemId(itemId);
                if (curNum > 0) {
                    minItems.mergeItemInfo(itemId, (int) curNum, 0);
                }

                // 再增加ds传来需要存储的物品
                addItems.mergeItemInfo(itemId, changeNum, 0);
                if (needNtfToDS(itemId)) {
                    haveCrystal = true;
                }
            }
            //检查货币是否不足
            if (!isItemsEnough(minItems)) {
                NKErrorCode.ItemNumNotEnough.throwError(
                        NKStringFormater.format("DsBag minItems:{} not enough reason:{}", minItems, reason));
            }
            //扣除物品
            MinItems(minItems);
            //添加物品
            AddItems2(addItems, false);
            //装填消息
            DSItemsChange.Builder replyBuilder = DSItemsChange.newBuilder()
                    .setUid(dsItemsChange.getUid()).setReason(dsItemsChange.getReason());
            if (haveCrystal == false) {
                for (DSItem dsItem : dsItemsChange.getChangedItemsList()) {
                    //因为货币走AddItems2中的ntf同步，不再这里同步了
                    if (needNtfToDS(dsItem.getItemId()) && dsItem.getItemChangeNum() > 0) {
                        continue;
                    }
                    long curNum = getItemNumByItemId(dsItem.getItemId());
                    replyBuilder.addChangedItems(DSItem.newBuilder()
                            .setItemId(dsItem.getItemId())
                            .setItemChangeNum(dsItem.getItemChangeNum())
                            .setNewItemNum(curNum)
                    );
                }
            }
            // 通知dsdbsvr记录已经转换过物品了
            long uid = player.getUid();
            SsDsdbsvr.RpcDsUpdateBackpackReq.Builder updateBackpackBuilder = SsDsdbsvr.RpcDsUpdateBackpackReq.newBuilder()
                    .setUid(uid)
                    .setMatchType(30)
                    .setSlot(1);
            //用来标识新版本的背包
            updateBackpackBuilder.getBackpackInfoBuilder().addDsOwnerBackpackMapBuilder()
                    .setBackpackOwnerId(4000000000000000L)
                    .addItemMap(AttrDSItem.proto_DSItem.newBuilder().setDsItemConfId(1).setDsItemNum(0))
                    .addItemMap(AttrDSItem.proto_DSItem.newBuilder().setDsItemConfId(2).setDsItemNum(0));
            try {
                DsdbService.get().rpcDsUpdateBackpack(updateBackpackBuilder);
            } catch (Throwable e) {
                LOGGER.error("irpcDSUpdateItems PlayerNotFoundException uid:{} req:{} e:"
                        , uid, updateBackpackBuilder, e);
                NKErrorCode.ItemCanNotChangeByDS.throwError(
                        NKStringFormater.format("irpcDSUpdateItems PlayerNotFoundException uid:{} req:{} e:"
                                , uid, updateBackpackBuilder, e));
            }
            return replyBuilder.build();
        }
        //普通的物品数量修改逻辑
        ChangedItems minItems = new ChangedItems(reason.getNumber(), "min");
        ChangedItems addItems = new ChangedItems(reason.getNumber(), "add");
        if (dsItemsChange.getParamsCount() > 0) {
            minItems.addChangeReservedParams(dsItemsChange.getParamsList());
            addItems.addChangeReservedParams(dsItemsChange.getParamsList());
        }
        //检查物品是否可以操作
        for (DSItem dsItem : dsItemsChange.getChangedItemsList()) {
            int itemId = dsItem.getItemId();
            int changeNum = (int) dsItem.getItemChangeNum();
            if (!isTYCItem(itemId)) {
                NKErrorCode.ItemCanNotChangeByDS.throwError(
                        NKStringFormater.format("DsBag not tyc item itemId:{} changeNum:{} reason:{}"
                                , itemId, dsItem.getItemChangeNum(), reason));
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                NKErrorCode.ItemCanNotBuy.throwError(
                        NKStringFormater.format("DsBag conf is null itemId:{} changeNum:{} reason:{}"
                                , itemId, changeNum, reason));
            }
            if (changeNum < 0) {
                //扣除
                minItems.mergeItemInfo(itemId, -changeNum, 0);
            } else if (changeNum > 0) {
                long curNum = getItemNumByItemId(itemId);
                //添加
                if (itemConf.getType() != ItemType.ItemType_Currency && curNum >= itemConf.getMaxNum()) {
                    NKErrorCode.ItemNumUpToMax.throwError(
                            NKStringFormater.format("DsBag itemId:{} changeNum:{} reason:{} curNum:{} maxNum:{}"
                                    , itemId, changeNum, reason, curNum, itemConf.getMaxNum()));
                }
                addItems.mergeItemInfo(itemId, changeNum, 0);
            }
        }
        //检查货币是否不足
        if (!isItemsEnough(minItems)) {
            NKErrorCode.ItemNumNotEnough.throwError(
                    NKStringFormater.format("DsBag minItems:{} not enough reason:{}", minItems, reason));
        }
        //扣除物品
        MinItems(minItems, dsItemsChange.getSubReason());
        //添加物品
        AddItems2(addItems, false);
        //装填消息
        DSItemsChange.Builder replyBuilder = DSItemsChange.newBuilder()
                .setUid(dsItemsChange.getUid()).setReason(dsItemsChange.getReason());
        for (DSItem dsItem : dsItemsChange.getChangedItemsList()) {
            //因为货币走AddItems2中的ntf同步，不再这里同步了
            if (needNtfToDS(dsItem.getItemId()) && dsItem.getItemChangeNum() > 0) {
                continue;
            }
            long curNum = getItemNumByItemId(dsItem.getItemId());
            replyBuilder.addChangedItems(DSItem.newBuilder()
                    .setItemId(dsItem.getItemId())
                    .setItemChangeNum(dsItem.getItemChangeNum())
                    .setNewItemNum(curNum)
            );
        }
        return replyBuilder.build();
    }

    /**
     * 通知DS当前物品数量
     *
     * @param itemIdList
     */
    public DSItemsChange.Builder ntfToDSItemNum(List<Integer> itemIdList) {
        long uid = player.getUid();
        DSItemsChange.Builder dsItemsChangeBuilder = DSItemsChange.newBuilder()
                .setUid(uid).setReason(ItemChangeReason.ICR_TYC_Init);
        for (Integer itemId : itemIdList) {
            long newNum = getItemNumByItemId(itemId);
            dsItemsChangeBuilder.addChangedItems(DSItem.newBuilder().setItemId(itemId).setNewItemNum(newNum));
        }
        return dsItemsChangeBuilder;
    }

    /**
     * 分解道具
     *
     * @param itemIdList 道具id
     * @param itemNumList 道具数量
     */
    public void decomposeItemList(List<Integer> itemIdList, List<Integer> itemNumList) {
        if (itemIdList.size() != itemNumList.size()) {
            LOGGER.error("decomposeItemList: itemIdList size {} != itemNumList size {}", itemIdList, itemNumList);
            return;
        }
        ItemChangeDetails itemChangeDetails = null;
        for (int i = 0; i < itemIdList.size(); i++) {
            int itemId = itemIdList.get(i);
            Item_BackpackItem itemConfig = BackpackItem.getInstance().get(itemId);
            if (itemConfig == null) {
                LOGGER.error("decomposeItemList item {} res not found", itemId);
                continue;
            }
            if (!itemConfig.getEnableDecompose()) {
                LOGGER.warn("decomposeItemList item {} not enable decompose", itemId);
                continue;
            }

            long ownItemNum = getItemNumByItemId(itemId);
            if (ownItemNum <= itemConfig.getDecomposeRetainNum()) {
                LOGGER.error("decomposeItemList error. uid:{} itemId:{} ownItemNum:{} decomposeRetainNum:{}",
                        player.getUid(), itemId, ownItemNum, itemConfig.getDecomposeRetainNum());
                continue;
            }
            // 实际分解的数量
            // long itemNum = Math.min(itemNumList.get(i), getItemNumByItemId(itemId));
            long realDecomposeItemNum = Math.min(itemNumList.get(i), ownItemNum - itemConfig.getDecomposeRetainNum());
            if (itemNumList.get(i) != realDecomposeItemNum) {
                LOGGER.debug("decomposeItemList uid:{} itemId:{} realDecomposeItemNum:{}", player.getUid(), itemId,
                        realDecomposeItemNum);
            }

            // 删除被分解的道具
            NKErrorCode errorCode = MinItem(itemId, (int) realDecomposeItemNum, ItemChangeReason.ICR_Decompose, 0, "");
            if (errorCode.hasError()) {
                LOGGER.error("decomposeItemList min item {} error: {}", itemId, errorCode);
                continue;
            }
            // 添加分解后的道具
            List<ItemInfo> addItemList = new ArrayList<>();
            for (ResCommon.Item item : itemConfig.getDecomposeItemList()) {
                addItemList.add(ItemInfo.newBuilder().setItemId(item.getItemId())
                        .setItemNum(item.getItemNum() * realDecomposeItemNum).build());
            }
            NKPair<NKErrorCode, ItemChangeDetails> addRes = AddItems2(addItemList, ItemChangeReason.ICR_Decompose,
                    false);
            if (addRes.getKey().hasError()) {
                LOGGER.error("decomposeItemList add item {} error: {}", addItemList, addRes.getKey());
                continue;
            }
            if (addRes.getValue() != null) {
                if (itemChangeDetails == null) {
                    itemChangeDetails = addRes.getValue();
                } else {
                    itemChangeDetails.merge(addRes.getValue());
                }
            }
        }
        if (itemChangeDetails != null) {
            itemChangeDetails.dry();
            player.getBagManager().sendGetItemsNtf(itemChangeDetails);
        }
    }

    /**
     * 发送rpc到battlesvr再转到ds
     *
     * @param rpcGSItemChangeReqBuilder
     * @param async
     */
    private void sendRpcGSItemChange(SsBattlesvr.RpcGSItemChangeReq.Builder rpcGSItemChangeReqBuilder, boolean async) {
        try {
            if (async) {
                CurrentExecutorUtil.runJob(() -> {
                    RpcResult<SsBattlesvr.RpcGSItemChangeRes.Builder> result = BattleService.get()
                            .rpcGSItemChange(rpcGSItemChangeReqBuilder);
                    return null;
                }, "DsBag sendRpcGSItemChange async", true);
            } else {
                RpcResult<SsBattlesvr.RpcGSItemChangeRes.Builder> result = BattleService.get()
                        .rpcGSItemChange(rpcGSItemChangeReqBuilder);
            }
        } catch (Throwable e) {
            LOGGER.error("DsBag sendRpcGSItemChange uid:{} async:{} changeItems:{} e:"
                    , player.getUid(), async, rpcGSItemChangeReqBuilder, e);
        }
    }

    /**
     * 获取rpc的req消息，玩家不在battle返回null
     *
     * @param uid
     * @param reason
     * @return
     */
    private SsBattlesvr.RpcGSItemChangeReq.Builder getGSItemChangeReqBuilder(long uid, ItemChangeReason reason) {
        long battleId = player.getUserAttr().getBattleInfo().getBattleid();
        if (battleId <= 0) {
            LOGGER.debug("DsBag getPlayerGSItemChangeReqBuilder uid:{} reason:{} is not in battle"
                    , player.getUid(), reason);
            return null;
        }
        SsBattlesvr.RpcGSItemChangeReq.Builder rpcGSItemChangeReqBuilder = SsBattlesvr.RpcGSItemChangeReq
                .newBuilder().setId(battleId);
        rpcGSItemChangeReqBuilder.getItemsChangeBuilder().setUid(uid).setReason(reason);
        return rpcGSItemChangeReqBuilder;
    }


    public boolean isWolfKillItem(int itemId) {
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (itemConf == null) {
            return false;
        }
        //5000到6000是狼人和躲猫猫
        return isWolfKillItemType(itemConf.getType().getNumber());
    }

    public boolean needNtfToDS(int itemId) {
        return (itemId >= 601 && itemId <= 700) || isWolfKillItem(itemId);
    }

    /**
     * 是否是TYC玩法的物品
     *
     * @param itemId
     * @return
     */
    private boolean isTYCItem(int itemId) {
        return (itemId >= 601 && itemId <= 700) || (itemId >= 250001 && itemId <= 259999);
    }

    public void changeProfileTheme(int profileThemeId) {
        itemEquipMgr.changeProfileTheme(profileThemeId);
    }

    public void changeDressUpItemStatus(long itemUUID, int status) {
        itemEquipMgr.changeDressUpItemStatus(itemUUID, status);
    }

    public void changeSlotRandomState(CsBag.ChangeSlotRandomState_C2S_Msg reqMsg) {
        for (CsBag.SlotRandomState slotRandomState : reqMsg.getSlotRandomStateList()) {
            NKErrorCode ret = itemEquipMgr.isFittingSlotAvailable(slotRandomState.getSlotId());
            if (ret != NKErrorCode.OK) {
                ret.throwError("fitting slot is not available");
                return;
            }
            if (slotRandomState.getSlotId() == MiscConf.getInstance().getMiscConf()
                    .getFittingSlotIdWithNoOutlookGroup()) {
                NKErrorCode.FittingSlotCannotChangeBackUpRandom.throwError("back up fitting slot");
                return;
            }
        }

        for (CsBag.SlotRandomState slotRandomState : reqMsg.getSlotRandomStateList()) {
            FittingSlot slot = player.getUserAttr().getFittingSlots().getSlots(slotRandomState.getSlotId());
            slot.setRandom(slotRandomState.getRandomState());
        }
        if (reqMsg.hasRandomOpen()) {
            player.getUserAttr().getFittingSlots().setRandomOpen(reqMsg.getRandomOpen());
        }
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo();
    }

    // 修改背包内装扮道具的showStatus, 自己可见, 其他玩家看不到, 不与外显装备状态同步
    public void changeDressUpItemShowStatus(long itemUUID, int showStatus) {
        Item item = getItemManager().getItemByUUID(itemUUID);
        if (item == null) {
            NKErrorCode.ItemNotExist.throwError("item not exist");
        }
        ItemDetailInfo detailInfo = player.getUserAttr().getItemInfo().getItemDetail(itemUUID);
        if (detailInfo == null) {
            detailInfo = new ItemDetailInfo();
            detailInfo.setItemUUId(itemUUID);
            detailInfo.setShowStatus(showStatus);
            player.getUserAttr().getItemInfo().putItemDetail(itemUUID, detailInfo);
        } else {
            detailInfo.setShowStatus(showStatus);
        }
    }

    // 如果购买了Arena局外皮肤，自动解锁Arena局内皮肤
    public void autoUnlockArenaInterSkin(ChangedItems itemsAfterOpen) {
        // 避免递归
        if (itemsAfterOpen.getChangeReason() == ItemChangeReason.ICR_ArenaByOuterSkin_VALUE) {
            return;
        }

        try {
            CurrentExecutorUtil.runJob(() -> {
                for (ChangeItem itemInfo : itemsAfterOpen.getChangeItems()) {
                    int innerSkinItemId = ArenaSkinEffectData.getInstance()
                            .getInterSkinItemIdOfOuterSkinItemId(itemInfo.getSrcItemId());
                    if (innerSkinItemId == 0) {
                        continue;
                    }
                    ChangedItems modifyItem = new ChangedItems(ItemChangeReason.ICR_ArenaByOuterSkin_VALUE, "");
                    modifyItem.mergeItemInfo(innerSkinItemId, 1, 0);
                    player.getBagManager().AddItems2(modifyItem, true);
                    LOGGER.info("player({} {} {}) got arena inner skin item({}) by outer skin item({})",
                            player.getUid(), player.getName(), player.getOpenId(), innerSkinItemId,
                            itemInfo.getSrcItemId());
                }
                return null;
            }, "autoUnlockArenaInterSkinJob", false);
        } catch (Exception e) {
            LOGGER.error("autoUnlockArenaInterSkinJob exception({})", e.toString());
        }
    }

    public void updateDressUpScore(ChangedItems changedItems) {
        boolean involved = false;
        ResHolder resHolder = ResLoader.getResHolder();

        for (var item : changedItems.getChangeItems()) {
            if (BackpackItemDressUpValueConfData.getInLoadingInstance(resHolder).isItemInvolved(item.getSrcItemId())) {
                involved = true;
                break;
            }

            for (var actItem : item.getActItems().getItemsList()) {
                if (BackpackItemDressUpValueConfData.getInLoadingInstance(resHolder)
                        .isItemInvolved(actItem.getItemId())) {
                    involved = true;
                    break;
                }
            }
        }

        if (!involved) {
            return;
        }

        updateDressUpScore();
    }

    public void updateDressUpScore() {
        int score = calculateCurrentDressUpValue();

        player.getUserAttr().getPlayerPublicEquipments().setDressUpValue(score);
        int rankId = RankingConfData.getInstance().getByRuleFirstOrDefault(RankRule.RR_DressUp, 0);
        if (rankId == 0) {
            return;
        }

        player.getRankManager().refresh(rankId, score, 1);
    }

    public void updateDressCount(){
        int totalFashionNum = player.getSeasonFashionBattleDataMgr().getTotalFashionNum();
        player.getUserAttr().getPlayerPublicEquipments().setDressCount(totalFashionNum);
        player.setPlayerGameTime(PlayerGameTimeType.PGTT_FashionNums, totalFashionNum);
    }

    private void updateAppearanceRoadLevel() {
        StringBuilder sb = new StringBuilder();
        sb.append('[');
        int totalFashionNum = 0;
        for (var entry : player.getUserAttr().getAppearanceRoad().getAppearanceInfo().values()) {
            totalFashionNum += entry.getValue();
            sb.append(entry.getK()).append(':').append(entry.getValue()).append(',');
        }
        sb.deleteCharAt(sb.length()-1);
        sb.append(']');
        var lvPair = /*AppearanceRoadData*/AppearanceRoadLevelDataConfig.getInstance().getLevel(totalFashionNum);
        player.getUserAttr().getPlayerPublicProfileInfo()
                .setFashionLv(lvPair.getKey())
                .setFashionSubLv(lvPair.getValue());
        player.getUserAttr().getAppearanceRoad().setUpdateTimeMs(DateUtils.currentTimeMillis());
        if (player.getUserAttr().getPlayerPublicProfileInfo().isFashionLvDirty() || player.getUserAttr().getPlayerPublicProfileInfo().isFashionSubLvDirty()) {
            FashionLevelChangeEvent event = new FashionLevelChangeEvent(player.getConditionMgr());
            int stage = player.getUserAttr().getPlayerPublicProfileInfo().getFashionLv();
            int subLevel =  player.getUserAttr().getPlayerPublicProfileInfo().getFashionSubLv();
            int level = AppearanceRoadLevelDataConfig.getInstance().getLevel(stage,subLevel);
            event.setFashionLevel(level);
            event.dispatch(player);
            if (totalFashionNum == 0) {
                sb = new StringBuilder();
            }
            TlogFlowMgr.sendFashionCollectFlow(player, totalFashionNum, lvPair.getKey()*100+lvPair.getValue(),sb.toString());
        }
    }

    private void fixAppearanceRoadReward() {
        long fixTimeTag = PropertyFileReader.getRealTimeLongItem("fix_appearance_road_reward_time",
                DateUtils.parseFullTimeStrSec("2025-01-15 00:00:00"));
        if (fixTimeTag > 0
                && player.getUserAttr().getAppearanceRoad().getUpdateTimeMs() < fixTimeTag*DateUtils.ONE_SECOND_MILLIS
                && (player.getUserAttr().getPlayerPublicProfileInfo().getFashionLv() > 0 || player.getUserAttr().getPlayerPublicProfileInfo().getFashionSubLv() > 0)) {
            var minItems = new ChangedItems(ItemChangeReason.ICR_AppearanceRoadLv_VALUE, "fixAppearanceRoadReward");
            for (AppearanceRoadLv roadLvInfo : player.getUserAttr().getAppearanceRoad().getLv().values()) {
                for (int isAwarded : roadLvInfo.getAwarded().getValues()) {
                    if (isAwarded == 1) {
                        ArrayList<ItemInfo> awards = AppearanceRoadData.getInstance().getAwards(roadLvInfo.getLv());
                        if (awards == null) {
                            continue;
                        }
                        for (ItemInfo itemInfo: awards) {
                            if (itemInfo.getExpireTimeMs() > 0){
                                continue;
                            }
                            minItems.mergeItemInfo(itemInfo);
                        }
                        break;
                    }
                }
            }
            String before =  player.getUserAttr().getAppearanceRoad().toString();
            int fashionLv = player.getUserAttr().getPlayerPublicProfileInfo().getFashionLv()*100+player.getUserAttr().getPlayerPublicProfileInfo().getFashionSubLv();
            if (minItems.getChangeItems().isEmpty()) {
                LOGGER.error("fixAppearanceRoadReward no need fix, uid:{} before:{} fashionLv:{}",player.getUid(), before, fashionLv);
                player.getUserAttr().getAppearanceRoad().clearLv().setUpdateTimeMs(DateUtils.currentTimeMillis());
                return;
            }
            var ret = MinItems(minItems);
            if (ret != NKErrorCode.OK) {
                LOGGER.error("min item err, uid:{} items:{} err:{}",player.getUid(), minItems.toString(), ret);
                return;
            }
            player.getUserAttr().getAppearanceRoad().clearLv().setUpdateTimeMs(DateUtils.currentTimeMillis());
            String after =  player.getUserAttr().getAppearanceRoad().toString();
            player.getUserAttr().getPlayerPublicProfileInfo().setFashionLv(0);
            player.getUserAttr().getPlayerPublicProfileInfo().setFashionSubLv(0);
            TlogFlowMgr.sendFashionCollectFlow(player, 0, 0,"");
            LOGGER.error("fixAppearanceRoadReward success, uid:{} fashionLv:{} before:{} after:{} items:{}",
                    player.getUid(),fashionLv, before, after, minItems.toString());
        }
    }

    public void updateAppearanceByType(HashMap<Integer, HashSet<Integer>> suitActiveType) {
        LOGGER.debug("updateAppearanceByType debug, uid:{} suitActiveType:{}",player.getUid(),suitActiveType);
        for (int type : suitActiveType.keySet()) {
            player.getUserAttr().getAppearanceRoad()
                    .putAppearanceInfo(type, new KvII().setK(type).setValue(suitActiveType.get(type).size()));
            player.getUserAttr().getAppearanceRoad()
                    .putActiveSuitBookByType(type, new ActiveSuitTypeBook().setType(type));
            player.getUserAttr().getAppearanceRoad().getActiveSuitBookByType(type).getActiveSuitBooks()
                    .addAll(suitActiveType.get(type));
        }
        updateAppearanceRoadLevel();
    }

    private int calculateCurrentDressUpValue() {
        int res = 0;
        for (int itemId : BackpackItemDressUpValueConfData.getInstance().getInvolvedItemIds()) {
            if (getItemNumByItemIdIgnoreTemp(itemId) > 0) {
                res++;
            }
        }

        return res;
    }
}
