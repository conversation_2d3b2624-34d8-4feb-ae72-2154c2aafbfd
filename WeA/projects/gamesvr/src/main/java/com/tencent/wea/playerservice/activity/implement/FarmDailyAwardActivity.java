package com.tencent.wea.playerservice.activity.implement;

import com.tencent.condition.event.player.common.UpgradeFarmDailyEvent;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.IEnumedException;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.*;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.DailySumBuyTimesInfo;
import com.tencent.wea.attr.MapAttrObj;
import com.tencent.wea.attr.UpgradeCheckInInfo;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.outputcontrol.OutputModuleUtil;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.task.RunTask;
import com.tencent.wea.protocol.AttrUpgradeCheckInInfo;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.xlsRes.*;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import it.unimi.dsi.fastutil.ints.IntArraySet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import static com.tencent.wea.analyzepb.Analyze.AnalyzeModuleDef.PLAYER_FarmDaily_Award;

public class FarmDailyAwardActivity extends TaskActivity {

    private static final Logger LOGGER = LogManager.getLogger(FarmDailyAwardActivity.class);

    private static final int FarmDailyModule = 1;

    public FarmDailyAwardActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
    }

    @Override
    public void afterLoginFinish() {
        resetTaskInLastDayLogin();
        isReachLevel();
    }

    private void initFarmDailyActivity(boolean isNew) {
        farmDailyNeedPlusData(isNew);
        checkActivityParam();
    }

    private void farmDailyNeedPlusData(boolean isNew) {
        //农场天天领需要拉一次历史数据，moba 和 大王不用 ;   moduleType 农场1   moba 2   大王 3   依次往后顺
        int moduleType = getModuleType();
        if (moduleType==1) {
            // 29号的购买数据
            int upgradeCheckLastDayBuyTime = player.getUserAttr().getUpgradeCheckLastDayBuyTime();
            LOGGER.info("player 29historyBuyTimes {} {}",player.getUid(),upgradeCheckLastDayBuyTime);
            int upgradeCheckHistoryBuyTimes = player.getUserAttr().getUpgradeCheckHistoryBuyTimes();
            boolean realTimeItem =PropertyFileReader.getRealTimeBooleanItem("find_history_upgradeCheck_data", false);
            if (upgradeCheckHistoryBuyTimes==0 && (realTimeItem || isNew)) {
                //拉取历史购买数据数据
                int historyBuyTimes = getTLogHistoryData();
                upgradeCheckHistoryBuyTimes=historyBuyTimes;
                LOGGER.info("player historyBuyTimes {} {}",player.getUid(),historyBuyTimes);
                player.getUserAttr().setUpgradeCheckHistoryBuyTimes(historyBuyTimes);
            }
            int farmDailySumBuyTimes = player.getUserAttr().getFarmDailySumBuyTimes();
            int sumHistoryBuyTimes = upgradeCheckHistoryBuyTimes+upgradeCheckLastDayBuyTime;
            if (farmDailySumBuyTimes < sumHistoryBuyTimes) {
                player.getUserAttr().setFarmDailySumBuyTimes(sumHistoryBuyTimes);
            }
        }
    }

    private void checkActivityParam() {
        int activityParamCount = getActivityMainConfig().getActivityParamCount();
        if (activityParamCount<10) {
            NKErrorCode.InvalidParams.throwError("current activity param count is less than 10");
        }
    }

    private void resetTaskInLastDayLogin() {
        /*com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData()
                .getFarmDailyAwardActivity();
        long weekendAwardTime =  DateUtils.getFirstDayOfWeek(farmDailyAwardActivity.getWeekendAwardTime()).getTime();
        long weekendAwardResetTimeMs = weekendAwardTime + (getResetWeek() - 1) * DateUtils.ONE_DAY_MILLIS;
        if (farmDailyAwardActivity.getWeekendAwardTime()< weekendAwardResetTimeMs) {
            //就是本周五
        }else {
            //下周五
            weekendAwardResetTimeMs = weekendAwardResetTimeMs + ( 7 ) * DateUtils.ONE_DAY_MILLIS;
        }
        long currentTimeMillis = Framework.currentTimeMillis();
        //重置活动任务
        for (int taskGroupId : getActivityMainConfig().getActivityTaskGroupList()) {
            ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
            if (taskGroup != null && taskGroup.getTaskIdListCount() > 0) {
                for (int taskId : taskGroup.getTaskIdListList()) {
                    if (activityUnit.getClearRedDotInfo().getFilterTask().contains(taskId)) {
                        LOGGER.debug("after login check task filter, uid:{} activityId:{} taskId:{}",player.getUid(),activityId,taskId);
                        continue;
                    }
                    RunTask task = player.getTaskManager().getTask(taskId);
                    if (task != null &&  task.getStatus() == TaskStatus.TS_Finish && task.getTaskIsExistReward() && currentTimeMillis>weekendAwardResetTimeMs && !DateUtils.isSameDay(currentTimeMillis,weekendAwardResetTimeMs)) {
                        //领奖了, 且任务包含奖励, 才去重置
                        resetTask();
                    }
                }
            }
        }*/
        long nextTurnRefreshTime = getNextTurnRefreshTime();
        long currentTimeMillis = Framework.currentTimeMillis();
        if (currentTimeMillis> nextTurnRefreshTime) {
            //重置活动任务
            for (int taskGroupId : getActivityMainConfig().getActivityTaskGroupList()) {
                ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
                if (taskGroup != null && taskGroup.getTaskIdListCount() > 0) {
                    for (int taskId : taskGroup.getTaskIdListList()) {
                        if (activityUnit.getClearRedDotInfo().getFilterTask().contains(taskId)) {
                            LOGGER.debug("after login check task filter, uid:{} activityId:{} taskId:{}",player.getUid(),activityId,taskId);
                            continue;
                        }
                        RunTask task = player.getTaskManager().getTask(taskId);
                        if (task != null &&  task.getTaskIsExistReward() ) {
                            resetTask();
                        }
                    }
                }
            }
            LOGGER.info("nextTurnRefreshTime currentTimeMillis {} {}",nextTurnRefreshTime,currentTimeMillis);
        }

    }

    private long getNextTurnRefreshTime(){
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData()
                .getFarmDailyAwardActivity();
        long lastWeekRefreshTime = farmDailyAwardActivity.getWeekendAwardTime();
        if (lastWeekRefreshTime == 0) {
            return 0;
        }

        long weekendAwardTime =  DateUtils.getFirstDayOfWeek(lastWeekRefreshTime).getTime();
        long weekendAwardResetTimeMs = weekendAwardTime + (getResetWeek() - 1) * DateUtils.ONE_DAY_MILLIS;
        if (lastWeekRefreshTime< weekendAwardResetTimeMs) {
            //就是本周五 零点
        }else {
            //下周五 零点
            weekendAwardResetTimeMs = weekendAwardResetTimeMs + ( 7 ) * DateUtils.ONE_DAY_MILLIS;
        }
        return weekendAwardResetTimeMs;

        /*long weekendAwardTime =  DateUtils.getFirstDayOfWeek(lastWeekRefreshTime).getTime();
        int weekDay = DateUtils.getDayOfWeekBeginMonday(Framework.currentTimeMillis());
        if (weekDay >= getResetWeek()) {
            return weekendAwardTime + (getResetWeek() - 1 + 7) * DateUtils.ONE_DAY_MILLIS;
        } else {
            return weekendAwardTime + (getResetWeek() - 1) * DateUtils.ONE_DAY_MILLIS;
        }*/
    }

    @Override
    public void init(boolean isNew) {
        initFarmDailyActivity(isNew);
        resetActivity();
        LOGGER.debug("farmDailyAwardActivity init");
    }

    @Override
    protected int getTaskGroupNumber() {
        return 0;
    }


    // 获取每周几重置
    private int getResetWeek() {
        if (getActivityMainConfig().getActivityParamCount() < 2) {
            return -1;
        }
        return getActivityMainConfig().getActivityParam(1);
    }

    // 发送剩余奖励重置数据
    private void sendMailRewardAndResetData() {

        int moduleType = getModuleType();

        if (!getIsUpgrade()) {
            return;
        }
        List<ResCommon.Item> rewardItemList = new ArrayList<>();
        int playerStarLevel = getPlayerStarLevel();
        List<ResActivity.ActivityCheckInManualConfig> checkInManualConfigs = ActivityCheckInManualData
                .getInstance()
                .getArrayList()
                .stream()
                .filter(v -> v.getActivityId() == this.activityId)
                .filter(v -> v.getModuleType() == moduleType)
                .filter(v -> v.getStarLevel() == playerStarLevel)
                .collect(Collectors.toList());
        for (int index = 1; index <= checkInManualConfigs.size(); index++) {
            getCheckInManualRewardByIndex(index, rewardItemList, true); // 补所有的高级奖励就行，基础奖励不用补
        }
        if (rewardItemList.isEmpty()) {
            LOGGER.warn("getFarmDailyReward get all failed:{}, {}",
                    player.getUid(), activityId);
            return;
        }
        if (getActivityMainConfig().getActivityParamCount() < 3) {
            LOGGER.warn("getFarmDaily mailConfId failed:{}, {}",
                    player.getUid(), activityId);
            return;
        }
        int mailConfId = getActivityMainConfig().getActivityParam(2);
        MailAttachmentList.Builder mailReward = MailAttachmentList.newBuilder();
        for (ResCommon.Item item : rewardItemList) {
            mailReward.addList(MailAttachment.newBuilder().setItemIfo(
                    ItemInfo.newBuilder().setItemId(item.getItemId()).setItemNum(item.getItemNum()).build()));
        }
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                long mailId = MailInteraction.sendTemplateMail(player.getUid(), mailConfId, mailReward,
                        MailInteraction.TlogSendReason.FarmDailyAward);
                if (mailId == 0) {
                    LOGGER.warn("farmDaily sendMailRewardAndResetData send mail failed:{}, {}",
                            player.getUid(), activityId);
                }
                LOGGER.debug("farmDaily sendMailRewardAndResetData send mail success:{}, {}, {}",
                        player.getUid(), activityId, mailId);
                return null;
            }, "sendMailReward", true);
        } catch (NKCheckedException e) {
            LOGGER.error("player {} farmDaily runJob catch {}", player.getUid(), e);
        }
    }

    // 是否升级为高级
    public boolean getIsUpgrade() {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        return farmDailyAwardActivity.getIsUpgrade();
    }

    // 领取指定某天的打卡奖励
    private void getCheckInManualRewardByIndex(int checkInIndex, List<ResCommon.Item> rewardItemList, boolean onlyUpgrade) {

        int moduleType = getModuleType();

        int playerStarLevel = getPlayerStarLevel();
        List<ResActivity.ActivityCheckInManualConfig> checkInManualConfigs = ActivityCheckInManualData
                .getInstance()
                .getArrayList()
                .stream()
                .filter(v -> v.getActivityId() == this.activityId)
                .filter(v -> v.getStarLevel() == playerStarLevel)
                .filter(v -> v.getModuleType() == moduleType)
                .filter(v -> v.getLevel() == checkInIndex).collect(Collectors.toList());
        ResActivity.ActivityCheckInManualConfig activityCheckInManualConfig = checkInManualConfigs.get(0);

        if (activityCheckInManualConfig != null) {
            UpgradeCheckInInfo checkInInfo = getManualInfoByCheckInIndex(checkInIndex);
            if (checkInInfo == null) {
                checkInInfo = new UpgradeCheckInInfo();
                checkInInfo.setId(checkInIndex);
                addCheckInInfoByCheckInIndex(checkInIndex, checkInInfo);
            }
            if (!onlyUpgrade && !checkInInfo.getIsGetBaseRewards()) {
                for (ResCommon.Item itemInfo : activityCheckInManualConfig.getBaseRewardInfoList()) {
                    rewardItemList.add(ResCommon.Item.newBuilder()
                            .setItemId(itemInfo.getItemId())
                            .setItemNum(itemInfo.getItemNum())
                            .build());
                }
                // 成功领奖
                checkInInfo.setIsGetBaseRewards(true);
            }
            if (getIsUpgrade() && !checkInInfo.getIsGetHigherRewards()) {
                for (ResCommon.Item itemInfo : activityCheckInManualConfig.getHigherRewardInfoList()) {
                    rewardItemList.add(ResCommon.Item.newBuilder()
                            .setItemId(itemInfo.getItemId())
                            .setItemNum(itemInfo.getItemNum())
                            .build());
                }
                checkInInfo.setIsGetHigherRewards(true);
            }
        } else {
            LOGGER.warn("getCheckInManualRewardByIndex invalid config : player:{}, checkInIndex:{}, activityId:{}",
                    player.getUid(), checkInIndex, getActivityMainConfig().getId());
        }
        LOGGER.info("player playerStartLevel rewardItemList {} {}",playerStarLevel, rewardItemList);
    }


    // 领取打卡奖励
    public NKErrorCode getFarmDailyAwardReward(int checkInIndex, CsActivity.GetFarmDailyReward_S2C_Msg.Builder rspMsg) {
        List<ResCommon.Item> rewardItemList = new ArrayList<>();
        if (checkInIndex == 0) {
            // 一键领取
            for (int index = 1; index <= getCheckInCount(); index++) {
                getCheckInManualRewardByIndex(index, rewardItemList, false);
            }
            if (rewardItemList.isEmpty()) {
                LOGGER.warn("getFarmDailyAward get all failed:{}, {}",
                        player.getUid(), getActivityMainConfig().getId());
                return NKErrorCode.ActivityUpgradeManualNotCheckIn;
            }
        } else {
            if (checkInIndex > getCheckInCount()) {
                LOGGER.warn("getTotalFarmDailyAwardReward checkInIndex is invalid, {},{},{}",
                        player.getUid(), checkInIndex, getCheckInCount());
                return NKErrorCode.ActivityUpgradeManualNotCheckIn;
            }
            getCheckInManualRewardByIndex(checkInIndex, rewardItemList, false);
            rspMsg.setCheckInIndex(checkInIndex);
            UpgradeCheckInInfo checkInInfo = getManualInfoByCheckInIndex(checkInIndex);
            if (checkInInfo != null) {
                AttrUpgradeCheckInInfo.proto_UpgradeCheckInInfo.Builder ssCheckInInfo = AttrUpgradeCheckInInfo.proto_UpgradeCheckInInfo.newBuilder();
                checkInInfo.copyToSs(ssCheckInInfo);
                rspMsg.setCheckInInfo(ssCheckInInfo.build());
            }
        }

        // 给与奖励
        ChangedItems changeItem = new ChangedItems(rewardItemList,
                ItemChangeReason.ICR_GetFarmDailyRewards.getNumber(), "");
        changeItem.setChangeReservedParams(5,getIsUpgrade() ? 1 : 0);
        changeItem.setActivityId(activityId);
        NKPair<NKErrorCode, ItemChangeDetails> addRes = player.getBagManager().AddItems2(
                changeItem, true, checkInIndex);
        NKErrorCode errorCode = addRes.getKey();
        if (!errorCode.isOk()) {
            LOGGER.warn("getCheckInManualReward add item failed:{},{},{}",
                    player.getUid(), changeItem.toString(), errorCode);
            return errorCode;
        }
        int totalPrice=getTotalPrice();
        if (getCheckInListCount() >=totalPrice) {
            delRedDot();
        }
        checkActivityRedPoint();
        LOGGER.info("player award playerStartLevel rewardItemList {} {}",getPlayerStarLevel(), rewardItemList);
        return NKErrorCode.OK;
    }

    // 获取打卡总次数
    private int getCheckInCount() {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        return farmDailyAwardActivity.getCheckInCount();
    }

    // 获取指定id的打卡手册信息
    private UpgradeCheckInInfo getManualInfoByCheckInIndex(int checkInIndex) {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        return farmDailyAwardActivity.getCheckInList(checkInIndex);
    }

    private int getTotalPrice() {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        int checkInCount = farmDailyAwardActivity.getCheckInCount();
        return getIsUpgrade()?checkInCount*2:checkInCount;
    }

    // 获取领取打卡奖励次数
    private int getCheckInListCount() {
        int checkInListCount = 0;
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();

        for (UpgradeCheckInInfo checkInInfo : farmDailyAwardActivity.getCheckInList().values()) {
            if (checkInInfo.getIsGetBaseRewards()) {
                checkInListCount++;
            }
            if(getIsUpgrade() && checkInInfo.getIsGetHigherRewards()){
                checkInListCount++;
            }
        }
        return checkInListCount;
    }

    private void refreshCheckInCount() {
        if (getCheckInCount() >= ActivityCheckInManualData.getInstance().getMaxCheckInDays(getActivityMainConfig().getId())) {
            LOGGER.debug("refreshCheckInCount over days player:{}, {},{}",
                    player.getUid(),getCheckInCount(),
                    ActivityCheckInManualData.getInstance().getMaxCheckInDays(getActivityMainConfig().getId()));
            return;
        }
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        if (farmDailyAwardActivity.getLastCheckInTimeMs() == 0 || !DateUtils.isSameDay(
                farmDailyAwardActivity.getLastCheckInTimeMs(), Framework.currentTimeMillis())) {
            farmDailyAwardActivity.addCheckInCount(1);
            farmDailyAwardActivity.setLastCheckInTimeMs(Framework.currentTimeMillis());
            // 刷新红点
            // updateRedDotInfo();
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
            CsActivity.CheckInManualUpgradeNtf.Builder ntf = CsActivity.CheckInManualUpgradeNtf.newBuilder()
                    .setActivityId(this.activityId);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_CHECKINMANUALUPGRADENTF, ntf);
            checkActivityRedPoint();
        }
    }


    // 升级打卡手册
    public NKErrorCode upgradeCheckInManual(CsActivity.UpgradeFarmDailyReward_C2S_Msg reqMsg,CsActivity.UpgradeFarmDailyReward_S2C_Msg.Builder rspMsg) {

        int activityParamConfigId = 0;
        if (reqMsg.getBuyNums()>1){
            activityParamConfigId = getActivityMainConfig().getActivityParam(6);
        }else {
            activityParamConfigId = getActivityMainConfig().getActivityParam(0);
        }
        //充值配置表   刮刮乐
        ResRecharge.RechargeScratchOffTicketCfg rechargeCfg = RechargeScratchOffTicketData.getInstance()
                .get(activityParamConfigId);
        if (rechargeCfg == null) {
            LOGGER.warn("farmDaily player:{} invalid recharge cfg:{}",
                    player.getUid(), getRechargeUpgradeFarmDailyConfigId());
            return NKErrorCode.ActivityCheckInManualInvalidRechargeCfg;
        }
        try {
            UpgradeFarmDailyMidasMetaData.Builder midas = UpgradeFarmDailyMidasMetaData.newBuilder().setActivityId(activityId)
                    .setRechargeId(rechargeCfg.getId()).setCommodityId(reqMsg.getCommodityId()).setBuyNums(reqMsg.getBuyNums());
            player.getPlayerMoneyMgr()
                    .midasDirectBuy(rechargeCfg.getMidasId(), DeliverGoodsMetaData.newBuilder().setUpgradeFarmDailyMidasMetaData(midas),
                            ItemChangeReason.ICR_UpgradeFarmDaily);
        } catch (Exception e) {
            LOGGER.error("farmDaily failed to buy midas upgrade ticket product, uid:{} id:{} commodity:{} err:{}",
                    player.getUid(), activityId, rechargeCfg.getCommodityId(), e);
            NKErrorCode errorCode =
                    (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                            : NKErrorCode.UnknownError;
            NKErrorCode.MidasBuyGoodsFailed.throwError("farmDaily failed to buy midas product, reason:{}", errorCode);
        }
        return NKErrorCode.OK;
    }

    // 确认升级打卡手册
    public NKErrorCode confirmUpgradeFarmDaily(int buyNums) {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData()
                .getFarmDailyAwardActivity();
        updateBuyTimesByModuleType(buyNums);
        int activityBuyTimes = farmDailyAwardActivity.getActiviytBuyTimes();
        farmDailyAwardActivity.setActiviytBuyTimes(activityBuyTimes+ buyNums);
        if (!farmDailyAwardActivity.getIsUpgrade()){
            // 扣除道具
            NKErrorCode minErr = player.getBagManager().MinItem(getItemId(), 1,
                    ItemChangeReason.ICR_FarmDailyCardDecrease, 0, "");
            if (!minErr.isOk()) {
                minErr.throwError("player {} FarmDailyAwardActivity unlock upgrade min item error, itemId: {}, itemNum: {}",
                        player.getUid(), getItemId(), 1);
                return NKErrorCode.ActivityFarmDailyAwardDecItemError;
            }
            farmDailyAwardActivity.setIsUpgrade(true);
        }
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
        isReachLevel();
        LOGGER.info("confirm upgrade farmDaily {}", player.getUid());
        checkActivityRedPoint();
        return NKErrorCode.OK;
    }

    private void updateBuyTimesByModuleType(int buyNums) {
        Integer moduleType = getModuleType();
        if (moduleType == FarmDailyModule) {
            int farmDailySumBuyTimes = player.getUserAttr().getFarmDailySumBuyTimes();
            player.getUserAttr().setFarmDailySumBuyTimes(farmDailySumBuyTimes+ buyNums);
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
        }else {
            MapAttrObj<Integer, DailySumBuyTimesInfo> dailyAwardSumBuyInfoMap = player.getUserAttr().getDailyAwardSumBuyInfoMap();
            int buyTimesModule =dailyAwardSumBuyInfoMap.get(moduleType)==null?0 : dailyAwardSumBuyInfoMap.get(moduleType).getBuyTimes();
            DailySumBuyTimesInfo dailySumBuyTimesInfo = new DailySumBuyTimesInfo();
            dailySumBuyTimesInfo.setBuyTimes(buyTimesModule+buyNums).setTypeId(moduleType);
            player.getUserAttr().putDailyAwardSumBuyInfoMap(moduleType,dailySumBuyTimesInfo);
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
        }
    }

    public   Integer getModuleType() {
        if (!checkParamCount()) {
            return FarmDailyModule;
        }
        // moduleType 农场1   moba 2   大王 3   依次往后顺
        return getActivityMainConfig().getActivityParam(10);
    }

    private boolean checkParamCount() {
        int activityParamCount = getActivityMainConfig().getActivityParamCount();
        if (activityParamCount > 10) {
            return true;
        }
        LOGGER.error("activityMainConfig activity param moduleType is null player:{}",player.getUid());
        return false;
    }

    private boolean checkParamCountActivityOrHistory() {
        int activityParamCount = getActivityMainConfig().getActivityParamCount();
        if (activityParamCount > 11) {
            return true;
        }
        LOGGER.error("activityMainConfig activity param 11 is null player:{}",player.getUid());
        return false;
    }


    //进入活动页面且没有高级自动扣除道具
    public NKErrorCode isNeedMinItem() {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        long itemNums = player.getBagManager().getItemNumByItemId(getItemId());
        if (itemNums > 0 && !farmDailyAwardActivity.getIsUpgrade()) {
            // 扣除道具
            NKErrorCode minErr = player.getBagManager().MinItem(getItemId(), 1,
                    ItemChangeReason.ICR_FarmDailyCardDecrease, 0, "");
            if (!minErr.isOk()) {
                minErr.throwError("player {} FarmDailyAwardActivity unlock upgrade min item error, itemId: {}, itemNum: {}",
                        player.getUid(), getItemId(), 1);
            }
            farmDailyAwardActivity.setIsUpgrade(true);
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
            checkActivityRedPoint();
            return NKErrorCode.OK;
        }
        return NKErrorCode.ActivityFarmDailyIsUpgradeOrItemNotEnough;
    }

    private void dispatchEvent() {
        var event = new UpgradeFarmDailyEvent(player.getConditionMgr());
        player.getPlayerEventManager().dispatch(event);
    }

    // 获取打开手册充值配置
    private int getRechargeUpgradeFarmDailyConfigId() {
        return getActivityMainConfig().getActivityParam(0);
    }

    // 获取权益卡道具id
    public int getItemId() {
        return getActivityMainConfig().getActivityParam(8);
    }

    // 获取权益卡商品id
    public int getCommodityId() {
        return getActivityMainConfig().getActivityParam(9);
    }

    // 增加指定id的打卡手册信息
    private void addCheckInInfoByCheckInIndex(int checkInIndex, UpgradeCheckInInfo checkInInfo) {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        farmDailyAwardActivity.putCheckInList(checkInIndex, checkInInfo);
        return;
    }


    //一次性获取购买过多少记录
    private int getTLogHistoryData(){
        /*Analyze.PlayerFarmDailyAwardAnalyze.Builder builder = Analyze.PlayerFarmDailyAwardAnalyze.newBuilder();
        int historyBuyTimes = AnalyzeApi.getTLogModuleData(String.valueOf(player.getUid()), PLAYER_FarmDaily_Award, builder);*/
        int historyBuyTimes = 0;
        String dataJson = "";
        try {
            String dataKey = Long.toString(player.getUid());
            TcaplusDb.AnalyzeDataTable.Builder builder = TcaplusDb.AnalyzeDataTable.newBuilder().setDataKey(dataKey).setModule(PLAYER_FarmDaily_Award.name());
            TcaplusManager.TcaplusReq tcaplusReq = TcaplusUtil.newGetReq(builder);
            TcaplusManager.TcaplusRsp queryRsp = tcaplusReq.send();
            if (!queryRsp.isOKIgnoreRecordNotExist()) {
                LOGGER.info("curr player analyzeDataTable not record:{}, uid:{}", queryRsp.getResult(),player.getUid());
                return 0;
            }
            TcaplusDb.AnalyzeDataTable dataTable = (TcaplusDb.AnalyzeDataTable) queryRsp.firstRecordData().msg;
            dataJson = dataTable.getDataJson();
            if ( dataJson.isEmpty()){
                return 0;
            }
            JSONObject historyJson = new JSONObject(dataJson);
            if (historyJson.has("farmDailyAwardsBuyCount")){
                historyBuyTimes = historyJson.getInt("farmDailyAwardsBuyCount");
            }
        }catch (Exception e){
            LOGGER.error("get analyzeDataTable err:{}, uid:{} {}", e,dataJson,player.getUid());
            return 0;
        }
        LOGGER.info("player history buyTimes {} {}", player.getUid(),historyBuyTimes);
        return historyBuyTimes;
    }


    //判断玩家当前星级
    private int getPlayerStarLevel() {
        int buyTimes = getBuyTimes();
        int playerStartLevel = 0;
        int lastConf = 0;
        List<ResActivity.FarmDailyAwardStartLevelConfig> arrayList = FarmDailyAwardStartLevelConfig.getInstance().getArrayList();
        List<ResActivity.FarmDailyAwardStartLevelConfig> currentActivityConf =
                arrayList.stream().filter(v -> v.getActivityId() == this.activityId).collect(Collectors.toList());
        for (int i = 0; i < currentActivityConf.size(); i++) {
            ResActivity.FarmDailyAwardStartLevelConfig currentConfig = currentActivityConf.get(i);
            if (buyTimes < currentConfig.getNeedBuyTimes()) {
                playerStartLevel = currentConfig.getStartLevel();
                break;
            }
            if (i < currentActivityConf.size() - 1) {
                ResActivity.FarmDailyAwardStartLevelConfig nextConfig = currentActivityConf.get(i + 1);
                if (buyTimes < nextConfig.getNeedBuyTimes()) {
                    playerStartLevel = currentConfig.getStartLevel();
                    break;
                }
            }
            if (i == currentActivityConf.size() - 1) {
                lastConf =currentActivityConf.size() - 1;
            }
        }
        if (playerStartLevel == 0){
            playerStartLevel = currentActivityConf.get(lastConf).getStartLevel();
        }
        return playerStartLevel;
    }

    private  int getBuyTimes() {
        int activityParamInAct = 1;
        int moduleType = getModuleType();
        int buyTimes = 0;
        if (moduleType== FarmDailyModule) {
            buyTimes = player.getUserAttr().getFarmDailySumBuyTimes();
        }else {
            boolean paramCount = checkParamCountActivityOrHistory();
            if (paramCount) {
                int activityParam = getActivityMainConfig().getActivityParam(11);
                if (activityParam == activityParamInAct) {
                    buyTimes = getActivityBuyTimes();
                }else {
                    DailySumBuyTimesInfo dailyAwardSumBuyInfoMap = player.getUserAttr().getDailyAwardSumBuyInfoMap(moduleType);
                    if (dailyAwardSumBuyInfoMap != null) {
                        buyTimes = dailyAwardSumBuyInfoMap.getBuyTimes();
                    }
                }
            }else {
                LOGGER.error("paramCount err:{}, uid:{}", moduleType,player.getUid());
                return 0;
            }
        }
        return buyTimes;
    }

    private int getActivityBuyTimes() {
        int buyTimes =0;
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        buyTimes = farmDailyAwardActivity.getActiviytBuyTimes();
        return buyTimes;
    }

    @Override
    public void onExpire() {

    }

    @Override
    public void stop() {
        sendMailRewardAndResetData();
    }

    @Override
    public void onStatusChange() {

    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATFarmDailyAward;
    }

    /**
     * 每日逻辑 各个活动根据需求自己清理相关数据
     *
     * @return void
     */
    @Override
    public void onMidNight() {
        resetActivity();
        LOGGER.info("farmDailyAwardActivity onMidNight reset data, uid:{} activityId:{}", player.getUid(), activityId);
    }

    private void resetActivity() {
        //重置打卡
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData()
                .getFarmDailyAwardActivity();
        if (farmDailyAwardActivity.getLastCheckInTimeMs() != 0 && getResetWeek() > 0) {
            long weekBeginTimeMs = DateUtils.getFirstDayOfWeek(farmDailyAwardActivity.getLastCheckInTimeMs()).getTime();
            long resetTimeMs = weekBeginTimeMs + (getResetWeek() - 1) * DateUtils.ONE_DAY_MILLIS;
            if ((farmDailyAwardActivity.getLastCheckInTimeMs() < resetTimeMs && DateUtils.currentTimeMillis() > resetTimeMs)
                    || DateUtils.currentTimeMillis() - resetTimeMs > DateUtils.ONE_WEEK_MILLIS) {
                sendMailRewardAndResetData();
                resetCheckData();
                player.getUserAttrMgr().collectAndSyncDirtyToClient();
                LOGGER.info("player reset activity checkIn {} {}", player.getUid(), DateUtils.currentTimeMillis());
            }
        }

        //重置周奖励
        long nextTurnRefreshTime = getNextTurnRefreshTime();
        long currentTimeMillis = Framework.currentTimeMillis();
        if (currentTimeMillis> nextTurnRefreshTime) {
            resetTask();
            LOGGER.info("player reset activity task {} {}", player.getUid(), DateUtils.currentTimeMillis());
        }
        isReachLevel();

        refreshCheckInCount();
    }

    private void resetCheckData() {
        com.tencent.wea.attr.FarmDailyAwardActivity farmDailyAwardActivity = getActivityUnit().getDetailData().getFarmDailyAwardActivity();
        farmDailyAwardActivity.setIsUpgrade(false);
        farmDailyAwardActivity.setLastCheckInTimeMs(0);
        farmDailyAwardActivity.setCheckInCount(0);
        farmDailyAwardActivity.getCheckInList().clear();
    }

    private void isReachLevel() {
        int playerStartLevel = getPlayerStarLevel();
        ResMisc.MiscConf miscConf = MiscConf.getInstance().getMiscConf();
        int playerFarmDailyBigAwardLevelLimit = miscConf.getPlayerFarmDailyBigAwardLevelLimit();
        if (playerStartLevel>=playerFarmDailyBigAwardLevelLimit){
            dispatchEvent();
        }
    }

    /**
     * 重置指定task集合
     *
     */
    private void resetTask() {
        IntArraySet taskIdSet = new IntArraySet();
        List<Integer> activityTaskGroupList = getActivityMainConfig().getActivityTaskGroupList();
        for (Integer activityTaskGroupId : activityTaskGroupList) {
            ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(activityTaskGroupId);
            if (taskGroup == null) {
                LOGGER.error("farm daily activity center task is empty {}", activityTaskGroupId);
                continue;
            }
            List<Integer> taskIdListList = taskGroup.getTaskIdListList();
            if (taskIdListList.isEmpty()) {
                LOGGER.error("farm daily taskIdListList is empty {}",activityTaskGroupId);
                continue;
            }
            taskIdSet.addAll(taskIdListList);
        }
        ArrayList<Integer> idLists = new ArrayList<>();
        for (int id : taskIdSet) {
            OutputModuleUtil.checkTaskPeriodType(id, TaskRefreshTriggerType.TRTT_Normal);
            ResTask.TaskConf conf = TaskConfData.getInstance().getTaskConf(id);
            if (conf == null) {
                LOGGER.error("player {} reset task {} config not exist", player.getUid(), id);
                continue;
            }
            if (conf.getFrontTask() > 0) {
                // 有前置任务，重置时直接删除
                player.getTaskManager().markDeleteRunningTask(id);
                player.getUserAttr().getTaskInfo().removeFinishTask(id);
            } else {
                player.getTaskManager().resetTask(id);
                idLists.add(id);
            }
        }
        player.getTaskManager().deleteRunningTasks();
        for (int id : idLists) {
            RunTask task = player.getTaskManager().getTask(id);
            if (task == null) {
                LOGGER.error("player {} reset task {} chain, task not exist", player.getUid(), id);
                continue;
            }
            for (TaskStatusInfo taskInfo : task.getTaskChainInfo()) {
                player.getTaskManager().addChangeTaskInfo(taskInfo);
            }
        }

        LOGGER.info("onMidNight reset WeekendTask, uid:{} activityId:{}", player.getUid(), activityId);
    }


    @Override
    public void checkActivityRedPoint() {
        boolean isShowRedPoint = activityUnit.getRedDotShow();


        //没打卡
        int totalPrice=getTotalPrice();
        if (getCheckInListCount() <totalPrice) {
            updateRedDotInfo();
            return;
        }

        for (int taskGroupId : getActivityMainConfig().getActivityTaskGroupList()) {
            ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
            if (taskGroup != null && taskGroup.getTaskIdListCount() > 0) {
                for (int taskId : taskGroup.getTaskIdListList()) {
                    if (activityUnit.getClearRedDotInfo().getFilterTask().contains(taskId)) {
                        LOGGER.debug("check task filter, uid:{} activityId:{} taskId:{}",player.getUid(),activityId,taskId);
                        continue;
                    }
                    RunTask task = player.getTaskManager().getTask(taskId);
                    if (task != null && task.getStatus() == TaskStatus.TS_Completed && task.getTaskIsExistReward()) {
                        // 完成任务，可领奖, 且任务包含奖励, 才去设置红点
                        LOGGER.debug("red dot reason, uid:{} id:{} reason:task task:{}, isExistReward:{}",
                                player.getUid(), activityId, task.getTaskId(), task.getTaskIsExistReward());
                        updateRedDotInfo();
                        return;
                    }
                    if (task != null && task.getStatus() == TaskStatus.TS_Finish ) {
                        delRedDot();
                        return;
                    }
                }
            }
        }
        if(isShowRedPoint){
            updateRedDotInfo();
            return;
        }

        delRedDot();

    }

    /**
     * 通用的活动gm
     * 使用 222 GmPlayerActivityGeneralGm 通用活动gm即可 第一个参数活动id 后面的参数会合成一个list发给活动instance
     * @param args 参数列表 不包含活动id参数的剩下参数
     * @return 错误码
     */
    @Override
    public NKErrorCode doGeneralGmCmd(List<String> args) {

        /*//Analyze.PlayerFarmDailyAwardAnalyze.Builder builder = Analyze.PlayerFarmDailyAwardAnalyze.newBuilder();
        //现网uid
        String playerUid = args.get(3);
        //目标uid
        String mplayerUid= args.get(4);
        //int historyBuyTimes = AnalyzeApi.getTLogModuleData(playerUid, PLAYER_FarmDaily_Award, builder);
        TcaplusDb.AnalyzeDataTable.Builder builder = TcaplusDb.AnalyzeDataTable.newBuilder().setDataKey(playerUid).setModule(PLAYER_FarmDaily_Award.name());
        TcaplusManager.TcaplusReq tcaplusReq = TcaplusUtil.newGetReq(builder);
        TcaplusManager.TcaplusRsp queryRsp = tcaplusReq.send();
        LOGGER.info("player queryRsp buyTimes {} {}", playerUid,queryRsp.getResult());
        if (!queryRsp.isOK()) {
            LOGGER.error("get analyzeDataTable err {} {}", queryRsp.getResult(),playerUid);
        }
        TcaplusDb.AnalyzeDataTable dataTable = (TcaplusDb.AnalyzeDataTable) queryRsp.firstRecordData().msg;
        String dataJson = dataTable.getDataJson();
        LOGGER.info("playerUid dataTable  {} {}", playerUid,dataJson);
        String[] strings = dataJson.split(":");
        int historyBuyTimes = 0;
        String result = StringUtils.substring(strings[1], 0, -1);
        historyBuyTimes = Integer.parseInt(result);
        LOGGER.info("playerUid historyBuyTimes {} {}", playerUid,historyBuyTimes);
        String jsonData = "{\"farmDailyAwardsBuyCount\":" + historyBuyTimes + "}";
        TcaplusDb.AnalyzeDataTable.Builder analyzeDataTable = TcaplusDb.AnalyzeDataTable.newBuilder().setDataKey(mplayerUid).setModule("PLAYER_FarmDaily_Award").setDataJson(jsonData);
        TcaplusManager.TcaplusRsp rs = TcaplusUtil.newInsertReq(analyzeDataTable).send();
        if (!rs.isOK()) {
            LOGGER.info("isOk playerUid mplayerUid {} {} {}", playerUid,mplayerUid,rs.isOK());
            return NKErrorCode.GMActionNotFound;
        }
        LOGGER.info("mplayerUid analyzeDataTable  {} {}", mplayerUid,rs.isOK());*/

        player.getUserAttr().setFarmDailySumBuyTimes(8);


        return NKErrorCode.OK;
    }
}
