package com.tencent.wea.playerservice.activity.implement;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.resourceloader.resclass.ActivityHYWarmUpCheckinData;
import com.tencent.resourceloader.resclass.ActivityHYWarmUpData;
import com.tencent.resourceloader.resclass.ActivityHYWarmUpProgressData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.HalfYearWarmUpDayInfo;
import com.tencent.wea.attr.MapAttrObj;
import com.tencent.wea.outputcontrol.BaseOutputModule;
import com.tencent.wea.outputcontrol.OutputModuleCtx;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.event.common.battle.PlayerFinishBattleEvent;
import com.tencent.wea.playerservice.outputcontrol.GamesvrOutputMgr;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.CsActivity.ActivityHYWarmUpDayInfo;
import com.tencent.wea.protocol.CsActivity.ActivityHYWarmUpInfoNtf;
import com.tencent.wea.protocol.CsActivity.HYWarmupRewardType;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.xlsRes.ResActivity.ActivityHYWarmUpCheckinConf;
import com.tencent.wea.xlsRes.ResActivity.ActivityHYWarmUpConf;
import com.tencent.wea.xlsRes.ResActivity.ActivityHYWarmUpProgressConf;
import com.tencent.wea.xlsRes.ResCommon.Item;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.GeneralOutputPeriodType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class HalfYearWarmUpActivity extends BaseActivity implements EventConsumer {
    private static final Logger LOGGER = LogManager.getLogger(HalfYearWarmUpActivity.class);

    private static final int SIGN_REDDOT_ID = 1;
    private static final int MAKEUP_REDDOT_ID = 2;
    private static final int PROCESS_REWARD_REDDOT_ID = 3;
    private static final int DAILY_REWARD_REDDOT_ID = 4;
    private static final int BIG_REWARD_REDDOT_ID = 5;

    private static final int BIG_REWARD_DAY = -1;  // 策划一直在改这里大奖的发放逻辑，先暂时这样吧

    public HalfYearWarmUpActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
        if (player != null) {
            player.getEventSwitch().register(this);
        }
    }

    @Override
    public void init(boolean isNew) {
        super.init(isNew);
        ntfActivityInfo();
    }

    @Override
    public boolean isDestroyed() {
        return false;
    }

    @Override
    public void onExpire() {
        player.getEventSwitch().unregister(this);
    }

    @Override
    public void stop() {

    }

    @Override
    public void onStatusChange() {

    }

    @Override
    public void onMidNight() {
        super.onMidNight();
        resetMakeUpTimes();
        ntfActivityInfo();
    }

    @Override
    public void onLogin() {
        super.onLogin();
        resetMakeUpTimes();
        ntfActivityInfo();
    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATHalfYearWarmUp;
    }

    private com.tencent.wea.attr.HalfYearWarmUpActivity getAttr() {
        return getActivityUnit().getDetailData().getHywarmupActiviyData();
    }

    private ActivityHYWarmUpConf getActivityConf() {
        ActivityHYWarmUpConf actConf = ActivityHYWarmUpData.getInstance().getConfByActivityId(activityId);
        if (null == actConf) {
            LOGGER.error("actId {} not exist in ActivityHYWarmUpData", activityId);
        }
        return actConf;
    }

    private ActivityHYWarmUpCheckinConf getCheckInConf(int day) {
        ActivityHYWarmUpConf actConf = getActivityConf();
        if (null == actConf) {
            return null;
        }
        List<Integer> checkInDataList = actConf.getCheckInDataListList();
        ActivityHYWarmUpCheckinConf checkinConf = null;
        for (Integer dataId : checkInDataList) {
            ActivityHYWarmUpCheckinConf conf = ActivityHYWarmUpCheckinData.getInstance().getConfById(dataId);
            if (null != conf) {
                if (day == conf.getDay()) {
                    checkinConf = conf;
                    break;
                }
            }
        }
        return checkinConf;
    }

    private ActivityHYWarmUpProgressConf getProgressConf(int day) {
        ActivityHYWarmUpConf actConf = getActivityConf();
        if (null == actConf) {
            return null;
        }
        List<Integer> progressDataList = actConf.getProgressDataListList();
        ActivityHYWarmUpProgressConf progressConf = null;
        for (Integer dataId : progressDataList) {
            ActivityHYWarmUpProgressConf conf = ActivityHYWarmUpProgressData.getInstance().getConfById(dataId);
            if (null != conf) {
                if (day == conf.getDay()) {
                    progressConf = conf;
                    break;
                }
            }
        }
        return progressConf;
    }

    public List<Item> getRewardList(int rewardType, int day) {
        switch (rewardType) {
            case HYWarmupRewardType.daily_VALUE:
                ActivityHYWarmUpCheckinConf dayConf = getCheckInConf(day);
                if (null != dayConf) {
                    return dayConf.getRewardsList();
                }
            case HYWarmupRewardType.progress_VALUE:
                ActivityHYWarmUpProgressConf progressConf = getProgressConf(day);
                if (null != progressConf) {
                    return  progressConf.getRewardsList();
                }
            case HYWarmupRewardType.bigReward_VALUE:
                ActivityHYWarmUpConf actConf = ActivityHYWarmUpData.getInstance().getConfByActivityId(activityId);
                if (null != actConf) {
                    return actConf.getFinalBigRewardList();
                }
            default:
                return null;
        }
    }

    private int getTotalCheckInDayCount() {
        int totalCheckInDayCount = 0;
        MapAttrObj<Integer, HalfYearWarmUpDayInfo> checkedDays = getAttr().getCheckedDays();
        for (HalfYearWarmUpDayInfo dayInfo : checkedDays.values()) {
            if (dayInfo.getCheckInTimeStamp() != 0) {
                totalCheckInDayCount ++;
            }
        }
        return totalCheckInDayCount;
    }

    /**
     * 判断某天是否打过卡
     */
    private boolean isAlreadyCheckIn(int day) {
        MapAttrObj<Integer, HalfYearWarmUpDayInfo> checkedDays = getAttr().getCheckedDays();
        HalfYearWarmUpDayInfo dayInfo = checkedDays.get(day);
        if (null == dayInfo) {
            return false;
        }
        return dayInfo.getCheckInTimeStamp() != 0;
    }

    /**
     * 重置每天的补打卡次数
     */
    private void resetMakeUpTimes() {
        int today = getCurDay();
        int recordday = getAttr().getMakeUpDay();
        if (today != recordday) {
            getAttr().setMakeUpTimes(0).setMakeUpDay(today);
        }
        LOGGER.debug("uid:{} makeup recordday:{} today:{}", player.getUid(), recordday, today);
    }

    /**
     * @return 已经补打卡的天数
     */
    private int getTotalMakeUpTimes() {
        int total = 0;
        for (HalfYearWarmUpDayInfo dayInfo : getAttr().getCheckedDays().values()) {
            if (dayInfo.getIsMakeUp()) {
                total++;
            }
        }
        return total;
    }

    private int getCurDay() {
        long beginTime = getActivityMainConfig().getTimeInfo().getBeginTime().getSeconds();
        long endTime = getActivityMainConfig().getTimeInfo().getEndTime().getSeconds();
        long now = DateUtils.currentTimeSec();
        if (now >= endTime) {
            now = endTime;
        }
        return DateUtils.getDayIntervalBySec(beginTime, now) + 1;
    }

    /**
     * 同步当前活动信息
     */
    public void ntfActivityInfo() {
        ActivityHYWarmUpInfoNtf.Builder builder = ActivityHYWarmUpInfoNtf.newBuilder();
        builder.setMakeUpTimes(getAttr().getMakeUpTimes());
        builder.setActivityId(activityId);
        for (HalfYearWarmUpDayInfo dayInfo : getAttr().getCheckedDays().values()) {
            ActivityHYWarmUpDayInfo.Builder dayInfoBuilder = ActivityHYWarmUpDayInfo.newBuilder()
                    .setDay(dayInfo.getDay())
                    .setIsMakeUp(dayInfo.getIsMakeUp())
                    .setIsRewarded(dayInfo.getIsRewarded())
                    .setCheckInTimeStamp(dayInfo.getCheckInTimeStamp());
            builder.addDayInfo(dayInfoBuilder);
        }
        builder.addAllRewardedList(getAttr().getRewardedList().getValues());
        LOGGER.debug("uid:{} ntfActivityInfo:{}", player.getUid(), builder);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ACTIVITYHYWARMUPINFONTF, builder);

        refreshCheckinRedDot(true);
    }

    /**
     * @param day 第几天
     * @param isMakeUp 是否为补签
    */
    private void checkin(int day, boolean isMakeUp) {
        MapAttrObj<Integer, HalfYearWarmUpDayInfo> checkedDays = getAttr().getCheckedDays();
        long now = DateUtils.currentTimeSec();
        if (checkedDays.containsKey(day)) {
            checkedDays.get(day)
                    .setIsMakeUp(isMakeUp)
                    .setCheckInTimeStamp(now);
        }else{
            HalfYearWarmUpDayInfo dayInfo = new HalfYearWarmUpDayInfo()
                    .setDay(day)
                    .setIsRewarded(false)
                    .setIsMakeUp(isMakeUp)
                    .setCheckInTimeStamp(now);
            checkedDays.put(day, dayInfo);
        }
        refreshCheckinRedDot(false);
        TlogFlowMgr.sendAnniversaryCheckinFlow(player, activityId, 0, day, (isMakeUp ? 1 : 0),
                getTotalMakeUpTimes(), checkedDays.size());
    }

    /**
     * 正常打卡
     */
    public NKErrorCode normalCheckin() {
        int today = getCurDay();
        if (isAlreadyCheckIn(today)) {
            return NKErrorCode.ActivityHYWarmUpAlreadyCheckIn;
        }
        checkin(today, false);
        //clickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, SIGN_REDDOT_ID);
        return NKErrorCode.OK;
    }

    /**
     * 补签打卡
     */
    public NKErrorCode makeUpCheckin(int day) {
        int curDay = getCurDay();
        if (day >= curDay ) {
            return NKErrorCode.ActivityHYWarmUpMakeUpDayInvalid;
        }
        if (null == getCheckInConf(day)) {
            return NKErrorCode.ActivityHYWarmUpMakeUpDayInvalid;
        }
        if (isAlreadyCheckIn(day)) {
            return NKErrorCode.ActivityHYWarmUpAlreadyCheckIn;
        }
        if (getAttr().getMakeUpTimes() < 1) {
            return NKErrorCode.ActivityHYWarmUpMakeUpTimesNotEnough;
        }

        getAttr().addMakeUpTimes(-1);
        checkin(day, true);
        //clickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, MAKEUP_REDDOT_ID);
        return NKErrorCode.OK;
    }

    /**
     * @param reason, ICR_ActivityHYWarmUpReward或者ICR_ActivityHYWarmUpProgressReward
     * @param subReason, 第几天
     * @param maxCnt, 目前都为1
     */
    private OutputModuleCtx makeOutputCtx(int reason, int subReason, int maxCnt) {
        OutputModuleCtx outputCtx = GamesvrOutputMgr.makePlayerActivityCtx(player, activityId, reason, subReason, 0, "");
        outputCtx.initPeriodKeyAndMaxCnt(
                BaseOutputModule.calcPeriodKeyByPeriodType(GeneralOutputPeriodType.GOPT_Lifelong, DateUtils.currentTimeSec()),
                maxCnt);
        return outputCtx.checkOutput(1);
    }

    private NKErrorCode sendReward(List<Item> rewardList, ItemChangeReason reason, int subReason) {
        LOGGER.info("uid:{} rewardType:{} day:{}", player.getUid(), reason, subReason);
        OutputModuleCtx outputCtx = makeOutputCtx(reason.getNumber(), subReason, 1);
        if (!outputCtx.canOutput()) {
            LOGGER.error("uid:{} reason:{} subReason:{} error: output control check fail", player.getUid(), reason, subReason);
            return NKErrorCode.OutputControlCheckFail;
        }

        ChangedItems rewardItems = new ChangedItems(rewardList, reason.getNumber(), "");
        NKPair<NKErrorCode, ItemChangeDetails> res = getPlayer().getBagManager().AddItems2(rewardItems, subReason);
        NKErrorCode errorCode = res.getKey();
        if (!errorCode.isOk()) {
            LOGGER.error("uid:{} get reward error, reason: {}, subReason: {}, err: {}",
                    player.getUid(), reason, subReason, errorCode.toString());
        }
        outputCtx.recordOutput();  // 产出控制系统计数
        return errorCode;
    }

    /**
     * 领取累计天数的分享奖励，需要完成累计天数的打卡
     */
    public NKErrorCode recvDailyReward(int day) {
        ActivityHYWarmUpCheckinConf checkinConf = getCheckInConf(day);
        if (checkinConf == null) {
            return NKErrorCode.ActivityHYWarmUpRewardConfNotFound;
        }
        if (day > getTotalCheckInDayCount()) {
            return NKErrorCode.ActivityHYWarmUpRecvRewardInvalid;
        }

        HalfYearWarmUpDayInfo dayInfo = getAttr().getCheckedDays().computeIfAbsent(
                day, k -> new HalfYearWarmUpDayInfo().setDay(k));
        if (dayInfo.getIsRewarded()) {
            return NKErrorCode.ActivityHYWarmUpAlreadyRecvReward;
        }
        dayInfo.setIsRewarded(true);
        return sendReward(checkinConf.getRewardsList(), ItemChangeReason.ICR_ActivityHYWarmUpReward, day);
    }

    /**
     * 领取进度奖励，需要完成一定次数的打卡
     */
    public NKErrorCode recvProgressReward(int day) {
        ActivityHYWarmUpProgressConf progressConf = getProgressConf(day);
        if (progressConf == null) {
            return NKErrorCode.ActivityHYWarmUpRewardConfNotFound;
        }
        if (day > getTotalCheckInDayCount()) {
            return NKErrorCode.ActivityHYWarmUpRecvRewardInvalid;
        }
        if (getAttr().getRewardedList().contains(day)) {
            return NKErrorCode.ActivityHYWarmUpAlreadyRecvReward;
        }

        getAttr().addRewardedList(day);
        return sendReward(progressConf.getRewardsList(), ItemChangeReason.ICR_ActivityHYWarmUpProgressReward, day);
    }

    /**
     * 领取大奖，需要完成所有配置天数打卡
     */
    public NKErrorCode recvBigReward() {
        ActivityHYWarmUpConf actConf = ActivityHYWarmUpData.getInstance().getConfByActivityId(activityId);
        if (null == actConf) {
            LOGGER.error("actId {} not exist in ActivityHYWarmUpData", activityId);
            return NKErrorCode.ActivityHYWarmUpRewardConfNotFound;
        }

//        MapAttrObj<Integer, HalfYearWarmUpDayInfo> checkedDays = getAttr().getCheckedDays();
//        List<Integer> checkInDataList = actConf.getCheckInDataListList();
//        for (Integer dataId : checkInDataList) {
//            ActivityHYWarmUpCheckinConf checkinConf = ActivityHYWarmUpCheckinData.getInstance().getConfById(dataId);
//            if (null == checkinConf) {
//                continue;
//            }
//
//            if (!isAlreadyCheckIn(checkinConf.getDay())) {
//                return NKErrorCode.ActivityHYWarmUpRecvRewardInvalid;
//            }
//        }
        int bigRewardDay = BIG_REWARD_DAY;  // 策划一直在改这里大奖的发放逻辑，先暂时这样吧
        if (getAttr().getRewardedList().contains(bigRewardDay)) {
            return NKErrorCode.ActivityHYWarmUpAlreadyRecvReward;
        }

        getAttr().addRewardedList(bigRewardDay);
        return sendReward(actConf.getFinalBigRewardList(), ItemChangeReason.ICR_ActivityHYWarmUpReward, bigRewardDay);
    }

    @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerFinishBattle)
    private void onEvent(PlayerFinishBattleEvent event) throws NKRuntimeException {
        int today = getCurDay();
        getAttr().addMakeUpTimes(1).setMakeUpDay(today);
        LOGGER.debug("uid:{} add makeup times: {}, day: {}", player.getUid(), getAttr().getMakeUpTimes(), today);
        ntfActivityInfo();
    }

    private void refreshCheckinRedDot(boolean isNtf) {
        int curDay = getCurDay();
        boolean hasRedDot = false;
        // 今天没签到加红点
        if (getProgressConf(curDay) != null && !isAlreadyCheckIn(curDay)) {
            //addClickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, SIGN_REDDOT_ID, false);
            hasRedDot = true;
        }

        // 当天之前可以补签的添加红点
        if (getAttr().getMakeUpTimes() > 0 && getAttr().getMakeUpDay() == curDay) {
            for (int i = 1; i < curDay; i++) {
                if (null != getCheckInConf(i) && !isAlreadyCheckIn(i)) {
                    //addClickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, MAKEUP_REDDOT_ID, false);
                    hasRedDot = true;
                    break;
                }
            }
        } else {
            // 没有补签资格，清除红点
            //clickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, MAKEUP_REDDOT_ID, false);
        }

        //进度奖励
        //clickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, DAILY_REWARD_REDDOT_ID, false);
        for (int i = 1; i <= curDay; i++) {
            ActivityHYWarmUpProgressConf progressConf = getProgressConf(i);
            if (progressConf != null && i <= getTotalCheckInDayCount() && !getAttr().getRewardedList()
                    .contains(i)) {
                //addClickRedDot(ActivityRedDotType.ARDT_HalfYearWarmUpSign, PROCESS_REWARD_REDDOT_ID, false);
                hasRedDot = true;
                break;
            }
        }

        if (hasRedDot) {
            updateRedDotInfo();
        } else {
            delRedDot();
        }
    }

}
