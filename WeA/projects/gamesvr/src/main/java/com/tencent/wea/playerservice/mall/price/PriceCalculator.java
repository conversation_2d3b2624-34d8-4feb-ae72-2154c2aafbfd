package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 价格计算器
 * 负责计算商品的最终购买价格
 */
public class PriceCalculator {

    private static final Logger LOGGER = LogManager.getLogger(PriceCalculator.class);

    private final PriceModifierRegistry modifierRegistry;

    public PriceCalculator() {
        this.modifierRegistry = PriceModifierRegistry.getInstance();
    }

    /**
     * 计算商品的最终购买价格
     *
     * @param player 玩家
     * @param commodityConf 商品配置
     * @param buyNum 购买数量
     * @param isDirectBuy 是否直购
     * @param businessBillNo 业务单号
     * @return 最终价格
     */
    public long calculateFinalPrice(Player player, MallCommodity commodityConf, int buyNum,
            boolean isDirectBuy, String businessBillNo) {
        // 计算基础价格
        long basePrice = calculateBasePrice(commodityConf);

        // 创建价格计算上下文
        PriceContext context = new PriceContext(player, commodityConf, buyNum,
                basePrice, isDirectBuy, businessBillNo);

        // 应用价格修改器
        return applyPriceModifiers(context, basePrice);
    }

    /**
     * 计算商品的最终购买价格（带详细信息）
     *
     * @param player 玩家
     * @param commodityConf 商品配置
     * @param buyNum 购买数量
     * @param isDirectBuy 是否直购
     * @param businessBillNo 业务单号
     * @return 价格计算详情
     */
    public PriceCalculationDetail calculateFinalPriceWithDetail(Player player, MallCommodity commodityConf,
            int buyNum, boolean isDirectBuy, String businessBillNo) {
        // 计算基础价格
        long basePrice = calculateBasePrice(commodityConf);

        // 创建价格计算上下文
        PriceContext context = new PriceContext(player, commodityConf, buyNum,
                basePrice, isDirectBuy, businessBillNo);

        // 应用价格修改器并记录详情
        return applyPriceModifiersWithDetail(context, basePrice);
    }

    /**
     * 计算基础价格（原有逻辑）
     *
     * @param commodityConf 商品配置
     * @return 基础价格
     */
    private long calculateBasePrice(MallCommodity commodityConf) {
        return commodityConf.getDiscountPrice() > 0 ?
                commodityConf.getDiscountPrice() : commodityConf.getPrice();
    }

    /**
     * 应用价格修改器
     *
     * @param context 价格计算上下文
     * @param currentPrice 当前价格
     * @return 修改后的价格
     */
    private long applyPriceModifiers(PriceContext context, long currentPrice) {
        List<PriceModifier> applicableModifiers = modifierRegistry.getApplicableModifiers(context);

        long finalPrice = currentPrice;
        for (PriceModifier modifier : applicableModifiers) {
            try {
                PriceModificationResult result = modifier.modifyPrice(context, finalPrice);
                if (result.isPriceChanged()) {
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("Price modified by '{}' for commodity {} from {} to {}: {}",
                                modifier.getName(), context.getCommodityId(),
                                finalPrice, result.getModifiedPrice(), result.getReason());
                    }
                    finalPrice = result.getModifiedPrice();
                }
            } catch (Exception e) {
                LOGGER.error("Error applying price modifier '{}' for commodity {}: {}",
                        modifier.getName(), context.getCommodityId(), e.getMessage(), e);
            }
        }

        return finalPrice;
    }

    /**
     * 应用价格修改器并记录详情
     *
     * @param context 价格计算上下文
     * @param currentPrice 当前价格
     * @return 价格计算详情
     */
    private PriceCalculationDetail applyPriceModifiersWithDetail(PriceContext context, long currentPrice) {
        List<PriceModifier> applicableModifiers = modifierRegistry.getApplicableModifiers(context);
        List<PriceModificationResult> modifications = new ArrayList<>();

        long finalPrice = currentPrice;
        for (PriceModifier modifier : applicableModifiers) {
            try {
                PriceModificationResult result = modifier.modifyPrice(context, finalPrice);
                modifications.add(result);

                if (result.isPriceChanged()) {
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("Price modified by '{}' for commodity {} from {} to {}: {}",
                                modifier.getName(), context.getCommodityId(),
                                finalPrice, result.getModifiedPrice(), result.getReason());
                    }
                    finalPrice = result.getModifiedPrice();
                }
            } catch (Exception e) {
                LOGGER.error("Error applying price modifier '{}' for commodity {}: {}",
                        modifier.getName(), context.getCommodityId(), e.getMessage(), e);
                modifications.add(PriceModificationResult.noChange(finalPrice));
            }
        }

        return new PriceCalculationDetail(currentPrice, finalPrice, modifications);
    }
}
