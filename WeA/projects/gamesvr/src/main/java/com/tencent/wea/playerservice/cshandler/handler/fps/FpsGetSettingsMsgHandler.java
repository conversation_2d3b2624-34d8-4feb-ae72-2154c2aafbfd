package com.tencent.wea.playerservice.cshandler.handler.fps;

import com.google.protobuf.Message;
import com.tencent.wea.attr.FpsSettings;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsFps;
import com.tencent.wea.protocol.CsHead;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class FpsGetSettingsMsgHandler extends AbstractGsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(FpsGetSettingsMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsFps.FpsGetSettings_C2S_Msg reqMsg = (CsFps.FpsGetSettings_C2S_Msg) request;
        CsFps.FpsGetSettings_S2C_Msg.Builder ret = CsFps.FpsGetSettings_S2C_Msg.newBuilder();

        FpsSettings settings = player.getUserAttr().getFpsSettings();
        CsFps.FpsSettings.Builder settingsBuilder = CsFps.FpsSettings.newBuilder();
        settingsBuilder.addAllShootMode(settings.getShootModeList());
        settingsBuilder.addAllViewMode(settings.getViewModeList());
        settingsBuilder.setAimBtnRotate(settings.getAimBtnRotate());
        settingsBuilder.setSniperFireMode(settings.getSniperFireMode());
        settingsBuilder.setJoyStickMode(settings.getJoyStickMode());
        settingsBuilder.setCameraSensitivity(settings.getCameraSensitivity());
        settingsBuilder.setAimCameraSensitivity(settings.getAimCameraSensitivity());
        settingsBuilder.setOpenGyro(settings.getOpenGyro());
        settingsBuilder.setGyroSensitivity(settings.getGyroSensitivity());
        settingsBuilder.setAimGyroSensitivity(settings.getAimGyroSensitivity());
        settingsBuilder.addAllSightCameraSensitivity(settings.getSightCameraSensitivityList());
        settingsBuilder.addAllSightGyroSensitivity(settings.getSightGyroSensitivityList());
        ret.setSettings(settingsBuilder);
        ret.setIsNew(!settings.getIsSetByPlayer());

        return ret;
    }
}