package com.tencent.wea.playerservice.player;


import static com.tencent.tconnd.Session.State.STATE_CONNECTED;
import static com.tencent.wea.wolfKill.Util.getWolfKillDefaultAni;

import com.google.common.collect.Lists;
import com.google.protobuf.Message;
import com.google.protobuf.TextFormat;
import com.tencent.condition.event.player.common.GameTimesStatEvent;
import com.tencent.condition.event.player.common.HeartBeatEvent;
import com.tencent.condition.event.player.common.LoginEvent;
import com.tencent.condition.event.player.common.LogoutEvent;
import com.tencent.condition.event.player.common.PlayerLeaveEvent;
import com.tencent.eventbuspro.EventSwitch;
import com.tencent.eventcenter.BaseEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.gameplay.GamePlay;
import com.tencent.gameplay.GamePlayHolder;
import com.tencent.nk.activity.ActivityTimeUtil;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.itop.ItopManager;
import com.tencent.nk.itop.MSDKAchievementReporter;
import com.tencent.nk.itop.achievement.AchieveBody;
import com.tencent.nk.itop.achievement.AchieveBody.AchiParamType;
import com.tencent.nk.player.PlayerUtil;
import com.tencent.nk.tlog.PlayerFlow;
import com.tencent.nk.tlog.TlogFlow;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.WhiteList;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.guid.BlessBagSerialId;
import com.tencent.opentracing.tps.TpsSpan;
import com.tencent.opentracing.tps.TpsTracer;
import com.tencent.opentracing.tps.TpsTracerMgr;
import com.tencent.pushmsg.PushTopic;
import com.tencent.pushmsg.PushTopicSubscriber;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.resclass.ActivityMainConfig;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.ChannelDisableSceneConf;
import com.tencent.resourceloader.resclass.ClientKVConf;
import com.tencent.resourceloader.resclass.CupsConfigData;
import com.tencent.resourceloader.resclass.FeatureOpenConfData;
import com.tencent.resourceloader.resclass.GuideAppConfigData;
import com.tencent.resourceloader.resclass.LanguagesData;
import com.tencent.resourceloader.resclass.LevelRoundRandomRuleData;
import com.tencent.resourceloader.resclass.LobbyConfigData;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.resourceloader.resclass.MatchTypeGroupConfData;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.MiscConfArena;
import com.tencent.resourceloader.resclass.NewYearPilotEntranceData;
import com.tencent.resourceloader.resclass.PlayerLevelConfData;
import com.tencent.resourceloader.resclass.QAInvestCallbackUrlConfigData;
import com.tencent.resourceloader.resclass.QAInvestData;
import com.tencent.resourceloader.resclass.RegionalConf;
import com.tencent.resourceloader.resclass.SeasonConfData;
import com.tencent.resourceloader.resclass.ServerIdipAreaConfig;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.resourceloader.resclass.VersionCompBattleConf;
import com.tencent.resourceloader.resclass.WolfKillDecorationAni;
import com.tencent.resourceloader.resclass.WolfKillVocation;
import com.tencent.resourceloader.resclass.XlsWhiteListPlayerGroupConfData;
import com.tencent.rpc.RpcResult;
import com.tencent.tbuspp.TbusppInstance;
import com.tencent.tbuspp.TbusppUtil;
import com.tencent.tcaplus.AsyncTcaplusManager;
import com.tencent.tcaplus.BaseDBObject;
import com.tencent.tcaplus.CoroUpdateQueueEntry;
import com.tencent.tcaplus.TcaplusErrorCode.TcaplusErrorException;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusTable;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.tcaplus.dao.PlayerCommodityBuyTimesSyncDao;
import com.tencent.tcaplus.dao.PlayerExtraInfoTableDao;
import com.tencent.tcaplus.dao.PlayerPublicDao;
import com.tencent.tcaplus.dao.PlayerPublicDao.PlayerPublicAttrKey;
import com.tencent.tcaplus.dao.RelationTableDao;
import com.tencent.tconnd.Session;
import com.tencent.tconnd.TconndManager;
import com.tencent.timiCoroutine.AsyncCallableGroup;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiCoroutine.ExecutorLocal;
import com.tencent.timiCoroutine.TimiCoroExecutorService;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.rpcratelimit.RpcRateLimit;
import com.tencent.timiutil.rpcratelimit.RpcRateLimitController;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.tool.FunctionUtil;
import com.tencent.timiutil.tool.JolTool;
import com.tencent.timiutil.tool.ObjLeakMgr;
import com.tencent.timiutil.tool.ServerInfoTool;
import com.tencent.util.BlueGreenDeploymentUtil;
import com.tencent.util.ConfigUtils;
import com.tencent.util.HistogramPeriod;
import com.tencent.util.MetricStat;
import com.tencent.util.NkProtoHelper.SerializedMessage;
import com.tencent.util.ReputationScoreUtil;
import com.tencent.util.TimeUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.arena.protocol.common.ArenaSdkShare.ArenaStatType;
import com.tencent.wea.attr.AlbumExtInfo;
import com.tencent.wea.attr.AlbumInfo;
import com.tencent.wea.attr.AlbumLikeHisInfo;
import com.tencent.wea.attr.ArenaMatchStat;
import com.tencent.wea.attr.ArenaStat;
import com.tencent.wea.attr.ArenaSeasonStat;
import com.tencent.wea.attr.ArenaStatKV;
import com.tencent.wea.attr.AttrProfiler;
import com.tencent.wea.attr.BasicInfo;
import com.tencent.wea.attr.BattleModeData;
import com.tencent.wea.attr.BindAccountInfo;
import com.tencent.wea.attr.ChatGroupKey;
import com.tencent.wea.attr.DBApplyRelation;
import com.tencent.wea.attr.DressItemInfo;
import com.tencent.wea.attr.DressUpDetailInfo;
import com.tencent.wea.attr.Interaction;
import com.tencent.wea.attr.Item;
import com.tencent.wea.attr.KeyValStr;
import com.tencent.wea.attr.MapAttrObj;
import com.tencent.wea.attr.MatchIsolateInfoDb;
import com.tencent.wea.attr.PakDownloadInfo;
import com.tencent.wea.attr.PlatPrivilegesInfo;
import com.tencent.wea.attr.PlayerBlessBagInfo;
import com.tencent.wea.attr.PlayerGamePlay;
import com.tencent.wea.attr.PlayerGameTime;
import com.tencent.wea.attr.PlayerPublicProfileInfo;
import com.tencent.wea.attr.PlayerPushTopic;
import com.tencent.wea.attr.PlayerRankGeoInfo;
import com.tencent.wea.attr.PushTopicHandle;
import com.tencent.wea.attr.QAInvestInfo;
import com.tencent.wea.attr.QualifyingInfo;
import com.tencent.wea.attr.SetAttrObj;
import com.tencent.wea.attr.StreamSetting;
import com.tencent.wea.attr.UserAttr;
import com.tencent.wea.attr.WelfareAerospaceTechEdData;
import com.tencent.wea.attr.WelfareHistoryBill;
import com.tencent.wea.attr.WolfKillTreasureEquipInfo;
import com.tencent.wea.attr.XlsWhiteList;
import com.tencent.wea.framework.AntiAddictMgr;
import com.tencent.wea.framework.GSConfig;
import com.tencent.wea.framework.GSEngine;
import com.tencent.wea.g6.irpc.proto.competition.Competition.WolfKillVocationTime;
import com.tencent.wea.interaction.player.InviteRegisterInteraction;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.midjoin.HokConfs;
import com.tencent.wea.playerservice.achievement.AchievementManager;
import com.tencent.wea.playerservice.activity.lifecycle.ActivityManager;
import com.tencent.wea.playerservice.activity.lifecycle.ActivityMsgHelper;
import com.tencent.wea.playerservice.activity.sharedata.IntellectualActivityMgr;
import com.tencent.wea.playerservice.aigcnpc.PlayerAigcNpcManager;
import com.tencent.wea.playerservice.album.PlayerAlbumManager;
import com.tencent.wea.playerservice.ams.PlayerAmsItemMgr;
import com.tencent.wea.playerservice.arena.PlayerArenaMgr;
import com.tencent.wea.playerservice.attr.PlayerGameAttrMgr;
import com.tencent.wea.playerservice.attr.PlayerGameDBMgr;
import com.tencent.wea.playerservice.attrdataopt.PlayerAttrDataOperateMgr;
import com.tencent.wea.playerservice.bag.BagManager;
import com.tencent.wea.playerservice.bag.ItemEquipMgr;
import com.tencent.wea.playerservice.bag.ItemManager;
import com.tencent.wea.playerservice.birthday.PlayerBirthdayMgr;
import com.tencent.wea.playerservice.bp.BPManager;
import com.tencent.wea.playerservice.chase.PlayerChaseMgr;
import com.tencent.wea.playerservice.chat.PlayerChatManager;
import com.tencent.wea.playerservice.coc.PlayerCocMgr;
import com.tencent.wea.playerservice.collection.CollectionManager;
import com.tencent.wea.playerservice.concert.PlayerConcertMgr;
import com.tencent.wea.playerservice.cshandler.handler.player.GMCommandMsgHandler;
import com.tencent.wea.playerservice.cups.CupsManager;
import com.tencent.wea.playerservice.discover.DiscoverManager;
import com.tencent.wea.playerservice.event.PlayerEventManager;
import com.tencent.wea.playerservice.event.common.PlayerChangeProfileEvent;
import com.tencent.wea.playerservice.event.common.PlayerGameTimesStatEvent;
import com.tencent.wea.playerservice.event.common.PlayerHeartBeatEvent;
import com.tencent.wea.playerservice.event.common.PlayerLoginEvent;
import com.tencent.wea.playerservice.event.common.PlayerOnMidNightEvent;
import com.tencent.wea.playerservice.event.common.PlayerPlayNewYearCallEvent;
import com.tencent.wea.playerservice.event.common.PlayerReturnEvt;
import com.tencent.wea.playerservice.event.common.PlayerSendDanmuEvent;
import com.tencent.wea.playerservice.gamemodule.containers.PlayerModuleContainer;
import com.tencent.wea.playerservice.hok.PlayerHokMgr;
import com.tencent.wea.playerservice.hotresource.HotResResourceManager;
import com.tencent.wea.playerservice.iaa.IAAManager;
import com.tencent.wea.playerservice.idipstatistics.IdipStatisticsManager;
import com.tencent.wea.playerservice.interaction.InteractionMgr;
import com.tencent.wea.playerservice.layout.LayoutMgr;
import com.tencent.wea.playerservice.lbs.LBSManager;
import com.tencent.wea.playerservice.level.PlayerGameLevelEstimationMgr;
import com.tencent.wea.playerservice.level.PlayerLevelIllustrationMgr;
import com.tencent.wea.playerservice.level.PlayerLevelRecordMgr;
import com.tencent.wea.playerservice.like.LikeManager;
import com.tencent.wea.playerservice.limit.LimitManager;
import com.tencent.wea.playerservice.limitexperienceiterm.PlayerLimitExperienceItemMgr;
import com.tencent.wea.playerservice.lobby.FireworksManager;
import com.tencent.wea.playerservice.login.LoginStatMgr;
import com.tencent.wea.playerservice.login.LoginStepEnum;
import com.tencent.wea.playerservice.loginplat.PlayerPcWebToPcSimulateMgr;
import com.tencent.wea.playerservice.mail.MailMgr;
import com.tencent.wea.playerservice.mall.MallManager;
import com.tencent.wea.playerservice.mall.MallWishListMgr;
import com.tencent.wea.playerservice.marquee.MarqueeNoticeMgr;
import com.tencent.wea.playerservice.match.PlayerMatchMMRScoreManager;
import com.tencent.wea.playerservice.match.PlayerMatchWarmRoundManager;
import com.tencent.wea.playerservice.mayday.MayDayUtil;
import com.tencent.wea.playerservice.messageslip.PlayerMessageSlipMgr;
import com.tencent.wea.playerservice.mod.PlayerModMgr;
import com.tencent.wea.playerservice.money.PlayerMoneyMgr;
import com.tencent.wea.playerservice.network.PlayerIDCNetworkInfoManager;
import com.tencent.wea.playerservice.newactivitypilot.PlayerNewActivityPilotMgr;
import com.tencent.wea.playerservice.newyearpilot.PlayerNewYearPilotMgr;
import com.tencent.wea.playerservice.notify.NotifyMgr;
import com.tencent.wea.playerservice.numericattr.PlayerNumericAttrMgr;
import com.tencent.wea.playerservice.outputcontrol.PlayerOutputMgr;
import com.tencent.wea.playerservice.party.XiaowoRecom;
import com.tencent.wea.playerservice.permit.PermitLevelAwardManager;
import com.tencent.wea.playerservice.permit.PermitManager;
import com.tencent.wea.playerservice.player.PlayerMgr.RemovePlayerReason;
import com.tencent.wea.playerservice.player.PlayerStateMgr.PlayerStateAction;
import com.tencent.wea.playerservice.playermodule.LetsGoPlayerBattleMgr;
import com.tencent.wea.playerservice.playerref.PlayerRefMgr;
import com.tencent.wea.playerservice.profile.ProfileManager;
import com.tencent.wea.playerservice.qqbot.PlayerQqBotMgr;
import com.tencent.wea.playerservice.qualifying.QualifyingManager;
import com.tencent.wea.playerservice.raffle.RaffleManager;
import com.tencent.wea.playerservice.rank.RankManager;
import com.tencent.wea.playerservice.recharge.MonthCardManager;
import com.tencent.wea.playerservice.recharge.RechargeMgr;
import com.tencent.wea.playerservice.recommend.PlayerRecommendMgr;
import com.tencent.wea.playerservice.recommend.TestConditionMgr;
import com.tencent.wea.playerservice.reddot.FindRedDotMgr;
import com.tencent.wea.playerservice.reddot.PlayerGeneralRedDotMgr;
import com.tencent.wea.playerservice.redenvelope.PlayerRedEnvelopRainActManager;
import com.tencent.wea.playerservice.relation.BlackManager;
import com.tencent.wea.playerservice.relation.FansFollowManager;
import com.tencent.wea.playerservice.relation.FriendManager;
import com.tencent.wea.playerservice.relation.FriendRecommendManager;
import com.tencent.wea.playerservice.relation.IntimateManager;
import com.tencent.wea.playerservice.relation.PlayerSnsManager;
import com.tencent.wea.playerservice.relation.RelationManager;
import com.tencent.wea.playerservice.report.PlayerReportUtil;
import com.tencent.wea.playerservice.returning.PlayerFarmReturningMgr;
import com.tencent.wea.playerservice.returning.PlayerGameModeReturnManager;
import com.tencent.wea.playerservice.returning.PlayerReturnActivityManager;
import com.tencent.wea.playerservice.reward.PlayerRewardRetrievalMgr;
import com.tencent.wea.playerservice.safety.AntiCheatInfoReportMgr;
import com.tencent.wea.playerservice.safety.PlayerCreditScoreMgr;
import com.tencent.wea.playerservice.safety.PlayerReputationScoreMgr;
import com.tencent.wea.playerservice.season.SeasonFashionBattleDataMgr;
import com.tencent.wea.playerservice.season.SeasonMgr;
import com.tencent.wea.playerservice.season.SeasonReviewMgr;
import com.tencent.wea.playerservice.sharegift.manager.ShareGiftModule;
import com.tencent.wea.playerservice.sns.SnsInvitationMgr;
import com.tencent.wea.playerservice.squad.PlayerActivitySquadMgr;
import com.tencent.wea.playerservice.squad.PlayerSquadMgr;
import com.tencent.wea.playerservice.statistics.PlayerMonitorMgr;
import com.tencent.wea.playerservice.stream.PlayerStreamManager;
import com.tencent.wea.playerservice.task.RunTask;
import com.tencent.wea.playerservice.task.TaskModule;
import com.tencent.wea.playerservice.task.manager.TaskRewardManager;
import com.tencent.wea.playerservice.tlog.TlogCache;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.tradingcard.manager.TradingCardModule;
import com.tencent.wea.playerservice.tv.PlayerGameTvMgr;
import com.tencent.wea.playerservice.ugc.manager.PlayerUgcManager;
import com.tencent.wea.playerservice.ugc.manager.UgcCoCreateMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcCreatorHomePageMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcDailyStageMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcHomeRecommendMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcMatchLobbyMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcPlayerGrowUpMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcRecommendSetMgr;
import com.tencent.wea.playerservice.ugc.manager.UgcStarWorldMgr;
import com.tencent.wea.playerservice.uic.BattleChatReportInfo;
import com.tencent.wea.playerservice.visitor.VisitorMgr;
import com.tencent.wea.playerservice.welfare.TencentWelfareManager;
import com.tencent.wea.playerservice.welfare.TencentWelfareUtils;
import com.tencent.wea.protocol.AttrArenaMatchStat;
import com.tencent.wea.protocol.AttrBattleInfo;
import com.tencent.wea.protocol.AttrBattleModeData;
import com.tencent.wea.protocol.AttrBattleResultData;
import com.tencent.wea.protocol.AttrChatGroupKey;
import com.tencent.wea.protocol.AttrDressUpDetailInfo.proto_DressUpDetailInfo;
import com.tencent.wea.protocol.AttrScenePlayerBasicInfo.proto_ScenePlayerBasicInfo;
import com.tencent.wea.protocol.AttrScenePlayerPublicInfo;
import com.tencent.wea.protocol.AttrSquadMember.proto_SquadMember;
import com.tencent.wea.protocol.AttrUserAttr;
import com.tencent.wea.protocol.CsCommon.StateType;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsInformation;
import com.tencent.wea.protocol.CsLetsgo.LetsGoBattleSettlementNtf;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.CsPlayer.ABTestType;
import com.tencent.wea.protocol.CsPlayer.FeatureOpenNtf;
import com.tencent.wea.protocol.CsPlayer.PlayerAfterLoginNtf;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsBattlesvr;
import com.tencent.wea.protocol.SsBattlesvr.RpcBattlePlayerOfflineRes;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.SsRoomsvr.RoomSvrErrorCode;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.protocol.common.G6Common;
import com.tencent.wea.protocol.common.G6Common.BattleDsTlogType;
import com.tencent.wea.protocol.common.G6Common.ExitLobbyCode;
import com.tencent.wea.protocol.common.G6Common.LevelDropItemInfo;
import com.tencent.wea.redis.PlayerBanInfoCache;
import com.tencent.wea.room.RoomUtil.UpdateMemberBaseInfoSource;
import com.tencent.wea.rpc.service.BattleService;
import com.tencent.wea.rpc.service.GameService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDbWrapper;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tlog.flow.TlogMacros;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResChannelEntrance;
import com.tencent.wea.xlsRes.ResCommon.PlayerLoginCondition;
import com.tencent.wea.xlsRes.ResGeneral;
import com.tencent.wea.xlsRes.ResLanguages;
import com.tencent.wea.xlsRes.ResLobby.LobbyConfig;
import com.tencent.wea.xlsRes.ResLobby.LobbyType;
import com.tencent.wea.xlsRes.ResMisc;
import com.tencent.wea.xlsRes.ResNR3E3Vocation;
import com.tencent.wea.xlsRes.ResQAInvest;
import com.tencent.wea.xlsRes.ResRegional;
import com.tencent.wea.xlsRes.ResSeason;
import com.tencent.wea.xlsRes.ResTask.TaskGroup;
import com.tencent.wea.xlsRes.ResWolfKillDecoration;
import com.tencent.wea.xlsRes.keywords.ActivityNameType;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.BattleEventType;
import com.tencent.wea.xlsRes.keywords.CoinType;
import com.tencent.wea.xlsRes.keywords.DevMonitorType;
import com.tencent.wea.xlsRes.keywords.DeviceLevel;
import com.tencent.wea.xlsRes.keywords.FeatureOpenType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.LanguageType;
import com.tencent.wea.xlsRes.keywords.LoginDeviceType;
import com.tencent.wea.xlsRes.keywords.MarqueeNoticePushScene;
import com.tencent.wea.xlsRes.keywords.PlayerGameDataStatDuration;
import com.tencent.wea.xlsRes.keywords.PlayerGameDataStatType;
import com.tencent.wea.xlsRes.keywords.PlayerGameTimeType;
import com.tencent.wea.xlsRes.keywords.PlayerGrayTagType;
import com.tencent.wea.xlsRes.keywords.PlayerLoginPlat;
import com.tencent.wea.xlsRes.keywords.PlayerStateType;
import com.tencent.wea.xlsRes.keywords.QAInvestStatus;
import com.tencent.wea.xlsRes.keywords.QualifyType;
import com.tencent.wea.xlsRes.keywords.QualifyingDegreeInfo;
import com.tencent.wea.xlsRes.keywords.RelationApplyType;
import com.tencent.wea.xlsRes.keywords.RelationTypeEnum;
import com.tencent.wea.xlsRes.keywords.RoomStatus;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import com.tencent.wea.xlsRes.keywords.UgcAuthType;
import com.tencent.wea.xlsRes.keywords.WhiteListPriorityEnum;
import com.tencent.wechatrobot.WechatLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.annotation.concurrent.NotThreadSafe;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openjdk.jol.info.GraphLayout;

import java.nio.ByteBuffer;

import static com.tencent.tconnd.Session.State.STATE_CONNECTED;
import static com.tencent.wea.wolfKill.Util.getWolfKillDefaultAni;

/**
 * 玩家
 *
 * <AUTHOR>
 * @date 2021/07/02
 */
@NotThreadSafe
public class Player extends PlayerModuleContainer implements PlayerFlow, EventConsumer, BaseDBObject, PushTopicSubscriber {

    public static final int ROOM_MGR_PROCESS_MASK = 1;
    public static final int PLAYER_AFTER_LOGIN_PROCESS_MASK = 1 << 1;
    public static final int MOD_MGR_PROCESS_MASK = 1 << 2;
    public static final int AFTER_LOGIN_NTF_MASK = ROOM_MGR_PROCESS_MASK
            | PLAYER_AFTER_LOGIN_PROCESS_MASK | MOD_MGR_PROCESS_MASK;
    // 登录后检查玩家封禁标志数据
    private static final Map<Integer, NKErrorCode> AFTER_LOGIN_CHECK_PLAYER_BAN_FLAG =
            Map.of(BanType.BT_AiImage_VALUE, NKErrorCode.AiImageIsBanned,
                    BanType.BT_Voice_VALUE, NKErrorCode.VoiceFunctionIsBanned,
                    BanType.BT_Search_VALUE, NKErrorCode.SearchIsBanned,
                    BanType.BT_AiChangeColor_VALUE, NKErrorCode.AiChangeColorIsBanned,
                    BanType.BT_FarmSignature_VALUE, NKErrorCode.FarmSignatureIsBanned,
                    BanType.BT_CreateOrModifyElement_VALUE, NKErrorCode.CreateOrModifyElementIsBanned,
                    BanType.BT_CreateOrModifyUgcAchievement_VALUE, NKErrorCode.CreateOrModifyUgcAchievementIsBanned,
                    BanType.BT_AiAnswer_VALUE, NKErrorCode.AiAnswerIsBanned,
                    BanType.BT_AigcNpcPal_VALUE, NKErrorCode.AigcNpcPalIsBanned);
    private static final Integer QQ_PLAT_PRIVILEGES_MAIL_TEMPLATE_ID = 6;       // qq平台特权邮件模板id
    private static final Integer WX_PLAT_PRIVILEGES_MAIL_TEMPLATE_ID = 7;       // wx平台特权邮件模板id

    private static final Logger LOGGER = LogManager.getLogger(Player.class);
    private static final ExecutorLocal<HistogramPeriod> AttrPartSizeHisto = ExecutorLocal.withInitial(() -> {
        HistogramPeriod histo = new HistogramPeriod();
        String name = "AttrPartSizeHisto-" + TimiCoroExecutorService.getCurrentExecutorServiceIndex();
        MetricStat.addHistogram(name, histo);
        return histo;
    });
    private static final ExecutorLocal<HistogramPeriod> AttrFullSizeHisto = ExecutorLocal.withInitial(() -> {
        HistogramPeriod histo = new HistogramPeriod();
        String name = "AttrFullSizeHisto-" + TimiCoroExecutorService.getCurrentExecutorServiceIndex();
        MetricStat.addHistogram(name, histo);
        return histo;
    });
    private static final ExecutorLocal<AttrProfiler> attrProfiler = ExecutorLocal
            .withInitial(() -> new AttrProfiler("userAttr"));
    private static final Logger jolLogger = LogManager.getLogger("jolStat");
    private static final AtomicBoolean checkerRunning = new AtomicBoolean(false);
    private static final int maxDbMainAttrSize = 0;
    private static final AtomicLong lastSizeCheck = new AtomicLong(0);
    private static int interval = 60 * 1000;//1min

    // 玩家属性
    private final PlayerGameAttrMgr userAttrMgr;
    private final MailMgr mailManager;
    // 事件
    private final EventSwitch eventSwitch = new EventSwitch();
    // 活动
    private final ActivityManager activityManager;
    private final PlayerXiaoWoMgr playerXiaoWoMgr;
    private final PlayerQqBotMgr playerQqBotMgr;

    private final PlayerFarmMgr playerFarmMgr;
    private final PlayerHouseMgr playerHouseMgr;
    private final PlayerCookMgr playerCookMgr;
    private final PlayerCompetitionMgr playerCompetitionMgr;
    private final com.tencent.wea.playerservice.party.PartyManager partyManager;
    private final XiaowoRecom xiaowoRecom;
    private final String debugStr = "";
    private final int dbMainAttrSize = 0;
    private final PlayerStarPMgr playerStarPMgr;
    // 统计数据
    private final PlayerEventManager playerEventManager;
    private final FriendManager friendManager;
    private final BagManager bagManager;
    private final IntimateManager intimateManager;
    private final MallManager mallManager;
    private final MallWishListMgr mallWishListMgr;
    private final RankManager rankManager;
    private final BlackManager blackManager;
    private final TaskModule taskModule;
    private final PlayerPakPlayMgr playerPakPlayMgr;
    // private DressUpManager playerDressUpMgr;
    private final ProfileManager profileManager;
    private final VisitorMgr visitorMgr;
    private final InteractionMgr interactionMgr;
    private final PlayerNumericAttrMgr numericAttrMgr;
    private final LimitManager limitManager;
    private final FriendRecommendManager friendRecommendManager;
    private final IdipStatisticsManager idipStatisticsManager;
    private final FansFollowManager fansFollowManager;
    private final UgcDailyStageMgr ugcDailyStageMgr;
    private final UgcStarWorldMgr ugcStarWorldMgr;
    private final PlayerSceneMgr playerSceneMgr;
    private final PlayerLobbyMgr playerLobbyMgr;
    private final PlayerSceneInviteMgr playerSceneInviteMgr;
    private final PlayerClubMgr clubMgr;
    private final PlayerConditionMgr playerConditionMgr;
    private final PlayerOnlineMgr playerOnlineMgr;
    private final PlayerUgcManager playerUgcManager;
    private final PlayerArenaMgr playerArenaMgr;
    private final PlayerNr3e8Mgr playerNr3e8Mgr;
    private final PlayerHokMgr playerHokMgr;
    // 活动小队相关
    private final PlayerMoneyMgr playerMoneyMgr;
    private final PlayerStateMgr playerStateMgr;
    private final SeasonMgr seasonMgr;
    private final PermitManager permitManager;
    private final PermitLevelAwardManager permitLevelAwardManager;
    private final RaffleManager raffleManager;
    private final UgcHomeRecommendMgr ugcHomeRecommendMgr;
    private final UgcRecommendSetMgr ugcRecommendSetMgr;

    private final UgcPlayerGrowUpMgr ugcPlayerGrowUpMgr;
    private final UgcCreatorHomePageMgr ugcCreatorHomePageMgr;
    /**
     * 友情小队相关
     */
    private final PlayerSquadMgr squadMgr;
    /**
     * 多人小队相关
     */
    private final PlayerActivitySquadMgr activitySquadMgr;
    private final RechargeMgr rechargeMgr;
    private final PlayerLevelIllustrationMgr levelIllustrationMgr;
    private final PlayerLevelRecordMgr levelRecordMgr;
    private final PlayerMessageSlipMgr messageSlipMgr;
    private final MonthCardManager monthCardManager;
    private final PlayerSpecRewardMgr specRewardMgr;
    private final PlayerPersonalityMgr playerPersonalityMgr;
    private final UgcCoCreateMgr ugcCoCreateMgr;
    private final PlayerCreditScoreMgr playerCreditScoreMgr;
    private final boolean isRobot;
    private final AchievementManager achievementManager;
    private final PlayerAlbumManager albumMgr;
    private final SnsInvitationMgr snsInvitationMgr;
    private final FireworksManager fireworksMgr;
    private final LayoutMgr layoutMgr;
    private final LBSManager lbsManager;
    private final PlayerAmsItemMgr amsItemMgr;
    private final PlayerLobbyMatchMgr lobbyMatchMgr;
    /**
     * 奖杯征程
     */
    private final CupsManager cupsManager;

    /**
     * 卡牌系统
     */
    private final TradingCardModule tradingCardModule;


    /**
     * 分享礼包系统
     */
    private final ShareGiftModule shareGiftModule;

    /**
     * 安全Tlog缓存
     */
    private final TlogCache tlogCache;
    private final PlayerIdipMgr idipMgr;
    private final HashSet<PlayerPublicAttrKey> dirtyFieldsOfPlayerPublic = new HashSet<>();
    // 全服玩家通知
    private final MarqueeNoticeMgr marqueeNoticeMgr;
    private final PlayerNewYearPilotMgr newYearPilotMgr;
    private final PlayerNewActivityPilotMgr newActivityPilotMgr;

    private final PlayerRecentActivityMgr recentActivityMgr;
    //    private final ActivityTimeChecker activityTimeChecker;

    private final PlayerConcertMgr concertMgr;

    private final QuickRewardMgr quickRewardMgr;
    private final HotResResourceManager hotResResourceManager;
    private final IAAManager iaaManager;
    private final PlayerOutputMgr outputMgr;
    private final PlayerWhiteListMgr playerWhiteListMgr;
    private final PlayerStreamManager playerStreamMgr;
    private final PlayerAigcNpcManager playerAigcNpcMgr;
    private final PlayerLabelMgr labelMgr;
    private final PlayerGrayTagManager playerGrayTagMgr;
    private final PlayerGameTvMgr gameTvMgr;
    private final PlayerRecommendMgr recommendMgr;
    private final PlayerReputationScoreMgr reputationScoreMgr;
    private final PlayerWolfKillMgr wolfKillMgr;
    private final FindRedDotMgr findRedDotMgr;
    private final UgcMatchLobbyMgr ugcMatchLobbyMgr;
    AtomicBoolean playerKickUpdated = new AtomicBoolean(false);
    SizeChecker sizeChecker = new SizeChecker();
    private long onMidnightTime = 0;
    private long processTime = Framework.currentTimeMillis();
    private long playerLastSizeCheck = 0;
    // 数据库操作
    private int updateDBErrorCount = 0;
    private CoroUpdateQueueEntry updateQueueEntry = null;
    private BanInfo idipBanInfo = null;
    private AlbumInfo albumInfo = null;       // 玩家个人相册信息, 存储在PlayerExtraInfoTable表里
    private AlbumLikeHisInfo albumLikeHisInfo = null;       // 玩家个人相册点赞信息, 存储在PlayerExtraInfoTable表里
    private AlbumExtInfo albumExtInfo = null;       // 玩家个人相册扩展信息, 存储在PlayerExtraInfoTable表里
    // after login mask
    private int afterLoginMask = 0;
    private Session session = null;
    // 匹配
    private PlayerMatchMgr playerMatchMgr;
    private LoadingState loadingState = LoadingState.LS_Invalid;
    private boolean banUpdate = false;
    private long isInProcessQueue = 0;
    private boolean isLoginByPc = false;
    private PlayerKickState kickState = PlayerKickState.None;
    private boolean isUseCache = false;
    private boolean isCheck = false;
    private boolean isReLogin = false;
    private boolean needReloadPlayer = false;
    private int reloadCnt = GSEngine.getSpecInstance().getPlayerService().getTotalReloadCount();

    private long lastFlushPlayerPublicTime = 0;

    private PlayerRoomMgr playerRoomMgr;
    private PlayerBattleMgr playerBattleMgr;
    private CollectionManager playerCollectionMgr;
    private PlayerChatManager playerChatManager;
    private PlayerFeatureOpenMgr featureOpenMgr = null;
    private NotifyMgr ntfMgr;
    private LikeManager likeManager = null;
    private QualifyingManager qualifyingManager = null;
    private PlayerMatchMMRScoreManager matchMMRScoreManager;
    private PlayerMatchWarmRoundManager warmRoundManager;
    private PlayerIDCNetworkInfoManager idcNetworkInfoManager;
    private PlayerAbTestMgr abTestMgr;
    private PlayerRedEnvelopRainActManager redEnvelopRainActMgr;
    private PlayerReturnActivityManager returnActivityManager;
    private PlayerSnsManager playerSnsManager;
    private long lastAchieveDailyOnlineReportMs = 0;
    private long lastHeartbeatEventTs = 0;
    private PlayerDsDBMgr playerDsDBMgr;
    private SeasonFashionBattleDataMgr seasonFashionBattleDataMgr;
    // 奖励补领相关
    private TaskRewardManager taskRewardManager;
    private RewardCompensateMgr rewardCompensateMgr;
    private TestConditionMgr testConditionMgr;
    private PlayerPasswordMgr passwordMgr;
    private BPManager bpManager;
    private final PlayerRewardRetrievalMgr retrievalMgr;
    private final PlayerBirthdayMgr birthdayMgr;
    private final PlayerGameLevelEstimationMgr playerGameLevelEstimationMgr;
    private SeasonReviewMgr seasonReviewMgr;
    private PlayerLiveLinkMgr liveLinkMgr;
    private PlayerRecentPlayMgr recentPlayMgr;
    private PlayerFarmReturningMgr farmReturningMgr;
    private PlayerChaseMgr chaseMgr;
    private PlayerGeneralRedDotMgr generalRedDotPlayerMgr;
    private PlayerAttrDataOperateMgr attrDataOptMgr;
    private PlayerModMgr modMgr;

    private PlayerGameModeReturnManager playerGameModeReturnManager;
    private PlayerLimitExperienceItemMgr playerLimitExperienceItermMgr;
    private PlayerPcWebToPcSimulateMgr playerPcWebToPcSimulateMgr;

    private long tlogCounter;

    private int arenaSelectHeadFrame = 0;
    private LevelDropItemInfo.Builder arenaHeroCoinDropInfo;
    private BattleChatReportInfo battleChatReportInfo = null;       // 玩家对局聊天上报信息
    private long roomModifyTime = 0;

    public Boolean isOptimizedLogin = false;

    // 简单的rpc控频
    private final RpcRateLimitController csRpcRateLimitController;


    // coc玩法
    private final PlayerCocMgr cocMgr;

    // 记录玩家所有协议请求，用于压测用例生成，仅用于测试环境
    public boolean logCsReq = false;
    public Set<String> pkgPrefix = null;

    /**
     * 玩家对象构造函数
     *
     * @param data    数据
     * @param version 版本
     */
    public Player(TcaplusDb.Player data, int version) {
        playerGameLevelEstimationMgr = new PlayerGameLevelEstimationMgr(this);
        int initContextId = LoginStatMgr.startStep(data.getOpenid(), LoginStepEnum.LS_INIT, LoginStepEnum.LS_LOGIN);
        userAttrMgr = new PlayerGameAttrMgr(this, new PlayerGameDBMgr(this));
        userAttrMgr.initUserAttr(data, version);

        if (PropertyFileReader.getRealTimeBooleanItem("player_userattr_db_size_enable", false)) {
            LOGGER.info("enable player db size checker");
            int dbMainAttrSize = data.getUserAttr().getSerializedSize();
            int main_attr_limit = PropertyFileReader.getRealTimeIntItem("player_userattr_db_size_limit_B", 256000);
            if (version != 0) {
                LOGGER.debug("Player[{}|{}|{}]: construct,  dbMainAttrSize size:{}, dbPlayer size:{},  dbattrlimit {}"
                        , getOpenId(), getUid(), version, dbMainAttrSize, data.getSerializedSize(), main_attr_limit);
            }
            if ((dbMainAttrSize > main_attr_limit * 0.8) && isTodayFirstLogin()) {
                LOGGER.warn("openid {} uid {} dbMainAttrSize {} > main_attr_limit {} * 0.8, dbPlayer size {}", getOpenId(), getUid(), dbMainAttrSize, main_attr_limit, data.getSerializedSize());
            }
        }

        isRobot = Player.isRobotOpenid(getOpenId());

        long saveInterval = PropertyFileReader.getRealTimeLongItem("player_backup_interval", 3000L);
        String logIdentification = String.format("Player[%s,%d]", getOpenId(), getUid());
        updateQueueEntry = new CoroUpdateQueueEntry(this, saveInterval, logIdentification);

        onMidnightTime = getUserAttr().getBasicInfo().getLastMidnightRefreshTimeMs();

        quickRewardMgr = new QuickRewardMgr(this);
        playerConditionMgr = new PlayerConditionMgr(this);
        playerOnlineMgr = new PlayerOnlineMgr(this);
        profileManager = new ProfileManager(this);
        activityManager = new ActivityManager(this);
//        activityTimeChecker = new ActivityTimeChecker(this);
        mailManager = new MailMgr(this);
        playerEventManager = new PlayerEventManager(this);
        friendManager = new FriendManager(this);
        bagManager = new BagManager(this);
        intimateManager = new IntimateManager(this);
        mallManager = new MallManager(this);
        mallWishListMgr = new MallWishListMgr(this);
        rankManager = new RankManager(this);
        blackManager = new BlackManager(this);
        taskModule = new TaskModule(this);
        achievementManager = new AchievementManager(this);
        albumMgr = new PlayerAlbumManager(this);
        playerPakPlayMgr = new PlayerPakPlayMgr(this);
//        playerDressUpMgr = new DressUpManager(this);
        //playerChatManager = new PlayerChatManager(this);
        visitorMgr = new VisitorMgr(this);
        interactionMgr = new InteractionMgr(this);
        numericAttrMgr = new PlayerNumericAttrMgr(this);
        ntfMgr = new NotifyMgr(this);
        likeManager = new LikeManager(this);
        limitManager = new LimitManager(this);
        playerSceneMgr = new PlayerSceneMgr(this);
        qualifyingManager = new QualifyingManager(this);
        seasonMgr = new SeasonMgr(this);
        playerLobbyMgr = new PlayerLobbyMgr(this);
        playerSceneInviteMgr = new PlayerSceneInviteMgr(this);
        permitManager = new PermitManager(this);
        permitLevelAwardManager = new PermitLevelAwardManager(this);
        playerUgcManager = new PlayerUgcManager(this);
        raffleManager = new RaffleManager(this);
        playerMoneyMgr = new PlayerMoneyMgr(this);
        squadMgr = new PlayerSquadMgr(this);
        activitySquadMgr = new PlayerActivitySquadMgr(this);
        rechargeMgr = new RechargeMgr(this);
        levelIllustrationMgr = new PlayerLevelIllustrationMgr(this);
        levelRecordMgr = new PlayerLevelRecordMgr(this);
        playerStateMgr = new PlayerStateMgr(this);

        setPlayerMatchMgr(new PlayerMatchMgr(this));
        setPlayerRoomMgr(new PlayerRoomMgr(this));
        clubMgr = new PlayerClubMgr(this);
        messageSlipMgr = new PlayerMessageSlipMgr(this);
        monthCardManager = new MonthCardManager(this);
        specRewardMgr = new PlayerSpecRewardMgr(this);
        playerPersonalityMgr = new PlayerPersonalityMgr(this);
        friendRecommendManager = new FriendRecommendManager(this);
        idipStatisticsManager = new IdipStatisticsManager(this);
        fansFollowManager = new FansFollowManager(this);
        ugcDailyStageMgr = new UgcDailyStageMgr(this);
        ugcStarWorldMgr = new UgcStarWorldMgr(this);
        ugcCoCreateMgr = new UgcCoCreateMgr(this);
        playerCreditScoreMgr = new PlayerCreditScoreMgr(this);
        snsInvitationMgr = new SnsInvitationMgr(this);
        ugcHomeRecommendMgr = new UgcHomeRecommendMgr(this);
        ugcRecommendSetMgr = new UgcRecommendSetMgr(this);
        ugcPlayerGrowUpMgr = new UgcPlayerGrowUpMgr(this);
        ugcCreatorHomePageMgr = new UgcCreatorHomePageMgr(this);
        lbsManager = new LBSManager(this);
        playerArenaMgr = new PlayerArenaMgr(this);
        playerNr3e8Mgr = new PlayerNr3e8Mgr(this);
        playerHokMgr = new PlayerHokMgr(this);
        new PlayerGiftMgr(this);

        setPlayerChatManager(new PlayerChatManager(this));
        setPlayerCollectionMgr(new CollectionManager(this));
        setPlayerBattleMgr(new LetsGoPlayerBattleMgr(this));
        setPlayerFeatureOpenMgr(new PlayerFeatureOpenMgr(this));
//        setQualifyingManager(new LetsGoQualifyingMgr(this));

        setMatchMMRScoreManager(new PlayerMatchMMRScoreManager(this));
        setWarmRoundManager(new PlayerMatchWarmRoundManager(this));
        setIdcNetworkInfoManager(new PlayerIDCNetworkInfoManager(this));
        setAbTestMgr(new PlayerAbTestMgr(this));
        liveLinkMgr = new PlayerLiveLinkMgr(this);
        playerXiaoWoMgr = new PlayerXiaoWoMgr(this);
        playerQqBotMgr = new PlayerQqBotMgr(this);
        playerFarmMgr = new PlayerFarmMgr(this);
        playerHouseMgr = new PlayerHouseMgr(this);
        playerCookMgr = new PlayerCookMgr(this);
        playerCompetitionMgr = new PlayerCompetitionMgr(this);
        partyManager = new com.tencent.wea.playerservice.party.PartyManager(this);
        xiaowoRecom = new XiaowoRecom(this);
        fireworksMgr = new FireworksManager(this);
        layoutMgr = new LayoutMgr(this);
        setRedEnvelopRainActMgr(new PlayerRedEnvelopRainActManager(this));
        idipMgr = new PlayerIdipMgr(this);
        tlogCache = new TlogCache(this);
        outputMgr = new PlayerOutputMgr(this);
        setPlayerDsDBMgr(new PlayerDsDBMgr(this));
        marqueeNoticeMgr = new MarqueeNoticeMgr(this);
        setReturnActivityManager(new PlayerReturnActivityManager(this));
        newYearPilotMgr = new PlayerNewYearPilotMgr(this);
        newActivityPilotMgr = new PlayerNewActivityPilotMgr(this);
        amsItemMgr = new PlayerAmsItemMgr(this);
        recentActivityMgr = new PlayerRecentActivityMgr(this);
        playerWhiteListMgr = new PlayerWhiteListMgr(this);
        playerStreamMgr = new PlayerStreamManager(this);
        playerSnsManager = new PlayerSnsManager(this);
        playerAigcNpcMgr = new PlayerAigcNpcManager(this);

        labelMgr = new PlayerLabelMgr(this);
        concertMgr = new PlayerConcertMgr(this);
        playerGrayTagMgr = new PlayerGrayTagManager(this);
        gameTvMgr = new PlayerGameTvMgr(this);
        recommendMgr = new PlayerRecommendMgr(this);
        seasonFashionBattleDataMgr = new SeasonFashionBattleDataMgr(this);
        hotResResourceManager = new HotResResourceManager(this);
        reputationScoreMgr = new PlayerReputationScoreMgr(this);
        wolfKillMgr = new PlayerWolfKillMgr(this);

        iaaManager = new IAAManager(this);
        taskRewardManager = new TaskRewardManager(this);
        rewardCompensateMgr = new RewardCompensateMgr(this);
        testConditionMgr = new TestConditionMgr(this);
        findRedDotMgr = new FindRedDotMgr(this);
        passwordMgr = new PlayerPasswordMgr(this);
        bpManager = new BPManager(this);

        cupsManager = new CupsManager(this);
        retrievalMgr = new PlayerRewardRetrievalMgr(this);
        birthdayMgr = new PlayerBirthdayMgr(this);
        tradingCardModule = new TradingCardModule(this);
        shareGiftModule = new ShareGiftModule(this);

        ugcMatchLobbyMgr = new UgcMatchLobbyMgr(this);

        csRpcRateLimitController = new RpcRateLimitController(data.getUid());

        lobbyMatchMgr = new PlayerLobbyMatchMgr(this);

        seasonReviewMgr = new SeasonReviewMgr(this);
        recentPlayMgr = new PlayerRecentPlayMgr(this);
        farmReturningMgr = new PlayerFarmReturningMgr(this);
        generalRedDotPlayerMgr = new PlayerGeneralRedDotMgr(this);
        playerGameModeReturnManager = new PlayerGameModeReturnManager(this);
        playerLimitExperienceItermMgr = new PlayerLimitExperienceItemMgr(this);
        playerPcWebToPcSimulateMgr = new PlayerPcWebToPcSimulateMgr(this);
        chaseMgr = new PlayerChaseMgr(this);
        attrDataOptMgr = new PlayerAttrDataOperateMgr(this);
        modMgr = new PlayerModMgr(this);
        tlogCounter = 0L;

        playerStarPMgr = new PlayerStarPMgr(this);

        LoginStatMgr.endStep(data.getOpenid(), LoginStepEnum.LS_INIT, initContextId);
        cocMgr = new PlayerCocMgr(this);
    }

    public static void sendMonitorFlow(Player player, Session.ClientInfo clientInfo, DevMonitorType type) {
//        String name = WhiteList.getOwnerName(player.getOpenId(), "", "");
//        if (name != "") {
//            TlogDevMonitorFlow.hire(player)
//                    .setMonitorType(type.getNumber())
//                    .setSoftware(clientInfo.systemSoftware)
//                    .setVersion(clientInfo.clientVersion)
//                    .setRtx(name)
//                    .logToTlogd();
//        }
    }

    // 更改自己在房间中的状态(个人在房间中的状态,非整个房间的状态)
    @Deprecated
    public static int updatePrivateRoomStatus(Player player, int status) {
        if (null == player) {
            return RoomSvrErrorCode.ROOMSVR_ERRORCODE_UNKNOWN_VALUE;
        }
        RoomStatus preStatus = player.getUserAttrMgr().getUserAttr().getRoomInfo().getStatus(); // @Deprecated
        player.getUserAttr().getRoomInfo().setStatus(RoomStatus.forNumber(PlayerRoomMgr.getState2RoomStatus(status)));
        LOGGER.debug("updatePrivateRoomStatus uid:{}xxxxxxxxxxxxxxxxxxx1 transfer {}",
                player.getUid(), player.getUserAttr().getRoomInfo().getStatus()); // @Deprecated
        /*player.getUserAttr().getPlayerPublicLiveStatus()
                .setRoomStatus(RoomStatus.forNumber(PlayerRoomMgr.getState2RoomStatus(status)));*/
        LOGGER.debug("updatePrivateRoomStatus uid:{}xxxxxxxxxxxxxxxxxxx2 transfer {}",
                player.getUid(), player.getUserAttr().getRoomInfo().getStatus()); // @Deprecated
        //PlayerTcaplus.flushTcaplusPlayerPublic(player.getUid(), player.getUserAttr().getPlayerPublicProfileInfo(), null);
        LOGGER.debug("updatePrivateRoomStatus uid:{}xxxxxxxxxxxxxxxxxxx3 transfer {}",
                player.getUid(), player.getUserAttr().getRoomInfo().getStatus()); // @Deprecated
        RoomStatus currStatus = player.getUserAttr().getRoomInfo().getStatus(); // @Deprecated
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                    "updatePrivateRoomStatus uid:{} from {} to {}, origin:{}, getState2RoomStatus:{}, transfer {} getStackTrace {}",
                    player.getUid(), preStatus,
                    currStatus, status, PlayerRoomMgr.getState2RoomStatus(status),
                    RoomStatus.forNumber(PlayerRoomMgr.getState2RoomStatus(status)),
                    FunctionUtil.getStackTrace());
        }
        return RoomSvrErrorCode.ROOMSVR_ERRORCODE_SUCC_VALUE;
    }

    // 在收到room状态变更通知的时候同步更改自己在房间中的状态(个人在房间中的状态,非整个房间的状态)
    @Deprecated
    public static int updatePrivateRoomStatusOnMemberModify(Player player,
                                                            SsGamesvr.RpcRoomMemberModifyNtfReq memberModifyNtfReq) {
        if (null == player) {
            return RoomSvrErrorCode.ROOMSVR_ERRORCODE_UNKNOWN_VALUE;
        }
        if (memberModifyNtfReq.getRoomID() != player.getCurrentRoomId()) { // @Deprecated
            return NKErrorCode.OK.getValue();
        }
        int status = memberModifyNtfReq.getRoomState();
        if (memberModifyNtfReq.getMemberNum() < 2 && status == StateType.ST_Room_Wait_VALUE) {
            LOGGER.debug("updatePrivateRoomStatusOnMemberModify 1  uid:{} status {} getMemberListCount {}",
                    player.getUid(), status, memberModifyNtfReq.getMemberNum());
            player.getUserAttr().getRoomInfo().setStatus(RoomStatus.RS_NORMAL);
            //player.getUserAttr().getPlayerPublicLiveStatus().setRoomStatus(RoomStatus.RS_NORMAL);
            //player.flushPlayerPublicProfile();
        } else {
            LOGGER.debug("updatePrivateRoomStatusOnMemberModify 2 uid:{} status {} getMemberListCount {}",
                    player.getUid(), status, memberModifyNtfReq.getMemberNum());
            for (MemberBaseInfo memberBaseInfo : memberModifyNtfReq.getMemberListList()) {
                if (player.getUid() == memberBaseInfo.getUid()) {
                    updatePrivateRoomStatus(player, 0);
                    break;
                }
            }
        }
        // 尝试进入队长场景
        //player.getPlayerSceneMgr().addRoomScene(memberModifyNtfReq.getSceneId(), memberModifyNtfReq.getMapId());

        return RoomSvrErrorCode.ROOMSVR_ERRORCODE_SUCC_VALUE;
    }

    /**
     * 发送登录流水
     *
     * @param player    玩家
     * @param session   会话
     * @param errorcode 错误代码
     */
    public static void sendLoginFlow(Player player, Session session, int errorcode, boolean isRegister) {
        //登录流水

        Session.ClientInfo clientInfo = session.getClientInfo();
        if (errorcode == 0 && player != null) {
            TlogFlowMgr.sendModPlayerLoginFlow(player, clientInfo, session.getIpAddr(),
                    TlogMacros.LOGIN_FLOW_TYPE.LOGIN_CS_TRIGGER);
            TlogFlowMgr.sendSecLoginFlow(player);
            sendMonitorFlow(player, clientInfo, DevMonitorType.DMT_Login);
        } else {
            TlogFlowMgr.sendPlayerLoginFailFlow(player, session, errorcode, isRegister);
        }
    }

    /**
     * 发送ASAIadinfo流水
     */
    public static void sendASAIadInfoFlow(Player player, CsPlayer.AsaIadInfo asaIadInfo) {
        // ASAIad流水
        TlogFlowMgr.sendPlayerASAIadInfoFlow(player, asaIadInfo);
    }

    /**
     * 发送注册流水
     *
     * @param player    玩家
     * @param session   会话
     * @param errorcode 错误代码
     */
    public static void sendRegisterFlow(Player player, Session session, int errorcode) {
        Session.ClientInfo clientInfo = session.getClientInfo();
        if (errorcode == 0 && player != null) {
            sendMonitorFlow(player, clientInfo, DevMonitorType.DMT_Login);
        }
        // TODO 注册失败流水
        /*else {
            TlogRequiredFields.Builder required = TlogRequiredFields.newBuilder();
            required.setVopenid(session.getOpenid())
                    .setVRoleID(session.getUid())
                    .setPlatID(clientInfo.platId);
            TlogPlayerRegisterDetail.hire(required)
                    .setErrorCode(errorcode)
                    .setDeviceId(clientInfo.getRealDeviceId())
                    .setVClientIP(session.getIpAddr())
                    .setTelecomOper(clientInfo.telecomOper)
                    .setRegChannel(clientInfo.channel)
                    .setNetwork(clientInfo.netWork)
                    .setMemory(clientInfo.memory)
                    //.setRecommendZoneIds(clientInfo.recommendZoneIds.toString())
                    .setSystemHardware(clientInfo.systemHardware)
                    .setSystemSoftware(clientInfo.systemSoftware)
                    .setLanguage(String.valueOf(LanguageType.LANGUAGE_TYPE_ZH_CN_VALUE))
                    .setClientVersion(clientInfo.clientVersion)
                    .logToTlogd();
        }*/
    }

    public static AttrProfiler getAttrProfiler() {
        return attrProfiler.get();
    }

    public static boolean isLoginOtherGameSvr(int onlineTableSvrId, long uid, long kickUid, long lastKeepAlive)
            throws RpcException, NKTimeoutException {
        if (onlineTableSvrId != 0 && (uid != kickUid || Framework.getInstance().getServerId() != onlineTableSvrId)) {
            // 通知踢线
            rpcGameSvrKickPlayer(onlineTableSvrId, kickUid);
            return true;
        }
        return false;
    }

    /**
     * @Description: 通知gamesvr踢线
     * @Author: digoldzhang
     * @Date: 2021/8/25
     */
    public static void rpcGameSvrKickPlayer(int svrId, long uid) {
//        去掉提前检查实例，防止实例检查错误
//        if (!TbusppInstance.isInstanceOnline(svrId)) {
//            LOGGER.warn("target svr offline:{}, uid:{}", svrId, uid);
//            return;
//        }

        SsGamesvr.RpcKickLoginPlayerReq.Builder rpcKickPlayerReq = SsGamesvr.RpcKickLoginPlayerReq.newBuilder()
                .setServerId(svrId)
                .setPlayerUid(uid);

        boolean isNeedCheckOnLine = false;
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("rpcGameSvrKickPlayer uid {} svrId {}", uid, TbusppUtil.ntoa(svrId));
            }
            RpcResult<SsGamesvr.RpcKickLoginPlayerRes.Builder> rpcResult = GameService.get()
                    .rpcKickLoginPlayer(rpcKickPlayerReq);

            if (!rpcResult.isOK()) {
                LOGGER.warn("RpcKickLoginPlayerRes {} error-{},zoneId: {}", uid, rpcResult.getRet(), svrId);
                isNeedCheckOnLine = true;
            } else if (rpcResult.getData().getResult() != 0 && rpcResult.getData().getResult() != -1) {
                LOGGER.error("kickPlayer{} error-{},zoneId: {}", uid, rpcResult.getData().getResult(), svrId);
                NKErrorCode.KickPlayerFail.throwError("kickPlayer error");
            }

        } catch (RpcException | NKTimeoutException e) {
            isNeedCheckOnLine = true;
            LOGGER.error("kickPlayer error, uid:{}  errCode:{}", uid, e.getEnumErrCode());
        }

        if (isNeedCheckOnLine) {
            if (TbusppInstance.isInstanceOnline(svrId)) {
                LOGGER.error("kickPlayer{} fail instance online ,zoneId: {}", uid, svrId);
                NKErrorCode.KickPlayerFail.throwError("game server is online{}", TbusppUtil.ntoa(svrId));
            }
        }
    }

    public static boolean isRobotOpenid(String openid) {
        return openid.startsWith("ROBOT_PRESSTEST_DEFAULT");
    }

    static public long getPlayVersion(int matchTypeId, MemberBaseInfo member) {
        String featureName = MatchTypeData.getInstance().getPlayName(matchTypeId);
        return GamePlay.getInstance().getFeatureField(featureName
                , member.getClientVersion(), true, member.getPlayInfoList()
                , gp -> gp.getFeatureType(), gp -> gp.getPakVersion());
    }

    public PlayerLabelMgr getLabelMgr() {
        return this.labelMgr;
    }

    private RelationManager getRelationManager(RelationTypeEnum type) {
        RelationManager relationManager;
        switch (type) {
            case RTE_FRIEND_RELATION:
                relationManager = friendManager;
                break;
            case RTE_INTIMATE_RELATION:
                relationManager = intimateManager;
                break;
            case RTE_BLACK_RELATION:
                relationManager = blackManager;
                break;
            default:
                return null;
        }
        return relationManager;
    }

    public void handlePlayerRelationApplySelfInsert(long friendUid, RelationTypeEnum type, RelationApplyType opType,
                                                    RelationMsgExtraInfo extraInfo, long time) {
        RelationManager relationManager = getRelationManager(type);
        if (relationManager == null) {
            NKErrorCode.RelationTypeNotExist.throwError("RelationType {} not exist", type);
            return;
        }
        DBApplyRelation dbRelation = new DBApplyRelation()
                .setUid(friendUid)
                .setApplyTime(time)
                .setApplyType(opType);
        relationManager.dbRelationInfo.getSendApplyInfo().put(friendUid, dbRelation);
    }

    public void handlePlayerRelationApply(long friendUid, RelationTypeEnum type, RelationApplyType opType,
                                          RelationMsgExtraInfo extraInfo, long time) {
        RelationManager relationManager = getRelationManager(type);
        if (relationManager == null) {
            NKErrorCode.RelationTypeNotExist.throwError("RelationType {} not exist", type);
            return;
        }

        NKErrorCode errorCode = NKErrorCode.OK;
        switch (opType) {
            case RAT_Add:
                errorCode = relationManager.handleAddRelationMsg(friendUid, extraInfo, time);
                break;
            case RAT_Remove:
                errorCode = relationManager.handleRemoveRelationMsg(friendUid, time);
                break;
            case RAT_TransferGameFriend:
                relationManager.handleTranslateToGameFriendMsg(friendUid);
                break;
            case RAT_Change:
                errorCode = intimateManager.handleChangeRelationMsg(friendUid, extraInfo, time);
                break;
            default:
                NKErrorCode.RelationTypeNotExist.throwError("RelationType {} opType {} not exist", type, opType);
        }
        errorCode.throwErrorIfNotOk("handle relation apply error: {}", errorCode.toString());
    }

    public void handlePlayerRelationAgree(long uid, RelationTypeEnum type, boolean agree, long time) {
        RelationManager relationManager = getRelationManager(type);
        if (relationManager == null) {
            NKErrorCode.RelationTypeNotExist.throwError("RelationType {} not exist", type);
            return;
        }

        if (agree) {
            relationManager.handleAgreeRelationMsg(uid, time);
        } else {
            relationManager.handleDenyRelationApplyMsg(uid);
        }
    }

    public void handlePlayerRelationRemove(long targetUid) {

        try {
            // 关系数据移除处理
            friendManager.removeRelation(targetUid);
        } catch (Exception ex) {
            LOGGER.error("removeRelation failed, uid:{}, targetUid:{}, ", this.getUid(), targetUid, ex);
        }

        try {
            // 黑名单数据移除处理
            blackManager.handleRemoveRelationMsg(targetUid, DateUtils.currentTimeMillis());
            blackManager.removeRelation(targetUid);
        } catch (Exception ex) {
            LOGGER.error("handlePlayerRelationRemove failed, uid:{}, targetUid:{}, ", this.getUid(), targetUid, ex);
        }

        LOGGER.debug("relation remove end, uid:{}, targetUid:{}", getUid(), targetUid);
    }

    public void loadPlayerRelation() {
        List<TcaplusDb.RelationTable> relations = RelationTableDao.getPlayerRelation(getUid());
        LOGGER.debug("loadPlayerRelation, {}, {}", getUid(), relations.size());
        relations.forEach(relation -> {
            switch (relation.getType()) {
                case RelationTypeEnum.RTE_FRIEND_RELATION_VALUE:
                    friendManager.updateRelationFromDB(relation);
                    return;
                case RelationTypeEnum.RTE_INTIMATE_RELATION_VALUE:
                    intimateManager.updateRelationFromDB(relation);
                    return;
                case RelationTypeEnum.RTE_BLACK_RELATION_VALUE:
                    blackManager.updateRelationFromDB(relation);
                    return;
                case RelationTypeEnum.RTE_BE_BLACK_RELATION_VALUE:
                    blackManager.getBeBlackInfo().updateRelationFromDB(relation);
                    return;
                default:
                    LOGGER.error("RelationType error:{}", relation.toString());
            }
        });
        intimateManager.checkApplyInfo();
        intimateManager.flushPublicProfileIntimateRelationInfo();

        CurrentExecutorUtil.runJobNoException("cacheFriendInteractHistory", () -> {
            getFriendManager().cacheFriendInteractHistory();
            return null;
        });
    }

    private void loadPlayerCommodityBuyTimesSync() {
        List<TcaplusDbWrapper.MallBuyTimesTable> playerCommodityBuyTimes = PlayerCommodityBuyTimesSyncDao.getPlayerCommodityBuyTimes(getUid());
        getMallManager().loadMallBuyRecord(playerCommodityBuyTimes);
    }

    private void loadFarmBeBlockedData() {
        getPlayerFarmMgr().loadFarmBeBlockedData();
    }

    public long getRegisterTime() {
        return getUserAttr().getPlayerPublicBasicInfo().getRegisterTimeMs();
    }

    public boolean regFromMiniGame() {
        var regChannel = getUserAttr().getPlayerPublicBasicInfo().getRegChannelDis();
        return GuideAppConfigData.getInstance().registerFromMiniGame(regChannel);
    }

    public long getLoginTime() {
        return getUserAttr().getPlayerPublicBasicInfo().getLoginTimeMs();
    }

    public PlayerChatManager getPlayerChatManager() {
        return playerChatManager;
    }

    public void setPlayerChatManager(PlayerChatManager playerChatManager) {
        this.playerChatManager = playerChatManager;
    }

    // 好友
    public FriendManager getFriendManager() {
        return friendManager;
    }

    // 亲密关系
    public IntimateManager getIntimateManager() {
        return intimateManager;
    }

    // 背包管理
    public BagManager getBagManager() {
        return bagManager;
    }

    public ItemManager getItemManager() {
        return getBagManager().getItemManager();
    }

    public ItemEquipMgr getItemEquipManager() {
        return getBagManager().getItemEquipMgr();
    }

    public TaskModule getTaskManager() {
        return taskModule;
    }

    public AchievementManager getAchievementManager() {
        return achievementManager;
    }

    public PlayerAlbumManager getAlbumManager() {
        return albumMgr;
    }

    //匹配
    public PlayerMatchMgr getPlayerMatchMgr() {
        return playerMatchMgr;
    }

    public void setPlayerMatchMgr(PlayerMatchMgr mgr) {
        if (playerMatchMgr == null) {
            playerMatchMgr = mgr;
        } else {
            LOGGER.info("call setPlayerMatchMgr more than once");
        }
    }

    public PlayerRoomMgr getPlayerRoomMgr() {
        return playerRoomMgr;
    }

    public void setPlayerRoomMgr(PlayerRoomMgr mgr) {
        if (playerRoomMgr == null) {
            playerRoomMgr = mgr;
        } else {
            LOGGER.info("call setPlayerRoomMgr more than once");
        }
    }

    public VisitorMgr getVisitorMgr() {
        return visitorMgr;
    }

    public InteractionMgr getInteractionMgr() {
        return interactionMgr;
    }

    public PlayerNumericAttrMgr getNumericAttrMgr() {
        return numericAttrMgr;
    }

    // 统计数据
    public PlayerEventManager getPlayerEventManager() {
        return playerEventManager;
    }

    //包体玩法记录管理
    public PlayerPakPlayMgr getPlayerPakPlayMgr() {
        return playerPakPlayMgr;
    }

    // 商城
    public MallManager getMallManager() {
        return mallManager;
    }

    // 商城心愿单
    public MallWishListMgr getMallWishListMgr() {
        return mallWishListMgr;
    }

    // 排行榜
    public RankManager getRankManager() {
        return rankManager;
    }

//    public ActivityTimeChecker getActivityTimeChecker() {
//        return activityTimeChecker;
//    }

    public MailMgr getMailManager() {
        return mailManager;
    }

    // 黑名单
    public BlackManager getBlackManager() {
        return blackManager;
    }

    public ActivityManager getActivityManager() {
        return activityManager;
    }

    //public DressUpManager getDressUpMgr() {
    //    return playerDressUpMgr;
    //}

    public PlayerBattleMgr getPlayerBattleMgr() {
        return playerBattleMgr;
    }

    public void setPlayerBattleMgr(PlayerBattleMgr mgr) {
        if (playerBattleMgr == null) {
            playerBattleMgr = mgr;
        } else {
            LOGGER.error("call setPlayerBattleMgr more than once");
        }
    }

    public void setPlayerCollectionMgr(CollectionManager mgr) {
        if (playerCollectionMgr == null) {
            playerCollectionMgr = mgr;
        } else {
            LOGGER.error("call setPlayerCollectionMgr more than once");
        }
    }

    public CollectionManager getCollectionMgr() {
        return playerCollectionMgr;
    }

    public void setPlayerFeatureOpenMgr(PlayerFeatureOpenMgr mgr) {
        if (featureOpenMgr == null) {
            featureOpenMgr = mgr;
        } else {
            LOGGER.info("call setPlayerFeatureOpenMgr more than once");
        }
    }

    public PlayerFeatureOpenMgr getFeatureOpenMgr() {
        return featureOpenMgr;
    }

    public PlayerConditionMgr getConditionMgr() {
        return playerConditionMgr;
    }

    public ProfileManager getProfileManager() {
        return profileManager;
    }

    public NotifyMgr getNtfMgr() {
        return this.ntfMgr;
    }

    public void setNtfMgr(NotifyMgr ntfMgr) {
        this.ntfMgr = ntfMgr;
    }

    public LikeManager getLikeManager() {
        return likeManager;
    }

    public QualifyingManager getQualifyingManager() {
        return qualifyingManager;
    }

    public void setQualifyingManager(QualifyingManager mgr) {
        if (qualifyingManager == null) {
            qualifyingManager = mgr;
        } else {
            LOGGER.error("call setQualifyingManager more than once");
        }
    }

    public PlayerMessageSlipMgr getPlayerMessageSlipMgr() {
        return messageSlipMgr;
    }

    public PlayerMoneyMgr getPlayerMoneyMgr() {
        return playerMoneyMgr;
    }

    public RechargeMgr getRechargeMgr() {
        return rechargeMgr;
    }

    public LimitManager getLimitManager() {
        return limitManager;
    }

    public PlayerSceneMgr getPlayerSceneMgr() {
        return playerSceneMgr;
    }

    public SeasonMgr getSeasonMgr() {
        return seasonMgr;
    }

    public PlayerLobbyMgr getPlayerLobbyMgr() {
        return playerLobbyMgr;
    }

    public PlayerSceneInviteMgr getPlayerSceneInviteMgr() {
        return playerSceneInviteMgr;
    }

    public PermitManager getPermitManager() {
        return permitManager;
    }

    public RaffleManager getRaffleManager() {
        return raffleManager;
    }

    public com.tencent.wea.playerservice.party.PartyManager getPartyManager() {
        return partyManager;
    }

    public XiaowoRecom getXiaowoRecom() {
        return xiaowoRecom;
    }

    public PlayerUgcManager getPlayerUgcManager() {
        return playerUgcManager;
    }

    public LayoutMgr getLayoutMgr() {
        return layoutMgr;
    }

    public PlayerClubMgr getClubMgr() {
        return clubMgr;
    }

    public PlayerOnlineMgr getPlayerOnlineMgr() {
        return playerOnlineMgr;
    }

    public PlayerSquadMgr getSquadMgr() {
        return squadMgr;
    }

    public PlayerActivitySquadMgr getActivitySquadMgr() {
        return activitySquadMgr;
    }

    public PlayerStateMgr getPlayerStateMgr() {
        return playerStateMgr;
    }

    public PlayerLevelIllustrationMgr getLevelIllustrationMgr() {
        return levelIllustrationMgr;
    }

    public PlayerLevelRecordMgr getLevelRecordMgr() {
        return levelRecordMgr;
    }

    public MonthCardManager getMonthCardManager() {
        return monthCardManager;
    }

    public PlayerPersonalityMgr getPlayerPersonalityMgr() {
        return playerPersonalityMgr;
    }

    public PlayerSpecRewardMgr getSpecRewardMgr() {
        return specRewardMgr;
    }

    public QuickRewardMgr getQuickRewardMgr() {
        return quickRewardMgr;
    }

    public PlayerMatchMMRScoreManager getMatchMMRScoreManager() {
        return matchMMRScoreManager;
    }

    public void setMatchMMRScoreManager(PlayerMatchMMRScoreManager mgr) {
        if (null == matchMMRScoreManager) {
            matchMMRScoreManager = mgr;
        } else {
            LOGGER.info("call setMatchMMRScoreManager more than once");
        }
    }

    public SeasonFashionBattleDataMgr getSeasonFashionBattleDataMgr() {
        return seasonFashionBattleDataMgr;
    }

    public PlayerArenaMgr getArenaMgr() {
        return playerArenaMgr;
    }

    public PlayerNr3e8Mgr getPlayerNr3e8Mgr() {
        return playerNr3e8Mgr;
    }

    public PlayerHokMgr getHokMgr() {
        return playerHokMgr;
    }

    public PlayerStreamManager getPlayerStreamMgr() {
        return playerStreamMgr;
    }

    public PlayerAigcNpcManager getPlayerAigcNpcMgr() {
        return playerAigcNpcMgr;
    }

    public PlayerSnsManager getPlayerSnsManager() {
        return playerSnsManager;
    }

    public PlayerConcertMgr getConcertMgr() {
        return concertMgr;
    }

    public PlayerRedEnvelopRainActManager getRedEnvelopRainActMgr() {
        return redEnvelopRainActMgr;
    }

    public void setRedEnvelopRainActMgr(PlayerRedEnvelopRainActManager mgr) {
        if (null == redEnvelopRainActMgr) {
            redEnvelopRainActMgr = mgr;
        } else {
            LOGGER.info("call setLuckyMoneyRainActMgr more than once");
        }
    }

    public FriendRecommendManager getFriendRecommendManager() {
        return friendRecommendManager;
    }

    public FansFollowManager getFansFollowManager() {
        return fansFollowManager;
    }

    public UgcDailyStageMgr getUgcDailyStageMgr() {
        return ugcDailyStageMgr;
    }

    public UgcStarWorldMgr getUgcStarWorldMgr() {
        return ugcStarWorldMgr;
    }

    public UgcCoCreateMgr getUgcCoCreateMgr() {
        return ugcCoCreateMgr;
    }

    public UgcHomeRecommendMgr getUgcHomeRecommendMgr() {
        return ugcHomeRecommendMgr;
    }

    public UgcRecommendSetMgr getUgcRecommendSetMgr() {
        return ugcRecommendSetMgr;
    }

    public UgcPlayerGrowUpMgr getUgcPlayerGrowUpMgr() {
        return ugcPlayerGrowUpMgr;
    }

    public UgcCreatorHomePageMgr getUgcCreatorHomePageMgr() {
        return ugcCreatorHomePageMgr;
    }

    public FireworksManager getFireworksMgr() {
        return fireworksMgr;
    }

    public SnsInvitationMgr getSnsInvitationMgr() {
        return snsInvitationMgr;
    }

    public PlayerMatchWarmRoundManager getWarmRoundManager() {
        return warmRoundManager;
    }

    public void setWarmRoundManager(PlayerMatchWarmRoundManager mgr) {
        if (null == warmRoundManager) {
            warmRoundManager = mgr;
        } else {
            LOGGER.info("call setWarmRoundManager more than once");
        }
    }

    public LBSManager getLbsManager() {
        return lbsManager;
    }

    public PlayerAmsItemMgr getAmsItemMgr() {
        return amsItemMgr;
    }

    public PlayerXiaoWoMgr getPlayerXiaoWoMgr() {
        return playerXiaoWoMgr;
    }

    public PlayerFarmMgr getPlayerFarmMgr() {
        return playerFarmMgr;
    }

    public PlayerStarPMgr getPlayerStarPMgr() {
        return playerStarPMgr;
    }

    public PlayerHouseMgr getPlayerHouseMgr() {
        return playerHouseMgr;
    }

    public PlayerCookMgr getPlayerCookMgr() {
        return playerCookMgr;
    }

    public PlayerCompetitionMgr getPlayerCompetitionMgr() {
        return playerCompetitionMgr;
    }

    public PlayerIDCNetworkInfoManager getIdcNetworkInfoManager() {
        return idcNetworkInfoManager;
    }

    public void setIdcNetworkInfoManager(PlayerIDCNetworkInfoManager mgr) {
        if (null == idcNetworkInfoManager) {
            idcNetworkInfoManager = mgr;
        } else {
            LOGGER.info("call setIdcNetworkInfoManager more than once");
        }
    }

    public PlayerAbTestMgr getAbTestMgr() {
        return abTestMgr;
    }

    public void setAbTestMgr(PlayerAbTestMgr abTestMgr) {
        this.abTestMgr = abTestMgr;
    }

    public PlayerLiveLinkMgr getLiveLinkMgr() {
        return liveLinkMgr;
    }

    public PlayerOutputMgr getOutputMgr() {
        return outputMgr;
    }

    public PlayerDsDBMgr getPlayerDsDBMgr() {
        return playerDsDBMgr;
    }

    public void setPlayerDsDBMgr(PlayerDsDBMgr playerDsDBMgr) {
        this.playerDsDBMgr = playerDsDBMgr;
    }

    public IdipStatisticsManager getIdipStatisticsManager() {
        return idipStatisticsManager;
    }

    public PlayerReturnActivityManager getReturnActivityManager() {
        return returnActivityManager;
    }

    public void setReturnActivityManager(PlayerReturnActivityManager mgr) {
        if (null == returnActivityManager) {
            returnActivityManager = mgr;
        } else {
            LOGGER.info("call setReturnActivityManager more than twice");
        }
    }

    public PlayerQqBotMgr getQqRobotManager() {
        return playerQqBotMgr;
    }

    public HotResResourceManager getHotResResourceManager() {
        return hotResResourceManager;
    }

    public UgcMatchLobbyMgr getUgcMatchLobbyMgr() {
        return ugcMatchLobbyMgr;
    }

    public PlayerLobbyMatchMgr getLobbyMatchMgr() {
        return lobbyMatchMgr;
    }

    public PlayerIdipMgr getIdipMgr() {
        return idipMgr;
    }

    public MarqueeNoticeMgr getMarqueeNoticeMgr() {
        return marqueeNoticeMgr;
    }

    public PlayerCreditScoreMgr getPlayerCreditScoreMgr() {
        return playerCreditScoreMgr;
    }

    public PlayerNewYearPilotMgr getNewYearPilotMgr() {
        return newYearPilotMgr;
    }

    public PlayerNewActivityPilotMgr getNewActivityPilotMgr() {
        return newActivityPilotMgr;
    }

    public PlayerRecentActivityMgr getRecentActivityMgr() {
        return recentActivityMgr;
    }

    public PlayerGrayTagManager getPlayerGrayTagMgr() {
        return playerGrayTagMgr;
    }

    public PlayerGameTvMgr getGameTvMgr() {
        return gameTvMgr;
    }

    public PlayerRecommendMgr getRecommendMgr() {
        return recommendMgr;
    }

    public PlayerReputationScoreMgr getReputationScoreMgr() {
        return reputationScoreMgr;
    }

    public PlayerWolfKillMgr getWolfKillMgr() {
        return wolfKillMgr;
    }

    public IAAManager getIaaManager() {
        return iaaManager;
    }

    public TaskRewardManager getTaskRewardManager() {
        return taskRewardManager;
    }

    public RewardCompensateMgr getRewardCompensateMgr() {
        return rewardCompensateMgr;
    }

    public TestConditionMgr getTestConditionMgr() {
        return testConditionMgr;
    }

    public FindRedDotMgr getFindRedDotMgr() {
        return findRedDotMgr;
    }

    public PlayerPasswordMgr getPasswordMgr() {
        return passwordMgr;
    }

    public BPManager getBpManager() {
        return bpManager;
    }

    public CupsManager getCupsManager() {
        return cupsManager;
    }

    public PlayerRewardRetrievalMgr getRetrievalMgr() {
        return retrievalMgr;
    }

    public PlayerBirthdayMgr getBirthdayMgr() {
        return birthdayMgr;
    }

    public TradingCardModule getTradingCardModule() {
        return tradingCardModule;
    }

    public ShareGiftModule getShareGiftModule() {
        return shareGiftModule;
    }

    public PlayerGameModeReturnManager getPlayerGameModeReturnManager() {
        return playerGameModeReturnManager;
    }

    public PlayerLimitExperienceItemMgr getPlayerLimitExperienceItermMgr() {
        return playerLimitExperienceItermMgr;
    }

    public boolean isRobot() {
        return isRobot;
    }

    public boolean isLoginByPc() {
        return isLoginByPc;
    }

    public void setLoginByPc(boolean loginByPc) {
        isLoginByPc = loginByPc;
    }

    public boolean isUseCache() {
        return isUseCache;
    }

    public void setUseCache(boolean useCache) {
        isUseCache = useCache;
    }

    public boolean isReLogin() {
        return isReLogin;
    }

    public void setReLogin(boolean reLogin) {
        isReLogin = reLogin;
    }

    public boolean isKickPlayer() {
        return kickState != PlayerKickState.None;
    }

    private void setPlayerKicked() {
        kickState = PlayerKickState.Kicked;
    }

    private void setPlayerKicking() {
        kickState = PlayerKickState.Kicking;
    }

    public boolean isNeedReloadPlayer() {
        return needReloadPlayer;
    }

    public void setNeedReloadPlayer(boolean needReloadPlayer) {
        this.needReloadPlayer = needReloadPlayer;
    }

    public void banUpdate() {
        LOGGER.warn("banupdate {} {}", getOpenId(), getUid());
        banUpdate = true;
    }

    /**
     * 是否已经完成加载
     *
     * @return
     */
    public boolean isLoaded() {
        return loadingState == LoadingState.LS_Loaded;
    }

    private void setLoaded() {
        setLoadingState(LoadingState.LS_Loaded);
    }

    public boolean isNotUse() {
        return loadingState == LoadingState.LS_NotUse;
    }

    public boolean isCheck() {
        return isCheck;
    }

    public void setCheck(boolean isCheck) {
        LOGGER.info("set isCheck:{}", isCheck);
        this.isCheck = isCheck;
    }

    private void setNotUse() {
        setLoadingState(LoadingState.LS_NotUse);
        PlayerRefMgr.getInstance().addPlayerNotUse(getUid());
    }

    public void changeToLoaded() {
        LOGGER.info("changeToLoaded, before:{}", loadingState);
        setLoaded();
        setCheck(false);
//        PlayerRefMgr.getInstance().removePlayerNotUse(getUid());
        LOGGER.info("changeToLoaded, after:{}", loadingState);
    }

    private void setLoading() {
        setLoadingState(LoadingState.LS_Loading);
    }

    private void setRegistering() {
        setLoadingState(LoadingState.LS_Registering);
    }

    public void setInvalidate() {
        setLoadingState(LoadingState.LS_Invalid);
    }

    public void setRemoving() {
        setLoadingState(LoadingState.LS_Removing);
    }

    public boolean isRemoving() {
        return loadingState == LoadingState.LS_Removing;
    }

    public LoadingState getLoadingState() {
        return loadingState;
    }

    private void setLoadingState(LoadingState state) {
        LOGGER.warn("setLoadingState {}-{}", loadingState, state);
        if (loadingState == LoadingState.LS_NotUse) {
            PlayerRefMgr.getInstance().removePlayerNotUse(getUid());
        }
        loadingState = state;
    }

    //最多20秒
    boolean getInProcessQueue() {
//        if (Framework.currentTimeMillis() >= isInProcessQueue
//                && (Framework.currentTimeMillis() - isInProcessQueue) < 20000) {
//            return true;
//        } else {

        if (isInProcessQueue > 0) {
            Monitor.getInstance().add.total(MonitorId.attr_player_in_process_queue_gt_20, 1);
            return true;
        }
        return false;

    }

    void setInProcessQueue(boolean isIn) {
        if (isIn) {
            isInProcessQueue = Framework.currentTimeMillis();
        } else {
            isInProcessQueue = 0;
        }
    }

    public void recordProcess() {
        PlayerRefMgr.getInstance().recordPlayerProcess(this);
    }

    /*
     * @return boolean false = fini true = needretry
     * */
    public boolean processPlayer() {
        processTime = Framework.currentTimeMillis();
        if (getLoadingState() == LoadingState.LS_Invalid || getLoadingState() == LoadingState.LS_NotUse) {
            LOGGER.error("player-{} loading stat is {} ,but still in process queue", this, getLoadingState());
            return false;
        }
        if (session == null) {
            LOGGER.warn("player-{} session is null", getUid());
            return false;
        }

        refreshCheck();
        reloadCheck();

        updateDb(false);

        this.getMailManager().recvGlobalMailsPeriodicallyJob();

        return false;
    }

    /**
     * 设置心跳
     *
     * @param isfull isfull
     * @return 增加了多少在线时长
     */
    public long playerSetHeartBeat(boolean isfull) {
        BasicInfo basicInfo = getUserAttr().getBasicInfo();
        long lastHb = basicInfo.getHeartBeatTimeMs();
        long now = Framework.currentTimeMillis();
        LOGGER.debug("setLastHeartBeatTimeMs: {}, setHeartBeatTimeMs: {}", lastHb, now);
        basicInfo.setLastHeartBeatTimeMs(lastHb);
        basicInfo.setHeartBeatTimeMs(now);
        getPlayerOnlineMgr().onHeartBeat();
//
        // 累加在线时长
        long addOnlineMillis = now - lastHb;
        if (lastHb >= basicInfo.getLoginTimeMs() && now > lastHb) {
            basicInfo.addDailyOnlineTimeMs(addOnlineMillis);
            if (getPlayerFarmMgr().getCurrentFarmId() > 0) {
                basicInfo.addFarmDailyOnlineTimeMs(addOnlineMillis);
            }
            getUserAttr().getPlayerPublicGameData().addTotalOnlineTime(addOnlineMillis);
            // update
            PlayerStatCluster.getInstance().addDailyOnlineTime(getUid()
                    , getUserAttr().getPlayerPublicGameData().getStatCluster(), addOnlineMillis);
        }
        if (now - lastAchieveDailyOnlineReportMs >= 5 * 60 * 1000 /* 5 min */) {
            lastAchieveDailyOnlineReportMs = now;
            sendAchievementReport(new AchieveBody(getUid(), getPlatId())
                    .AddAchievementParam(AchiParamType.APT_TodayTotalGameDuration,
                            basicInfo.getDailyOnlineTimeMs() / 1000, 1, 0));
        }

//
//        if (isfull || basicInfo.getLastHeartBeatTimeMs() <= MiscConf.getInstance().getReloadTime()) {
//
//            sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERMISCNTF, MiscConf.getInstance().getMiscConfNtf());
//        }
//
//        return addOnlineMillis;
        if (now - lastHeartbeatEventTs >= PropertyFileReader.getRealTimeLongItem("hb_event_itv", 0)) {
            lastHeartbeatEventTs = now;
            new PlayerHeartBeatEvent(this).dispatch();
            playerEventManager.dispatch(new HeartBeatEvent(playerConditionMgr).setDailyOnlineTimeMs(getUserAttr().getBasicInfo().getDailyOnlineTimeMs()));
            if (getReturnActivityManager().isReturnActivityActive()) {
                new PlayerReturnEvt(this).dispatch();
            }
        }

        return 0;
    }

    public void heartBeatCheck(long addOnlineMillis) {
        getPlayerStateMgr().checkSingleBattleKeepAlive();
        getPlayerMoneyMgr().heartBeat();
        getFriendManager().notifyAllOnlineFriendPlayerInfoAsync();
        getClubMgr().syncLiveInfo();
        getPasswordMgr().onHeartBeat();
        getPlayerUgcManager().checkReportStarWorldBpExpToPlatform();
    }

    public BanInfo getIdipBanInfo() {
        if (idipBanInfo == null) {
            idipBanInfo = BanInfo.newBuilder().build();
        }
        return idipBanInfo;
    }

    public void setIdipBanInfo(BanInfo idipBanInfo) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("setIdipBanInfo debug, uid:{} idipBanInfo:{} size:{} trace:{}",
                    getUid(), idipBanInfo.toString(), idipBanInfo.getSerializedSize(), FunctionUtil.getStackTrace());
        }
        boolean banLadderBefore = false;
        if (this.idipBanInfo != null) {
            banLadderBefore = this.idipBanInfo.containsBanStatusMap(BanType.BT_GameMode_VALUE) ||
                    this.idipBanInfo.containsExtendedBanStatusMap(BanType.BT_GameMode_VALUE);
        }
        this.idipBanInfo = idipBanInfo;
        if (banLadderBefore || this.idipBanInfo.containsBanStatusMap(BanType.BT_GameMode_VALUE) ||
                this.idipBanInfo.containsExtendedBanStatusMap(BanType.BT_GameMode_VALUE)) {
            getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.GameModeBanChange);
        }

        if (this.idipBanInfo.containsBanStatusMap(BanType.BT_SP_GameMode_VALUE)) {
            getPlayerRoomMgr().updateStarPBanTime();
        }
    }

    public BanStatus getBanStatus(BanType banType) {
        if (idipBanInfo != null && idipBanInfo.getBanStatusMapMap() != null
                && idipBanInfo.getBanStatusMapMap().get(banType.getNumber()) != null) {
            return idipBanInfo.getBanStatusMapMap().get(banType.getNumber());
        }
        return null;
    }

    public List<Integer> getBannedMatchTypeList() {
        if (idipBanInfo == null) {
            return Collections.emptyList();
        }
        ExtendedBanStatus extendedBanStatus = idipBanInfo.getExtendedBanStatusMapMap().get(BanType.BT_GameMode_VALUE);
        if (extendedBanStatus == null) {
            return Collections.emptyList();
        }
        List<Integer> bannedList = new ArrayList<>();
        IdipBanGameModeStatus banGameModeStatus = extendedBanStatus.getBanGameModeStatus();
        if (banGameModeStatus.hasBanAll() && banGameModeStatus.getBanAll() && banGameModeStatus.hasBanAllStatus()) {
            BanStatus banMode = banGameModeStatus.getBanAllStatus();
            if (banMode.hasBanBefore() && banMode.getBanBefore() > DateUtils.currentTimeMillis()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("player:{} all match type banned", getUid());
                }
                bannedList.add(0);
                return bannedList;
            }
        }
        for (var entry : banGameModeStatus.getBanModeMapMap().entrySet()) {
            if (entry.getValue().hasBanBefore() && entry.getValue().getBanBefore() > DateUtils.currentTimeMillis()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("player:{} group:{} is banned", getUid(), entry.getKey());
                }
                bannedList.addAll(MatchTypeGroupConfData.getInstance().getMatchTypeListByGroupId(entry.getKey()));
            }
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player:{} banned match type list:{}", getUid(), bannedList);
        }
        return bannedList;
    }

    // 获取指定玩法封禁信息
    private NKPair<Boolean, BanStatus> getModeBanStatus(int matchType) {
        if (idipBanInfo == null) {
            return new NKPair<>(false, null);
        }
        ExtendedBanStatus extendedBanStatus = idipBanInfo.getExtendedBanStatusMapMap().get(BanType.BT_GameMode_VALUE);
        if (extendedBanStatus == null) {
            return new NKPair<>(false, null);
        }
        // 先检查是否封禁全部玩法，再检查指定玩法是否被封禁
        IdipBanGameModeStatus banGameModeStatus = extendedBanStatus.getBanGameModeStatus();
        if (banGameModeStatus.hasBanAll() && banGameModeStatus.getBanAll() && banGameModeStatus.hasBanAllStatus()) {
            BanStatus banMode = banGameModeStatus.getBanAllStatus();
            if (banMode.hasBanBefore() && banMode.getBanBefore() > DateUtils.currentTimeMillis()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("player:{} matchType:{} is banned because of banAll", getUid(), matchType);
                }
                return new NKPair<>(true, banMode);
            }
        }
        int groupId = MatchTypeGroupConfData.getInstance().getGroupByMatchType(matchType);
        if (groupId == 0) {
            LOGGER.warn("matchType:{} not found group", matchType);
            return new NKPair<>(false, null);
        }
        if (banGameModeStatus.containsBanModeMap(groupId)) {
            BanStatus banMode = banGameModeStatus.getBanModeMapMap().get(groupId);
            if (banMode != null && banMode.hasBanBefore() && banMode.getBanBefore() > DateUtils.currentTimeMillis()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("player:{} matchType:{} group:{} is banned", getUid(), matchType, groupId);
                }
                return new NKPair<>(true, banMode);
            }
        }
        return new NKPair<>(false, null);
    }

    // 检查指定玩法是否封禁
    public boolean checkIsBanGameMode(int matchType) {
        return getModeBanStatus(matchType).getKey();
    }

    public void checkIsBanGameModeAndNotify(int matchType) {
        var ret = getModeBanStatus(matchType);
        if (ret.getKey()) {
            var banMode = ret.getValue();
            sendBanInfoNtf(BanType.BT_GameMode, banMode.getBanBefore(), NKErrorCode.LadderModeIsBanned,
                    banMode.getBanReason(), true);
            NKErrorCode.LadderModeIsBanned.throwError("player:{} matchType:{} is banned", getUid(), matchType);
        }
    }

    // 检查是否封禁玩家排位
    public void checkIsBanLadderAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_GameMode, NKErrorCode.LadderModeIsBanned);
    }

    public boolean checkIsBanLadder() {
        return checkIsBannedInternal(BanType.BT_GameMode);
    }

    // 检查是否封禁昵称修改
    public boolean checkIsBanNickName() {
        return checkIsBannedInternal(BanType.BT_ChangeName);
    }

    // 检查是否封禁头像修改
    public boolean checkIsBanProfile() {
        return checkIsBannedInternal(BanType.BT_Profile);
    }

    // 检查是否解封排行榜
    private boolean checkShouldUnbanRank() {
        BanStatus banStatus = getBanStatus(BanType.BT_Rank);
        return banStatus != null && banStatus.hasBanBefore()
                && banStatus.getBanBefore() <= Framework.currentTimeMillis();
    }

    public void tryUnbanRank() {
        // 检查封禁信息是否过时
        if (checkShouldUnbanRank()) {
            LOGGER.info("player {} ban rank info expired", getOpenId());
            BanInfo.Builder builder = getIdipBanInfo().toBuilder();
            builder.removeBanStatusMap(BanType.BT_Rank_VALUE);
            setIdipBanInfo(builder.build());
            getRankManager().unbanAll();
        }
    }

    // 检查是否封禁玩家语音功能
    public boolean checkIsBanVoice() {
        return checkIsBannedInternal(BanType.BT_Voice);
    }

    public void checkIsBanVoiceAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_Voice, NKErrorCode.VoiceFunctionIsBanned);
    }

    // 检查是否封禁玩家创建地图
    public boolean checkIsBanCreateMap() {
        return checkIsBannedInternal(BanType.BT_CreateMap);
    }

    public void checkIsBanCreateMapAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_CreateMap, NKErrorCode.CreateMapIsBanned);
    }

    // 检查是否封禁玩家创建房间
    public boolean checkIsBanCreateRoom() {
        return checkIsBannedInternal(BanType.BT_CreateRoom);
    }

    public void checkIsBanCreateRoomAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_CreateRoom, NKErrorCode.CreateRoomIsBanned);
    }

    // 检查是否封禁玩家搜索
    public boolean checkIsBanSearch() {
        return checkIsBannedInternal(BanType.BT_Search);
    }

    public void checkIsBanSearchAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_Search, NKErrorCode.SearchIsBanned);
    }

    // 检查是否封禁玩家添加好友
    public boolean checkIsBanAddFriend() {
        return checkIsBannedInternal(BanType.BT_AddFriend);
    }

    public void checkIsBanAddFriendAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_AddFriend, NKErrorCode.AddFriendIsBanned);
    }

    // 检查是否封禁使用AI配色
    public boolean checkIsBanAiColor() {
        return checkIsBannedInternal(BanType.BT_AiChangeColor);
    }

    public void checkIsBanAiColorAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_AiChangeColor, NKErrorCode.AiChangeColorIsBanned);
    }

    public void checkIsBanUseFireworksAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_UseFireworks, NKErrorCode.FireworksUseIsBanned);
    }

    // 检查是否封禁社团操作
    public boolean checkIsBanClub() {
        return checkIsBannedInternal(BanType.BT_Club);
    }

    public void checkIsBanClubAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_Club, NKErrorCode.ClubIsBanned);
    }

    // 检查是否封禁ai视频动捕
    public boolean checkIsBanAiAnicap() {
        return checkIsBannedInternal(BanType.BT_AiAnicap);
    }

    public void checkIsBanAiAnicapAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_AiAnicap, NKErrorCode.AiAnicapIsBanned);
    }

    // 检查是否封禁Npc智能对话
    public boolean checkIsBanAiAnswer() {
        return checkIsBannedInternal(BanType.BT_AiAnswer);
    }

    public void checkIsBanAiAnswerAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_AiAnswer, NKErrorCode.AiAnswerIsBanned);
    }

    public boolean checkIsBanAigcNpcPal() {
        return checkIsBannedInternal(BanType.BT_AigcNpcPal);
    }

    public void checkIsBanAigcNpcPalAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_AigcNpcPal, NKErrorCode.AigcNpcPalIsBanned);
    }

    public boolean checkIsBanClubSearch() {
        return checkIsBannedInternal(BanType.BT_ClubSearch);
    }

    public void checkIsBanClubSearchAndNotify() {
        checkIsBannedAndNotifyInternal(BanType.BT_ClubSearch, NKErrorCode.ClubSearchIsBanned);
    }

    private boolean checkIsBannedInternal(BanType banType) {
        BanStatus banStatus = getBanStatus(banType);
        if (banStatus != null) {
            return banStatus.hasBanBefore() && banStatus.getBanBefore() > Framework.currentTimeMillis();
        }
        return false;
    }

    // 检查是否封禁，如果封禁，推送封禁通知(show=true)并throw
    public void checkIsBannedAndNotifyInternal(BanType banType, NKErrorCode errorCode) {
        BanStatus banStatus = getBanStatus(banType);
        if (banStatus != null) {
            if (banStatus.hasBanBefore() && banStatus.getBanBefore() > Framework.currentTimeMillis()) {
                sendBanInfoNtf(banType, banStatus.getBanBefore(), errorCode,
                        banStatus.getBanReason(), true);
                errorCode.throwError("operation {} is banned", banType);
            }
        }
    }

    /**
     * 登录后给客户端推送封禁ntf消息
     */
    public void sendAfterLoginBanNtf() {
        if (idipBanInfo == null) {
            return;
        }

        long currentTime = Framework.currentTimeMillis();
        AFTER_LOGIN_CHECK_PLAYER_BAN_FLAG.forEach((banTypeNumber, nkErrorCode) -> {
            BanType banType = BanType.forNumber(banTypeNumber);
            if (banType == null) {
                return;
            }

            BanStatus banStatus = getBanStatus(banType);
            if (banStatus == null) {
                return;
            }

            if (banStatus.hasBanBefore() && banStatus.getBanBefore() > currentTime) {
                // 给客户端推送封禁消息
                sendBanInfoNtf(banType, banStatus.getBanBefore(), nkErrorCode, banStatus.getBanReason(), false);
            }
        });
    }

    /**
     * 获取缓存中的个人相册点赞信息
     *
     * @return 缓存中的PlayerExtraInfoTable表数据
     */
    public AlbumLikeHisInfo getPlayerAlbumLikeHisInfo() {
        if (albumLikeHisInfo == null) {
            albumLikeHisInfo = new AlbumLikeHisInfo();
        }
        return albumLikeHisInfo;
    }

    /**
     * 获取缓存中的个人相册点赞信息
     *
     * @return 缓存中的PlayerExtraInfoTable表数据
     */
    public AlbumExtInfo getPlayerAlbumExtInfo() {
        if (albumExtInfo == null) {
            albumExtInfo = new AlbumExtInfo();
        }
        return albumExtInfo;
    }


    /**
     * 获取缓存中的个人相册信息
     *
     * @return 缓存中的PlayerExtraInfoTable表数据
     */
    public AlbumInfo getPlayerAlbumInfo() {

        if (albumInfo == null) {
            albumInfo = new AlbumInfo();
        }

        return albumInfo;
    }

    public void setPlayerExtraInfoToCache(TcaplusDb.PlayerExtraInfoTable extraInfoTable) {
        setPlayerExtraInfoToCache(extraInfoTable, true);
    }

    /**
     * 设置PlayerExtraInfoTable表数据到缓存中
     *
     * @param extraInfoTable 要设置的PlayerExtraInfoTable表数据
     */
    public void setPlayerExtraInfoToCache(TcaplusDb.PlayerExtraInfoTable extraInfoTable, boolean updatePicLike) {

        if (albumInfo == null) {
            albumInfo = new AlbumInfo();
        }
        if (this.getUserAttr().getAlbum().getIsDataMigrate()) {
            // 个人相册数据已迁移后, 设置PlayerExtraInfoTable表数据中的个人相册数据到缓存中
            albumInfo.mergeFromDto(extraInfoTable.getAlbumInfo());
        }
        if (updatePicLike) {
            getPlayerAlbumLikeHisInfo().mergeFromDto(extraInfoTable.getAlbumLike());
            getAlbumManager().clearPicLikeInfo();
            getAlbumManager().updatePicLikeCountCache();
            getAlbumManager().setLikeInfoLoad(true);
            getAlbumManager().loadTempPiiPicLikeParams();

            getPlayerAlbumExtInfo().mergeFromDto(extraInfoTable.getAlbumExtInfo());
            getAlbumManager().refreshAlbumLimitCondition();
            getAlbumManager().clearDelAlbumPicExt();
        }
    }

    /**
     * 刷新PlayerExtraInfoTable表数据到db中
     *
     * @param tableBuilder 要设置的PlayerExtraInfoTable表Builder
     * @param changeFields 要更新的字段，为空更新所有字段
     */
    public void flushPlayerExtraInfoNoRedis(TcaplusDb.PlayerExtraInfoTable.Builder tableBuilder,
                                            PlayerExtraInfoTableDao.PlayerExtraInfoTableField... changeFields) {

        NKErrorCode code = PlayerExtraInfoTableDao.updatePlayerExtraInfoTableNoRedis(this.getUid(), tableBuilder, changeFields);
        if (code.getValue() != NKErrorCode.OK.getValue()) {
            LOGGER.error("updatePlayerExtraInfoTableNoRedis failed, uid:{}, code:{}", this.getUid(), code);
        }
    }

    /**
     * 刷新PlayerExtraInfoTable表数据到db和redis中
     *
     * @param tableBuilder 要设置的PlayerExtraInfoTable表Builder
     * @param changeFields 要更新的字段，为空更新所有字段
     */
    public void flushPlayerExtraInfoWithRedis(TcaplusDb.PlayerExtraInfoTable.Builder tableBuilder,
                                              PlayerExtraInfoTableDao.PlayerExtraInfoTableField... changeFields) {

        NKErrorCode code = PlayerExtraInfoTableDao.updatePlayerExtraInfoTable(this.getUid(), tableBuilder, changeFields);
        if (code.getValue() != NKErrorCode.OK.getValue()) {
            LOGGER.error("updatePlayerExtraInfoTable failed, uid:{}, code:{}", this.getUid(), code);
        }
    }

    /**
     * 加载PlayerExtraInfoTable表数据到缓存
     */
    public void loadPlayerExtraInfoTableIntoCache() {

        // 从tcaplus里面查询PlayerExtraInfoTable表数据
        TcaplusDb.PlayerExtraInfoTable table = PlayerExtraInfoTableDao.getPlayerExtraInfoTableFromDb(this.getUid());
        if (table != null) {
            // 如果查询结果不为空, 直接设置到缓存
            setPlayerExtraInfoToCache(table);
            return;
        }

        // 如果查询结果为空, 则初始化PlayerExtraInfoTable表数据, 并写入PlayerExtraInfoTable表中
        TcaplusDb.PlayerExtraInfoTable.Builder tableBuilder = TcaplusDb.PlayerExtraInfoTable.newBuilder()
                .setUid(this.getUid())
                .setUpdateTime(Framework.currentTimeMillis());

        NKErrorCode code = PlayerExtraInfoTableDao.insertPlayerExtraInfoTable(this.getUid(), tableBuilder);
        if (code.getValue() != NKErrorCode.OK.getValue()) {
            LOGGER.error("fail to insert player extra info table, uid:{}, code:{}", this.getUid(), code);
            return;
        }

        // 数据写入PlayerExtraInfoTable表后, 设置到缓存中
        setPlayerExtraInfoToCache(tableBuilder.build());
    }

    public void sendFeatureOpenNtf() {
        FeatureOpenConfData confData = FeatureOpenConfData.getInstance();
        if (confData == null) {
            LOGGER.error("cannot find feature open config data, player:{}", getUid());
            return;
        }
        FeatureOpenNtf.Builder ntfBuilder = FeatureOpenNtf.newBuilder();
        for (var entry : confData.getBuilderListForClient().entrySet()) {
            for (CsPlayer.FeatureOpenItem.Builder item : entry.getValue()) {
                if (item.getIsShow()) {
                    // 默认展示的不下发，仅下发关闭的内容
                    continue;
                }
                ntfBuilder.addItems(item);
            }
        }
        if (ntfBuilder.getItemsCount() <= 0) {
            return;
        }
        sendNtfMsg(MsgTypes.MSG_TYPE_FEATUREOPENNTF, ntfBuilder);
    }

    @Override
    public int getPlatId() {
        return userAttrMgr.getPlatid();
    }

    @Override
    public String getOpenId() {
        return userAttrMgr.getOpenid();
    }

    @Override
    public int getLevel() {
        return getUserAttr().getPlayerPublicProfileInfo().getLevel();
    }

    @Override
    public long getRoleId() {
        return userAttrMgr.getUid();
    }

    @Override
    public String getGameSvrId() {
        return TlogFlow.getGameSvrId();
    }

    @Override
    public String getGameAppId() {
        return "";//PlayerUtil.accountType2AppId(getUserAttr().getBasicInfo().getAccountInfo().getAccountType());
    }

    @Override
    public TconndApiAccount getAccountType() {
        return getUserAttr().getPlayerProfileInfo().getAccountType();
    }

    // 获取当前gamesvr的zoneId
    @Override
    public int getZoneAreaId() {
        return Framework.getInstance().getZoneAreaId();
    }

    @Deprecated
    @Override
    public String getClientVersion() {
        return session == null ? "" : session.getClientInfo().clientVersion;
    }

    @Override
    public long getClientVersion64() {
        return getUserAttr().getPlayerPublicGameSettings().getClientVersion64();
    }

    public long getDSVersion64() {
        return getUserAttr().getPlayerPublicGameSettings().getDSVersion64();
    }

    public String getClientResVersion() {
        return session == null ? "" : session.getClientInfo().resVersion;
    }

    public NKErrorCode updatePlayInfos(String clientVersion, List<GamePlayInfo> playInfos) {
        if (session == null) {
            return NKErrorCode.UserIsOffline;
        }

        var clientInfo = session.getClientInfo();
        if (!clientInfo.clientVersion.equals(clientVersion)) {
            LOGGER.error("player:{} update play infos but cli version:{}-{} conflict"
                    , getUid(), clientInfo.clientVersion, clientVersion);
            return NKErrorCode.InvalidParams;
        }

        if (!clientInfo.updateGamePlayInfos(getUid(), playInfos)) {
            LOGGER.debug("player:{} update play infos but no dirty", getUid());
            return NKErrorCode.OK;
        }
        var dirtyIds = new HashSet<Integer>();
        if (updateGamePlayPublicInfo(false, dirtyIds)) {
            // sync room info
            getPlayerRoomMgr().sendRpcRoomPlayerOnline();
            // sync cs patch
            PlayerResPatcher.checkPatch(this, dirtyIds);

            var gamePlayAttr = getUserAttr().getPlayerPublicGameSettings().getGamePlay();
            var xiaowoGp = gamePlayAttr.get(PlayerXiaoWoMgr.XIAOWO_FEATURE_ID);
            if (xiaowoGp != null && xiaowoGp.isDirty()) {
                getPlayerXiaoWoMgr().afterLogin(false);
            }
            var farmGp = gamePlayAttr.get(PlayerFarmMgr.FARM_FEATURE_ID);
            if (farmGp != null && farmGp.isDirty()) {
                getPlayerFarmMgr().afterLogin(false);
                getPlayerHouseMgr().afterLogin(false);
            }
        }
        return NKErrorCode.OK;
    }

    public List<com.tencent.wea.protocol.common.GamePlayBrief> dumpPlayInfos() {
        var infos = new ArrayList<com.tencent.wea.protocol.common.GamePlayBrief>();
        var gamePlayAttr = getUserAttr().getPlayerPublicGameSettings().getGamePlay();
        for (var gp : gamePlayAttr.values()) {
            var info = com.tencent.wea.protocol.common.GamePlayBrief.newBuilder();
            info.setFeatureType(gp.getFeatureType());
            info.setPakVersion(gp.getPakVersion());
            infos.add(info.build());
        }
        return infos;
    }

    public PlayerGamePlay getGamePlayPublicInfo(int featureType) {
        var gamePlayAttr = getUserAttr().getPlayerPublicGameSettings().getGamePlay();
        return gamePlayAttr.get(featureType);
    }

    GamePlayHolder getGamePlayHolder() {
        return session != null ? session.getClientInfo().getGamePlay() : null;
    }

    boolean updateGamePlayPublicInfo(boolean fullSync, Set<Integer> dirtyIds) {
        GamePlayHolder gamePlayHolder = getGamePlayHolder();
        if (null == gamePlayHolder) {
            LOGGER.error("player:{} got play holder nil", getUid());
            return false;
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player({}:{}:{}) gamePlayHolder.getEntries({}) fullSync({}) dirtyIds({})",
                    getUid(), getOpenId(), getName(), gamePlayHolder.getEntries(), fullSync, dirtyIds);
        }

        MapAttrObj<Integer, PlayerGamePlay> gamePlayAttr = getUserAttr().getPlayerPublicGameSettings().getGamePlay();
        if (fullSync) {
            gamePlayAttr.clear();
        }
        boolean isDirty = false;
        for (var entry : gamePlayHolder.getEntries()) {
            var gp = gamePlayAttr.computeIfAbsent(entry.getFeatureType(), type -> {
                return new PlayerGamePlay().setFeatureType(type);
            });
            if (gp.getPakVersion() == entry.getPakVersion() && gp.getConfVersion() == entry.getConfVersion()) {
                continue;
            }
            gp.setPakVersion(entry.getPakVersion());
            gp.setConfVersion(entry.getConfVersion());
            isDirty = true;
            if (null != dirtyIds) {
                dirtyIds.add(entry.getFeatureType());
            }
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player({}:{}:{}) newGamePlay({})", getUid(), getOpenId(), getName(), gamePlayAttr.toString());
        }

        return isDirty;
    }

    @Override
    public String getSecReportData() {
        return session == null ? "" : session.getClientInfo().secReportData;
    }

    @Override
    public int getAreaId() {
        return getUserAttr().getPlayerProfileInfo().getAccountType().getNumber();
    }

    public int getIdipArea() {
        int worldId = Framework.getInstance().getWorldId();
        String envFlag = PropertyFileReader.getItem("env_flag", "");

        if (ServerEngine.getInstance().isBusiness() || ServerEngine.getInstance().isPreBusiness()) {
            return ServerIdipAreaConfig.getInstance().getFormalIdipArea(
                    worldId, envFlag, getAccountType().getNumber());
        } else {
            return ServerIdipAreaConfig.getInstance().getTestIdipArea(worldId, envFlag);
        }
    }

    @Override
    public boolean isNPC() {
        if (getOpenId() != null) {
            String[] parts = StringUtils.split(getOpenId(), "_");
            return parts != null && parts.length == 2;
        }
        return false;
    }

    @Override
    public String getServerIp() {
        return Framework.getInstance().getGameSvrIp();
    }

    @Override
    public String getSequence() {
        TpsSpan currentSpan = TpsTracer.currentSpan();
        return currentSpan == null ? "" : currentSpan.getTraceId();
    }

    @Override
    public String getTelecomOper() {
        if (session != null) {
            return session.getClientInfo().telecomOper;
        }
        return null;
    }

    @Override
    public String getNetwork() {
        if (session != null) {
            return session.getClientInfo().netWork;
        }
        return null;
    }

    @Override
    public String getClientIP() {
        if (session != null) {
            return session.getIpAddr();
        }
        return null;
    }

    @Override
    public String getClientIPV6() {
        return null;
    }

    @Override
    public int getClientPlat() {
        if (session != null) {
            return session.getClientInfo().getClientDeviceType();
        }
        return getUserAttr().getPlayerPublicBasicInfo().getClientDeviceType();
    }

    //主玩法排位赛季ID
    @Override
    public int getSeasonId() {
        QualifyingInfo qualifyingInfo = getUserAttr().getPlayerPublicGameData().getQualifyingInfo();
        if (qualifyingInfo != null) {
            return qualifyingInfo.getSeason();
        }
        return 0;
    }

    /**
     * 主赛季id，在seasonMgr中结算的
     *
     * @return int
     */
    public int getMainSeasonId() {
        return getUserAttr().getSeasonInfo().getSeasonId();
    }

    /**
     * 检查登录条件是否满足
     *
     * @param loginCondition 登录条件
     * @return boolean
     */
    public boolean checkLoginCondition(PlayerLoginCondition loginCondition) {
        PlayerLoginPlat loginPlat = getUserAttr().getPlayerPublicBasicInfo().getLoginPlat();
        TconndApiAccount accountType = getUserAttr().getPlayerPublicBasicInfo().getAccountType();
        return (loginCondition.getLoginPlat().getNumber() == 0 || loginCondition.getLoginPlat() == loginPlat) &&
                (loginCondition.getAccountType().getNumber() == 0 || loginCondition.getAccountType() == accountType);
    }

    @Override
    public String getCountry() {
        if (!ServerInfoTool.isOverseaEnv()) {
            // 国内使用地理位置中的数据进行上报
            PlayerRankGeoInfo geoInfo = getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
            if (geoInfo != null) {
                return geoInfo.getRegion().name();
            }
        } else {
            // 海外使用账号注册时的区域id数据进行上报
            ResRegional.Regional regionConf =
                    RegionalConf.getInstance().get(getUserAttr().getPlayerPublicBasicInfo().getRegisterRegionId());
            if (regionConf != null) {
                return regionConf.getAplha2Code();
            }
        }
        return null;
    }

    @Override
    public String getProvince() {
        PlayerRankGeoInfo geoInfo = getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        if (geoInfo != null) {
            return String.valueOf(geoInfo.getProvince());
        }
        return null;
    }

    @Override
    public String getCity() {
        PlayerRankGeoInfo geoInfo = getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        if (geoInfo != null) {
            return String.valueOf(geoInfo.getCity());
        }
        return null;
    }

    @Override
    public String getDistrict() {
        PlayerRankGeoInfo geoInfo = getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        if (geoInfo != null) {
            return String.valueOf(geoInfo.getTown());
        }
        return null;
    }

    @Override
    public DeviceLevel getDeviceLevel() {
        if (session != null) {
            return session.getClientInfo().deviceLevel;
        }
        return DeviceLevel.DEVICE_LEVEL_LOWEST;
    }

    @Override
    public String getUserAgent() {
        if (session != null) {
            return session.getClientInfo().getUserAgent();
        }
        return null;
    }

    public String getAccessToken() {
        return getUserAttr().getPlayerProfileInfo().getAccessToken();
    }

    @Override
    public String getLanguage() {
        // 获取对应的语言id
        int language = 0;
        if (!ServerInfoTool.isOverseaEnv()) {
            language = LanguageType.LANGUAGE_TYPE_ZH_CN_VALUE;
        } else {
            language = this.getUserAttr().getPlayerPublicGameSettings().getClientLanguage();
        }

        // 读取语种配置表数据
        ResLanguages.Languages languages = LanguagesData.getInstance().get(String.valueOf(language));
        if (languages != null) {
            return languages.getAttClient();
        }

        return null;
    }

    /*
     * - 当用户关闭了隐私开关：【个性化推荐】时，在加好友场景下
     *   1: 该用户收到的好友申请均不带理由
     *   2: 该用户发送的好友申请均不带理由
     * - 当用户关闭了隐私开关时，在好友推荐场景下
     *   1: 客户端控制了好友推荐页面不展示，引导用户去开启开关
     */
    public boolean isPersonalizedRecommendationDisabled() {
        // 这个需求是当时临时加，没有服务器. 不然最好放PlayerPublicGameSettings
        final int PERSONALIZED_RECOMMENDATION_CLIENT_CACHE_ID = 31;
        var clientCache = getUserAttr().getClientCacheData().get(PERSONALIZED_RECOMMENDATION_CLIENT_CACHE_ID);
        return clientCache != null && "false".equals(clientCache.getVal());
    }

    /**
     * 获得玩家Uid
     *
     * @return
     */
    public long getUid() {
        return userAttrMgr.getUid();
    }

    @Override
    public long getShortUid() {
        return getUserAttr().getPlayerPublicProfileInfo().getShortUid();
    }

    @Override
    public boolean getIsSimulate() {
        if (session != null) {
            return session.getClientInfo().getIsSimulate();
        }
        return false;
    }

    @Override
    public String getJQCallInfo() {
        if (session == null) {
            return "";
        }
        return session.getJqCallInfo();
    }

    @Override
    public long updateAndGetTlogCounter() {
        tlogCounter = tlogCounter == Long.MAX_VALUE ? 0 : tlogCounter + 1;
        return tlogCounter;
    }

    @Override
    public int getIsVa() {
        if (session != null) {
            return session.getClientInfo().getIsVa();
        }
        return 0;
    }

    @Override
    public int getChannel() {
        if (session != null) {
            return session.getClientInfo().channel;
        }
        return 0;
    }

    @Override
    public int getLoginPlat() {
        if (session != null) {
            return session.getClientInfo().getLoginPlat().getNumber();
        }
        return getUserAttr().getPlayerPublicBasicInfo().getLoginPlat().getNumber();
    }

    @Override
    public String getDeeplink() {
        return session == null ? "" : session.getDeeplink();
    }

    public PlayerClientInfo.Builder getClientInfoBuilder() {
        PlayerClientInfo.Builder clientInfoBuilder = PlayerClientInfo.newBuilder();
        Session curSession = getSession();
        if (curSession != null) {
            clientInfoBuilder.setLoginPlat(curSession.getClientInfo().getLoginPlat().getNumber());
            clientInfoBuilder.setDevicePlat(curSession.getClientInfo().getDevicePlatform());
            clientInfoBuilder.setCloudGamePlat(curSession.getClientInfo().getCloudGamePlat());
        }
        return clientInfoBuilder;
    }

    public MemberBaseInfo getMemberBaseInfo() {
        return getMemberBaseInfoBuilder().build();
    }

    public MemberBaseInfo.Builder getMemberBaseInfoBuilder() {
        MemberBaseInfo.Builder memberInfo = MemberBaseInfo.newBuilder();

        UserAttr userAttr = getUserAttr();
        memberInfo.setUid(getUid());
        memberInfo.setName(getName());
        memberInfo.setFace(userAttr.getPlayerPublicProfileInfo().getProfile());
        if (getMonthCardManager().isMonthCardActive()) {
            memberInfo.setPrivilegeLevel(1);
        }
        memberInfo.setGender(userAttr.getPlayerPublicProfileInfo().getGender());
        memberInfo.setOpenId(getOpenId());
        memberInfo.setPlatId(getPlatId());
        memberInfo.setClientVersion(getCompVersion());
        memberInfo.setLevel(getLevel());
        memberInfo.setAccountType(getAccountType());
        memberInfo.setQualifyingInfo(getQualifyingManager().getQualifyingInfo());
        // 客户端信息
        memberInfo.setClientInfo(getClientInfoBuilder());
        // 副玩法段位信息 目前先这样
        memberInfo.putAllSecondaryGameplayQualifyingInfos(
                getQualifyingManager().getSecondaryGameplayQualifyingInfo());
        memberInfo.setSceneId(userAttr.getPlayerPublicSceneData().getLobbyInfo().getLobbyId());
        memberInfo.setFashionValue(userAttr.getPlayerPublicEquipments().getFashionValue());
        memberInfo.addAllUnLockGameModeSet(userAttr.getUnLockGameModeSet().getValues());
        memberInfo.addAllDressUpItems(userAttr.getPlayerPublicEquipments().getDressUpInfosList());
        // 添加PlayerPublicEquipments.dressItemInfo的装备信息(铭牌、称号、头像框等)
        for (DressItemInfo dressItemInfo : userAttr.getPlayerPublicEquipments().getDressItemInfo().values()) {
            if (dressItemInfo.getItemId() == 0) {
                continue;
            }
            memberInfo.addDressUpItems(dressItemInfo.getItemId());
        }
        for (DressUpDetailInfo dressUpDetailInfo : userAttr.getPlayerPublicEquipments().getDressUpDetailInfos().values()) {
            proto_DressUpDetailInfo.Builder dressUpDetailInfoBuilder = proto_DressUpDetailInfo.newBuilder();
            dressUpDetailInfo.copyToSs(dressUpDetailInfoBuilder);
            memberInfo.putDressUpItemDetails(dressUpDetailInfo.getItemId(), dressUpDetailInfoBuilder.build());
        }
        memberInfo.setHeadFrame(getHeadFrame());
        memberInfo.setNamePlate(getNamePlate());
        memberInfo.setProfileTheme(userAttr.getPlayerPublicEquipments().getProfileTheme()); // 设置玩家的默认主页背景
        memberInfo.setCreatorAccountInfo(CreatorAccountInfo.newBuilder()
                .setAuthType(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthType())
                .setAuthDesc(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthDesc())
                .build());
        if (getPlayerRoomMgr().getTeamId() > 0) { //队伍语音临时存在玩家身上，同步的时候，一并带到队伍里
            memberInfo.getMemberClientInfoBuilder().setVoiceState(getUserAttr().getRoomInfo().getRoomMemberClientInfo().getVoiceState());
            memberInfo.getMemberClientInfoBuilder().setVoiceRoomTag(getUserAttr().getRoomInfo().getRoomMemberClientInfo().getVoiceRoomTag());
        }

        userAttr.getPlayerPublicSummaryInfo().getBattleModeData().values().forEach(modeDataValue -> {
            AttrBattleModeData.proto_BattleModeData.Builder modeDataBuilder =
                    AttrBattleModeData.proto_BattleModeData.newBuilder();
            modeDataBuilder.setId(modeDataValue.getId());
            modeDataValue.getRecentBattleResultData().values().forEach(resultDataValue -> {
                AttrBattleResultData.proto_BattleResultData.Builder resultDataBuilder =
                        AttrBattleResultData.proto_BattleResultData.newBuilder();
                resultDataBuilder.setResult(resultDataValue.getResult());
                resultDataBuilder.setId(resultDataValue.getId());
                resultDataBuilder.setEndTime(resultDataValue.getEndTime());
                resultDataBuilder.setBattleRole(resultDataValue.getBattleRole());
                resultDataBuilder.setSpecialBattleData(resultDataValue.getSpecialBattleData().getCopySsBuilder());
                modeDataBuilder.addRecentBattleResultData(resultDataBuilder);
            });

            //modeDataValue
            memberInfo.addBattleRecords(modeDataBuilder.build().toByteString());
        });

        // 填充MMR分数
        memberInfo.setMmrScoresInfo(getMatchMMRScoreManager().getMMRScoresInfo());
        // 填充温暖局分数
        memberInfo.setWarmRoundInfo(getWarmRoundManager().getWarmRoundInfo());
        // 填充大王别抓我玩法特殊数据
        memberInfo.setChaseUserData(getChaseMgr().getChaseMemberBaseInfoData());

        if (GSConfig.isMultiIdcDsEnable()) {
            // 填充网络信息
            memberInfo.setIdcNetworkInfo(getIdcNetworkInfoManager().getIDCNetworkInfo());
            // 填充匹配区域id
            memberInfo.setLoginCountryCode(getPlayerMatchMgr().getLoginCountryCode());
        }

        // 新手局的ab测试组id
        memberInfo.setLevelGuideGroupId(getAbTestMgr().getTABTestGroupId(ABTestType.ABTT_NEWBIE_BATTLE_TYPE));
        memberInfo.setRegisterTimeMs(getRegisterTime());
        memberInfo.setLoginTimeMs(getLoginTime());
        memberInfo.setMobileGearLevel(userAttr.getPlayerProfileInfo().getMobileGearLevel());

        // 玩家开播相关设置
        memberInfo.setStreamStateInfo(
                StreamStateInfo.newBuilder().setIsStreamOn(getUserAttr().getStreamSetting().getOnStream())
                        .setStreamToken(getUserAttr().getStreamSetting().getStreamToken()));
        for (StreamSetting streamSetting : getUserAttr().getStreamSettingList().values()) {
            memberInfo.addStreamStateInfoList(StreamStateInfo.newBuilder().setIsStreamOn(streamSetting.getOnStream())
                    .setStreamToken(streamSetting.getStreamToken())
                    .setStreamPlatTypeVal(streamSetting.getStreamPlatType()));
        }

        memberInfo.setCreatorAccountInfo(CreatorAccountInfo.newBuilder()
                .setAuthType(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthType())
                .setAuthDesc(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthDesc())
                .build());
        memberInfo.setDsReplayRecord(getIdipMgr().isNeedDsReplayRecord());
        memberInfo.setQqTeamTaskId(profileManager.getQQTeamTaskId());
        memberInfo.setIsLadderBan(checkIsBanLadder());
        memberInfo.addAllBannedMatchTypeList(getBannedMatchTypeList());
        MatchIsolateInfoDb matchIsolateInfoDb = userAttr.getMatchStatics().getMatchIsolateInfo();
        memberInfo.setMatchIsolateInfo(MatchIsolateInfo.newBuilder().setAiLevel(matchIsolateInfoDb.getAilevel())
                .setTime(matchIsolateInfoDb.getTime()).setType(matchIsolateInfoDb.getType()));
        if (getWolfKillMgr().getWolfKillInfo().getUid() > 0) {
            memberInfo.setWolfKillReputationScore(getWolfKillMgr().getWolfKillInfo().getReputationScore());
            memberInfo.setWolfKillActionScore(getWolfKillMgr().getWolfKillInfo().getActionScore());
        }
        memberInfo.setUserIp(getSession() != null ? getSession().getIpAddr() : "");
        memberInfo.setUserProfile(getUserAttr().getPlayerPublicProfileInfo().getProfile());
        memberInfo.setDeviceType(LoginDeviceType.LDT_MOBILE_VALUE);
        if (getIsSimulate()) {
            memberInfo.setDeviceType(LoginDeviceType.LDT_SIMULATOR_VALUE);
        } else {
            switch (getLoginPlat()) {
                case PlayerLoginPlat.PLP_PC_VALUE: {
                    memberInfo.setDeviceType(LoginDeviceType.LDT_PC_VALUE);
                }
                break;
            }
        }

        if (getDeviceLevel() != null) {
            memberInfo.setDeviceLevel(getDeviceLevel());
        }
        memberInfo.addAllGrayTags(getPlayerGrayTagMgr().getGrayTagsList());
        // 玩家回归特权
        memberInfo.setReturningPrivilege(getReturnActivityManager().isPrivilegeValid());
        memberInfo.setReturning(getUserAttr().getPlayerPublicProfileInfo().getReturning());

        if (ReputationScoreUtil.playerReputationScoreSwitch(this.getClientVersion64())) {
            // 设置玩家信誉分数据
            Map<Integer, Integer> playerReputationScoreMap = getReputationScoreMgr().getPlayerScoreIdToReputationScoreMap();
            if (playerReputationScoreMap != null && !playerReputationScoreMap.isEmpty()) {
                memberInfo.putAllPlayerReputationScoreInfo(playerReputationScoreMap);
            }
        }

        // 主玩法关卡
        for (int poolId : LevelRoundRandomRuleData.getInstance().getExcludeRecentPoolIds()) {
            Collection<Integer> relatedModes = LevelRoundRandomRuleData.getInstance()
                    .getExcludeRecentPoolPlayModeIds(poolId);

            int recentMax = LevelRoundRandomRuleData.getInstance().getExcludeRecentPoolSize(poolId);
            List<BattleRecentLevel.Builder> recentLevels = Lists.newArrayList();

            for (int mode : relatedModes) {
                BattleModeData data = userAttr.getPlayerPublicSummaryInfo().getBattleModeData().get(mode);
                if (data == null || data.getRecentBattleResultDataSize() == 0) {
                    continue;
                }

                for (var record : data.getRecentBattleResultData().values()) {
                    BattleRecentLevel.Builder builder = BattleRecentLevel.newBuilder().setMode(mode)
                            .setEndTime(record.getEndTime());
                    record.getLevelRounds().values().forEach(d -> builder.addLevel(d.getLevelId()));
                    recentLevels.add(builder);
                }
            }

            recentLevels.sort(Comparator.comparingLong(BattleRecentLevelOrBuilder::getEndTime).reversed());

            for (int i = 0; i < Math.min(recentLevels.size(), recentMax); i++) {
                memberInfo.addRecentRounds(recentLevels.get(i));
            }
        }

        List<Integer> involvedABTestIds = Lists.newArrayList(
                ABTestType.ABTT_SURVIVAL_LEVEL_RATIO_VALUE,
                ABTestType.ABTT_RANDOM_EVENT_VALUE,
                ABTestType.ABTT_PROMOTION_COMPETITION_THREE_ROUND_VALUE,
                ABTestType.ABTT_PROMOTION_COMPETITION_ONE_ROUND_VALUE,
                ABTestType.ABTT_ARENA_NOVICE_CHOOSES_HERO_V2_VALUE,
                ABTestType.ABTT_RANDOM_EVENT_V2_VALUE,
                ABTestType.ABTT_CHASE_NEWBIE_FIXED_MAP_VALUE,
                ABTestType.ABTT_CHASE_NEWBIE_FIXED_ROLE_VALUE,
                ABTestType.ABTT_CHASE_NEWBIE_GUIDE_VALUE
        );
        for (var testId : involvedABTestIds) {
            int groupId = abTestMgr.getTABTestGroupId(testId);
            if (groupId == 0) {
                continue;
            }

            memberInfo.addAbtest(KeyValueInt32.newBuilder().setKey(testId).setValue(groupId));
        }

        memberInfo.getStatusDetailsBuilder().setStatusDetails(userAttr.getPlayerPublicLiveStatus().getCopyCsBuilder()
                .getStatusDetails());
        memberInfo.getStatusDetailsBuilder().setSceneData(userAttr.getPlayerPublicSceneData().getCopyCsBuilder());

        BattlePlayerSummary.Builder summary = BattlePlayerSummary.newBuilder();
        Map<Integer, Integer> kvs = new HashMap<>();

        var gameCount = getUserAttr().getPlayerPublicGameData().getPlayerGameTimes(PlayerGameTimeType.PGTT_GameTimes);
        kvs.put(BattlePlayerSummaryType.BPST_BattleNum_VALUE, gameCount == null ? 0 : (int) gameCount.getValue());

        if (!isHaveHOKBattleRecord(userAttr.getPlayerPublicSummaryInfo().getBattleModeData().values())) {
            kvs.put(BattlePlayerSummaryType.BPST_HOKTeamGuide_VALUE, 1);
        }
        kvs.put(BattlePlayerSummaryType.BPST_AnimeDressOutline_VALUE,
                userAttr.getPlayerPublicEquipments().getAnimeDressOutline());

        for (var e : kvs.entrySet()) {
            summary.addKvList(KeyValueInt32.newBuilder().setKey(e.getKey()).setValue(e.getValue()));
        }

        memberInfo.setSummary(summary);

        // 狼人杀白名单配置
        String wolfKillWhiteModuleIds = PropertyFileReader.getRealTimeItem("wolfkill_white_list_module_ids", "");
        if (wolfKillWhiteModuleIds.length() > 0) {
            for (String moduleId : wolfKillWhiteModuleIds.split(";")) {
                boolean isWhite = XlsWhiteListPlayerGroupConfData.getInstance()
                        .isInAdminWhiteList(getOpenId(), moduleId);
                if (isWhite) {
                    LOGGER.info("WolfkillAdminWhite openid:{}, uid:{}, moduleId:{}, isWhite",
                            getOpenId(), getUid(), moduleId);
                    memberInfo.addWolfkillAdminWhiteModuleIds(moduleId);
                }
            }
        }
        memberInfo.setHideProfileToFriend(getUserAttr().getPlayerPublicGameSettings().getHideProfileToFriend());
        memberInfo.setHideProfileToStranger(getUserAttr().getPlayerPublicGameSettings().getHideProfileToStranger());

        // 白名单
        for (XlsWhiteList xlsWhiteList : getUserAttr().getXlsWhiteList().values()) {
            if (xlsWhiteList.getStatus() == 1) {
                memberInfo.addWhitelistModuleId(xlsWhiteList.getModuleId());
            }
        }

        // update game play feature info
        var playInfos = dumpPlayInfos();
        if (playInfos != null || !playInfos.isEmpty()) {
            memberInfo.addAllPlayInfo(playInfos);
        }


        // 设置狼人杀的一些其他字段
        WolfKillGameInfo.Builder wolfKillGameInfo = calcWolfKillGameInfo();
        memberInfo.setWolfKillGameInfo(wolfKillGameInfo);

        memberInfo.setArenaHeadFrame(getArenaHeadFrame());
        memberInfo.setArenaHeadPic(getArenaMgr().getArenaAttr().getProfile().getHeadPic());

        // 设置大厅模式类型设置
        memberInfo.setLobbyModeTypeSetting(getUserAttr().getPlayerPublicGameSettings().getLobbyModeType());
        // 设置玩家的组队秀背景
        memberInfo.setTeamShowBackgroundTheme(getUserAttr().getPlayerPublicEquipments().getTeamShowTheme());
        // 设置分包下载信息
        Collection<PakDownloadInfo> pakDownloadInfos;
        // 根据版本设置做新旧赋值逻辑的切换
        fillPakInfo(memberInfo);

        memberInfo.addAllBlackListTopN(getBlackManager().getMatchBlackMgr().getTopNBlackListUid());
        memberInfo.setHeatPowerRank(getPlayerBattleMgr().getHokRank());

        // 填充mayday玩法数据
        MayDayUtil.fillMayDayEndlessModeSaveData(this, memberInfo);

        //填充大厅匹配信息
        getLobbyMatchMgr().fillLobbyMatchInfo(memberInfo);

        //填充arena数据
        try {
            fillArenaData(memberInfo);
        } catch (Exception e) {
            LOGGER.error("fillArenaData Exception", e);
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("uid {} getMemberBaseInfo {}",
                    getUid(), TextFormat.shortDebugString(memberInfo));
        }
        return memberInfo;
    }

    /**
     * 填充分包下载数据到MemberBaseInfo
     *
     * @param memberInfo
     */
    private void fillPakInfo(MemberBaseInfo.Builder memberInfo) {
        // 旧分包数据
        for (PakDownloadInfo pakDownloadInfo : getUserAttr().getClientPakInfo().getPakInfo().values()) {
            memberInfo.addPakDownloadInfoList(pakDownloadInfo.getCopyCsBuilder());
        }
        // 大小包数据
        for (PakDownloadInfo pakDownloadInfo : getUserAttr().getClientPakInfo().getDetailPakInfo().values()) {
            memberInfo.addDetailPakDownloadInfoList(pakDownloadInfo.getCopyCsBuilder());
        }
    }

    private boolean isHaveHOKBattleRecord(Collection<BattleModeData> battleModeDataList) {
        if (battleModeDataList == null) {
            return false;
        }
        try {
            for (BattleModeData battleModeData : battleModeDataList) {
                if (HokConfs.isInHokType(battleModeData.getId())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            LOGGER.error("getHOKBattleRecordCount failed ");
        }
        return true;
    }

    private void fillArenaData(MemberBaseInfo.Builder memberInfo) {
        ArenaData.Builder builder = ArenaData.newBuilder();
        builder.setIsBigPackage(this.getArenaMgr().isBigPackage());
        builder.setUserFightDirectFlag(playerHokMgr.getAttackDirectInfo());
        //填充随机事件
        ResMisc.MiscConfArena miscConfArena = MiscConfArena.getInstance().getMiscConfArena();
        if (miscConfArena != null) {
            int openRandomEventMatchNum = miscConfArena.getOpenRandomEventMatchNum();
            builder.setIsMeetRandomEventCondition(this.getConfPlayTimes() >= openRandomEventMatchNum);//填充随机事件
        }
        builder.setClientSetDSParam(playerArenaMgr.getClientJsonStr());
        builder.setNewBeeTeamMatchFlag(playerHokMgr.getNewBeeTeamMatchFlag());
        builder.setNewBeeSingleABTestFlag(playerHokMgr.getNewBeeSingleSpecialFlag());
        fillArenaHeroStarInfo(builder);
        fillArenaHeroInfo(builder);
        memberInfo.setArenaData(builder);

    }

    // 填充主目标系统数据
    private void fillArenaHeroStarInfo(ArenaData.Builder arenaDataBuilder) {
        for (var heroInfo : playerArenaMgr.getArenaAttr().getHeroMap().values()) {
            var starInfo = heroInfo.getHeroStarInfo();
            arenaDataBuilder.addArenaHeroStarData(ArenaHeroStarData.newBuilder()
                    .setHeroId(heroInfo.getHeroId())
                    .setStar(starInfo.getStar())
                    .setLevel(starInfo.getLevel()).build());
        }
        for (var heroInfo : playerArenaMgr.getArenaAttr().getLimitedTimeFreeHeroMap().values()) {
            var starInfo = heroInfo.getHeroStarInfo();
            arenaDataBuilder.addArenaHeroStarData(ArenaHeroStarData.newBuilder()
                    .setHeroId(heroInfo.getHeroId())
                    .setStar(starInfo.getStar())
                    .setLevel(starInfo.getLevel()).build());
        }
        arenaDataBuilder.addArenaHeroStarData(ArenaHeroStarData.newBuilder()
                .setHeroId(0)
                .setStar(playerArenaMgr.getTotalStar()).build());
        // 主目标开关状态
        arenaDataBuilder.setHeroStarDisable(playerArenaMgr.isHeroStarSystemOpen(false) ? 0 : 1);
    }

    // 填充英雄数据
    private void fillArenaHeroInfo(ArenaData.Builder arenaDataBuilder) {
        ArenaSeasonStat seasonStat = null;
        ResSeason.SeasonConf seasonConf = SeasonConfData.getInstance().getCurrOrLatestSeason();
        if (seasonConf != null) {
            seasonStat = playerArenaMgr.getArenaAttr().getStats().getSeasonStats(seasonConf.getSeasonId());
        }
        for (var heroInfo : playerArenaMgr.getArenaAttr().getHeroMap().values()) {
            var heroBuilder = ArenaHeroInfo.newBuilder().setHeroId(heroInfo.getHeroId());
            heroBuilder.getCeDataBuilder().setHeroId(heroInfo.getHeroId())
                    .addAllCeItems(heroInfo.getCopySsBuilder().getCeItemsList());
            // 填充英雄赛季使用次数数据
            if (seasonStat != null) {
                for (var matchStat : seasonStat.getStats().values()) {
                    if (matchStat != null) {
                        var heroStat = matchStat.getHeroStats(heroInfo.getHeroId());
                        if (heroStat != null) {
                            heroBuilder.putStatData(matchStat.getMatchType(), heroStat.getCopySsBuilder().build());
                        }
                    }
                }
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("heroId:{} heroInfo:{}", heroInfo.getHeroId(), heroBuilder);
            }
            arenaDataBuilder.putHeroInfo(heroInfo.getHeroId(), heroBuilder.build());
        }
    }

    /**
     * 获取 配置表配置的关卡玩法次数
     *
     * @return
     */
    public long getConfPlayTimes() {
        long num = 0;
        ResMisc.MiscConfArena miscConfArena = MiscConfArena.getInstance().getMiscConfArena();
        if (miscConfArena == null) {
            LOGGER.debug("miscConfArena is null");
            return num;
        }
        for (Integer matchId : miscConfArena.getOpenRandomEventMatchIdList()) {
            ArenaMatchStat matchStats = this.getArenaMgr().getArenaAttr().getStats().getMatchStats(matchId);
            if (matchStats != null) {
                ArenaStatKV stats = matchStats.getStats(ArenaStatType.ArenaPlayTimes_VALUE);
                if (stats != null) {
                    num += stats.getValue();
                }
            }
        }
        return num;
    }

    private WolfKillGameInfo.Builder calcWolfKillGameInfo() {
        // TODO:后续改成判断玩法再填充这个字段
        UserAttr userAttr = getUserAttr();

        List<ItemType> listWolfKillAni = Arrays.asList(ItemType.ItemType_NR3E_ReportAnim, ItemType.ItemType_NR3E_AttackAnim,
                ItemType.ItemType_NR3E_MVPAnim);

        WolfKillGameInfo.Builder wolfKillGameInfo = WolfKillGameInfo.newBuilder();
        // 狼人身份专精点数
        wolfKillGameInfo.setRoleInfoPoints(getWolfKillMgr().getRoleInfoTotalPoints());
        // 超凡分
        wolfKillGameInfo.setHyperCoreScore(userAttr.getWolfKillInfo().getHyperCoreScore());
        // 大乱斗上局所选项
        wolfKillGameInfo.setBrawlLastSelected(userAttr.getWolfKillInfo().getBrawlLastSelected());

        HashSet<ItemType> needAllItemType = new HashSet<>();
        List<Integer> excludeSelf = new ArrayList<>();
        excludeSelf.addAll(MiscConf.getInstance().getMiscConf().getDressDefaultItemsList());

        for (ItemType itemType : listWolfKillAni) {
            DressItemInfo dressReportItemInfo = userAttr.getPlayerPublicEquipments().getDressItemInfo()
                    .get(itemType);

            if (dressReportItemInfo != null) {
                // 过期道具替换
                List<Item> listExpired = getItemManager().delExpiredItemId(dressReportItemInfo.getItemId());
                if (!listExpired.isEmpty()) {
                    //
                    LOGGER.debug("WolfKill listExpired:{}", listExpired);
                    dressReportItemInfo = userAttr.getPlayerPublicEquipments().getDressItemInfo()
                            .get(itemType);
                }

                ResWolfKillDecoration.WolfKillResDecorationAniItem aniItem = WolfKillDecorationAni.get(dressReportItemInfo.getItemId());
                if (aniItem == null) {
                    LOGGER.error("WolfKill dressItemId:{}", dressReportItemInfo.getItemId());
                    continue;
                }
                LOGGER.debug("WolfKill aniItem itemId:{}", aniItem.getId());
                if (itemType == ItemType.ItemType_NR3E_ReportAnim) {
                    wolfKillGameInfo.setEquipReportAni(dressReportItemInfo.getItemId());
                    if (aniItem.getIsRandom() == 1) {
                        wolfKillGameInfo.setEquipReportAniIsRandom(1);
                        needAllItemType.add(itemType);
                        excludeSelf.add(aniItem.getId());
                    } else {
                        wolfKillGameInfo.addAllReportAni(aniItem.getId());
                    }
                } else if (itemType == ItemType.ItemType_NR3E_AttackAnim) {
                    wolfKillGameInfo.setEquipAttackAni(dressReportItemInfo.getItemId());
                    if (aniItem.getIsRandom() == 1) {
                        wolfKillGameInfo.setEquipAttackAniIsRandom(1);
                        needAllItemType.add(itemType);
                        excludeSelf.add(aniItem.getId());
                    } else {
                        wolfKillGameInfo.addAllAttackAni(aniItem.getId());
                    }
                } else if (itemType == ItemType.ItemType_NR3E_MVPAnim) {
                    wolfKillGameInfo.setEquipMvpAni(dressReportItemInfo.getItemId());
                    if (aniItem.getIsRandom() == 1) {
                        wolfKillGameInfo.setEquipMvpAniIsRandom(1);
                        needAllItemType.add(itemType);
                        excludeSelf.add(aniItem.getId());
                    } else {
                        wolfKillGameInfo.addAllMvpAni(aniItem.getId());
                    }
                }
            }
        }

        if (!needAllItemType.isEmpty()) {
            // 限时动画不参与随机
            // 20250218, 修改限时动画参与随机
            Map<ItemType, List<Integer>> itemTypes = getItemManager().getItemsByItemTypeWithTypeExcludeItems(needAllItemType,
                    excludeSelf);
            Map<ItemType, Integer> mapDefault = getWolfKillDefaultAni();
            for (ItemType itemType : listWolfKillAni) {
                if (needAllItemType.contains(itemType)) {
                    if (itemTypes.get(itemType) != null) {
                        if (itemType == ItemType.ItemType_NR3E_ReportAnim) {
                            wolfKillGameInfo.addAllAllReportAni(itemTypes.get(itemType));
                        } else if (itemType == ItemType.ItemType_NR3E_AttackAnim) {
                            wolfKillGameInfo.addAllAllAttackAni(itemTypes.get(itemType));
                        } else if (itemType == ItemType.ItemType_NR3E_MVPAnim) {
                            wolfKillGameInfo.addAllAllMvpAni(itemTypes.get(itemType));
                        }
                    } else {
                        // 装备默认动画
                        if (itemType == ItemType.ItemType_NR3E_ReportAnim) {
                            wolfKillGameInfo.addAllReportAni(mapDefault.get(itemType));
                        } else if (itemType == ItemType.ItemType_NR3E_AttackAnim) {
                            wolfKillGameInfo.addAllAttackAni(mapDefault.get(itemType));
                        } else if (itemType == ItemType.ItemType_NR3E_MVPAnim) {
                            wolfKillGameInfo.addAllMvpAni(mapDefault.get(itemType));
                        }
                    }
                }
            }
        }

        //表情，只需要itemId，不需要位置
        MapAttrObj<Integer, Interaction> wolfKillInteractions = userAttr.getWolfKillInteractions();
        for (Interaction interaction : wolfKillInteractions.values()) {
            if (interaction.getItemUuid() == 0) {
                continue;
            }
            Item item = getItemManager().getItemByUUID(interaction.getItemUuid());
            if (item != null) {
                wolfKillGameInfo.addEquipEmoji(item.getItemId());
            }
        }

        // 判断背包里的职业
        List<Integer> unlockIds = new ArrayList<>();
        unlockIds.addAll(WolfKillVocation.getInstance().getUnlockList());

        Map<Integer, ResNR3E3Vocation.NR3E3VocationData> vocationMapData = WolfKillVocation.getInstance().getLockMap();
        for (Map.Entry<Integer, ResNR3E3Vocation.NR3E3VocationData> vocationMapItem : vocationMapData.entrySet()) {
            ResNR3E3Vocation.NR3E3VocationData vocationData = vocationMapItem.getValue();
            if (!bagManager.isItemsEnough(
                    vocationData.getIdentityItemUnlock(), 1, ItemChangeReason.ICR_WereWolfSetSideIdentity)) {
            } else {
                unlockIds.add(vocationMapItem.getKey());
            }
        }

        // 过滤掉服务器下架的职业，即使解锁
        for (Integer unlockVocationId : unlockIds) {
            try {
                Map<Integer, WolfKillVocationTime> nr3e3VocationConfig = WujiConfigMgr.getNr3e3VocationConfig();
                WolfKillVocationTime wolfKillVocationTime = nr3e3VocationConfig.get(unlockVocationId);
                if (wolfKillVocationTime != null) {
                    long nowTime = Framework.currentTimeMillis() / 1000;
                    if (nowTime < wolfKillVocationTime.getStartTime()) {
                        continue;
                    }
                    if (nowTime > wolfKillVocationTime.getEndTime()) {
                        continue;
                    }
                }
                wolfKillGameInfo.addUnlockVocationIdList(unlockVocationId);
            } catch (Exception e) {
                LOGGER.error("WolfKillVocationMsgHandler parse time error:{}", e);
            }
        }

        //珍宝系统, inUse的都下发
//        wolfKillGameInfo.clearEquipTreasure();
        for (Integer treasureId : userAttr.getWolfKillInfo().getTreasureEquipInfo().keySet()) {
            WolfKillTreasureEquipInfo wolfKillTreasureEquipInfo = userAttr.getWolfKillInfo().getTreasureEquipInfo(treasureId);
            if (wolfKillTreasureEquipInfo.getInUse()) {
                wolfKillGameInfo.addEquipTreasure(treasureId);
            }
        }
        // 珍宝等级
//        int itemId = MiscConf.getInstance().getMiscConf().getWolfKillTreasureItemId();
//        if (itemId>0){
//            long itemNum = bagManager.getMoneyNum(itemId);
//            List<Long> levelAndNum = getWolfKillRealTreasureLevel(itemNum, getClientVersion64());
//            wolfKillGameInfo.setTreasureLevel(levelAndNum.get(0).intValue());
//        }

        if (getWolfKillMgr().getWolfKillInfo().getUid() > 0) {
            // 狼人杀屏蔽身份
            for (var data : getWolfKillMgr().getWolfKillInfo().getShieldVocation().getDataList()) {
                for (var vocationId : data.getVocationListList()) {
                    if (vocationId > 0) {
                        wolfKillGameInfo.addShieldVocationIdList(vocationId);
                    }
                }
            }

            // 狼人月卡
            wolfKillGameInfo.setMonthCardLevel(getWolfKillMgr().getMonthCardLevel());
            wolfKillGameInfo.setMonthCardEndTs(getWolfKillMgr().getMonthCardEndTs());
        }

        wolfKillGameInfo.setTreasureLevel(userAttr.getWolfKillInfo().getLv());
        LOGGER.debug("wolfKillGameInfo:{}", () -> wolfKillGameInfo);

        return wolfKillGameInfo;
    }


    public AttrScenePlayerPublicInfo.proto_ScenePlayerPublicInfo.Builder getScenePlayerExtraInfo() {
        AttrScenePlayerPublicInfo.proto_ScenePlayerPublicInfo.Builder extraInfo =
                AttrScenePlayerPublicInfo.proto_ScenePlayerPublicInfo.newBuilder();
        extraInfo.setName(getName());
//        extraInfo.setSkin(getDressUpMgr().getCurrentSkinStr());
        extraInfo.setFace(getUserAttr().getPlayerPublicProfileInfo().getProfile());
        extraInfo.setPrivilegeLevel(getUserAttr().getPlayerPublicGameSettings().getPrivilegeLevel());
        extraInfo.setPrivilegeSwitch(getUserAttr().getPlayerPublicGameSettings().getPrivilegeSwitch());
        extraInfo.setAvatarInfo(getUserAttr().getPlayerPublicProfileInfo().getPlatAvatarInfo());
        extraInfo.setGender(getUserAttr().getPlayerPublicProfileInfo().getGender());
        extraInfo.setOpenId(getOpenId());
        extraInfo.setRoomStatus(getUserAttr().getRoomInfo().getStatus()); // @Deprecated
        extraInfo.setRoomId(getCurrentRoomId()); // @Deprecated
        extraInfo.setBenefitCardEnable(getUserAttr().getPlayerPublicGameSettings().getBenefitCardEnable());

        proto_ScenePlayerBasicInfo.Builder basicInfo = proto_ScenePlayerBasicInfo.newBuilder();
        basicInfo.setLobbySceneId(getUserAttr().getSceneInfo().getLobbySceneId());
        basicInfo.setLobbyMapId(getUserAttr().getSceneInfo().getLobbyMapId());
        extraInfo.setBasicInfo(basicInfo);
        return extraInfo;
    }

    /**
     * 获得玩家角色名字
     *
     * @return
     */
    public String getName() {
        return userAttrMgr.getUserAttr().getPlayerPublicProfileInfo().getNickname();
    }

    public String getPlatName() {
        return userAttrMgr.getUserAttr().getPlayerProfileInfo().getOriginPlatNickname();
    }

    // 头像
    public String getProfile() {
        return userAttrMgr.getUserAttr().getPlayerPublicProfileInfo().getProfile();
    }

    /**
     * 获得玩家性别
     *
     * @return
     */
    public int getGender() {
        return userAttrMgr.getUserAttr().getPlayerPublicProfileInfo().getGender();
    }

    public HeadFrame getHeadFrame() {
        HeadFrame.Builder retBuilder = HeadFrame.newBuilder();
        DressItemInfo dressItemInfo = userAttrMgr.getUserAttr().getPlayerPublicEquipments()
                .getDressItemInfo(ItemType.ItemType_Frame);
        if (dressItemInfo != null) {
            retBuilder.setDressUpType(ItemType.ItemType_Frame)
                    .setItemId(dressItemInfo.getItemId())
                    .setItemUUID(dressItemInfo.getItemUUID());
        }
        return retBuilder.build();
    }

    public NamePlate getNamePlate() {
        NamePlate.Builder retBuilder = NamePlate.newBuilder();
        DressItemInfo dressItemInfo = userAttrMgr.getUserAttr().getPlayerPublicEquipments()
                .getDressItemInfo(ItemType.ItemType_NamePlate);
        if (dressItemInfo != null) {
            retBuilder.setDressUpType(ItemType.ItemType_NamePlate)
                    .setItemId(dressItemInfo.getItemId())
                    .setItemUUID(dressItemInfo.getItemUUID());
        }
        return retBuilder.build();
    }

    public PlayerDressItemInfo getPortrait() {
        PlayerDressItemInfo.Builder retBuilder = PlayerDressItemInfo.newBuilder();
        DressItemInfo dressItemInfo = userAttrMgr.getUserAttr().getPlayerPublicEquipments()
                .getDressItemInfo(ItemType.ItemType_Portrait);
        if (dressItemInfo != null) {
            retBuilder.setDressUpType(ItemType.ItemType_Portrait)
                    .setItemId(dressItemInfo.getItemId())
                    .setItemUUID(dressItemInfo.getItemUUID());
        }
        return retBuilder.build();
    }

    public TcaplusTable.Record getData() {
        return userAttrMgr.getDbPlayer();
    }

    public void clearSession() {
        if (session != null) {
            session.setDisconnect();
            session = null;
            //GamesvrInfo.getInstance().addLogin();
            getPlayerStateMgr().circulationPlayerState(PlayerStateAction.LOGOUT);
        }
    }

    public Session getSession() {
        return session;
    }

    /**
     * 设置连接，并调用login
     *
     * @param s 会话
     */
    public void bindSession(Session s, PayInfo payInfo) {
        //这个不应该有
        s.markStopWatch("begin bindSession");
        if (!s.checkState(STATE_CONNECTED)) {
            NKErrorCode.SessionStateError.throwError(Level.TRACE, "session state error " + s.getState().name());
            return;
        }
        //如果登陆过程中已经被踢线了，那么直接踢了
        PlayerLogin.PlayerLoginLock playerLoginLock = PlayerLogin.getPlayerLoginLock(s);
        if (playerLoginLock != null && playerLoginLock.getMarkKick()) {
            playerLoginLock.throwKickCodeWhenLogin();
            return;
        }

        s.markStopWatch("bindSession.playerLoginLock");
        //会在tconndmgr的线程里清理无效session所以这里同步一下
        if (s.tryLock()) {
            try {
                if (!s.checkState(STATE_CONNECTED)) {
                    NKErrorCode.SessionStateError.throwError(Level.TRACE, "session state error " + s.getState().name());
                    return;
                }
                TconndManager.getInstance().clearDeleteCheck(s);
                session = s;
                //GamesvrInfo.getInstance().addLogin();
                session.setUid(getUid());
                TconndManager.getInstance().mapOpenidSession(session);
                TconndManager.getInstance().addUidSessionMap(session);
                session.setState(Session.State.STATE_LOGIN);
                if (payInfo != null) {
                    if (payInfo.getPayToken().length() > 0) {
                        session.setPayToken(payInfo.getPayToken());
                        LOGGER.error("login payToken null,uid:{}", s.getUid());
                        Monitor.getInstance().add.total(MonitorId.attr_login_pay_token_null_count, 1);
                    }
                    if (!payInfo.getPayPf().isEmpty()) {
                        session.setPayPf(payInfo.getPayPf());
                        LOGGER.error("login pf null,uid:{}", s.getUid());
                        Monitor.getInstance().add.total(MonitorId.attr_login_pay_pf_null_count, 1);
                    }
                    if (!payInfo.getPayPfKey().isEmpty()) {
                        session.setPayPfKey(payInfo.getPayPfKey());
                    }
                }
            } finally {
                s.unlock();
            }

            login();
        } else {
            LOGGER.info("lock failed, openId:{} sessionId:{} uid:{}", s.getOpenid(), s.getSessionId(), getUid());
            NKErrorCode.LockFailed.throwError(Level.ERROR, "session removed for timeout in connection build");
        }
    }

    public PlayerGameAttrMgr getUserAttrMgr() {
        return getModule(GameModuleId.GMI_LetsGoAttr);
    }

    //关卡评价
    public PlayerGameLevelEstimationMgr getPlayerGameLevelEstimationMgr() {
        return playerGameLevelEstimationMgr;
    }

    public SeasonReviewMgr getSeasonReviewMgr() {
        return seasonReviewMgr;
    }

    public PlayerRecentPlayMgr getRecentPlayMgr() {
        return recentPlayMgr;
    }

    public PlayerFarmReturningMgr getFarmReturningMgr() {
        return farmReturningMgr;
    }

    public PlayerGeneralRedDotMgr getPlayerGeneralRedDotMgr() {
        return generalRedDotPlayerMgr;
    }

    public PlayerChaseMgr getChaseMgr() {
        return chaseMgr;
    }

    /**
     * 获取服务离线标记
     *
     * @return
     */
    public boolean isSvrOffline() {
        return ServerEngine.getInstance().isOfflining();
    }

    /**
     * 是否放在PlayerModuleBase里面更合理点? 识别player是不是有pending rpc或job
     * 这个接口在server下线时候用, 允许一定程度的服务降级
     */
    public boolean canBeEvicted() {
        if (!isOnline()) {
            return true;
        }
        if (playerStateMgr.isMatching()) {
            return false;
        }

        // 在服务处于offline状态的时候, 处于对局状态的用户先不踢除
        boolean svrOfflinePlayerKickBattleSwitch = PropertyFileReader.getRealTimeBooleanItem(
                "offline_player_kick_battle_switch", false);
        if (svrOfflinePlayerKickBattleSwitch && playerBattleMgr.isInBattle()) {
            return false;
        }

        // 目前无法获取到非串行的job数量
        int serialJobCount = CurrentExecutorUtil.getSerialJobCount(LocalServiceType.LOCAL_GAMESVR_PLAYER_SERVICE,
                getUid());
        return serialJobCount <= 0;
    }

    /**
     * 开始更新
     *
     * @return boolean
     *///只在暴力踢线,或者停服时用
    public boolean kickUpdate() {
        if (!checkCanUpdate()) {
            return true;
        }
        if (!playerKickUpdated.compareAndSet(false, true)) {
            return true;
        }
        userAttrMgr.collectDirtyToDb();
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                LOGGER.debug("kickupdate openid {} uid {}", getOpenId(), getUid());
                TcaplusManager.TcaplusReq req = TcaplusUtil.newUpdateReq(userAttrMgr.getDbPlayerBuilder())
                        .setVersion(-1)
                        .setResultFlag(1).setChangeField(userAttrMgr.getDbPlayer().getChange());
                userAttrMgr.getDbPlayer().clearChange();
                TcaplusManager.getInstance().tcaplusSend(req);
                return null;
            }, "kickUpdate", true);
        } catch (NKCheckedException e) {
            LOGGER.error("kickupdate job run fail", e);
        }
        return true;
    }

    private boolean needUpdateAll() {
        return userAttrMgr.needUpdateAll();
    }

    private void sizeCheck() {
        if (checkerRunning.compareAndSet(false, true)) {
            JolTool.runJob(sizeChecker);
        }
    }

    /**
     * 全量存盘，低频调用
     */
    public boolean forceFullUpdateDb() {
        return updateQueueEntry.updateDb(true, true);
    }

    /**
     * idip需要一个同步的回写接口
     */
    public void idipRemovePlayer() {
        userAttrMgr.collectDirtyToDb();
        updateDb(true);
        PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.IdipRemove);
    }

    public boolean loginCacheVersionCheck() {
        setCheck(true);
        getUserAttrMgr().getDbPlayerBuilder().setLoginCheckTime(DateUtils.currentTimeMillis());
        return updateDb(true);
    }

    /**
     * 更新数据库
     *
     * @param needWait needWait
     * @return boolean
     */
    public boolean updateDb(boolean needWait) {
        sizeCheck();
        recordProcess();

        //noinspection ConstantConditions
        if (userAttrMgr.getDbPlayer().getChange().isEmpty()) {
            LOGGER.debug("player {} openid {} not full write and change field is empty", getUid(), getOpenId());
            //没有变更，无需回写
            return true;
        }

        final boolean isall = needUpdateAll();

        LOGGER.debug("player {} {} updateDb, need wait:{}", getOpenId(), getUid(), needWait);
        return updateQueueEntry.updateDb(isall, needWait);
    }

    /**
     * 热加载
     */
    public void reloadCheck() {
        int serverReloadCnt = GSEngine.getSpecInstance().getPlayerService().getTotalReloadCount();
        if (reloadCnt < serverReloadCnt) {
            forEachModule(m -> {
                try {
                    m.onReload();
                } catch (Exception e) {
                    LOGGER.error("on reload module {} failed. e:", m.getModuleId(), e);
                    Monitor.getInstance().add.total(MonitorId.attr_modules_after_load_exception, 1);
                }
                return true;
            }, "PlayerModuleContainerOnReload");
            reloadCnt = serverReloadCnt;
        }
    }

    /**
     * 加载
     */
    public void load() {

        try {
            int loadContextId = LoginStatMgr.startStep(getOpenId(), LoginStepEnum.LS_LOAD, LoginStepEnum.LS_LOGIN);
            setLoading();
            modulesPrepareLoad();
            modulesOnLoad();
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                int afterLoadContextId = LoginStatMgr.startStep(getOpenId(), LoginStepEnum.LS_AFTER_LOAD, 0);
                modulesAfterLoad();
                LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_AFTER_LOAD, afterLoadContextId);
                //getBufferManager().prepareClientAllBuffer();
                return null;
            }, "load", true);
            setLoaded();
            LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_LOAD, loadContextId);
        } catch (Exception e) {
            setInvalidate();
            LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_LOAD, "load exception");
            LOGGER.error("player openId {} uid {} modulesAfterLoad error, have error on modulesAfterLoad ", getOpenId(),
                    getUid(), e);
            throw new NKRuntimeException(NKErrorCode.UnknownError, "Wrapper exception", e);
        }

    }

    public void onRegister() {
        setRegistering();
        LOGGER.debug("player {} enter onRegister", getUid());
        getUserAttr().setUid(getUid());
        getUserAttr().getPlayerPublicBasicInfo().setRegisterTimeMs(Framework.currentTimeMillis());

        if (modulesPrepareRegister() != 0) {
            LOGGER.error("player openId {} uid {} register error, have error on prepare register", getOpenId(),
                    getUid());
            NKErrorCode.UnknownError.throwError("UnknownError");
        }
        if (modulesOnRegister() != 0) {
            LOGGER.error("player openId {} uid {} register error, have error on register", getOpenId(), getUid());
            NKErrorCode.UnknownError.throwError("UnknownError");
        }
    }

    /**
     * 处理客户端上报邀请者
     *
     * @param inviterUid
     * @param activityId 需要运行中的活动ID
     */
    public void setInviter(long inviterUid, int activityId, boolean isRecall) {
        var activityCfg = ActivityMainConfig.getInstance().get(activityId);
        if (activityCfg == null || !ActivityTimeUtil.checkActivityOpen(activityCfg)) {
            LOGGER.error("player {} activity {} not open", getUid(), activityId);
            return;
        }
        flushPlayerPublic(); // 刷新playerPublic因为InviteRegister要用
        if (isRecall) {
            getUserAttr().getPlayerProfileInfo().setInviteRecallUid(inviterUid);
        } else {
            if (getUserAttr().getPlayerProfileInfo().getInviteRegisterUid() > 0) {
                return;
            }
            getUserAttr().getPlayerProfileInfo().setInviteRegisterUid(inviterUid);
        }
        // 被邀请注册的玩家 注册&升级的时候发
        InviteRegisterInteraction.sendInviterRegister(
                inviterUid, getUid(),
                getUserAttr().getPlayerProfileInfo().getRegisterDeviceId(),
                isRecall ? DateUtils.currentTimeMillis() : getRegisterTime(),
                getLevel(), activityCfg.getActivityType().getNumber(), activityId, isRecall, true);
    }

    /**
     * 注册后
     */
    public void afterRegister() {
        // 注册事件
        // PlayerPublicDao.flushPlayerPublicFull(getUserAttr());
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                modulesAfterRegister();
                getUserAttr().getBasicInfo().setIsRegistering(false);

                // QQ成就上报
                AchieveBody achieveBody = new AchieveBody(getUid(), getPlatId())
                        .AddAchievementParam(AchiParamType.APT_PlayerLevel, getLevel(), 1, 0)
                        .AddAchievementParam(AchiParamType.APT_RegisterTime, getRegisterTime() / 1000, 1, 0)
                        .AddAchievementParam(AchiParamType.APT_RegisterChannel,
                                getUserAttr().getPlayerPublicBasicInfo().getRegChannelDis(), 0, 0);
                sendAchievementReport(achieveBody);

                PlayerRefMgr.getInstance().afterPlayerRegister(this);
                return null;
            }, "afterRegister", true);

        } catch (NKCheckedException e) {
            throw new NKRuntimeException(NKErrorCode.UnknownError, "NKCheckedException wrapper", e);
        }
    }

    public void sendAchievementReport(AchieveBody body) {
        try {
            CurrentExecutorUtil.runJob(() -> {
                MSDKAchievementReporter.getInstance().SendAchievement(
                        getPlatId(),
                        getAccountType().getNumber(),
                        getOpenId(),
                        getUserAttr().getPlayerProfileInfo().getAccessToken(),
                        body);

                return null;
            }, "sendAchievementReport", true);
        } catch (NKCheckedException e) {
            LOGGER.error("sendAchievementReport failed", e);
        }
    }

    //成功setsession以后会执行
    private void login() {
        getSession().markStopWatch("player.login");
        BlueGreenDeploymentUtil.getInstance().checkBlueGreenMark(getUid()); //检查下灰度数据是否路由到了非灰度节点
        BlueGreenDeploymentUtil.getInstance().setBlueGreenMark2Redis(getUid());
        getPlayerStateMgr().login();
        modulesPrepareLogin();
        getSession().markStopWatch("player.modulesPrepareLogin");
        try {
            // 发送user attr
            LOGGER.debug("player openId {} uid {} send full attr ntf on login", getOpenId(), getUid());
            getUserAttrMgr().syncFullToClient();
        } catch (Exception e) {
            LOGGER.error("\n", e);
        }
        getSession().markStopWatch("player.syncFullToClient");
        afterLoginMask = 0;
        modulesOnLogin();
        getSession().markStopWatch("player.modulesOnLogin");
        updateGamePlayPublicInfo(true, null);
        //getUserAttr().getBasicInfo().setLimitNotifyTimes(true);
    }

    /**
     * 登陆时，回包前的操作
     *
     * @throws NKCheckedException 异常
     */
    public void onReLogin() {
        setReLogin(true);
        getPlayerOnlineMgr().onReLogin();
        getPlayerOnlineMgr().updatePlayerOnlineTableOnlineState(PlayerStateType.PST_Online);
    }

    public TpsSpan startPlayerSpan(String name) {
        TpsTracer tracer = TpsTracerMgr.getInstance().getDefaultTracer();
        TpsSpan span = tracer.startClientSpan(name);
        span.makeCurrent();
        return span;
    }

    /**
     * 登陆后的操作
     *
     * @param todayFirstLogin 今天首次登陆时间
     * @throws NKCheckedException 异常
     */
    public void afterLogin(boolean todayFirstLogin) throws NKCheckedException {
        long uid = session.getUid();
        //异步执行聊天注册
        //GsLocalChatService.get().playerLogin(uid);
        // OpenIdToUidDao.updateLoginTime(getOpenId(), getPlatId(), getUid());

        TpsSpan lastSpan = TpsTracer.currentSpan();
        CurrentExecutorUtil.runJob((Callable<Void>) () -> {
            TpsTracer.makeSpanCurrent(lastSpan);

            TpsSpan curSpan = startPlayerSpan("player.afterLogin");
            int afterLoginContextId = LoginStatMgr.startStep(getOpenId(),
                    LoginStepEnum.LS_AFTER_LOGIN, "todayFirstLogin:" + todayFirstLogin, 0);


            int loadOtherDataContextId = LoginStatMgr.startStep(getOpenId(),
                    LoginStepEnum.LS_LOAD_OTHER_DATA, "", afterLoginContextId);

            boolean doConcurrently = PropertyFileReader.getRealTimeBooleanItem("module_concurrent", true);
            if (doConcurrently) {
                // 并发执行，会等待全部执行完成才继续
                try {
                    AsyncCallableGroup acg = new AsyncCallableGroup();
                    acg.addCallable(() -> {
                        loadPlayerRelation();
                        return null;
                    });
                    acg.addCallable(() -> {
                        loadPlayerCommodityBuyTimesSync();
                        return null;
                    });
                    acg.addCallable(() -> {
                        getPlayerStateMgr().enterTeamShowSceneIfNeed();
                        return null;
                    });
                    acg.addCallable(() -> {
                        getAbTestMgr().teamShowABTestInfoRefreshWhileLogin();
                        return null;
                    });
                    acg.getAll(5000);
                } catch (NKCheckedException e) {
                    LOGGER.error("afterLogin ", e);
                }
            } else {
                loadPlayerRelation();
                loadPlayerCommodityBuyTimesSync();
                getPlayerStateMgr().enterTeamShowSceneIfNeed();
                getAbTestMgr().teamShowABTestInfoRefreshWhileLogin();
            }

            LoginStatMgr.endStep(getOpenId(),
                    LoginStepEnum.LS_LOAD_OTHER_DATA, loadOtherDataContextId);

            int loginEventContextId = LoginStatMgr.startStep(getOpenId(),
                    LoginStepEnum.LS_DISPATCH_LOGIN_EVENT, afterLoginContextId);

            // 登录事件
            new PlayerLoginEvent(this).dispatch();

            LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_DISPATCH_LOGIN_EVENT, loginEventContextId);

            int moduleAfterLoginContextId = LoginStatMgr.startStep(getOpenId(),
                    LoginStepEnum.LS_MODULE_AFTER_LOGIN, afterLoginContextId);
            if (doConcurrently) {
                modulesAfterLoginConcurrently(todayFirstLogin);
            } else {
                modulesAfterLogin(todayFirstLogin);
            }
            getPlayerEventManager().dispatch(new LoginEvent(getConditionMgr()).setPlayerPlat(getLoginPlat()));
            LoginStatMgr.endStep(getOpenId(),
                    LoginStepEnum.LS_MODULE_AFTER_LOGIN, moduleAfterLoginContextId);

            int beforeAfterLoginNtfContextId = LoginStatMgr.startStep(getOpenId(),
                    LoginStepEnum.LS_BEFORE_AFTER_LOGIN_NTF, afterLoginContextId);
            try {
                // 指令系统需要依赖所有的afterLogin初始化完成, 所以从模块中提取到这里执行
                getInteractionMgr().loadInteractions();
                getInteractionMgr().interact(PlayerInteraction.InteractionExeStage.IES_AfterLogin);
            } catch (Exception e) {
                LOGGER.error("player {} interact() exception:{}", getUid(), e);
            }
            // 未读消息依赖InteractionMgr里的PII消息加载，因此拆出来放在InteractionMgr.interact()之后
            List<TcaplusDb.PlayerInteractionTable> unreadTables =
                    getInteractionMgr()
                            .getUnHandledInstructions()
                            .values().stream()
                            .filter(x -> x.getBody().getInstruction()
                                    == PlayerInteraction.PlayerInteractionInstruction.PII_OFFLINE_ONLY_INSTRUCTION_CHAT_SEND)
                            .collect(Collectors.toList());
            // 指令系统batchget出来的数据按照时间顺序排序，需要倒序
            Collections.reverse(unreadTables);
            getPlayerChatManager().ntfUnReadMsg(unreadTables);

            uploadLogCheck();
            qaInvestListCheckAfterLogin();
            playerSetHeartBeat(true);
            getUserAttr().getNewYearPilotInfo().setEnabled(NewYearPilotEntranceData.getInstance().getEntranceEnabled());
            LoginStatMgr.endStep(getOpenId(),
                    LoginStepEnum.LS_BEFORE_AFTER_LOGIN_NTF, beforeAfterLoginNtfContextId);

            sendPlayerAfterLoginNtf(PLAYER_AFTER_LOGIN_PROCESS_MASK, "afterLogin", curSpan);
            // 推送某些场景较少跟服务器交互的封禁信息
            sendAfterLoginBanNtf();

            // 推送玩法解锁相关信息  ps: 整合到了 login_s2c 中
            if (!isOptimizedLogin) {
                // 如果是优化前的客户端登录，需要这个推送
                sendFeatureOpenNtf();
            }

            // 更新玩家已绑定渠道列表(从msdk 拉取)
            UpdateAccountBindList();
            loadFarmBeBlockedData();
            getUserAttrMgr().initModPublicInfo();

            // 再次尝试同步资源
            PlayerResPatcher.checkLogin(this);
            LOGGER.info("LoginMsg[{}|{}]: afterlogin runjob ok, shortUid:{}", getOpenId(), getUid(), getShortUid());

            // 脑力大人发送跑马灯
            IntellectualActivityMgr.getInstance().playerLoginCheckAndSend(uid);

            LoginStatMgr.endStep(getOpenId(),
                    LoginStepEnum.LS_AFTER_LOGIN, afterLoginContextId);

            // 通知ActivitySvr玩家登陆
            ActivityMsgHelper.sendPlayerLoginMsgToActivitySvr(this);
            // 用户登录触发反外挂用户上报
            getPlayerCreditScoreMgr().antiCheatLoginProcess();

            return null;
        }, "afterLogin", true);

//        flushPlayerPublic();

        SerializedMessage serializedCsNtf = null;
        if (ConfigUtils.isClientKVForLoginPlat()) {
            serializedCsNtf = ClientKVConf.getInstance().getSerializedCsNtf(getLoginPlat(),getPlatId());
        } else {
            serializedCsNtf = ClientKVConf.getInstance().getSerializedCsNtf();
        }

        if (serializedCsNtf != null) {
            sendNtfMsg(MsgTypes.MSG_TYPE_CLIENTKVCONFNTF, serializedCsNtf);
        }
    }

    private void afterLoginFinish() {
        try {
            //异步执行after login
            afterLoginFinish(isTodayFirstLogin());
        } catch (Exception e) {
            LOGGER.error("\n", e);
        }
    }

    private void afterLoginFinish(boolean todayFirstLogin) throws NKCheckedException {
        //异步执行聊天注册
        //GsLocalChatService.get().playerLogin(uid);
        CurrentExecutorUtil.runJob((Callable<Void>) () -> {
            int afterLoginFinishContextId = LoginStatMgr.startStep(getOpenId(),
                    LoginStepEnum.LS_AFTER_LOGIN_FINISH, 0);
            getInteractionMgr().interact(PlayerInteraction.InteractionExeStage.IES_AfterLoginFinish);
            modulesAfterLoginFinish(todayFirstLogin);
            getInteractionMgr().interact(PlayerInteraction.InteractionExeStage.IES_AfterLoginFinishDone);
            // QQ成就上报
            AchieveBody achieveBody = new AchieveBody(getUid(), getPlatId())
                    .AddAchievementParam(AchiParamType.APT_PlayerLevel, getLevel(), 1, 0)
                    .AddAchievementParam(AchiParamType.APT_LastLoginTime,
                            getLoginTime() / 1000, 1, 0)
                    .AddAchievementParam(AchiParamType.APT_LoginChannel,
                            getUserAttr().getPlayerPublicBasicInfo().getChannelId(), 1, 0);
            if (!getName().isEmpty()) {
                achieveBody.AddAchievementParam(AchiParamType.APT_RoleName, getName(), 1, 0)
                        .AddAchievementParam(AchiParamType.APT_NickName, getName(), 1, 0);
            }
            sendAchievementReport(achieveBody);
            fixStrangerFollowSetting();

            DiscoverManager.getInstance().initPlayerIsClicked(this);

            LOGGER.info("LoginMsg[{}|{}]: afterLoginFinish runjob ok", getOpenId(), getUid());

            LoginStatMgr.endStep(getOpenId(),
                    LoginStepEnum.LS_AFTER_LOGIN_FINISH, afterLoginFinishContextId);

            return null;
        }, "afterLoginFinish", true);
        // 在测试环境检查DS版本列表
        if (ServerEngine.getInstance().isIdcTest()) {
            int versionGroupId = getLobbyVersionGroupId();
            if (!isGroupIDValid(versionGroupId)) {
                var reqStrVer = VersionUtil.decodeClientVersion(getClientVersion64());
                String dsVerInfo = "你的版本号：" + reqStrVer + " " + versionGroupId + "\r\n";
                ntfPlayerDsInvalidTips(dsVerInfo + getStringDsVer());
            }
        }
    }

    public void clientDone() {
        try {
            LOGGER.info("LoginMsg[{}|{}]: clientDone", getOpenId(), getUid());
            if (!isReLogin()) {
                PlayerLogin.sendWechatLoginNotice(getOpenId(), getUid(), getSession().getIpAddr(), VersionUtil.decodeClientVersion(getClientVersion64()));
            }
            if (ServerEngine.getInstance().canUseGmCmd()) {
                GMCommandMsgHandler.sendAllGMInfoNtf(this);
            }
            // 登陆的最后再刷新一下keepAlive时间
            getPlayerOnlineMgr().refreshLastKeepAliveTime();
            // 执行afterLoginFinish
            afterLoginFinish();
        } catch (Exception e) {
            LOGGER.error("\n", e);
        }
    }

    private void ntfPlayerDsInvalidTips(String dsErrInfo) {
        CsPlayer.PlayerNoticeMsgNtf.Builder ntf = CsPlayer.PlayerNoticeMsgNtf.newBuilder()
                .setType(PlayerNoticeMsgType.PNT_TEST_ENV_DS_VER_FAIL)
                .setNotice(dsErrInfo)
                .setMsgQueued(true);
        sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, ntf);
    }

    private boolean isGroupIDValid(int groupID) {
        var DSInfoList = ResLoader.getResHolder().getDsVerList();
        for (var ds : DSInfoList) {
            if (ds.value == groupID) {
                return true;
            }
        }
        return false;
    }

    private String getStringDsVer() {
        var dsList = ResLoader.getResHolder().getDsVerList();
        var dsInfo = new StringBuilder("\r\n【可用的DS版本】\r\n");
        int idx = 1;
        for (var ds : dsList) {
            dsInfo.append(ds.key).append("  ").append(ds.value);
            if (idx % 3 == 0) {
                dsInfo.append("\r\n");
            } else {
                dsInfo.append(",  ");
            }
            idx++;
        }
        return dsInfo.toString();
    }

    private void processCheck() {
        if (!isOnline()) {
            return;
        }
        long intervalTime = PropertyFileReader.getRealTimeLongItem("process_player_add_interval_time", 10000);
        if (processTime + intervalTime < Framework.currentTimeMillis()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("processCheck recordProcess,{}", getUid());
            }
            processTime = Framework.currentTimeMillis();
            recordProcess();
        }
    }

    // 每秒tick，里面逻辑必须轻量，逻辑较多或者可能有阻塞情况，必须用 runjob 方式异步执行
    public void tickPerSecond() {
        getPlayerOnlineMgr().tickPerSecond();
        BlueGreenDeploymentUtil.getInstance().setBlueGreenMark2Redis(getUid());
        processCheck();
        try {
            CurrentExecutorUtil.runJob(() -> {
                tlogCache.tickPerSecond();
                playerCreditScoreMgr.tickPerSecond();
                playerRoomMgr.tickPerSecond();
                playerGrayTagMgr.tickPerSecond();
                outputMgr.tickPerSecond();
                playerSnsManager.tickPerSecond();
                playerPersonalityMgr.tickPerSecond();
                playerChatManager.tickPerSecond();
                playerAigcNpcMgr.tickPerSecond();
                playerNr3e8Mgr.tickPerSecond();
                modMgr.tickPerSecond();
                return null;
            }, "tlogCacheTickPerSecond", true);
        } catch (NKCheckedException e) {
            LOGGER.error("tlogCacheTickPerSecond failed", e);
        }
    }

    public void addPlayerGameTime(PlayerGameTimeType gameTimeType, long addValue) {
        int preLoginDays = 0;
        if (gameTimeType.getNumber() == PlayerGameTimeType.PGTT_LoginDays.getNumber()) {
            preLoginDays = (int) getPlayerGameTime(PlayerGameTimeType.PGTT_LoginDays);
        }
        // 注意：请在设置前进行次操作，用于修复玩家切赛季后当前赛季的数据
        getSeasonFashionBattleDataMgr().addSeasonFashionGameTimes(gameTimeType, addValue);

        if (!getUserAttr().getPlayerPublicGameData().getPlayerGameTimes().containsKey(gameTimeType)) {
            getUserAttr().getPlayerPublicGameData().getPlayerGameTimes().put(gameTimeType, new PlayerGameTime());
        }
        getUserAttr().getPlayerPublicGameData().getPlayerGameTimes(gameTimeType).addValue(addValue);
        getFriendManager().addChangeField(PlayerPublicInfoField.PLAYER_GAME_TIMES);

        if (gameTimeType.getNumber() == PlayerGameTimeType.PGTT_LoginDays.getNumber() && clubMgr.enableOneClickJoinClub()) {
            LOGGER.debug("player:{} login days preLoginDays:{} add:{}", getUid(), preLoginDays, addValue);
            clubMgr.modifyClubScoreIfNecessary(preLoginDays, 0, true, false);
        }
    }

    public void setPlayerGameTime(PlayerGameTimeType gameTimeType, long setValue) {
        // 注意：请在设置前进行次操作，用于修复玩家切赛季后当前赛季的数据
        getSeasonFashionBattleDataMgr().setSeasonFashionGameTimes(gameTimeType, setValue);

        if (!getUserAttr().getPlayerPublicGameData().getPlayerGameTimes().containsKey(gameTimeType)) {
            getUserAttr().getPlayerPublicGameData().getPlayerGameTimes().put(gameTimeType, new PlayerGameTime());
        }
        getUserAttr().getPlayerPublicGameData().getPlayerGameTimes(gameTimeType).setValue(setValue);
        if (getUserAttr().getPlayerPublicGameData().getPlayerGameTimes(gameTimeType).isValueDirty()) {
            getFriendManager().addChangeField(PlayerPublicInfoField.PLAYER_GAME_TIMES);
        }
    }

    public long getPlayerGameTime(PlayerGameTimeType gameTimeType) {
        if (!getUserAttr().getPlayerPublicGameData().getPlayerGameTimes().containsKey(gameTimeType)) {
            return 0L;
        }
        return getUserAttr().getPlayerPublicGameData().getPlayerGameTimes(gameTimeType).getValue();
    }

    // 副玩法统计数据
    public long addPlayerGameTimesStat(int gameType, PlayerGameDataStatType dataType, PlayerGameDataStatDuration durationType, long value) {
        long oldValue = GameTimesStatUtil.getGameTimesStat(gameType, dataType, getUserAttr().getGameTimesStatistics(), false);
        long newValue = GameTimesStatUtil.addGameTimesStat(gameType, dataType, durationType, value, getUserAttr().getGameTimesStatistics());
        LOGGER.info("addPlayerGameTimesStat,player:{} {} {},gameType:{},dataType:{},durationType:{},value:{}->{}",
                getUid(), getOpenId(), getName(), gameType, dataType, durationType, oldValue, newValue);

        // arena hero需要独立统计数据
        getArenaMgr().getHeroUnlockStat().addGameTimesStat(gameType, dataType, durationType, value);

        GameTimesStatEventData.Builder eventDataBuilder = GameTimesStatEventData.newBuilder();
        eventDataBuilder.setGameType(gameType);
        eventDataBuilder.setType(dataType);
        eventDataBuilder.setOldValue(oldValue);
        eventDataBuilder.setNewValue(newValue);
        getPlayerEventManager().dispatch(new PlayerGameTimesStatEvent(this).setData(eventDataBuilder.build()));

        getPlayerEventManager().dispatch(new GameTimesStatEvent(getConditionMgr())
                .setGameTimesStat(eventDataBuilder.build()));
        return newValue;
    }

    // 获取统计次数
    // includeShardGameTypes,是否包含共享统计数据的副玩法
    public long getPlayerGameTimesStat(int gameType, PlayerGameDataStatType dataType, boolean includeShardGameTypes) {
        return GameTimesStatUtil.getGameTimesStat(gameType, dataType, getUserAttr().getGameTimesStatistics(), includeShardGameTypes);
    }

    private void getOngoingDsAddrInfo(DsOngoingAddrInfo.Builder dsAddrInfo) {
        // DS相关信息
        int enterDsContextId = LoginStatMgr.startStep(getOpenId(), LoginStepEnum.LS_ENTER_DS, 0);
        int goingDsContextId = LoginStatMgr.startStep(getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, enterDsContextId);
        LOGGER.info("player battle get ongoing ds address, openId:{} uid:{}", getOpenId(), getUid());
        // 触发保护逻辑，避免弱网重连玩家room未online
        getPlayerRoomMgr().sendRpcRoomPlayerOnlineIfNeed();
        long currBattleId = getUserAttr().getBattleInfo().getBattleid();
        long giveUpBattleId = getNumericAttrMgr().get(PlayerNumericAttrMgr.Key.GIVE_UP_BATTLE_ID);
        // coc玩法重登时强制退出和结算
        if (currBattleId > 0 && getUserAttr().getBattleInfo().getMatchType() == 1201) {
            getPlayerBattleMgr().giveUpCurrentBattle(G6Common.QuitBattleCode.QUIT_BATTLE_CODE_LEAVE_VALUE);
            LOGGER.info("BattleGetOngoingDsAddrMsgHandler coc forve give up.uid:{}, battleId:{}", getUid(),
                    currBattleId);
            return;
        }
        if (currBattleId == 0 || currBattleId == giveUpBattleId) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("BattleGetOngoingDsAddrMsgHandler currBattleId == 0 || currBattleId == " +
                                "giveUpBattleId, uid:{}, currBattleId {}, giveUpBattleId: {}, roomInfo: {}", getUid(),
                        currBattleId, giveUpBattleId, getUserAttr().getRoomInfo().toString());
            }
            LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, "noDs", goingDsContextId);
        } else {
            BattleService battleService = BattleService.get();
            if (battleService == null) {
                LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, "no battle service", goingDsContextId);
            } else {
                SsBattlesvr.RpcBattlePlayerOnlineReq.Builder ssReqMsg = SsBattlesvr.RpcBattlePlayerOnlineReq.newBuilder();
                ssReqMsg.setBallteId(getUserAttr().getBattleInfo().getBattleid());
                ssReqMsg.setUid(getUid());
                ssReqMsg.setVersionGroupId(getBattleVersionGroupId(getUserAttr().getBattleInfo().getMatchType()));
                try {
                    RpcResult<SsBattlesvr.RpcBattlePlayerOnlineRes.Builder> rpcResult = battleService.rpcBattlePlayerOnline(ssReqMsg);
                    int result = rpcResult.getData().getResult();
                    if (result == NKErrorCode.OK.getValue()) {
                        if (rpcResult.getData().hasBattleInfo() && rpcResult.getData().getBattleInfo().hasDsAddr()) {
                            dsAddrInfo.setDsAddr(rpcResult.getData().getBattleInfo().getDsAddr());
                        } else {
                            dsAddrInfo.setDsAddr(getUserAttr().getBattleInfo().getDsAddr());
                        }
                        if (rpcResult.getData().hasBattleInfo()) {
                            dsAddrInfo.setSceneId(rpcResult.getData().getBattleInfo().getSceneId());
                        } else {
                            dsAddrInfo.setSceneId(getUserAttr().getBattleInfo().getSceneId());
                        }
                        dsAddrInfo.setBattleId(getUserAttr().getBattleInfo().getBattleid());
                        dsAddrInfo.setDesModInfo(getUserAttr().getBattleInfo().getDesModInfo());
                        dsAddrInfo.setDsAuthToken(getUserAttr().getBattleInfo().getDsAuthToken());
                        dsAddrInfo.setMatchType(getUserAttr().getBattleInfo().getMatchType());
                        // 返回局内的聊天key
                        ChatGroupKey globalChatGroupKey = getUserAttr().getBattleInfo().getGlobalChatGroupKey();
                        dsAddrInfo.setChatGroupKey(AttrChatGroupKey.proto_ChatGroupKey.newBuilder().setChatType(globalChatGroupKey.getChatType())
                                .setId(globalChatGroupKey.getId())
                                .setSubID(globalChatGroupKey.getSubID()).build());
                        ChatGroupKey sideChatGroup = getUserAttr().getBattleInfo().getSideChatGroupKey();
                        dsAddrInfo.setSideChatGroupKey(AttrChatGroupKey.proto_ChatGroupKey.newBuilder().setChatType(sideChatGroup.getChatType())
                                .setId(sideChatGroup.getId())
                                .setSubID(sideChatGroup.getSubID()).build());
                        //dsAddrInfo.setPakType(getUserAttr().getBattleInfo().getPakType()); 废弃
                        dsAddrInfo.addAllPakGroupIdList(getUserAttr().getBattleInfo().getPakGroupIdListList());

                        // 返回赛事信息
                        dsAddrInfo.setCompetitionBasicInfo(rpcResult.getData().getBattleInfo().getCompetitionBasicInfo());

                        String[] matchTypeStr = new String[]{String.valueOf(getUserAttr().getBattleInfo().getMatchType())};
                        Monitor.getInstance().add.fail(MonitorId.attr_tyc_battle_ongoing_query_count, 1, matchTypeStr);
                        // ds状态置为对局中
                        getPlayerStateMgr().enterBattleScene();
                    } else {
                        if (result != NKErrorCode.BattleDsInCreating.getValue()) {
                            getPlayerBattleMgr().clearBattleInfo(getUserAttr().getBattleInfo().getBattleid(), "BattleGetOngoingDsAddrMsgHandler " +
                                    "rpcBattlePlayerOnline return" + result);
                            LOGGER.error("GetOngoingDsAddr not exist, uid:{}, battleId:{}, ret:{}", getUid(), currBattleId, result);
                            getPlayerRoomMgr().sendRpcRoomPlayerOnline();
                        } else {
                            LOGGER.error("GetOngoingDsAddr warn, uid:{}, battleId:{} in creating ds, ret:{}", getUid(), currBattleId, result);
                        }
                    }
                    dsAddrInfo.setErrorCode(result);
                } catch (NKTimeoutException | RpcException e) {
                    LOGGER.error("GetOngoingDsAddr rpcBattlePlayerOnline throw exception err:{}, uid:{}", e, getUid());
                }
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("Call BattleGetOngoingDsAddrMsgHandler, uid:{}, retDsAddr:{}", getUid(), dsAddrInfo.getDsAddr());
                }
                LoginStatMgr.endStep(getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, goingDsContextId);
            }
        }
        // mod 额外数据
        ConcurrentHashMap<Integer, ModPlayerAdditionalData> datas = ((PlayerModMgr) getModule(GameModuleId.GMI_ModMgr)).getModAdditionalData();
        for (Map.Entry<Integer, ModPlayerAdditionalData> entry : datas.entrySet()) {
            if (!entry.getValue().hasOnGoingDsInfo()) {
                continue;
            }
            dsAddrInfo.setModOnGoingData(entry.getValue().getOnGoingDsInfo());
        }
    }

    public void sendPlayerAfterLoginNtf(int flag, String sendSrc, TpsSpan span) {
        afterLoginMask |= flag;
        if ((afterLoginMask & AFTER_LOGIN_NTF_MASK) == AFTER_LOGIN_NTF_MASK) {
            LOGGER.debug("last call send {}", sendSrc);
            PlayerAfterLoginNtf.Builder ntfBuild = PlayerAfterLoginNtf.newBuilder();
            ntfBuild.setReserved(NKErrorCode.OK.getValue());

            DsOngoingAddrInfo.Builder dsAddrInfo = ntfBuild.getDsInfoBuilder();
            getOngoingDsAddrInfo(dsAddrInfo);
            // mod 额外数据
            ConcurrentHashMap<Integer, ModPlayerAdditionalData> datas = ((PlayerModMgr) getModule(GameModuleId.GMI_ModMgr)).getModAdditionalData();
            for (Map.Entry<Integer, ModPlayerAdditionalData> entry : datas.entrySet()) {
                if (!entry.getValue().hasPlayerData()) {
                    continue;
                }
                ntfBuild.setModPlayerData(entry.getValue().getPlayerData());
            }
            sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERAFTERLOGINNTF, ntfBuild);
            LOGGER.info("LoginMsg[{}|{}]: send PlayerAfterLoginNtf ok", getOpenId(), getUid());
            if (!isOptimizedLogin || ServerEngine.getInstance().isDsStartToken()) {
                clientDone(); // 如果是优化前的客户端登录，强制执行一次done
            }
        } else {
            LOGGER.debug("wait other call send, cur {}", sendSrc);
        }

        if (span != null) {
            span.end();
        }
    }

    public boolean checkPlayerMarqueeNoticeScene(MarqueeNoticePushScene scene, List<Integer> sceneIdList) {
        if (scene == MarqueeNoticePushScene.MNPS_Lobby) {
            return getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Lobby &&
                    (sceneIdList.size() == 0 ||
                            sceneIdList.contains(getPlayerLobbyMgr().getMapId()));
        }
        return true;
    }

    public void setLastUpdateMainVersion(long curClientVersion) {
        long lastClientVersion = getUserAttr().getPlayerPublicGameSettings().getClientVersion64();
        long configVersion = CupsConfigData.getInstance().getOpenClientVersion();
        if (lastClientVersion < configVersion && curClientVersion >= configVersion) {
            LOGGER.debug("player {} setLastUpdateMainVersionTimeMs: update client version from {} to {}, config version {}",
                    getUid(), lastClientVersion, curClientVersion, configVersion);
            getUserAttr().setLastUpdateMainVersionTimeMs(DateUtils.currentTimeMillis());
        }
    }

    public boolean setLoginInfo(boolean todayFirstLogin, Session session, CsPlayer.Login_C2S_Msg reqMsg) {
        setLoginByPc(reqMsg.getSystemSoftware().contains("Windows"));
        getUserAttrMgr().setProcessingClientMsg(true);
        getUserAttrMgr().setHasSyncFullToClient(false);

        UserAttr userAttr = getUserAttr();
        userAttr.getPlayerProfileInfo().setMobileGearLevel(reqMsg.getMobileGearLevel());
        // 上次登录时间
        BasicInfo basicInfo = userAttr.getBasicInfo();
        basicInfo.setLimitNotifyTimes(false);
        long lastLoginTimeMs = basicInfo.getLoginTimeMs();
        basicInfo.setLastLoginTimeMs(lastLoginTimeMs);
        basicInfo.setLoginTimeMs(Framework.currentTimeMillis());
        LOGGER.debug("loginMsgHandler debug update loginTimeMs, Uid:{} lastLoginTimeMs:{} ", getUid(), lastLoginTimeMs);
        if (!DateUtils.isSameDay(lastLoginTimeMs, basicInfo.getLoginTimeMs())) {
            todayFirstLogin = true;
            if (session.getOpenid().length() > 10) {
                PlayerMonitorMgr.getInstance().accessDaily.addAndGet(1);
            }
            basicInfo.setDailyOnlineTimeMs(0);
            basicInfo.setFarmDailyOnlineTimeMs(0);
        }

        // 设置白名单客户端标记，非0的才设置
        int clientSign = WhiteList.getWhiteListPriorityLevelForClient(reqMsg.getDeviceId(), session.getOpenid());
        if (clientSign != 0) {
            basicInfo.setClientWhiteListSign(clientSign);
        }
        if (!session.getAccessToken().isEmpty()) {
            userAttr.getPlayerProfileInfo().setAccessToken(session.getAccessToken());
        } else {
            userAttr.getPlayerProfileInfo().setAccessToken(reqMsg.getAccessToken());
        }
        userAttr.getPlayerPublicProfileInfo()
                .setPlatId(reqMsg.getPlatId())
                .setChannelId(session.getAccountType() != null ? session.getAccountType().getNumber() : 0);
        // 设置更新大版本时间
        long curClientVersion = VersionUtil.encodeClientVersion(reqMsg.getClientVersion());
        setLastUpdateMainVersion(curClientVersion);
        userAttr.getPlayerPublicGameSettings().setClientVersion64(curClientVersion);
        LOGGER.debug("setLoginInfo setClientVersion, uid:{} clientVersion:{} clientVersion64:{}",
                reqMsg.getUid(), reqMsg.getClientVersion(),
                userAttr.getPlayerPublicGameSettings().getClientVersion64());
        userAttr.getPlayerPublicBasicInfo()
                .setChannelId(reqMsg.getChannel())
                .setSvrId(Framework.getInstance().getServerId())
                .setOsVersion(reqMsg.getClientVersion())
                .setNetWork(reqMsg.getNetWork())
                .setSystemSoftware(reqMsg.getSystemSoftware())
                .setSystemHardware(reqMsg.getSystemHardware())
                .setLoginTimeMs(Framework.currentTimeMillis())
                .setLoginPlat(reqMsg.getLoginPlat())
                .setTrackId(reqMsg.getTrackId())
                .setClientDeviceType(reqMsg.getClientDeviceType());
        if (reqMsg.hasLanguage() && reqMsg.getLanguage() != 0) {
            userAttr.getPlayerPublicGameSettings().setClientLanguage(reqMsg.getLanguage());
        }
        session.setJqCallInfo(reqMsg.getJqCallInfo());
        session.setGameMatrixExtraData(reqMsg.getGameMatrixExtraData());
        session.setDeeplink(reqMsg.getDeeplink());
        return todayFirstLogin;
    }

    public void checkAfterGetPlayer(CsPlayer.Login_C2S_Msg reqMsg, Session session) {
        checkSimulate(reqMsg, session);
        checkLoginDeviceId(reqMsg, session);
        checkClientVersion(reqMsg, session);
    }

    public void checkSimulate(CsPlayer.Login_C2S_Msg reqMsg, Session session) {
        if (!PropertyFileReader.getRealTimeBooleanItem("limit_simulate", false)) {
            return;
        }

        int level = WhiteList.getWhiteListPriorityLevel(reqMsg.hasDeviceId() ? reqMsg.getDeviceId() : "",
                reqMsg.getOpenid(), reqMsg.getPlatId(), "login");
        if (level >= WhiteListPriorityEnum.WhiteListPriority_Super_VALUE) {
            return;
        }
        if (reqMsg.getIsSimulate()) {
            session.sendNtfMsg(MsgTypes.MSG_TYPE_SIMULATELIMITNTF, CsPlayer.SimulateLimitNtf.newBuilder());
            NKErrorCode.SimulateLimit.throwError("Simulate Limit Login");
        }
    }

    public void checkLoginDeviceId(CsPlayer.Login_C2S_Msg reqMsg, Session session) {
        if (!PropertyFileReader.getRealTimeBooleanItem("only_register_device_can_login", false)) {
            return;
        }
        int priorityLevel = WhiteList.getWhiteListPriorityLevel(reqMsg.hasDeviceId() ? reqMsg.getDeviceId() : "",
                reqMsg.getOpenid(), reqMsg.getPlatId(), "login");
        if (priorityLevel >= WhiteListPriorityEnum.WhiteListPriority_Super_VALUE) {
            return;
        }
        if (!reqMsg.hasDeviceId() || reqMsg.getDeviceId().length() == 0) {
            sendDeviceHasNoPermissionNtf(session);
            NKErrorCode.DeviceHasNoPermission
                    .throwError("DeviceHasNoPermission, openId:{}, uid:{}");
        }
        if (getUserAttr().getPlayerProfileInfo().getRegisterDeviceId().length() == 0) {
            getUserAttr().getPlayerProfileInfo().setRegisterDeviceId(reqMsg.getDeviceId());
            return;
        }
        if (!getUserAttr().getPlayerProfileInfo().getRegisterDeviceId().equals(reqMsg.getDeviceId())) {
            sendDeviceIdCheckErrorNtf(session);
            NKErrorCode.OnlyRegisterDeviceCanLogin
                    .throwError("openid:{}, uid:{}, registerDeviceId:{}, loginDeviceId:{}", reqMsg.getOpenid(),
                            reqMsg.getUid(), getUserAttr().getPlayerProfileInfo().getRegisterDeviceId(),
                            reqMsg.getDeviceId());
        }
    }

    public void checkClientVersion(CsPlayer.Login_C2S_Msg reqMsg, Session session) {
        String clientVersion = VersionUtil
                .decodeClientVersion(getUserAttr().getPlayerPublicGameSettings().getClientVersion64());

        if (PropertyFileReader.getRealTimeBooleanItem("forbid_low_client_version_login", true)) {
            //登录检测下历史登录大版本号（前三位），禁止低版本登录, checkAlways检查过版本号合法性，这边不再check
            if (VersionUtil.CLIENT_VERSION_COMPARE_RESULT.GT == VersionUtil.clientVersionCompare(
                    getUserAttr().getPlayerPublicGameSettings().getClientVersion64(),
                    VersionUtil.encodeClientVersion(reqMsg.getClientVersion()))) {
                LOGGER.error("openid {} uid {}, LoginLowAppVersion error {} {}",
                        session.getOpenid(), session.getUid(), reqMsg.getClientVersion(), clientVersion);
                NKErrorCode.LoginLowAppVersion.throwError("LoginLowAppVersion");
            }
            //登录检测下历史登录版本号，禁止低版本登录, checkAlways检查过版本号合法性，这边不再check
            if (VersionUtil.CLIENT_VERSION_COMPARE_RESULT.GT == VersionUtil.clientVersionCompareAll(
                    getUserAttr().getPlayerPublicGameSettings().getClientVersion64(),
                    VersionUtil.encodeClientVersion(reqMsg.getClientVersion()))) {
                LOGGER.error("openid {} uid {}, LoginLowVersion error {} {}",
                        session.getOpenid(), session.getUid(), reqMsg.getClientVersion(), clientVersion);
                NKErrorCode.LoginLowVersion.throwError("LoginLowVersion");
            }
        } else {
            LOGGER.info("openid {} uid {} skip client low version check by cfg", session.getOpenid(),
                    session.getUid());
        }

    }

    public void checkBanInfo() {
        BanInfo idipBanInfo = PlayerBanInfoCache.getBanInfoCache(getUid());
        if (idipBanInfo != null) {
            setIdipBanInfo(idipBanInfo);
            BanStatus accountStatus = getBanStatus(BanType.BT_BanLogin);
            if (accountStatus != null && accountStatus.getBanBefore() > DateUtils.currentTimeMillis()) {
                //banInfoNtf
                sendBanInfoNtf(BanType.BT_BanLogin, accountStatus.getBanBefore(), NKErrorCode.BanLogin,
                        accountStatus.getBanReason(), true);
                //封号
                Monitor.getInstance().add.total(MonitorId.attr_login_ban, 1);
                NKErrorCode.BanLogin.throwError("BanLogin");
            }

            // SP保密测试因IP地址变更遭到封禁
            BanStatus accountStatusSPLoginIP = getBanStatus(BanType.BT_SP_Login_IP);
            if (accountStatusSPLoginIP != null && accountStatusSPLoginIP.getBanBefore() > DateUtils.currentTimeMillis()) {
                session.sendNtfMsg(MsgTypes.MSG_TYPE_IPHASCHANGEDNTF, CsPlayer.IPHasChangedNtf.newBuilder());
                NKErrorCode.BanLoginIPHasChanged.throwError("BanLogin");
            }

            // 如果排行榜封禁已经到期，先解封，PlayerUgcManager需要在afterLogin里拿到最新的状态
            tryUnbanRank();
        }
    }

    public void KickIfNeed() {
        int regionId = this.getUserAttr().getPlayerPublicBasicInfo().getRegisterRegionId();
        int channelId = this.getUserAttr().getPlayerPublicProfileInfo().getChannelId();
        MapAttrObj<TconndApiAccount, BindAccountInfo> accountChannels = this.getUserAttr().getPlayerPublicBasicInfo()
                .getBindAccountInfos();

        boolean checkResult = GlobalKickMgr.checkInGlobalKick(regionId, channelId, accountChannels);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player uid:{}, regionId:{}, accountChannels:{}, checkResult:{}",
                    this.getUid(), regionId, accountChannels.toString(), checkResult);
        }
        if (checkResult) {
            GlobalKickInfo globalKickInfo = GlobalKickMgr.GetGlobalKickInfo();
            NKErrorCode code = NKErrorCode.forNumber(globalKickInfo.getGlobalKickData().getErrCode());
            if (code == null) {
                code = NKErrorCode.Idip2ClientCodeDefault;
            }

            this.kickPlayer(code, code.name());
            this.kickUpdate();
        }

        // 正常情况下, PlayerOnlineTable已经会拦住, 这里是确保
        if (!Framework.getInstance().isDataCompatibleWith(userAttrMgr.getDbPlayer().getLastLoginSvrVersion())) {
            String notice = String.format("serverVersion:{} < lastLoginSvrVersion:{}",
                    Framework.getInstance().getServerVersion(),
                    userAttrMgr.getDbPlayer().getLastLoginSvrVersion());
            WechatLog.debugPanicLog("uid:{} {}", notice);
            this.kickPlayer(NKErrorCode.GameServerVersionTooLow, notice);
        }
    }

    /**
     * 通知客户端上报日志
     */
    private void uploadLogCheck() {
//        if (getUserAttr().getBasicInfo().getAccountInfo().getTags().containsKey("uploadLogFlag")) {
//            CsPlayer.UploadLogNtf.Builder builder = CsPlayer.UploadLogNtf.newBuilder();
//            sendNtfMsg(MsgTypes.MSG_TYPE_UPLOADLOGNTF, builder);
//            getUserAttr().getBasicInfo().getAccountInfo().getTags().remove("uploadLogFlag");
//        }
    }

    public boolean isValidQaInvestLabel(int userLabel) {
        if (userLabel == 0) {
            return true;
        }
        return getUserAttr().getQaInvestTag(userLabel) != null;
    }

    /**
     * 通知客户端调查问卷
     */
    private void qaInvestListCheckAfterLogin() {
        Collection<ResQAInvest.QAInvest> serverQAInvestList = QAInvestData.getInstance()
                .getQAInvestListByServerId(Framework.getInstance().getWorldId());
        if (serverQAInvestList != null) {
            CsPlayer.QAInvestConfigListNtf.Builder ntfBuilder = CsPlayer.QAInvestConfigListNtf.newBuilder();
            for (ResQAInvest.QAInvest qaInvestConfig : serverQAInvestList) {
                if (isCompletedQAInvest(qaInvestConfig.getId())) {
                    continue;
                }
                // 没过期的问卷
                if (qaInvestConfig.getEndTime().getSeconds() >= Framework.currentTimeSec()
                        && (qaInvestConfig.getGender() == 0
                        || getUserAttr().getPlayerPublicProfileInfo().getGender() == qaInvestConfig.getGender())
                        && isValidQaInvestLabel(qaInvestConfig.getUserLabel())) {
                    ntfBuilder.addUncompletedQAInvestList(qaInvestConfig);
                }
            }
            // ntf
//            if (ntfBuilder.getUncompletedQAInvestListCount() > 0) {
            ResQAInvest.QAInvestCallbackUrl urlConfig = QAInvestCallbackUrlConfigData.getInstance()
                    .getByServerId(Framework.getInstance().getWorldId());
            if (urlConfig != null) {
                ntfBuilder.setCallbackId(urlConfig.getId());
            }
            sendNtfMsg(MsgTypes.MSG_TYPE_QAINVESTCONFIGLISTNTF, ntfBuilder);
//            }
        } else {
            LOGGER.debug("empty qa invest:{}", Framework.getInstance().getWorldId());
        }
    }

    private boolean isCompletedQAInvest(Integer qaInvestId) {
        QAInvestInfo investInfo = getUserAttr().getQaInvest().get(qaInvestId);
        return (investInfo != null && investInfo.getInvestStatus() == QAInvestStatus.QAIS_Rewarded);
    }

    //踢线，如果是该玩家正在登陆过程中，则打上标记，由登陆流程来将其踢除
    public void kickPlayer(NKErrorCode code, String message) {
        if (code != NKErrorCode.ServerClose) {
            Monitor.getInstance().add.total(MonitorId.attr_kick_player, 1);
        }

        if (PlayerLogin.waitKickWhenLogin(getUid(), code, message)) {
            LOGGER.debug("player openid {} uid {} waitKickWhenLogin ",
                    getOpenId(), getUid());
            return;
        }

        kickPlayerInternal(code, message);
    }

    //直接发送kickntf,并关闭连接
    //this will directly disconnect session
    void kickPlayerInternal(NKErrorCode code, String message) {
        setPlayerKicking();
        if (session != null) {
            session.kickOffNtf(code, message);
        }
        closeConnection(code);
        LOGGER.debug("player openid {} uid {} code:{} message:{}", getOpenId(), getUid(), code, message);
        if (PropertyFileReader.getRealTimeBooleanItem("RemovePlayerCacheWhenKick", false)) {
            PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.KickPlayerInternal);
        }
        setPlayerKicked();
    }

    public PlayerPublicProfileInfo getPlayerPublicProfileInfo() {
        return getUserAttr().getPlayerPublicProfileInfo();
    }

    //关闭连接，并调用logout
    public void closeConnection(NKErrorCode errorCode) {
        if (isOnline()) {
            try {
                logout(errorCode);
                if (session != null) {
                    LOGGER.info("player closeConnection {} {} {} {}",
                            getUid(), getOpenId(), errorCode, session.getSessionId());
                } else {
                    LOGGER.info("player closeConnection {} {} {}",
                            getUid(), getOpenId(), errorCode);
                }

            } catch (Exception e) {
                LOGGER.error("player-{} logout failed.", getUid(), e);
            }

            clearSession();
            disconnect();
            if (PlayerRefMgr.getInstance().checkGetPlayer(getUid())) {
                // 如果还有缓存
                // 先回写一遍db，然后将player缓存置为不可用
                try {
                    //updateDb(true);
                    //改成定时回写后，脏位收集也是定时触发的，这里需要调用flushUserAttr强制收集下脏数据
                    flushUserAttr(true);
//                    setNotUse();
                } catch (Exception e) {
                    // 失败则直接清除player
                    LOGGER.error("player-{} logout updateDb failed.", getUid(), e);
                    PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.CloseConnection);
                }
            }
        } else {
            LOGGER.debug("player openid {} uid {} , closeConnection player is not online", getOpenId(), getUid());
            if (errorCode == NKErrorCode.LoginException) {
                if (PropertyFileReader.getRealTimeBooleanItem("RemovePlayerWhenLoginException", true)) {
                    // 登陆失败则直接清除player
                    LOGGER.error("player-{} login failed， remove player", getUid());
                    setNeedReloadPlayer(true);
//                    PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.LoginException);

                }
            }
        }
    }

    /**
     * 断开连接时调用
     */
    private void disconnect() {
        LOGGER.debug("player disconnect {}|{}", getOpenId(), getUid());
        try {
            modulesOnDisconnect();
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        // 保底
        flushUserAttr(true);
//        setNotUse();
    }

    // 短时间离开，不是真正的掉线
    public void leave() {
        LOGGER.debug("player leave {}|{}", getOpenId(), getUid());
        modulesOnLeave();
        flushUserAttr(true);

        // 发送玩家离开事件
        getPlayerEventManager().dispatch(new PlayerLeaveEvent(getConditionMgr()));
    }

    // 客户端开始活跃，从 leave 状态回来会调用一次
    public void onActive() {
        LOGGER.debug("player onActive {}|{}", getOpenId(), getUid());
        getPlayerOnlineMgr().onReLogin();
        // 触发玩家房间状态登录 同步逻辑
        getPlayerRoomMgr().syncRoomOnline();
        // 触发玩家大富翁状态登录
//        getPlayerNr3e8Mgr().syncPlayerOnline();
        getPlayerFarmMgr().onActive();
        modMgr.onActive();
    }

    // 长时间离开，判定为 logout
    public void offline() {
        LOGGER.debug("player offline {}|{}", getOpenId(), getUid());
        try {
            logout(NKErrorCode.HeartBeatTimeOut);
        } catch (Exception e) {
            LOGGER.error("player-{} logout failed.", getUid(), e);
        }
    }

    //每次session断开都会执行
    private void logout(NKErrorCode errorCode) {
        LOGGER.info("player logout {} {}", getUid(), errorCode);
        getPlayerXiaoWoMgr().setXiaowoState(false);
        getPlayerFarmMgr().setFarmState(false);
        if (null != session) {
            LOGGER.info("player logout {} {} {}", getUid(), errorCode, session.getSessionId());
            //防沉迷
            AntiAddictMgr.getInstance().addLogoutQueue(getOpenId(), session);
            // 用户登出触发反外挂用户移除
            getPlayerCreditScoreMgr().antiCheatLogoutProcess(errorCode);

            BasicInfo basicInfo = getUserAttr().getBasicInfo();
            long nowMillis = Framework.currentTimeMillis();
            basicInfo.setLogoutTimeMs(nowMillis);
            long onlineIntervalMs = basicInfo.getLogoutTimeMs() - basicInfo.getLoginTimeMs();
            basicInfo.setTotalLoginTimeMs(basicInfo.getTotalLoginTimeMs() + onlineIntervalMs);
            if (nowMillis > basicInfo.getHeartBeatTimeMs()) {
//            basicInfo.setOnlineMillisRefreshAt8(basicInfo.getOnlineMillisRefreshAt8() + nowMillis - basicInfo.getHeartBeatTimeMs());
                basicInfo.addDailyOnlineTimeMs(nowMillis - basicInfo.getHeartBeatTimeMs());
                if (getPlayerFarmMgr().getCurrentFarmId() > 0) {
                    basicInfo.addFarmDailyOnlineTimeMs(nowMillis - basicInfo.getHeartBeatTimeMs());
                }
                getUserAttr().getPlayerPublicGameData().addTotalOnlineTime(nowMillis - basicInfo.getHeartBeatTimeMs());
            }
            getUserAttr().getPlayerPublicBasicInfo().setLogoutTimeMs(nowMillis);

            Session.ClientInfo clientInfo;
            clientInfo = session.getClientInfo();
            // 登出流水
            TlogFlowMgr.sendAvatarAutoReportFlow(this);
            sendMonitorFlow(this, clientInfo, DevMonitorType.DMT_Logout);
            TlogFlowMgr.sendModPlayerLogoutFlow(this, clientInfo, basicInfo,
                    TlogMacros.LOGIN_FLOW_TYPE.LOGIN_CS_TRIGGER, errorCode.getValue(),
                    basicInfo.getBeforeMidnightTimeMs());
            TlogFlowMgr.sendSecLogoutFlow(this);
            // 通知营地
            PlayerReportUtil.reportPlayerLogout(this);
            // QQ成就上报
            sendAchievementReport(new AchieveBody(getUid(), getPlatId())
                    .AddAchievementParam(AchiParamType.APT_SingleGameDuration, onlineIntervalMs / 1000, 1, 0));

            getTradingCardModule().onLogout();
            getCocMgr().onLogout();
            // Starp 聊天系统上报
            getPlayerStarPMgr().reportPlayerLogoutToStarPChat(getUid());
            // Starp上报
            getPlayerStarPMgr().reportPlayerLogout(getUid(), errorCode);
        }

        if (errorCode == NKErrorCode.SessionDisconnect) {
            // 如果是掉线，只统计时间相关的信息，剩下的流程由其他事件触发后执行， 保底的事件是 HeartBeatTimeOut
            getPlayerEventManager().dispatch(new LogoutEvent(getConditionMgr()));
            playerStateMgr.logout();
            playerOnlineMgr.updatePlayerOnlineTableOnlineState(PlayerStateType.PST_Offline);
            return;
        }
        // 缓存处理
        if (PropertyFileReader.getRealTimeBooleanItem("RemovePlayerCacheWhenLogout", false)) {
            PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.CloseConnection);
        }
    }

    // 在player缓存销毁时调用
    private void logoutWhenDestroy() {
        LOGGER.debug("player {} start logoutWhenDestroy", getUid());
        if (getPlayerLobbyMgr().getLobbyId() > 0) {
            getPlayerLobbyMgr().procExitCurrentLobby(ExitLobbyCode.EXIT_LOBBY_CODE_OFFLINE.getNumber());
        }

        getUserAttr().getPlayerPublicProfileInfo().setAtXiaowo(false);

        try {
            modulesOnLogout();
        } catch (Exception e) {
            LOGGER.error("", e);
        }

        try {
            // 更新登录svrId
            playerStateMgr.circulationPlayerState(PlayerStateAction.LOGOUT);
            playerOnlineMgr.updatePlayerOnlineTableSvrId(0, PlayerStateType.PST_Offline);
        } catch (TcaplusErrorException e) {
            LOGGER.warn("logout replace svrId error, {},{}", getUid(), e.getMessage());
        }
        // TlogCache处理
        tlogCache.flush();
        LOGGER.debug("player {} fini logoutWhenDestroy", getUid());
    }

    public boolean insertPlayer() {
        LOGGER.debug("player {} do insert db", getUid());
        //其他的数据都在dbplayer上了，属性系统要手动flush一下
        userAttrMgr.setDbPlayerUserAttr();
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newInsertReq(userAttrMgr.getDbPlayerBuilder()).send();
        if (rsp.hasFirstRecord()) {
            userAttrMgr.getDbPlayer().setVersion(rsp.firstRecordData().version);
            return true;
        } else {
            return false;
        }
    }

    public UserAttr getUserAttr() {
        return userAttrMgr.getUserAttr();
    }

    public long getCreatorId() {
        return userAttrMgr.getUserAttr().getPlayerPublicProfileInfo().getCreatorId();
    }

    public UgcAuthType getUgcAuthType() {
        return userAttrMgr.getUserAttr().getPlayerPublicProfileInfo().getUgcAuthType();
    }

    /**
     * 获取当前对局所属的roomId
     *
     * @return
     */
    public long getCurrentRoomId() {
        return getUserAttr().getBattleInfo().getRelatedRoomId();
    }

    /**
     * 登记当前对局的roomId
     *
     * @param roomId
     */
    public void setCurrentRoomId(long roomId) {
        getUserAttr().getRoomInfo().setRoomid(roomId);
    }

    public void setCurrentMatchType(int matchType) {
        int currentMatchType = getCurrentMatchType();
        getUserAttr().getRoomInfo().setCurrentMatchType(matchType);
        if (session != null) {
            getSession().getClientInfo().setCurrentMatchType(matchType);
        }
        getUserAttr().getPlayerPublicLiveStatus().setBattleMatchType(matchType);
        getPlayerRoomMgr().setLastMatchTypeId(currentMatchType);
    }

    public int getCurrentMatchType() {
        return getUserAttr().getRoomInfo().getCurrentMatchType();
    }

    public boolean offlineBattle() {
        try {
            BattleService battleService = BattleService.get();
            if (getUserAttr().getBattleInfo().getBattleid() != 0 && null != battleService) {
                SsBattlesvr.RpcBattlePlayerOfflineReq.Builder ssReqMsg = SsBattlesvr.RpcBattlePlayerOfflineReq
                        .newBuilder();
                ssReqMsg.setBallteId(getUserAttr().getBattleInfo().getBattleid());
                ssReqMsg.setUid(getUid());
                RpcResult<RpcBattlePlayerOfflineRes.Builder> rpcResult = battleService
                        .rpcBattlePlayerOffline(ssReqMsg);
                if (rpcResult.getRet() != 0) {
                    LOGGER.error("Battle online failed is player:{} battleId:{} res:{}", getUid(),
                            getUserAttr().getBattleInfo().getBattleid(),
                            rpcResult.getRet());
                    return false;
                }
            }
        } catch (NKCheckedException e) {
            LOGGER.error("offlineRoom-{} {}", getUid(), e);
            return false;
        }
        return true;
    }

    public void updatePlayerBriefData(AttrUserAttr.proto_UserAttr proto) {
        try {
            //LocalSubscribeService.get().onPlayerBriefDataChange(getUid(), proto);
        } catch (Exception e) {
            LOGGER.error("player-{} update brief data field", getUid(), e);
        }
    }

    public void flushUserAttr() {
        flushUserAttr(false, true);
    }

    public void flushUserAttrNoPublic() {
        flushUserAttr(false, false);
    }

    public void flushUserAttr(boolean needWait) {
        flushUserAttr(needWait, true);
    }

    /**
     * 刷新用户attr
     */
    public void flushUserAttr(boolean needWait, boolean needFlushPublic) {
        LOGGER.debug("player {} {} flushUserAttr", getOpenId(), getUid());
        if (getLoadingState() == LoadingState.LS_Registering) {
            return;
        }

        dirtyFieldsOfPlayerPublic.addAll(PlayerPublicDao.collectDirtyFields(getUserAttr()));
        boolean isPublicProfileDirtyForFarm = isPublicProfileDirtyForFarm();

        long dbDirtyFieldCount = userAttrMgr.flushUserAttr();

        //3.更新脏数据
        if (dbDirtyFieldCount > 0) {
            updateDb(needWait);
            getPlayerChatManager().publicChatNotice(ChatType.CT_World);
            getPlayerChatManager().publicChatNotice(ChatType.CT_NewStar);
            getPlayerChatManager().publicCommunityChannelChatNotice();
            getRecentActivityMgr().flush();
        }

        // Simple Field指玩家直接可见数据
        if (needFlushPublic && dirtyFieldsOfPlayerPublic != null && !dirtyFieldsOfPlayerPublic.isEmpty()) {
            HashSet<PlayerPublicAttrKey> tmpChangeFields = new HashSet<>(dirtyFieldsOfPlayerPublic);
            boolean isPublicProfileDirty = PlayerPublicDao.hasCollectSimpleDirtyFieldsField(tmpChangeFields);
            if (needWait) {
                PlayerPublicDao.flushPlayerPublicAttr(getUserAttr(), tmpChangeFields);
                lastFlushPlayerPublicTime = DateUtils.currentTimeMillis();
                dirtyFieldsOfPlayerPublic.clear();
            } else if (!Collections.disjoint(PlayerPublicDao.ImmediateFlushPlayerPublicAttrKey, tmpChangeFields) || DateUtils.currentTimeMillis() - lastFlushPlayerPublicTime >
                    PropertyFileReader.getLongItem("flush_player_public_interval_ms", 30 * 1000L)) {
                // 限制更新频率，除了需要立刻更新的字段
                PlayerPublicDao.asyncFlushPlayerPublicAttr(getUserAttr(), tmpChangeFields);
                lastFlushPlayerPublicTime = DateUtils.currentTimeMillis();
                dirtyFieldsOfPlayerPublic.clear();
            }

            if (isPublicProfileDirty) {
//                if (tmpChangeFields.contains(PlayerPublicAttrKey.PublicEquipments)) {
//                    getPlayerBattleMgr().updateBattleDsPlayerPublicInfo();
//                }
                getPlayerLobbyMgr().updateLobbyDsPlayerPublicInfo();

                // 小窝 农场 小屋的ds同步 降低下频率
                if (isPublicProfileDirtyForFarm) {
                    getPlayerFarmMgr().updateFarmDsPlayerPublicInfo();
                }
            }
        }

        // 尝试更新一下玩家的活动数据
        getPlayerLobbyMgr().tryUpdateLobbyDsPlayerActivityInfo();
    }

    public boolean isPublicProfileDirtyForFarm() {
        if (getUserAttr().getPlayerPublicEquipments().isDirty()) {
            return true;
        }
        if (getUserAttr().getPlayerPublicGameSettings().isHeadPublicInfoTypeDirty()) {
            return true;
        }
        if (getUserAttr().getPlayerPublicGameSettings().isBirthdayVisibleRangeDirty()) {
            return true;
        }
        if (getUserAttr().getPlayerPublicProfileInfo().isPersonalityStateDirty()) {
            return true;
        }
        if (getUserAttr().getPlayerPublicProfileInfo().isBirthdayMonthDayDirty()) {
            return true;
        }
        if (getUserAttr().getPlayerPublicProfileInfo().isIntimateRelationInfoDirty()) {
            return true;
        }
        return false;
    }

    /**
     * 是否在线
     *
     * @return
     */
    public boolean isOnline() {
        return session != null;
//        if (session.checkState(STATE_DISCONNECTED)) {
//            return false;
//        }
    }

    public void sendNtfMsg(int type, Message.Builder body) {
        String methodName = MsgTypes.getMsgName(type);
        TpsSpan curSpan = startPlayerSpan(methodName);
        curSpan.addNtfEvent(body);
        sendNtfMsg(type, body, null);
        curSpan.end();
    }

    public void sendNtfMsg(int type, SerializedMessage bodyData) {
        session.sendNtfMsg(type, bodyData.getBuilder(), bodyData.getBinary());
    }

    public void sendNtfMsgWhenFail(int type, int errcode, Message.Builder body) {
        session.sendMsg(CsHead.CSHeader.newBuilder().setSeqId(0).setType(type).setErrorCode(errcode).
                setServerTs(Framework.currentTimeMillis()), body, (ByteBuffer) null, 0);
    }

    public void sendNtfMsg(int type, Message.Builder body, byte[] bodyBytes) {
        if (session != null) {
            session.sendNtfMsg(type, body, bodyBytes);
        } else {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("sendNtfMsg error session == null :{} {} {}", getUid(), type, body);
            }
        }
    }

    public void sendNtfMsg(int type, ByteBuffer bodyBuff) {
        if (session != null) {
            session.sendNtfMsg(type, bodyBuff);
        } else {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("sendNtfMsg error session == null :{} {} {}", getUid(), type, bodyBuff);
            }
        }
    }

    public void sendError(int type, NKErrorCode errorCode) {
        if (session != null) {
            if (null == errorCode) {
                errorCode = NKErrorCode.UnknownError;
            }
            session.sendErrorMsg(type, errorCode);
        }
    }

    public void sendError(int type, int errorCode) {
        if (session != null) {
            NKErrorCode err = NKErrorCode.forNumber(errorCode);
            if (null == err) {
                LOGGER.error(" sendError not exist :{}", errorCode);
                err = NKErrorCode.UnknownError;
            }
            session.sendErrorMsg(type, err);
        }
    }

    public void sendError(int type, int errorCode, String msg) {
        if (session != null) {
            NKErrorCode err = NKErrorCode.forNumber(errorCode);
            if (null == err) {
                LOGGER.error(" sendError not exist :{}", errorCode);
                err = NKErrorCode.UnknownError;
            }
            session.sendErrorMsg(type, err, msg);
        }
    }

    // 不检查errorCode是否在NKErrorCode中有定义，仅用于热更情况下的新增错误码
    public void sendErrorForHotpatch(int type, int errorCode, String msg) {
        if (session != null) {
            session.sendErrorMsg(type, 0, errorCode, msg);
        }
    }

    public void kickOffNtf(NKErrorCode code, String message) {
        if (session != null) {
            session.kickOffNtf(code, message);
        }
    }

    public void kickOffNtf(NKErrorCode code) {
        if (session != null) {
            session.kickOffNtf(code);
        }
    }

    public void sendBanInfoNtf(BanType banType, long banBefore, NKErrorCode errorCode, String reason, boolean show) {
        if (session == null) {
            return;
        }
        CsPlayer.PlayerBanInfoNtf.Builder banInfoNtf = CsPlayer.PlayerBanInfoNtf.newBuilder();
        banInfoNtf.setBanBefore(banBefore);
        banInfoNtf.setBanType(banType);
        banInfoNtf.setSid(session.getSessionId());
        banInfoNtf.setErrorCode(errorCode.getValue());
        banInfoNtf.setReason(reason);
        banInfoNtf.setShow(show);
        sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERBANINFONTF, banInfoNtf);
    }

    /**
     * 推送玩家信息变化消息
     *
     * @param changeField
     */
    public void sendPlayerProfileChangeNtf(PlayerPublicInfoField changeField) {
        CsInformation.PlayerProfileChangeNtf.Builder changeNtf = CsInformation.PlayerProfileChangeNtf.newBuilder();
        changeNtf.setChangeField(changeField);
        sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERPROFILECHANGENTF, changeNtf);
        new PlayerChangeProfileEvent(this, changeField).dispatch();
    }

    /**
     * 是否是当天第一次登陆
     *
     * @return boolean
     */
    public boolean isTodayFirstLogin() {
        long loginDay = DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis());
        long lastLoginDay = DateUtils.getDayBeginTimeMs(getUserAttr().getBasicInfo().getLastLoginTimeMs());
        long lastHeartbeatDay = DateUtils.getDayBeginTimeMs(getUserAttr().getBasicInfo().getLastHeartBeatTimeMs());

        LOGGER.debug("loginDay: {}, lastLoginDay: {}, lastHeartbeatTime: {}, lastHeartbeatDay: {}", loginDay,
                lastLoginDay, getUserAttr().getBasicInfo().getLastHeartBeatTimeMs(), lastHeartbeatDay);

        return loginDay > lastLoginDay || loginDay > lastHeartbeatDay;
    }

    public int getBelongPhysicalZoneId() {
        return 1;
    }

    public long getCompVersion() {
        long dsDevEnvId = getDSVersion64();
        if (!ServerEngine.getInstance().isBusiness() && dsDevEnvId > 0) {
            return dsDevEnvId;
        } else {
            return getClientVersion64();
        }
    }

    public long getPlayVersion(int matchTypeId) {
        String featureName = MatchTypeData.getInstance().getPlayName(matchTypeId);
        return GamePlay.getInstance().getFeatureField(featureName, getCompVersion(), true
                , getUserAttr().getPlayerPublicGameSettings().getGamePlay().values()
                , gp -> gp.getFeatureType(), gp -> gp.getPakVersion());
    }

    public long getPlayVersion(String featureName) {
        return GamePlay.getInstance().getFeatureField(featureName, getCompVersion(), true
                , getUserAttr().getPlayerPublicGameSettings().getGamePlay().values()
                , gp -> gp.getFeatureType(), gp -> gp.getPakVersion());
    }

    public int getLobbyVersionGroupId() {
        return getLobbyVersionGroupId(getPlayerLobbyMgr().getMapId());
    }

    public int getLobbyVersionGroupId(int mapId) {
        int versionGroup = 0;
        LobbyConfig lobbyConfig = LobbyConfigData.getInstance().getLobbyConfig(mapId);
        if (lobbyConfig == null) {
            return versionGroup;
        }
        if (lobbyConfig.getType() == LobbyType.LT_Main) {
            versionGroup = VersionCompBattleConf.getInstance().getLobbyVersionGroup(getCompVersion());
        } else {
            versionGroup = VersionCompBattleConf.getInstance().getUgcLobbyVersionGroup(getCompVersion());
        }
        return Math.max(versionGroup, 0);
    }

    public String getLobbyGroupId(int mapId, long ugcId) throws NKRuntimeException {
        int areaId = getAreaId();
        // 合区处理
        if (getPlayerGrayTagMgr().hasGrayTag(PlayerGrayTagType.PGTT_Lobby_Together)) {
            areaId = TconndApiAccount.TCONND_ITOP_CHANNEL_Unknown.getNumber();
        }
        return LobbyConfigData.getInstance().getLobbyGroupId(getCompVersion(), mapId, areaId, ugcId);
    }

    public int getXiaowoVersionGroupId() {
        return playerXiaoWoMgr.getVersionGroupId();
    }

    public int getBattleVersionGroupId(int matchType) {
        int versionGroup = VersionCompBattleConf.getInstance()
                .getBattleVersionGroup(getPlayVersion(matchType), matchType);
        return Math.max(versionGroup, 0);
    }

    public long getExpIncrLimit() {
        int max = PlayerLevelConfData.getInstance().getExpUpperLimit();
        return max - getPlayerMoneyMgr().getCoinNum(CoinType.CT_Exp.getNumber());
    }

    public long getOnMidnightTime() {
        return onMidnightTime;
    }

    /**
     * 更新检查
     */
    public void refreshCheck() {
        refreshOnlineProc();
        final long todayBeginTimeMs = DateUtils.getToday(Framework.currentTimeMillis());
        // 基于上次凌晨刷新时间和当前时间作比较
        if (getUserAttr().getBasicInfo().getLastMidnightRefreshTimeMs() < todayBeginTimeMs
                && Framework.currentTimeMillis() > todayBeginTimeMs) {
            try {
                LOGGER.debug("player {} try call setLastMidnightRefreshTimeMs", getUid());
                getUserAttr().getBasicInfo().setLastMidnightRefreshTimeMs(Framework.currentTimeMillis());
                addPlayerGameTime(PlayerGameTimeType.PGTT_LoginDays, 1);
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnMidnight();
                    this.onMidnightTime = DateUtils.currentTimeMillis();
                    Session session = getSession();
                    // 0点打一条登录流水，方便数据统计
                    if (session != null && Framework.currentTimeMillis() < todayBeginTimeMs + 300 * 1000
                            && getLoginTime() < todayBeginTimeMs) {
                        Session.ClientInfo clientInfo = session.getClientInfo();
                        TlogFlowMgr.sendModPlayerLoginFlow(this, clientInfo, session.getIpAddr(),
                                TlogMacros.LOGIN_FLOW_TYPE.MIDNIGHT_TRIGGER);
                    } else if (session == null) {
                        LOGGER.info("refreshCheck invalid session:{}", getUid());
                    }
                    new PlayerOnMidNightEvent(this).dispatch();
                    return null;
                }, "modulesOnMidnight", true);


            } catch (NKCheckedException e) {
                LOGGER.error("refreshMidnight failed", e);
            }
        }

        final long nextDayBeginTimeMs = todayBeginTimeMs + DateUtils.ONE_DAY_MILLIS;
        if (getUserAttr().getBasicInfo().getBeforeMidnightTimeMs() < todayBeginTimeMs
                && Framework.currentTimeMillis() < nextDayBeginTimeMs
                && Framework.currentTimeMillis() > nextDayBeginTimeMs - 300 * 1000) {
            try {
                long lastBeforeMidnightTimeMs = getUserAttr().getBasicInfo().getBeforeMidnightTimeMs();
                getUserAttr().getBasicInfo().setBeforeMidnightTimeMs(Framework.currentTimeMillis());
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnMidnightBefore();

                    Session.ClientInfo clientInfo = session.getClientInfo();
                    BasicInfo basicInfo = getUserAttr().getBasicInfo();
                    TlogFlowMgr.sendModPlayerLogoutFlow(this, clientInfo, basicInfo,
                            TlogMacros.LOGIN_FLOW_TYPE.MIDNIGHT_TRIGGER, NKErrorCode.OK.getValue(),
                            lastBeforeMidnightTimeMs);
                    return null;
                }, "modulesOnMidnightBefore", true);
            } catch (NKCheckedException e) {
                LOGGER.error("refreshMidnightBefore failed", e);
            }
        }

        long weekBeginMs = DateUtils.getFirstDayOfWeek(Framework.currentTimeMillis()).getTime();
        if (getUserAttr().getBasicInfo().getLastWeekRefreshTimeMs() < weekBeginMs) {
            try {
                getUserAttr().getBasicInfo().setLastWeekRefreshTimeMs(Framework.currentTimeMillis());
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnWeekStart();
                    return null;
                }, "modulesOnWeekStart", true);
            } catch (NKCheckedException e) {
                LOGGER.error("modulesOnWeekStart failed", e);
            }
        }

        //凌晨3点刷新
        final long threeTimeMs = todayBeginTimeMs + 3 * DateUtils.ONE_HOUR_MILLIS;
        if (getUserAttr().getBasicInfo().getThreeClockRefreshTimeMs() < threeTimeMs
                && Framework.currentTimeMillis() > threeTimeMs) {
            try {
                getUserAttr().getBasicInfo().setThreeClockRefreshTimeMs(Framework.currentTimeMillis());
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnDayThreeStart();

                    return null;
                }, "modulesOnDayThreeStart", true);
            } catch (NKCheckedException e) {
                LOGGER.error("modulesOnDayThreeStart failed", e);
            }
        }

        //凌晨5点刷新
        final long fiveTimeMs = todayBeginTimeMs + 5 * DateUtils.ONE_HOUR_MILLIS;
        if (getUserAttr().getBasicInfo().getFiveClockRefreshTimeMs() < fiveTimeMs
                && Framework.currentTimeMillis() > fiveTimeMs) {
            try {
                getUserAttr().getBasicInfo().setFiveClockRefreshTimeMs(Framework.currentTimeMillis());
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnDayFiveStart();
                    return null;
                }, "modulesOnDayFiveStart", true);
            } catch (NKCheckedException e) {
                LOGGER.error("refreshDayFiveStart failed", e);
            }
        }

        //凌晨6点刷新
        long sixTimeMs = todayBeginTimeMs + 6 * DateUtils.ONE_HOUR_MILLIS;
        if (getUserAttr().getBasicInfo().getSixClockRefreshTimeMs() < sixTimeMs
                && Framework.currentTimeMillis() > sixTimeMs) {
            try {
                getUserAttr().getBasicInfo().setSixClockRefreshTimeMs(Framework.currentTimeMillis());
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnDaySixStart();
                    return null;
                }, "modulesOnDaySixStart", true);
            } catch (NKCheckedException e) {
                LOGGER.error("refreshWeekStart failed", e);
            }
        }

        //每小时刷新
        long lastEveryHourRefreshTimeMs = getUserAttr().getBasicInfo().getEveryHourRefreshTimeMs();
        boolean hourlyRefresh = false;
        if (lastEveryHourRefreshTimeMs == 0) {
            hourlyRefresh = true;
        } else {
            long lastHours = lastEveryHourRefreshTimeMs / DateUtils.ONE_HOUR_MILLIS;
            if (Framework.currentTimeHour() - lastHours >= 1) {
                hourlyRefresh = true;
            }
        }
        if (hourlyRefresh) {
            try {
                getUserAttr().getBasicInfo().setEveryHourRefreshTimeMs(Framework.currentTimeMillis());
                CurrentExecutorUtil.runJob(() -> {
                    modulesOnEveryHourStart();
                    return null;
                }, "modulesOnEveryHourStart", true);
            } catch (NKCheckedException e) {
                LOGGER.error("refreshEveryHourStart failed", e);
            }
        }
    }

    /**
     * Warning：这里的逻辑必须是轻量级处理逻辑
     * 每次在线的proc调用，
     * 同步刷新
     */
    private void refreshOnlineProc() {
        if (!isOnline()) {
            return;
        }
        taskModule.refresh();
        bagManager.refresh();
        // 不一定时整点 所以得tick 内部已限频率
        redEnvelopRainActMgr.refresh();
        getActivityManager().onlineProc();
        getLbsManager().onlineProc();
        getRankManager().onlineProc();
        getIaaManager().onlineProc();
        getGameTvMgr().onlineProc();
        getRecommendMgr().onlineProc();
        getTradingCardModule().onlineProc();
        getPlayerEventManager().onlineProc();
        getPlayerCompetitionMgr().onlineProc();
        getPlayerLimitExperienceItermMgr().onlineProc();

        getUserAttr().getNewYearPilotInfo().setEnabled(NewYearPilotEntranceData.getInstance().getEntranceEnabled());

        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace("refreshPerClientRequest {}", getUid());
        }

    }


    // 立即刷新player的个人公共信息到tcaplus
    public void flushPlayerPublic() {
        PlayerPublicDao.flushPlayerPublicAttr(getUserAttr());
//        PlayerPublicDao.setSinglePlayerPublicDataToRedis(getUid(),
//                PlayerPublicDao.getPartPlayerPublicFromUserAttr(getUserAttr()));
    }

    /**
     * @return 交换机，如果有交换机会进一步派发
     */
    @Override
    public EventSwitch getEventSwitch() {
        return eventSwitch;
    }

    /**
     * @return 如果判定销毁，则不会派发事件
     */
    @Override
    public boolean isDestroyed() {
        return false;
    }

    public void onDestroy() {
        LOGGER.info("player {} start destroy", getUid());
        try {
            ObjLeakMgr.getInstance().offer(this);
            logoutWhenDestroy();
        } catch (Exception e) {
            LOGGER.error("player {} logoutWhenDestroy fail, e:", getUid(), e);
        } finally {
            getSeasonFashionBattleDataMgr().cancelTimer();
        }

        try {
            flushUserAttr(true);
            userAttrMgr.reportUserAttrSize();
        } catch (Exception e) {
            LOGGER.error("player {} flushUserAttr fail onDestroy , e:", getUid(), e);
        } finally {
            setInvalidate();
        }

        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                ServerEngine.getInstance().getMetadataClient().onUnload(MetaDataType.MDT_Player, getUid());
                return null;
            }, "MetaDataPlayerUnload", true);
        } catch (NKCheckedException e) {
            LOGGER.error("ntf proxy unload fail,{} e:{}", getUid(), e.getMessage());
        }
        LOGGER.info("player {} finish destroy", getUid());
    }

    protected void setVersion(int version) {
        userAttrMgr.getDbPlayer().setVersion(version);
    }

    @Override
    public AsyncTcaplusManager.TcaplusReq getUpdateRequest(boolean isAll) {
        return userAttrMgr.getTcaplusUpdateReq(isAll);
    }

    @Override
    public void onUpdateSuccess(AsyncTcaplusManager.TcaplusRsp response, AsyncTcaplusManager.TcaplusReq req) {
        LOGGER.debug("player {} {} onUpdateSuccess", getOpenId(), getUid());
        if (null != response && null != response.firstRecordData()) {
            setVersion(response.firstRecordData().version);
        }

        updateDBErrorCount = 0;

        Monitor.getInstance().add.total(MonitorId.attr_tcaplus_player_update_success_cnt, 1);
    }

    @Override
    public void onUpdateFailed(RuntimeException exception, AsyncTcaplusManager.TcaplusReq tcaplusReq) {
        ++updateDBErrorCount;
        if (!isCheck()) {
            LOGGER.warn("onUpdateFailed, not check, update force:{}", getUid());
            setVersion(-1);
            int updateErrorCntToRemove = PropertyFileReader.getIntItem("player_update_failed_to_remove_from_cache", 1);
            if (updateDBErrorCount >= updateErrorCntToRemove) {
                LOGGER.info("player {} update failed count {} limit exceed, will kick and remove", getUid(),
                        updateDBErrorCount);
                kickPlayer(NKErrorCode.DBOpFailed, NKErrorCode.DBOpFailed.name());
                PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.DBUpdateFailed);
            } else {
                updateDb(false);
            }
        } else {
            PlayerRefMgr.getInstance().removePlayer(getUid(), RemovePlayerReason.DBUpdateFailed);
        }

        Monitor.getInstance().add.total(MonitorId.attr_tcaplus_player_update_failed_cnt, 1);
    }

    @Override
    public boolean checkCanUpdate() {
        if (playerKickUpdated.get()) {
            LOGGER.info("update skip for kickupdated openid {} uid {}", getOpenId(), getUid());
            return false;
        }
        if (!Framework.getInstance().isDataCompatibleWith(userAttrMgr.getDbPlayer().getLastLoginSvrVersion())) {
            LOGGER.warn("openId:{} uid:{} lastLoginSvrVersion:{} greater than this serverVersion:{}",
                    getOpenId(), getUid(), userAttrMgr.getDbPlayer().getLastLoginSvrVersion(),
                    Framework.getInstance().getServerVersionNumber());
            return false;
        }
        if (userAttrMgr.getDbPlayer().getChange().isEmpty()) {
            LOGGER.info("update skip for no change openid {} uid {}", getOpenId(), getUid());
            return false;
        }
        if (!(isLoaded() || isRemoving())) {
            LOGGER.info("update skip for player {} status {}", getUid(), getLoadingState());
            return false;
        }
        if (banUpdate) {
            LOGGER.info("update skip for player {} banupdate", getUid());
            return false;
        }
        return true;
    }

    // 更新玩家已绑定渠道列表(从msdk 拉取)
    public void UpdateAccountBindList() {
        if (isRobot() || StringUtils.isBlank(getUserAttr().getPlayerProfileInfo().getAccessToken())) {
            return;
        }
        try {
            List<ItopManager.BindInfo> bindInfoList = ItopManager.getInstance()
                    .GetBindInfo(this.getPlatId(), this.getAccountType().getNumber(), this.getOpenId(),
                            this.getUserAttr().getPlayerProfileInfo().getAccessToken());
            if (!bindInfoList.isEmpty()) {
                Map<TconndApiAccount, BindAccountInfo> tempNew = new HashMap<>();
                for (ItopManager.BindInfo bindInfo : bindInfoList) {
                    if (TconndApiAccount.forNumber(bindInfo.accountType) == null) {
                        LOGGER.error("GetBindInfo accountType:{} not in TconndApiAccount enum", bindInfo.accountType);
                        continue;
                    }
                    BindAccountInfo tempAccountInfo = new BindAccountInfo();
                    tempAccountInfo.setAccountType(TconndApiAccount.forNumber(bindInfo.accountType))
                            .setTimestamp(bindInfo.timestamp);
                    tempNew.put(TconndApiAccount.forNumber(bindInfo.accountType), tempAccountInfo);
                }
                //更新BindAccountInfos
                this.getUserAttr().getPlayerPublicBasicInfo().putBindAccountInfosAll(tempNew);
            }

        } catch (Exception e) {
            LOGGER.error("GetBindInfo throw Exception uid:{} e:{}", getUid(), e);
        }
    }

    /**
     * 平台特权信息更新
     *
     * @param platPrivileges
     * @return 是否更新平台特权标记
     */
    public boolean setPlatPrivilegesInfo(int platPrivileges) {

        boolean isUpdatePlatPrivileges = false;

        ResGeneral.FeatureOpenConf conf = FeatureOpenConfData.getInstance()
                .get(FeatureOpenType.FOT_PlatPrivileges, getClientVersion64());
        if (conf == null || !conf.getIsShow()) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("platPrivileges function closed, openId:{}, uid:{}", this.getOpenId(), this.getUid());
            }
            return isUpdatePlatPrivileges;
        }

        // 用户注册渠道跟平台特权黑名单check判断
        String registerChannelId = this.getUserAttr().getPlayerPublicBasicInfo().getRegChannelDis();
        Integer channelSceneId = MiscConf.getInstance()
                .getPlatPrivilegesChannelSceneId(this.getAccountType().getNumber());
        if (channelSceneId != null) {
            ResChannelEntrance.ChannelDisableScene channelDisableScene =
                    ChannelDisableSceneConf.getInstance().get(channelSceneId);
            if (channelDisableScene != null && channelDisableScene.getChannelIdList() != null) {
                for (String channelId : channelDisableScene.getChannelIdList()) {
                    if (registerChannelId.equals(channelId)) {
                        LOGGER.info("player register channel match disable channel id list, openid:{}, uid:{}, " +
                                        "registerChannelId:{}, channelSceneId:{}, disableChannelIdList:{}",
                                this.getOpenId(), this.getUid(), registerChannelId, channelSceneId,
                                channelDisableScene.getChannelIdList());
                        return isUpdatePlatPrivileges;
                    }
                }
            }
        }

        PlatPrivilegesInfo info = this.getUserAttr().getPlayerPublicProfileInfo().getPlatProvileges();
        long currentTimeMs = Framework.currentTimeMillis();

        if (platPrivileges == PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE) {
            // 当用户信息表中的数据已开启特权, 且特权非当天时, 重置用户信息表中的平台特权信息
            if (info.getPlatPrivileges() != PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE &&
                    info.getExpireTimeMs() < currentTimeMs) {
                updatePlatPrivilegesInfo(PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE, 0, false,
                        true, true);
                isUpdatePlatPrivileges = true;
                Monitor.getInstance().add.total(MonitorId.attr_gamesvr_update_plat_privileges, 1);
            }
        } else {
            // 判断请求中的平台特权类型是否跟当前账号类型相符
            if ((platPrivileges == PlatPrivilegesType.QQ_PLAT_PRIVILEGES_VALUE &&
                    this.getAccountType().getNumber() == TconndApiAccount.TCONND_ITOP_CHANNEL_QQ_VALUE) ||
                    (platPrivileges == PlatPrivilegesType.WX_PLAT_PRIVILEGES_VALUE &&
                            this.getAccountType().getNumber() == TconndApiAccount.TCONND_ITOP_CHANNEL_WX_VALUE)) {
                // 当用户信息表中的数据未开启特权, 或者开启的特权非当天时, 更新用户信息表中的平台特权信息
                if (info.getPlatPrivileges() == PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE ||
                        info.getExpireTimeMs() < currentTimeMs) {
                    updatePlatPrivilegesInfo(platPrivileges, TimeUtil.getTomorrowMidnightTimeMs(), false,
                            true, true);
                    isUpdatePlatPrivileges = true;
                    Monitor.getInstance().add.total(MonitorId.attr_gamesvr_update_plat_privileges, 1);
                }
            }
        }

        return isUpdatePlatPrivileges;
    }

    /**
     * 发放平台特权邮件
     */
    public void sendPlatPrivilegesMail() {

        ResGeneral.FeatureOpenConf conf = FeatureOpenConfData.getInstance()
                .get(FeatureOpenType.FOT_PlatPrivileges, getClientVersion64());
        if (conf == null || !conf.getIsShow()) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("platPrivileges function closed, openId:{}, uid:{}", this.getOpenId(), this.getUid());
            }
            return;
        }

        PlatPrivilegesInfo info = this.getUserAttr().getPlayerPublicProfileInfo().getPlatProvileges();
        long currentTimeMs = Framework.currentTimeMillis();

        // 当用户无当前有效的平台特权, 或者平台特权奖励已经发放时, 直接返回
        if (info.getPlatPrivileges() == PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE ||
                info.getExpireTimeMs() < currentTimeMs || info.getIsSendGift()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("no need to send plat privileges mail, openId:{}, uid:{}, platPrivilegesInfo:{}, " +
                                "currentTimeMs:{}",
                        this.getOpenId(), this.getUid(), info, currentTimeMs);
            }
            return;
        }

        // 获取待发送的邮件模板id
        int mailTemplateId = 0;
        if (this.getAccountType().getNumber() == TconndApiAccount.TCONND_ITOP_CHANNEL_QQ_VALUE) {
            mailTemplateId = QQ_PLAT_PRIVILEGES_MAIL_TEMPLATE_ID;
        } else if (this.getAccountType().getNumber() == TconndApiAccount.TCONND_ITOP_CHANNEL_WX_VALUE) {
            mailTemplateId = WX_PLAT_PRIVILEGES_MAIL_TEMPLATE_ID;
        }

        if (mailTemplateId == 0) {
            LOGGER.debug("get mail template id is invalid, openId:{}, uid:{}, accountType:{}",
                    this.getOpenId(), this.getUid(), this.getAccountType().getNumber());
            return;
        }

        // 发送奖励邮件
        long mailId = MailInteraction.sendTemplateMail(this.getUid(), mailTemplateId,
                MailInteraction.TlogSendReason.platPrivilegesGift);
        if (mailId < 0) {
            LOGGER.error("sendTemplateMail failed, openId:{}, uid:{}, mailTemplateId:{}, mailId:{}",
                    this.getOpenId(), this.getUid(), mailTemplateId, mailId);
            return;
        }

        LOGGER.debug("send plat privileges mail success, openId:{}, uid:{}, mailId:{}",
                this.getOpenId(), this.getUid(), mailId);
        // 更新平台特权信息
        PlatPrivilegesInfo newInfo = this.getUserAttr().getPlayerPublicProfileInfo().getPlatProvileges();
        updatePlatPrivilegesInfo(newInfo.getPlatPrivileges(), newInfo.getExpireTimeMs(),
                true, false, false);
    }

    /**
     * 更新平台特权信息
     *
     * @param platPrivilegesType
     * @param expireTimeMs
     * @param isSendGift
     * @param isSendClientNtf
     * @param isSendTlogFlow
     */
    private void updatePlatPrivilegesInfo(int platPrivilegesType, long expireTimeMs, boolean isSendGift,
                                          boolean isSendClientNtf, boolean isSendTlogFlow) {
        ResGeneral.FeatureOpenConf conf = FeatureOpenConfData.getInstance()
                .get(FeatureOpenType.FOT_PlatPrivileges, getClientVersion64());
        if (conf == null || !conf.getIsShow()) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("platPrivileges function closed, openId:{}, uid:{}", this.getOpenId(), this.getUid());
            }
            return;
        }

        // 更新player里的平台特权信息
        this.getUserAttr().getPlayerPublicProfileInfo().getPlatProvileges()
                .setPlatPrivileges(platPrivilegesType)
                .setExpireTimeMs(expireTimeMs)
                .setIsSendGift(isSendGift)
                .setUpdateTime(Framework.currentTimeMillis());
        LOGGER.debug("plat privileges update, openId:{}, uid:{}, platPrivilegesType:{}, expireTimeMs:{}, " +
                        "isSendGift:{}",
                this.getOpenId(), this.getUid(), platPrivilegesType, expireTimeMs, isSendGift);
        friendManager.addChangeField(PlayerPublicInfoField.PLAT_PRIVILEGES);
        if (isSendClientNtf) {
            // 给客户端推送平台特权信息消息
            CsPlayer.PlatPrivilegesNtf.Builder builder = CsPlayer.PlatPrivilegesNtf.newBuilder()
                    .setPlatPrivileges(platPrivilegesType)
                    .setExpireTimeMs(expireTimeMs);
            this.sendNtfMsg(MsgTypes.MSG_TYPE_PLATPRIVILEGESNTF, builder);
        }

        if (isSendTlogFlow) {
            // 上报平台特权tlog流水
            TlogFlowMgr.sendPlatPrivilegesFlow(this);
        }
    }

    /**
     * 获取玩家当前的平台特权类型
     *
     * @return
     */
    public int getPlayerPlatPrivilegesType() {

        ResGeneral.FeatureOpenConf conf = FeatureOpenConfData.getInstance()
                .get(FeatureOpenType.FOT_BlessBag, getClientVersion64());
        if (conf == null || !conf.getIsShow()) {
            return PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE;
        }

        PlatPrivilegesInfo info = this.getUserAttr().getPlayerPublicProfileInfo().getPlatProvileges();
        if (info != null && info.getPlatPrivileges() != PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE &&
                Framework.currentTimeMillis() < info.getExpireTimeMs()) {
            return info.getPlatPrivileges();
        }

        return PlatPrivilegesType.NO_OPEN_PRIVILEGES_VALUE;
    }

    /**
     * 申请用户福袋
     */
    public void applyPlayerBlessBag() {

        ResGeneral.FeatureOpenConf conf = FeatureOpenConfData.getInstance()
                .get(FeatureOpenType.FOT_BlessBag, getClientVersion64());
        if (conf == null || !conf.getIsShow()) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("blessBag function closed, openId:{}, uid:{}", this.getOpenId(), this.getUid());
            }
            return;
        }

        // 福袋分享屏蔽渠道号逻辑判断, 如果玩家注册渠道号位于屏蔽渠道号列表中, 不开放福袋能力
        List<Integer> blackChannelIdList = MiscConf.getInstance().getBlessBagBlackChannelId();
        if (blackChannelIdList != null && blackChannelIdList.size() > 0) {
            int playerChannelId = this.getUserAttr().getPlayerPublicBasicInfo().getChannelId();
            if (blackChannelIdList.contains(playerChannelId)) {
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info("player register channel in share ingore channel list, openId:{}, uid:{}, channelId:{}",
                            this.getOpenId(), this.getUid(), playerChannelId);
                }
                return;
            }
        }

        // 判断用户是否有当天有效的福袋数据, 如果有, 直接返回
        PlayerBlessBagInfo blessBagInfo = this.getUserAttr().getPlayerBlessBagInfo();
        if (blessBagInfo.getBagId() != 0 && blessBagInfo.getExpireTimeMs() > Framework.currentTimeMillis()) {
            return;
        }

        long blessBagId = BlessBagSerialId.getInstance().allocGuid();
        long expireTimeMs = TimeUtil.getTomorrowMidnightTimeMs();
        blessBagInfo.setBagId(blessBagId)
                .setExpireTimeMs(expireTimeMs)
                .setUpdateTime(Framework.currentTimeMillis());
        LOGGER.debug("update player bless bag info, openId:{}, uid:{}, blessBagId:{}, expireTimeMs:{}",
                this.getOpenId(), this.getUid(), blessBagId, expireTimeMs);
        Monitor.getInstance().add.total(MonitorId.attr_gamesvr_alloc_bless_bag, 1);

        // 上报福袋操作tlog流水
        Integer worldId = Framework.getInstance().getWorldId();
        String envFlag = PropertyFileReader.getItem("env_flag", "");
        TlogFlowMgr.sendBlessBagOperateFlow(this, blessBagId, expireTimeMs,
                ServerIdipAreaConfig.getInstance()
                        .getFormalIdipArea(worldId, envFlag, this.getAccountType().getNumber()));

        // 给客户端推送ntf消息
        CsPlayer.PlayerBlessBagInfoNtf.Builder ntfBuilder = CsPlayer.PlayerBlessBagInfoNtf.newBuilder()
                .setBagId(blessBagId)
                .setExpireTimeMs(expireTimeMs);
        this.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERBLESSBAGINFONTF, ntfBuilder);
    }

    /**
     * 是否正在直播
     *
     * @return
     */
    public boolean isStreamOn() {
        return getUserAttr().getStreamSetting().getOnStream();
    }

    /**
     * 玩家是否处于回归状态
     *
     * @return true表示处于回归 false没有
     */
    public boolean isInReturningStatus() {
        return getReturnActivityManager().isReturnActivityActive();
    }

    /*
     * 获取玩家回流状态
     */
    public boolean getReturning() {
        return getUserAttr().getPlayerPublicProfileInfo().getReturning();
    }

    /*
     * 获取玩家回流过期时间
     */
    public long getReturnExpiredSec() {
        return getUserAttr().getPlayerPublicProfileInfo().getReturnExpiredSec();
    }

    /**
     * 安全Tlog缓存
     */
    public void handleClientTlogReportData(int type, String rawData) {
        if (!GSConfig.getEnableSecTlog()) {
            return;
        }
        switch (type) {
            case BattleDsTlogType.BDTT_SEC_ROUND_START_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_ROUND_END_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_ROUND_DETAIL_VALUE:
            case BattleDsTlogType.BDTT_SEC_ITEM_GET_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_ROUND_DETAIL_START_VALUE:
            case BattleDsTlogType.BDTT_SEC_CANYON_EAT_CHICKEN_ROUND_DETAIL_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_CANYON_3V3_ROUND_DETAIL_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_AFK_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_CANYON_3V3OP_ROUND_DETAIL_FLOW_VALUE:
                tlogCache.handleClientReportData(BattleDsTlogType.forNumber(type), rawData);
                break;
            default: {
                LOGGER.error("invalid type {}", type);
                break;
            }
        }
    }

    /**
     * 处理大厅/对局内共有的事件上报数据逻辑
     *
     * @param eventData
     */
    public void dsOnReportCommonEventDetailData(List<G6Common.BattleEventContext> eventData) {
        for (G6Common.BattleEventContext eventContext : eventData) {
            switch (eventContext.getSubEventType().getNumber()) {
                case BattleEventType.BET_PLAY_NEW_YEAR_CALL_VALUE: {
                    new PlayerPlayNewYearCallEvent(this).dispatch();
                    break;
                }
                case BattleEventType.BET_SEND_DANMU_VALUE: {
                    new PlayerSendDanmuEvent(this).dispatch();
                    break;
                }
            }
        }
    }

    /**
     * 安全Tlog缓存
     */
    public void handleDsTlogReportData(SsGamesvr.RpcReportTlogDataNtfReq.Builder req) {
        if (!GSConfig.getEnableSecTlog()) {
            return;
        }
        switch (req.getTlogType()) {
            case BattleDsTlogType.BDTT_SEC_ROUND_START_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_ROUND_END_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_ROUND_DETAIL_VALUE:
            case BattleDsTlogType.BDTT_SEC_ITEM_GET_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_GAME_SAFE_DATA_VALUE:
            case BattleDsTlogType.BDTT_SEC_VERIFY_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_CANYON_EAT_CHICKEN_ROUND_DETAIL_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_CANYON_3V3_ROUND_DETAIL_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_AFK_FLOW_VALUE:
            case BattleDsTlogType.BDTT_SEC_CANYON_3V3OP_ROUND_DETAIL_FLOW_VALUE:
                tlogCache.handleDsReportData(req.getBattleId(), BattleDsTlogType.forNumber(req.getTlogType()),
                        req.getData(), req.hasRoundStartData() ? req.getRoundStartData() : null);
                break;
            default: {
                LOGGER.error("invalid type {}", req.getTlogType());
                break;
            }
        }
    }

    /**
     * 安全Tlog缓存，在玩家开始新对局时刷新一些cache的数据
     */
    public void handleTlogBattleStart(long battleId, AttrBattleInfo.proto_BattleInfo battleInfo, MatchDynamicConfigData matchDynamicConfigData) {
        if (!GSConfig.getEnableSecTlog()) {
            return;
        }
        try {
            tlogCache.handleBattleStart(battleId, battleInfo, matchDynamicConfigData);
        } catch (Exception e) {
            LOGGER.error("TlogCache handleBattleStart failed", e);
        }
    }

    /**
     * 安全Tlog缓存。这个接口在玩家掉落物和段位分结算完成后调用
     */
    public void handleTlogBattleSettlement(long battleId, LetsGoBattleSettlementNtf.Builder settlementNtf) {
        if (!GSConfig.getEnableSecTlog()) {
            return;
        }
        try {
            tlogCache.handleBattleSettlement(battleId, settlementNtf);
        } catch (Exception e) {
            LOGGER.error("TlogCache handleBattleSettlement failed", e);
        }
    }

    /**
     * 获取B站导量标识
     *
     * @return
     */
    public String getTrackId() {
        return getUserAttr().getPlayerPublicBasicInfo().getTrackId();
    }

    // 小红花逻辑
    public void recordXhhFailureBill(int num, String transNo) {
        WelfareAerospaceTechEdData aerospaceTechEdData = getUserAttr().getWelfareData().getAerospaceTechEdData();
        WelfareHistoryBill bills = aerospaceTechEdData.getBills(transNo);
        if (bills != null) {
            LOGGER.info("player record the same bill for xhh, player:{} no:{}", getUid(), transNo);
            return;
        }
        bills = new WelfareHistoryBill();
        bills.setTransactionNo(transNo);
        bills.setBillType(1);
        bills.setBillNum(num);
        bills.setRecordTimeMs(DateUtils.currentTimeMillis());
        bills.setRetryTimes(0);
        aerospaceTechEdData.putBills(transNo, bills);
    }

    public void recordMedalFailureBill(int num, String transNo) {
        WelfareAerospaceTechEdData aerospaceTechEdData = getUserAttr().getWelfareData().getAerospaceTechEdData();
        WelfareHistoryBill bills = aerospaceTechEdData.getBills(transNo);
        if (bills != null) {
            LOGGER.info("player record the same bill for medal, player:{} no:{}", getUid(), transNo);
            return;
        }
        bills = new WelfareHistoryBill();
        bills.setTransactionNo(transNo);
        bills.setBillType(2);
        bills.setBillNum(num);
        bills.setRecordTimeMs(DateUtils.currentTimeMillis());
        bills.setRetryTimes(0);
        aerospaceTechEdData.putBills(transNo, bills);
    }

    public void xhhRetryFailureBill() {
        // 针对线上问题做一个修复
        List<Integer> taskIdList = new ArrayList<>();
        // 拿到活动的
        for (ResActivity.ActivityMainConfig activityMainConfig : ActivityMainConfig.getInstance().getArrayList()) {
            if (activityMainConfig.getActivityType() == ActivityType.ATNNWelfare &&
                    activityMainConfig.getActivityNameType() == ActivityNameType.ANTWelfareAerospaceTechEd) {
                if (activityMainConfig.getActivityParamCount() < 2) {
                    continue;
                }
                int processTaskGroupId = activityMainConfig.getActivityTaskGroup(1);
                TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(processTaskGroupId);
                if (taskGroup == null) {
                    continue;
                }
                taskIdList = taskGroup.getTaskIdListList();
                break;
            }
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("task id list from xhh activity, player:{} list:{}", getUid(), taskIdList);
        }
        WelfareAerospaceTechEdData aerospaceTechEdData = getUserAttr().getWelfareData().getAerospaceTechEdData();
        boolean needTriggerProduce = true;
        if (taskIdList.size() > 0) {
            Set<String> taskIdSet = new HashSet<>();
            for (Integer taskId : taskIdList) {
                taskIdSet.add(String.valueOf(taskId));
            }
            // 判断之前是否处理过，即bills里存在任何一个taskid为transationNo
            for (WelfareHistoryBill bill : aerospaceTechEdData.getBills().values()) {
                if (taskIdSet.contains(bill.getTransactionNo())) {
                    // 说明之前处理过
                    needTriggerProduce = false;
                    break;
                }
            }
        }
        if (needTriggerProduce) {
            // 先清理身上所有修复之前的待重试订单
            LOGGER.info("player start to repair xhh and medal produce logic, player:{}", getUid());
            aerospaceTechEdData.clearBills();
            for (int taskId : taskIdList) {
                RunTask task = getTaskManager().getTask(taskId);
                if (task == null) {
                    continue;
                }
                boolean needRepair =
                        task.getStatus() == TaskStatus.TS_Rewarded || task.getStatus() == TaskStatus.TS_Finish;
                for (int i = 0; i < task.getTaskRewardConfig().getItemIdListCount(); i++) {
                    int itemId = task.getTaskRewardConfig().getItemIdList(i);
                    Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
                    if (itemConf.getType() == ItemType.ItemType_GongyiXHH) {
                        if (needRepair) {
                            TencentWelfareManager.getInstance().produceXHH(this, getUid(),
                                    getUserAttr().getPlayerPublicProfileInfo().getPlatOpenId(),
                                    getAccountType().getNumber(), getAccessToken(),
                                    task.getTaskRewardConfig().getNumList(i), "");
                        }
                        recordXhhFailureBill(task.getTaskRewardConfig().getNumList(i), String.valueOf(taskId));
                    } else if (itemConf.getType() == ItemType.ItemType_GongyiMedal) {
                        if (needRepair) {
                            TencentWelfareManager.getInstance().awardMedal(this, getUid(),
                                    getUserAttr().getPlayerPublicProfileInfo().getPlatOpenId(),
                                    getAccountType().getNumber(), getAccessToken(),
                                    task.getTaskRewardConfig().getNumList(i), "");
                        }
                        recordMedalFailureBill(task.getTaskRewardConfig().getNumList(i), String.valueOf(taskId));
                    }
                }
            }
        }
        if (aerospaceTechEdData.getBillsSize() == 0) {
            return;
        }
        Set<String> noToDelete = new HashSet<>();
        for (WelfareHistoryBill bill : aerospaceTechEdData.getBills().values()) {
            String[] split = bill.getTransactionNo().split("_");
            if (split.length == 1) {
                LOGGER.debug("skip repair bill, player:{} billNo:{}", getUid(), bill.getTransactionNo());
                continue;
            }
            if (bill.getRetryTimes() > 3) {
                LOGGER.error("player retry xhh bill over 3 times, player:{} type:{} no:{}", getUid(),
                        bill.getBillType(), bill.getTransactionNo());
                noToDelete.add(bill.getTransactionNo());
                continue;
            }
            int ret = 0;
            if (bill.getBillType() == 1) {
                ret = TencentWelfareManager.getInstance().produceXHH(this, getUid(),
                        getUserAttr().getPlayerPublicProfileInfo().getPlatOpenId(),
                        getAccountType().getNumber(), getAccessToken(), bill.getBillNum(), bill.getTransactionNo());
            } else if (bill.getBillType() == 2) {
                ret = TencentWelfareManager.getInstance().awardMedal(this, getUid(),
                        getUserAttr().getPlayerPublicProfileInfo().getPlatOpenId(),
                        getAccountType().getNumber(), getAccessToken(), bill.getBillNum(), bill.getTransactionNo());
            }
            if (ret == 0 || ret == TencentWelfareUtils.ERR_CODE_XHH_LIMITED
                    || ret == TencentWelfareUtils.ERR_CODE_MEDAL_LIMITED) {
                LOGGER.info("bill retry succ with ret, player:{} no:{} ret:{}", getUid(), bill.getTransactionNo(), ret);
                noToDelete.add(bill.getTransactionNo());
            }
        }
        for (String no : noToDelete) {
            aerospaceTechEdData.removeBills(no);
        }
    }

    public int getMatchIsolateID() {
        MatchIsolateInfoDb matchIsolateInfo = getUserAttr().getMatchStatics().getMatchIsolateInfo();
        if (0 == matchIsolateInfo.getType()) {
            return 0;
        }

        if (null == MatchIsolateType.forNumber(matchIsolateInfo.getType())) {
            LOGGER.warn("getMatchIsolateID type error {} {}", getUid(), matchIsolateInfo);
            return 0;
        }

        if (Framework.currentTimeSec() > matchIsolateInfo.getTime()) {
            return 0;
        }

        return matchIsolateInfo.getType();
    }

    public proto_SquadMember.Builder getSquadMember() {
        proto_SquadMember.Builder joiner = proto_SquadMember.newBuilder()
                .setUid(getUid())
                .setNickname(getName())
                .setGender(getGender())
                .setProfile(getProfile())
                .setLevel(getLevel())
                .setOpenid(getOpenId())
                .setJoinTimeMs(DateUtils.currentTimeMillis());
        return joiner;
    }

    public TlogRequiredFields.Builder makeTlogRequiredFieldsPb() {
        var pb = TlogRequiredFields.newBuilder()
                .setPlatID(getPlatId())
                .setVopenid(getOpenId())
                .setVRoleID(getRoleId())
                .setAppId(PlayerUtil.accountType2AppId(getAccountType()))
                .setLevel(getLevel())
                .setServerIp(getServerIp())
                .setISequence(getSequence())
                .setTelecomOper(getTelecomOper() != null ? getTelecomOper() : "")
                .setNetwork(getNetwork() != null ? getNetwork() : "")
                .setClientIP(getClientIP() != null ? getClientIP() : "")
                .setClientVersion(getClientVersion() != null ? getClientVersion() : "")
                .setVClientIPV6(getClientIPV6() != null ? getClientIPV6() : "")
                .setClientPlat(getClientPlat())
                .setSeasonId(getSeasonId())
                .setCountry(getCountry() != null ? getCountry() : "")
                .setProvince(getProvince() != null ? getProvince() : "")
                .setCity(getCity() != null ? getCity() : "")
                .setDistrict(getDistrict() != null ? getDistrict() : "")
                .addReservePara(getLanguage() != null ? getLanguage() : "")
                .addReservePara(String.valueOf(getAccountType().getNumber()))
                .addReservePara(String.valueOf(getShortUid()))
                .addReservePara(String.valueOf(getLoginPlat()))
                .addReservePara(String.valueOf(getIsVa()))
                .addReservePara(String.valueOf(getJQCallInfo()))
                .addReservePara(String.valueOf(updateAndGetTlogCounter()))
                .addReservePara(String.valueOf(getChannel()));
        return pb;
    }

    // 玩家设置相关逻辑
    public void fixStrangerFollowSetting() {
        final String fixStr = "fix_s_foll_set#";
        KeyValStr kv = getUserAttr().getFixTag(fixStr);
        if (kv == null) {
            getUserAttr().getPlayerPublicGameSettings().setStrangerFollow(true);
            kv = new KeyValStr();
            kv.setK(fixStr);
            kv.setV("1");
            getUserAttr().putFixTag(fixStr, kv);
        }
    }

    /**
     * 是否禁用公共聊天
     *
     * @return
     */
    public boolean forbiddenPublicChat() {
        if (getUserAttr().getPlayerPublicGameSettings().getGameProtectionMainSwitch()) {
            return getUserAttr().getPlayerPublicGameSettings().getGameProtectionSpecificSwitch()
                    .getForbiddenPublicChat();
        }
        return false;
    }

    /**
     * 是否禁止陌生人私聊
     *
     * @return
     */
    public boolean forbiddenStrangerChat() {
        if (getUserAttr().getPlayerPublicGameSettings().getGameProtectionMainSwitch()) {
            return getUserAttr().getPlayerPublicGameSettings().getGameProtectionSpecificSwitch()
                    .getForbiddenStrangerChat();
        }
        return false;
    }

    /**
     * 是否禁止陌生人的room邀请
     *
     * @return
     */
    public boolean forbiddenStrangerRoomInvitation() {
        if (getUserAttr().getPlayerPublicGameSettings().getGameProtectionMainSwitch()) {
            return getUserAttr().getPlayerPublicGameSettings().getGameProtectionSpecificSwitch()
                    .getForbiddenStrangerRoomInvitation();
        }
        // 兼容旧开关设置
        if (getUserAttr().getPlayerPublicGameSettings().getHideRoomInvitation()) {
            return true;
        }
        return false;
    }

    /**
     * 是否禁止陌生人进入小窝
     *
     * @return
     */
    public boolean forbiddenStrangerVisitXiaowo() {
        if (getUserAttr().getPlayerPublicGameSettings().getGameProtectionMainSwitch()) {
            return getUserAttr().getPlayerPublicGameSettings().getGameProtectionSpecificSwitch()
                    .getForbiddenStrangerVisitXiaowo();
        }
        return false;
    }
    // 玩家设置相关逻辑

    public int getArenaHeadFrame() {
        return this.arenaSelectHeadFrame;
    }

    public void setArenaHeadFrame(int headFrame) {
        this.arenaSelectHeadFrame = headFrame;
    }

    public LevelDropItemInfo.Builder getArenaHeroCoinDropInfo() {
        return arenaHeroCoinDropInfo;
    }

    public void setArenaHeroCoinDropInfo(LevelDropItemInfo.Builder info) {
        arenaHeroCoinDropInfo = info;
    }

    public void clearArenaHeroCoinDropInfo() {
        if (arenaHeroCoinDropInfo != null) {
            arenaHeroCoinDropInfo.clear();
        }
    }

    public void dispatch(BaseEvent event) {
        getPlayerEventManager().dispatch(event);
    }

    /**
     * 获取对局聊天上报信息
     *
     * @return 对局聊天上报信息
     */
    public BattleChatReportInfo getBattleChatReportInfo() {
        return battleChatReportInfo;
    }

    /**
     * 设置对局聊天上报信息
     *
     * @param battleChatReportInfo 对局聊天上报信息
     */
    public void setBattleChatReportInfo(BattleChatReportInfo battleChatReportInfo) {
        this.battleChatReportInfo = battleChatReportInfo;
    }

    /**
     * 检查cs协议是否能通过频率控制
     *
     * @param msgId       消息id
     * @param rateLimit   Handler的注解的频率控制
     * @param recvMillSec tconnd收到消息的时间缀毫秒
     * @return true表示通过了rpc频率控制, false表示访问太快 应该拒绝
     */
    public boolean csRpcRateLimit(int msgId, RpcRateLimit rateLimit, long recvMillSec) {
        return csRpcRateLimitController.checkRateLimit(msgId, recvMillSec, rateLimit);
    }

    public enum LoadingState {
        LS_Invalid,
        LS_Registering,
        LS_Loading,
        LS_Loaded,
        LS_NotUse,
        //        LS_Check,
        LS_Removing,
    }

    enum PlayerKickState {
        None, Kicking, Kicked
    }

    class SizeChecker implements Runnable {

        private void checkSize() {
            jolLogger.error("start checkSize player {} {} ", getOpenId(), getUid());
            long maxSize = PropertyFileReader.getRealTimeLongItem("PlayerMaxSize", 1024 * 1024);
            GraphLayout graphLayout = JolTool.parseWithFilter(Player.this, maxSize, null);
            if (graphLayout == null) {
                return;
            }
            //  GraphLayout graphLayout = JolTool.parseRetain(Player.this, maxSize, getClass().getClassLoader(),
            //  CoroutineMgr.getInstance());
            int maxlv = PropertyFileReader.getRealTimeIntItem("SizeCheckLv", 2);
            long curSize = JolTool.getTotalSize(graphLayout);
            String jolInfo = JolTool.getLeveledStringBuffer(graphLayout, maxlv).toString();
            if (curSize >= maxSize) {
                jolLogger.error("found player size larger than {} >= {}", curSize, maxSize);
                jolLogger.error("\n{}", graphLayout.toFootprint());
                WechatLog.debugPanicLog(NKStringFormater
                        .format("found player {} {} size larger than {} >= {}\n{}", getOpenId(), getUid(),
                                curSize, maxSize));
                WechatLog
                        .debugPanicLog(
                                NKStringFormater.format("\n{}", jolInfo));
                //jolLogger.error("footprint \n{}", JolTool.parseRetain(graphLayout, getClass().getClassLoader(),
                // CoroutineMgr.getInstance()).toFootprint());
                jolLogger.debug("idipBanInfo size:{}", idipBanInfo.getSerializedSize());
            }

            long start = Framework.currentTimeMillis();
            jolLogger.error("\n{}", jolInfo);
            jolLogger.error("end checkSize player {} {} cost {} size {}", getOpenId(), getUid(),
                    Framework.currentTimeMillis() - start, curSize);
        }

        @Override
        public void run() {
            try {
                interval = PropertyFileReader.getRealTimeIntItem("SizeCheckInterval", 10 * 1000);
                if (ServerEngine.getInstance().isEnvName("haoyangwei_dev") || ServerEngine.getInstance()
                        .isEnvName("客户端daily idc")) {
                    interval = 10 * 1000;
                }
                if (!GSEngine.getInstance().isBusiness()
                        && !GSEngine.getInstance().isPressTest()) {
                    if (Framework.currentTimeMillis() - playerLastSizeCheck >= interval) {
                        playerLastSizeCheck = Framework.currentTimeMillis();
                        checkSize();
                    }
                } else {
                    if (Framework.currentTimeMillis() - lastSizeCheck.get() >= interval) {
                        lastSizeCheck.set(Framework.currentTimeMillis());
                        checkSize();
                    }
                }
            } catch (Exception e) {
                jolLogger.error("", e);
            } finally {
                checkerRunning.set(false);
            }
        }
    }

    public boolean isEstimation(int levelId) {
        SetAttrObj<Integer> levelIds = playerGameLevelEstimationMgr.getPlayerLevelEstimation().getLevelId();
        if (levelIds.contains(levelId)) {
            return true;
        }
        return false;
    }

    // 检查是否封禁了ugc自定义成就创建和修改
    public boolean checkIsBanCreateOrModifyUgcAchievement() {
        BanStatus banStatus = getBanStatus(BanType.BT_CreateOrModifyUgcAchievement);
        return banStatus != null && banStatus.hasBanBefore()
                && banStatus.getBanBefore() > Framework.currentTimeMillis();
    }

    PlayerPushTopic getPushTopicInfo() {
        return getUserAttr().getPushTopicInfo();
    }

    @Override
    public String getAccountName() {
        return getOpenId();
    }

    @Override
    public PushTopic.Handle queryPushTopicHandle(String index) {
        PushTopicHandle handle = getPushTopicInfo().getHandle(index);
        if (null == handle) {
            return null;
        }
        return new PushTopic.Handle(handle.getTopic(), handle.getPartition());
    }

    @Override
    public void recordPushTopicHandle(String index, PushTopic.Handle handle) {
        if (null == handle) {
            return;
        }
        if (StringUtils.isAnyBlank(index, handle.topic, handle.partition)) {
            return;
        }
        var pushTopicInfo = getPushTopicInfo();
        pushTopicInfo.putHandle(index, new PushTopicHandle()
                .setIndex(index)
                .setTopic(handle.topic)
                .setPartition(handle.partition)
                .setUpTimeSec(DateUtils.currentTimeSec()));

        int washoutCount = pushTopicInfo.getHandleSize() - PropertyFileReader.getRealTimeIntItem("player_push_topic_max_handle", 8);
        if (washoutCount > 0) {
            // wash out
            ArrayList<String> indices = new ArrayList<>(pushTopicInfo.getHandle().keySet());
            indices.sort(Comparator.comparingLong((String k) -> pushTopicInfo.getHandle(k).getUpTimeSec()));
            for (int i = 0; i < indices.size() && i < washoutCount; ++i) {
                pushTopicInfo.removeHandle(indices.get(i));
                LOGGER.warn("player:{} wash out push handle:{}", getUid(), indices.get(i));
            }
        }
    }

    public PlayerCocMgr getCocMgr() {
        return cocMgr;
    }

    public long getRoomModifyTime() {
        return roomModifyTime;
    }

    public void setRoomModifyTime(long roomModifyTime) {
        this.roomModifyTime = roomModifyTime;
    }

    // SP 组队信息,精简组队数据
    public MemberBaseInfo.Builder getSPMemberBaseInfoBuilder() {
        MemberBaseInfo.Builder memberInfo = MemberBaseInfo.newBuilder();
        UserAttr userAttr = getUserAttr();
        memberInfo.setUid(getUid());
        memberInfo.setName(getName());
        memberInfo.setFace(userAttr.getPlayerPublicProfileInfo().getProfile());
        memberInfo.setGender(userAttr.getPlayerPublicProfileInfo().getGender());
        memberInfo.setOpenId(getOpenId());
        memberInfo.setPlatId(getPlatId());
        memberInfo.setClientVersion(getCompVersion());
        memberInfo.setLevel(getLevel());
        memberInfo.setAccountType(getAccountType());

//        memberInfo.setQualifyingInfo(getQualifyingManager().getQualifyingInfo());
//        // 副玩法段位信息 目前先这样
//        memberInfo.putAllSecondaryGameplayQualifyingInfos(
//                getQualifyingManager().getSecondaryGameplayQualifyingInfo());

        // StarP目前玩法只有PVP排位，如果以后有其他排位再补充或直接使用上面的注释
        QualifyingDegreeInfo typeQualifyDegreeInfo = getQualifyingManager().getQualifyingInfo(QualifyType.QT_StarP.getNumber());
        if (typeQualifyDegreeInfo.getIntegral() > 0) {
            memberInfo.putSecondaryGameplayQualifyingInfos(QualifyType.QT_StarP.getNumber(), typeQualifyDegreeInfo);
        }

        memberInfo.setSceneId(userAttr.getPlayerPublicSceneData().getLobbyInfo().getLobbyId());
        memberInfo.setFashionValue(userAttr.getPlayerPublicEquipments().getFashionValue());
        memberInfo.addAllDressUpItems(userAttr.getPlayerPublicEquipments().getDressUpInfosList());
        // 添加PlayerPublicEquipments.dressItemInfo的装备信息(铭牌、称号、头像框等)
        for (DressItemInfo dressItemInfo : userAttr.getPlayerPublicEquipments().getDressItemInfo().values()) {
            if (dressItemInfo.getItemId() == 0) {
                continue;
            }
            memberInfo.addDressUpItems(dressItemInfo.getItemId());
        }
        for (DressUpDetailInfo dressUpDetailInfo : userAttr.getPlayerPublicEquipments().getDressUpDetailInfos()
                .values()) {
            proto_DressUpDetailInfo.Builder dressUpDetailInfoBuilder = proto_DressUpDetailInfo.newBuilder();
            dressUpDetailInfo.copyToSs(dressUpDetailInfoBuilder);
            memberInfo.putDressUpItemDetails(dressUpDetailInfo.getItemId(), dressUpDetailInfoBuilder.build());
        }
        memberInfo.setHeadFrame(getHeadFrame());
        memberInfo.setNamePlate(getNamePlate());
        memberInfo.setProfileTheme(userAttr.getPlayerPublicEquipments().getProfileTheme()); // 设置玩家的默认主页背景
        memberInfo.setCreatorAccountInfo(CreatorAccountInfo.newBuilder()
                .setAuthType(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthType())
                .setAuthDesc(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthDesc())
                .build());
        if (getPlayerRoomMgr().getTeamId() > 0) { //队伍语音临时存在玩家身上，同步的时候，一并带到队伍里
            memberInfo.getMemberClientInfoBuilder()
                    .setVoiceState(getUserAttr().getRoomInfo().getRoomMemberClientInfo().getVoiceState());
            memberInfo.getMemberClientInfoBuilder()
                    .setVoiceRoomTag(getUserAttr().getRoomInfo().getRoomMemberClientInfo().getVoiceRoomTag());
        }
        if (GSConfig.isMultiIdcDsEnable()) {
            // 填充网络信息
            memberInfo.setIdcNetworkInfo(getIdcNetworkInfoManager().getIDCNetworkInfo());
            // 填充匹配区域id
            memberInfo.setLoginCountryCode(getPlayerMatchMgr().getLoginCountryCode());
        }
        memberInfo.setRegisterTimeMs(getRegisterTime());
        memberInfo.setLoginTimeMs(getLoginTime());
        memberInfo.setMobileGearLevel(userAttr.getPlayerProfileInfo().getMobileGearLevel());
        // 玩家开播相关设置
        memberInfo.setStreamStateInfo(
                StreamStateInfo.newBuilder().setIsStreamOn(getUserAttr().getStreamSetting().getOnStream())
                        .setStreamToken(getUserAttr().getStreamSetting().getStreamToken()));
        memberInfo.setCreatorAccountInfo(CreatorAccountInfo.newBuilder()
                .setAuthType(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthType())
                .setAuthDesc(getUserAttr().getPlayerPublicProfileInfo().getUgcAuthDesc())
                .build());
        memberInfo.setQqTeamTaskId(profileManager.getQQTeamTaskId());
        memberInfo.setUserIp(getSession() != null ? getSession().getIpAddr() : "");
        memberInfo.setUserProfile(getUserAttr().getPlayerPublicProfileInfo().getProfile());
        memberInfo.setDeviceType(getIsSimulate() ? 1 : 0);
        if (getDeviceLevel() != null) {
            memberInfo.setDeviceLevel(getDeviceLevel());
        }
        memberInfo.addAllGrayTags(getPlayerGrayTagMgr().getGrayTagsList());
        memberInfo.getStatusDetailsBuilder().setStatusDetails(userAttr.getPlayerPublicLiveStatus().getCopyCsBuilder()
                .getStatusDetails());
        memberInfo.getStatusDetailsBuilder().setSceneData(userAttr.getPlayerPublicSceneData().getCopyCsBuilder());
        memberInfo.setHideProfileToFriend(getUserAttr().getPlayerPublicGameSettings().getHideProfileToFriend());
        memberInfo.setHideProfileToStranger(getUserAttr().getPlayerPublicGameSettings().getHideProfileToStranger());
        // 白名单
        for (XlsWhiteList xlsWhiteList : getUserAttr().getXlsWhiteList().values()) {
            if (xlsWhiteList.getStatus() == 1) {
                memberInfo.addWhitelistModuleId(xlsWhiteList.getModuleId());
            }
        }
        // update game play feature info
        var playInfos = dumpPlayInfos();
        if (playInfos != null && !playInfos.isEmpty()) {
            for (com.tencent.wea.protocol.common.GamePlayBrief onePlayInfo : playInfos) {
                if (GamePlay.typeOf("StarP") == onePlayInfo.getFeatureType()) {
                    // 只需要将sp副玩法的版本号带过去
                    memberInfo.addPlayInfo(onePlayInfo);
                }
            }
        }
        memberInfo.setStarPBanTime(getPlayerStarPMgr().getBanTime());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("starp getMemberBaseInfo uid:{} data:{}",
                    getUid(), TextFormat.shortDebugString(memberInfo));
        }
        return memberInfo;
    }

    public void sendDeviceIdCheckErrorNtf(Session session) {
        session.sendNtfMsg(MsgTypes.MSG_TYPE_DEVICEIDCHECKERRORNTF, CsPlayer.DeviceIdCheckErrorNtf.newBuilder());
    }

    public void sendDeviceHasNoPermissionNtf(Session session) {
        session.sendNtfMsg(MsgTypes.MSG_TYPE_DEVICEHASNOPERMISSIONNTF, CsPlayer.DeviceHasNoPermissionNtf.newBuilder());
    }
}
