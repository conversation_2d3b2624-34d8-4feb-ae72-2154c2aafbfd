package com.tencent.wea.playerservice.event.common.house;

import com.tencent.eventcenter.EventConsumer;
import com.tencent.eventcenter.EventRouterEnum;
import com.tencent.resourceloader.resclass.FarmItemConf;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.keywords.EventDataType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.EventType;
import com.tencent.wea.xlsRes.keywords.FarmCropRipeQuality;
import java.util.Collection;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class PlayerFarmRoomCropMachineHarvestEvent extends BasePlayerEvent {

    private static final Logger LOGGER = LogManager.getLogger(PlayerFarmRoomCropMachineHarvestEvent.class);

    public PlayerFarmRoomCropMachineHarvestEvent(Player player, int itemId, FarmCropRipeQuality quality) {
        super(player);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("new PlayerFarmRoomCropMachineHarvestEvent, uid:{}, itemId:{}, FarmCropRipeQuality:{}", player.getUid(), itemId, quality);
        }

        var farmItemConf = FarmItemConf.get(itemId);
        if (farmItemConf != null) {
            this.eventData.put(EventDataType.EDT_FarmCropCategory, farmItemConf.getCropCategory().getNumber());
        } else {
            this.eventData.put(EventDataType.EDT_FarmCropCategory, 0);
        }
        if (quality != null) {
            this.eventData.put(EventDataType.EDT_FarmCropRipeQuality, quality.getNumber());
        } else {
            this.eventData.put(EventDataType.EDT_FarmCropRipeQuality, 0);
        }
    }

    @Override
    public long getStatisticParam() {
        return 0;
    }

    @Override
    public long getStatisticData() {
        return 0;
    }

    @Override
    public int getEventType() {
        return EventType.ET_PlayerFarmRoomCropMachineHarvest.getNumber();
    }

    @Override
    @EventRouterEnum(router = EventRouterType.ERT_PlayerFarmRoomCropMachineHarvest)
    public Collection<? extends EventConsumer> routeToConditionRegistry() {
        return super.routeToConditionRegistry();
    }
}
