package com.tencent.wea.playerservice.lbs;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.event.common.PlayerPublicGameSettingsChangeEvent;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.lbs.selflocation.NearbyList;
import com.tencent.wea.playerservice.lbs.selflocation.SelfLocation;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsLbs.LBSDelete_C2S_Msg;
import com.tencent.wea.protocol.CsLbs.LBSDelete_S2C_Msg;
import com.tencent.wea.protocol.CsLbs.LBSGetNearby_C2S_Msg;
import com.tencent.wea.protocol.CsLbs.LBSGetNearby_S2C_Msg;
import com.tencent.wea.protocol.CsLbs.LBSUpdate_C2S_Msg;
import com.tencent.wea.protocol.CsLbs.LBSUpdate_S2C_Msg;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class LBSManager extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(LBSManager.class);
    private SelfLocation selfLocation;
    private NearbyList nearbyList;

    public LBSManager(Player player) {
        super(GameModuleId.GMI_LBS, player);
        selfLocation = SelfLocation.ofNowhere(player);
        nearbyList = null;

        player.getEventSwitch().register(new OnSettingChange());
    }

    public LBSUpdate_S2C_Msg.Builder handleUpdate(LBSUpdate_C2S_Msg reqMsg) {
        LBSUpdate_S2C_Msg.Builder resBuilder = LBSUpdate_S2C_Msg.newBuilder();
        if (isHideLocation()) {
            LOGGER.error("player hide its location, uid:{}", player.getUid());
            NKErrorCode.LBSNotActivate.throwError("location is hide");
            return resBuilder;
        }

        if (!selfLocation.isExpired()) {
            LOGGER.error("update location too frequent, uid:{}", player.getUid());
            NKErrorCode.LBSUpdateTooFrequent.throwError("update location too frequent");
            return resBuilder;
        }

        if (!reqMsg.hasLatitude() || !reqMsg.hasLongitude()) {
            LOGGER.error("both latitude and longitude should be assigned, uid:{}", player.getUid());
            NKErrorCode.LBSCoordinateInvalid.throwError("both latitude and longitude should be assigned");
            return resBuilder;
        }

        long latitudeE6 = Math.round(reqMsg.getLatitude() * 1_000_000);
        long longitudeE6 = Math.round(reqMsg.getLongitude() * 1_000_000);

        selfLocation = SelfLocation.of(player, (int) latitudeE6, (int) longitudeE6);
        LOGGER.debug("create new self location, uid:{}", player.getUid());

        return resBuilder;
    }

    public LBSGetNearby_S2C_Msg.Builder handleGetNearby(LBSGetNearby_C2S_Msg reqMsg) {
        LBSGetNearby_S2C_Msg.Builder resBuilder = LBSGetNearby_S2C_Msg.newBuilder();

        if (nearbyList == null || nearbyList.isExpired()) {
            nearbyList = selfLocation.ofNearby().orElse(null);
            if (nearbyList == null) {
                LOGGER.error("nearby list not activate, uid:{}", player.getUid());
                NKErrorCode.LBSNotActivate.throwError("nearby list not activate");
                return resBuilder;
            }
            LOGGER.debug("create new nearby list, uid:{}", player.getUid());
            nearbyList.proc();
        }

        return resBuilder.setListId(nearbyList.getCreateTs()).addAllList(nearbyList.getList());
    }

    // 这是同步接口，注意不要直接在cs消息里调用
    public NearbyList getNearbyListSynchronize() {
        // copy from handleGetNearby
        if (nearbyList == null || nearbyList.isExpired()) {
            nearbyList = selfLocation.ofNearby().orElse(null);
            if (nearbyList == null) {
                LOGGER.error("nearby list not activate, uid:{}", player.getUid());
                NKErrorCode.LBSNotActivate.throwError("nearby list not activate");
                return null;
            }
            LOGGER.debug("create new nearby list, uid:{}", player.getUid());
            nearbyList.procSynchronize();
        }
        return nearbyList;
    }

    public LBSDelete_S2C_Msg.Builder handleDelete(LBSDelete_C2S_Msg reqMsg) {
        LBSDelete_S2C_Msg.Builder resBuilder = LBSDelete_S2C_Msg.newBuilder();
        closeAll();
        return resBuilder;
    }

    public void onlineProc() {
        selfLocation.proc();
        if (nearbyList != null) {
            nearbyList.proc();
        }
    }

    private void closeAll() {
        selfLocation.close();
        nearbyList = null;
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {

    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {

    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {
        selfLocation.close();
    }

    @Override
    public void onMidNight() {

    }

    @Override
    public void onWeekStart() {

    }

    private boolean isHideLocation() {
        return player.getUserAttr().getPlayerPublicGameSettings().getHideLocation();
    }

    private class OnSettingChange implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_PlayerGameSettingsChangePlayer)
        public NKErrorCode onEvent(PlayerPublicGameSettingsChangeEvent event) throws NKRuntimeException {
            if (isHideLocation()) {
                closeAll();
            }

            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }
}
