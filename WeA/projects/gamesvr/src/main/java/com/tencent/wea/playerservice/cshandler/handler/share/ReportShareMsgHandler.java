package com.tencent.wea.playerservice.cshandler.handler.share;

import com.google.protobuf.Message;
import com.tencent.condition.event.player.common.LogoutEvent;
import com.tencent.condition.event.player.common.ShareGameEvent;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.event.common.PlayerShareGameEvent;
import com.tencent.wea.playerservice.event.common.PlayerShareTaskEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.CsShare;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ReportShareMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ReportShareMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsShare.ReportShare_C2S_Msg reqMsg = (CsShare.ReportShare_C2S_Msg)request;
        if (reqMsg.getShareType() == CsShare.ReportShareType.RST_SHARE_TASK) {
            new PlayerShareTaskEvent(player, reqMsg.getShareId()).dispatch();
        }
        // 所有分享类型都走这个逻辑
        new PlayerShareGameEvent(player, reqMsg.getShareId()).dispatch();
        player.getPlayerEventManager().dispatch(
                new ShareGameEvent(player.getConditionMgr())
                        .setShareId(reqMsg.getShareId()));

        return CsShare.ReportShare_S2C_Msg.newBuilder();
    }
}