package com.tencent.wea.playerservice.ugc.secret;

import com.tencent.cos.CosManager.ApplyKeyParam;
import com.tencent.cos.CosManager.SecretInfo;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.ugc.data.SecretData;
import com.tencent.wea.playerservice.ugc.data.UgcData;
import com.tencent.wea.protocol.CsUgc.UgcApplyKeyInfo_C2S_Msg;
import com.tencent.wea.protocol.CsUgc.UgcApplyKeyInfo_S2C_Msg.Builder;
import com.tencent.wea.protocol.common.ApplyReason;
import com.tencent.wea.xlsRes.keywords.CosOperateScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ResSecret extends BaseSecret {

    private static final Logger LOGGER = LogManager.getLogger(ResSecret.class);

    @Override
    public void start() {

    }

    @Override
    public void end() {

    }

    @Override
    public SecretData ApplyGetSecret(Player player) {
        SecretData secretData = new SecretData();
        secretData.secretInfo = getStaticGetSecret();
        return secretData;
    }

    public SecretInfo getSecretAndCacheProtected(Player player, ApplyKeyParam param, int reason) {
        boolean skipApplyKeyReqLimitExceeded = reason != ApplyReason.AR_ServerStaticPut_VALUE;
        SecretInfo secretInfoGlobal = null;
        if (skipApplyKeyReqLimitExceeded) {
            secretInfoGlobal = getSecretAndCacheGlobal(param.getBucket());
        }

        SecretInfo secretInfo = getSecretAndCache(player, param, reason, skipApplyKeyReqLimitExceeded);
        if (secretInfo.errorCode == NKErrorCode.OK) {
            LOGGER.debug("get from reason:{}", reason);
            return secretInfo;
        } else if (secretInfo.errorCode == NKErrorCode.UgcApplyKeyReqLimitExceeded && secretInfoGlobal != null) {
            LOGGER.info("get from reason 300");
            return secretInfoGlobal;
        } else {
            secretInfo.errorCode.throwError("param error please check");
        }
        return null;
    }

    @Override
    public SecretData ApplyPutSecret(Player player, int reason, long id) {
        super.checkPut(player, id, reason);

        SecretData secretData = new SecretData();
        secretData.uniqueId = getGenId(player, reason, id);
        ApplyKeyParam param = new ApplyKeyParam();

        param.setPubId(secretData.uniqueId);
        param.setUid(player.getUid());
        param.setUgcId(id);
        param.setCreatorId(player.getCreatorId());

        UgcData ugcData = getAndCheckUgcData(player, id);
        if (ugcData.getMainEditor() != 0) {
            param.setCreatorId(ugcData.getMainEditor());
        }

        param.setBucket(ugcData.getBucket());
        param.setUgcResType(ugcData.getUgcResType().getNumber());

        int aimReason = super.getRealReason(reason,ugcData.getMapType());
        secretData.bucket = ugcData.getBucket();
        secretData.secretInfo = getSecretAndCacheProtected(player, param, aimReason);
        secretData.km = getKm(GetSource(), player, reason, id);

        return secretData;
    }

    @Override
    public Source GetSource() {
        return Source.Group;
    }

    @Override
    public NKPair<NKErrorCode, SecretData> ProcessApplyKeyInfo(Player player, UgcApplyKeyInfo_C2S_Msg req,
            Builder res) {
        return null;
    }
}
