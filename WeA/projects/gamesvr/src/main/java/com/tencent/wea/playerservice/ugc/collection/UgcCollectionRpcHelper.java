package com.tencent.wea.playerservice.ugc.collection;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.rpc.RpcResult;
import com.tencent.ugc.CollectionUtils;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.common.AlgoInfo;
import com.tencent.wea.protocol.common.UgcCollectionBrief;
import com.tencent.wea.protocol.common.UgcCollectionModifyInfo;
import com.tencent.wea.protocol.common.UgcCollectionOperateType;
import com.tencent.wea.rpc.service.UgcService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class UgcCollectionRpcHelper {
    public static NKErrorCode operateUgcCollection(Player operator, String collectionId,
                                                   UgcCollectionOperateType op,
                                                   UgcCollectionModifyInfo.Builder modified) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("can not find ugc service");
            return NKErrorCode.UgcServerFoundFail;
        }

        SsUgcsvr.RpcUgcCollectionOpReq.Builder req = SsUgcsvr.RpcUgcCollectionOpReq.newBuilder()
                .setCollectionId(collectionId)

                .setOp(op.getNumber());
        if (operator != null) {
            req.setOperator(operator.getCreatorId());
        } else {
            // 来自idip的请求, 自动填充
            req.setOperator(CollectionUtils.getCreatorIdFromCollectionId(collectionId));
        }
        if (modified != null) {
            req.setModified(modified);
        }
        try {
            RpcResult<SsUgcsvr.RpcUgcCollectionOpRes.Builder> res = service.rpcUgcCollectionOp(req);
            if (res.getRet() != 0) {
                LOGGER.error("operateUgcCollection failed, collectionId:{}, op:{}, ret:{}",
                        collectionId, op, res.getRet());
                return int2NKerrorCode(res.getRet());
            }
            if (res.getData().getResult() != 0) {
                LOGGER.error("operateUgcCollection failed, collectionId:{}, op:{}, result:{}",
                        collectionId, op, res.getData().getResult());
                return int2NKerrorCode(res.getRet());
            }
            if (op == UgcCollectionOperateType.UCOT_MODIFY && res.getData().hasOut()) {
                UgcCollectionRpcHelper.handleTlog(operator, res.getData().getOut(), op);
                modified.clearMapsAfterEdit();
                for (var map : res.getData().getOut().getMapsList()) {
                    modified.addMapsAfterEdit(map.getMapId());
                }
            }
            return NKErrorCode.OK;
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("operateUgcCollection exception:", e);
            return e.getEnumErrCode();
        }
    }

    private static void handleTlog(Player player, UgcCollectionBrief collectionBrief, UgcCollectionOperateType op) {
        if (op == UgcCollectionOperateType.UCOT_MODIFY) {
            StringBuilder tagsSB = new StringBuilder();
            for (int i = 0; i < collectionBrief.getInfo().getTagsCount(); i++) {
                if (i > 0) {
                    tagsSB.append(",");
                }
                tagsSB.append(collectionBrief.getInfo().getTags(i));
            }
            TlogFlowMgr.sendUgcCollectionFlow(player, collectionBrief.getCollectionId(), 2,
                    tagsSB.toString(), collectionBrief.getInfo().getType(), collectionBrief.getMapCount());
        }
    }

    public static SsUgcsvr.RpcUgcCollectionOpRes batchOperateUgcCollection(Player operator, List<String> collectionIds,
                                                                           UgcCollectionOperateType op) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("can not find ugc service");
            return null;
        }

        SsUgcsvr.RpcUgcCollectionOpReq.Builder req = SsUgcsvr.RpcUgcCollectionOpReq.newBuilder()
                .setOperator(operator.getCreatorId())
                .addAllCollectionIds(collectionIds)
                .setOp(op.getNumber());
        try {
            RpcResult<SsUgcsvr.RpcUgcCollectionOpRes.Builder> res = service.rpcUgcCollectionOp(req);
            return res.getData().build();
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("batchOperateUgcCollection exception:", e);
            return null;
        }
    }

    // 废弃勿用
    public static NKPair<NKErrorCode, List<UgcCollectionBrief.Builder>> getCollectionsByCreatorId(Player operator,
                                                                                                  long creatorId) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("can not find ugc service");
            return new NKPair<>(NKErrorCode.UgcServerFoundFail, null);
        }

        SsUgcsvr.RpcUgcCollectionGetBriefsReq.Builder req = SsUgcsvr.RpcUgcCollectionGetBriefsReq.newBuilder()
                .setOperator(operator.getCreatorId())
                .setCreatorId(creatorId);
        try {
            RpcResult<SsUgcsvr.RpcUgcCollectionGetBriefsRes.Builder> res = service.rpcUgcCollectionGetBriefs(req);
            if (res.getRet() != 0) {
                LOGGER.error("getCollectionsByCreatorId failed, creatorId:{}, ret:{}",
                        creatorId, res.getRet());
                return new NKPair<>(int2NKerrorCode(res.getRet()), null);
            }
            if (res.getData().getResult() != 0) {
                LOGGER.error("getCollectionsByCreatorId failed, creatorId:{}, result:{}",
                        creatorId, res.getData().getResult());
                return new NKPair<>(int2NKerrorCode(res.getData().getResult()), null);
            }
            return new NKPair<>(NKErrorCode.OK, res.getData().getBriefsBuilderList());
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("getCollectionsByCreatorId exception:", e);
            return new NKPair<>(e.getEnumErrCode(), null);
        }
    }

    public static NKPair<NKErrorCode, List<UgcCollectionBrief.Builder>> batchGetCollections(Player operator,
                                                                                            List<String> collectionIds,
                                                                                            boolean focus) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("can not find ugc service");
            return new NKPair<>(NKErrorCode.UgcServerFoundFail, null);
        }

        SsUgcsvr.RpcUgcCollectionGetBriefsReq.Builder req = SsUgcsvr.RpcUgcCollectionGetBriefsReq.newBuilder()
                .setOperator(operator.getCreatorId())
                .addAllCollectionIds(collectionIds)
                .setFocus(focus);
        try {
            RpcResult<SsUgcsvr.RpcUgcCollectionGetBriefsRes.Builder> res = service.rpcUgcCollectionGetBriefs(req);
            if (res.getRet() != 0) {
                LOGGER.error("batchGetCollections failed, collectionIds:{}, ret:{}",
                        collectionIds, res.getRet());
                return new NKPair<>(int2NKerrorCode(res.getRet()), null);
            }
            if (res.getData().getResult() != 0) {
                LOGGER.error("batchGetCollections failed, collectionIds:{}, result:{}",
                        collectionIds, res.getData().getResult());
                return new NKPair<>(int2NKerrorCode(res.getData().getResult()), null);
            }
            return new NKPair<>(NKErrorCode.OK, res.getData().getBriefsBuilderList());
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("batchGetCollections exception:", e);
            return new NKPair<>(e.getEnumErrCode(), null);
        }
    }

    public static class ExcellentCollections {
        ExcellentCollections(Collection<UgcCollectionBrief.Builder> pinned,
                             Collection<UgcCollectionBrief.Builder> others) {
            if (pinned != null) {
                this.pinned.addAll(pinned);
            }
            if (others != null) {
                this.others.addAll(others);
            }
        }

        public ArrayList<UgcCollectionBrief.Builder> pinned = new ArrayList<>();
        public ArrayList<UgcCollectionBrief.Builder> others = new ArrayList<>();
    }

    public static NKPair<NKErrorCode, ExcellentCollections> getExcellentCollections(
            Player player, int pageCap, int pageNo) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("can not find ugc service");
            return new NKPair<>(NKErrorCode.UgcServerFoundFail, null);
        }

        var req = SsUgcsvr.RpcUgcCollectionGovRecommendListReq.newBuilder()
                .setOperator(player.getCreatorId())
                .setPageCap(pageCap)
                .setPageNo(pageNo)
                .setOpenId(player.getOpenId())
                .setUid(player.getUid());
        try {
            var res = service.rpcUgcCollectionGovRecommendList(req);
            if (res.getRet() != 0) {
                LOGGER.error("getExcellentCollections failed, operator:{}, ret:{}",
                        player.getCreatorId(), res.getRet());
                return new NKPair<>(int2NKerrorCode(res.getRet()), null);
            }
            if (res.getData().getResult() != 0) {
                LOGGER.error("getExcellentCollections failed, operator:{}, result:{}",
                        player.getCreatorId(), res.getData().getResult());
                return new NKPair<>(int2NKerrorCode(res.getData().getResult()), null);
            }
            return new NKPair<>(NKErrorCode.OK, new ExcellentCollections(
                    res.getData().getTopBuilderList(), res.getData().getOtherBuilderList()));
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("getExcellentCollections exception:", e);
            return new NKPair<>(e.getEnumErrCode(), null);
        }
    }

    public static NKPair<NKErrorCode, SsUgcsvr.RpcUgcCollectionRecommendListRes.Builder> getRecommendedCollections(
            Player player, List<Integer> tagIds, int pageNo, AlgoInfo.Builder algoInfo) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("can not find ugc service");
            return new NKPair<>(NKErrorCode.UgcServerFoundFail, null);
        }

        var req = SsUgcsvr.RpcUgcCollectionRecommendListReq.newBuilder()
                .setOperator(player.getCreatorId())
                .addAllTagIds(tagIds)
                .setPage(pageNo)
                .setOpenId(player.getOpenId())
                .setUid(player.getUid());

        try {
            var res = service.rpcUgcCollectionRecommendList(req);
            if (res.getRet() != 0) {
                LOGGER.error("getRecommendedCollections failed, operator:{}, ret:{}",
                        player.getCreatorId(), res.getRet());
                return new NKPair<>(int2NKerrorCode(res.getRet()), null);
            }
            if (res.getData().getResult() != 0) {
                LOGGER.error("getRecommendedCollections failed, operator:{}, result:{}",
                        player.getCreatorId(), res.getData().getResult());
                return new NKPair<>(int2NKerrorCode(res.getData().getResult()), null);
            }
            if (res.getData().hasAlgoInfo()) {
                algoInfo.setExpTag(res.getData().getAlgoInfo().getExpTag())
                        .setRecId(res.getData().getAlgoInfo().getRecid());
            }
            return new NKPair<>(NKErrorCode.OK, res.getData());
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("getRecommendedCollections exception:", e);
            return new NKPair<>(e.getEnumErrCode(), null);
        }
    }

    private static NKErrorCode int2NKerrorCode(int code) {
        NKErrorCode nke = NKErrorCode.forNumber(code);
        if (nke == null) {
            return NKErrorCode.UgcCollectionUnknownError;
        }
        return nke;
    }

    private static final Logger LOGGER = LogManager.getLogger(UgcCollectionRpcHelper.class);
}
