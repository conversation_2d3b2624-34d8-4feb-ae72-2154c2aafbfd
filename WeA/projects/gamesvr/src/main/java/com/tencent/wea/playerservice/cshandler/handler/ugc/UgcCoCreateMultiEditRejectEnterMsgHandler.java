package com.tencent.wea.playerservice.cshandler.handler.ugc;

import com.google.protobuf.Message;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.cshandler.GamesvrPbMsgHandlerFactory;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.ugc.manager.UgcCoCreateMgr;
import com.tencent.wea.protocol.CsUgc;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.MsgTypes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class UgcCoCreateMultiEditRejectEnterMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(UgcCoCreateMultiEditRejectEnterMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsUgc.UgcCoCreateMultiEditRejectEnter_C2S_Msg reqMsg = (CsUgc.UgcCoCreateMultiEditRejectEnter_C2S_Msg)request;
        CsUgc.UgcCoCreateMultiEditRejectEnter_S2C_Msg.Builder rspMsg = CsUgc.UgcCoCreateMultiEditRejectEnter_S2C_Msg.newBuilder();

        // 请求参数校验
        if (reqMsg.getUgcId() == 0L || reqMsg.getReplyUid() == 0L || reqMsg.getReplyCreatorId() == 0L) {
            NKErrorCode.InvalidParams.throwError("param ugcId or replyUid or replyCreatorId is 0");
        }

        Monitor.getInstance().add.total(MonitorId.attr_ugc_co_create_multi_edit_operate, 1);

        // 共创地图多人编辑拒绝进入处理
        int code = player.getUgcCoCreateMgr().coCreateMultiEditRejectEnter(reqMsg.getUgcId(), reqMsg.getReplyUid(),
                reqMsg.getReplyCreatorId());
        if (code != NKErrorCode.OK.getValue()) {
            Monitor.getInstance().add.fail(MonitorId.attr_ugc_co_create_multi_edit_operate, 1);
            NKErrorCode.forNumber(code).throwError("co create multi edit reject enter failed");
        } else {
            Monitor.getInstance().add.succ(MonitorId.attr_ugc_co_create_multi_edit_operate, 1);
        }

        return rspMsg;
    }
}
