package com.tencent.wea.playerservice.cshandler.handler.match;

import com.google.protobuf.Message;
import com.google.protobuf.Timestamp;
import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.CupsConfigData;
import com.tencent.resourceloader.resclass.MatchDateData;
import com.tencent.resourceloader.resclass.MatchModeSortInfo;
import com.tencent.resourceloader.resclass.MatchNewTagDateData;
import com.tencent.resourceloader.resclass.MatchRecommendData;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.resourceloader.resclass.UgcMatchMiniGameAreaIdDataConf;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.XlsWhiteList;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.player.PlayerRoomMgr;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsMatch;
import com.tencent.wea.protocol.CsMatch.MatchStatus;
import com.tencent.wea.protocol.CsMatch.MatchTypeInfo;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsUgcsvr.UgcMatchModeSortInfo;
import com.tencent.wea.protocol.common.GameModeType;
import com.tencent.wea.protocol.common.PlayerNoticeMsgType;
import com.tencent.wea.starp.StarPConfs;
import com.tencent.wea.xlsRes.ResMatch.MatchDate;
import com.tencent.wea.xlsRes.ResMatch.MatchType;
import com.tencent.wea.xlsRes.ResMatch.MatchTypeSettleProc;
import com.tencent.wea.xlsRes.keywords.MetaBattleDataType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class MatchTypeListMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(MatchTypeListMsgHandler.class);

    private static final int UserLabelConfId = 3;

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        //CsMatch.MatchTypeList_C2S_Msg reqMsg = (CsMatch.MatchTypeList_C2S_Msg)request;
        long currentTime = DateUtils.currentTimeSec();
        CsMatch.MatchTypeList_S2C_Msg.Builder rspMsg = CsMatch.MatchTypeList_S2C_Msg.newBuilder();
        MatchTypeData matchTypeDataIns = MatchTypeData.getInstance();
        MatchDateData matchDateDataIns = MatchDateData.getInstance();
        MatchNewTagDateData matchNewTagDateDataIns = MatchNewTagDateData.getInstance();
        MatchRecommendData matchRecommendDataIns = MatchRecommendData.getInstance();
        List<MatchType> matchTypeList = matchTypeDataIns.dataArray;
        long cliVersion64 = player.getClientVersion64();
        int defaultSelectMatchType = getPlayerDefaultSelectMatchType(player,currentTime,matchTypeDataIns,matchDateDataIns);
        List<MatchType> matchTypeListUgc = new ArrayList<>();
        List<Integer> newRecommendIds = new ArrayList<>();
        for (MatchType matchTypeConf : matchTypeList) {
            if (!checkGrayMatchTypeWhitelist(matchTypeConf, player)) {
                continue;
            }
            if (matchTypeConf.hasLowestVersion() && com.tencent.util.VersionUtil.versionLT(cliVersion64, matchTypeConf.getLowestVersion())) {
                continue;
            }
            if (matchTypeConf.hasHighestVersion() && com.tencent.util.VersionUtil.versionGT(cliVersion64, matchTypeConf.getHighestVersion())) {
                continue;
            }
            // 如果是ugc的需要额外处理
            if (matchTypeConf.getGameModeType() == GameModeType.GMT_UgcMatch_VALUE && matchTypeConf.getModeID() != 4) {
                matchTypeListUgc.add(matchTypeConf);
                continue;
            }
            // 啾灵玩法不显示
            if (StarPConfs.isStarPGame(matchTypeConf.getId()) || StarPConfs.is920StarP(matchTypeConf.getId())) {
                continue;
            }
            MatchTypeInfo.Builder matchTypeInfo = MatchTypeInfo.newBuilder();
            matchTypeInfo.setMatchTypeId(matchTypeConf.getId());
            matchTypeInfo.setIsPermanent(matchTypeConf.getIsPermanent());
//            matchTypeInfo.getMatchTypeConfBuilder().mergeFrom(matchTypeConf);
            if (player.getUserAttr().getUnLockGameModeSet().contains(matchTypeConf.getId())) {
                matchTypeInfo.setIsUnLock(true);
            }
            if (matchTypeConf.getIsPermanent()) {
                matchTypeInfo.setStatus(MatchStatus.MS_Open);
            } else {
                NKPair<MatchStatus, MatchDate> matchTypeDateStatus = matchDateDataIns.getMatchDateStatus(matchTypeConf.getId(), currentTime);
                if (matchTypeDateStatus.getKey() == MatchStatus.MS_Show || matchTypeDateStatus.getKey() == MatchStatus.MS_Open) {
                    matchTypeInfo.setPreShowTime(matchTypeDateStatus.getValue().getPreShowTime());
                    matchTypeInfo.setStartTime(matchTypeDateStatus.getValue().getStartTime());
                    matchTypeInfo.setEndTime(matchTypeDateStatus.getValue().getEndTime());
                    matchTypeInfo.setOpenPeriod(matchTypeDateStatus.getValue().getOpenPeriod());
                }
                matchTypeInfo.setStatus(matchTypeDateStatus.getKey());
            }
            // 客户端从特殊链接进入后，强制开启玩法
            if (isForceOpenMatchType(matchTypeConf, player)) {
                matchTypeInfo.setStatus(MatchStatus.MS_Open);
            }
            if (matchTypeInfo.getStatus() == MatchStatus.MS_Open && PlayerRoomMgr.isMatchTypeUrgentClose(matchTypeConf.getId())) {
                matchTypeInfo.setStatus(MatchStatus.MS_Closed);
            }
            matchTypeInfo.setIsNew(matchNewTagDateDataIns.isNewMatchTag(matchTypeConf.getId()));
            matchTypeInfo.addAllRecommendIds(matchRecommendDataIns.getMatchTypeRecommendList(matchTypeConf.getId()));

            matchTypeInfo.setSort(MatchModeSortInfo.getInstance().getMatchModeSortId(matchTypeConf.getId()));
            matchTypeInfo.addAllMatchUnlockType(player.getPlayerRoomMgr().getMatchTypeUnlockType(matchTypeConf.getId()));
            matchTypeInfo.setWinTimes((int)player.getSeasonFashionBattleDataMgr().getMatchTypeBattleData(matchTypeConf.getId(),
                    MetaBattleDataType.MBDT_WinTimes));
            matchTypeInfo.setDefaultSelect(defaultSelectMatchType == matchTypeConf.getId());
            matchTypeInfo.setIsUgcMode(matchTypeConf.getGameModeType() == GameModeType.GMT_UgcMatch_VALUE);
            rspMsg.addMatchTypeList(matchTypeInfo);
            newRecommendIds.addAll(matchRecommendDataIns.getMatchTypeNewRecommendList(matchTypeConf.getId()));
        }
        getMatchTypeListUgc(player, matchTypeListUgc, newRecommendIds, rspMsg);
        rspMsg.addAllRecommendIds(newRecommendIds);


        if (player.getClientVersion64() < CupsConfigData.getCupAdditionTipsLowVersion()){
            CsPlayer.PlayerNoticeMsgNtf.Builder ntf = CsPlayer.PlayerNoticeMsgNtf.newBuilder()
                    .setType(PlayerNoticeMsgType.PNT_UNKNOWN)
                    .setNotice("升级版本查看奖杯加倍相关信息") // 是切版本临时一次性的, 不用走配置表
                    // .addAllParams()
                    .setMsgQueued(true);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, ntf);
        }

        return rspMsg;
    }

    // 默认选择的玩法
    private int getPlayerDefaultSelectMatchType(Player player, long currentTime, MatchTypeData matchTypeDataIns, MatchDateData matchDateDataIns) {
        int qualifyingMatchTypeId = 0;
        int defaultMatchTypeId = 0;
        int labelMatchTypeId = 0;
        for (MatchType matchTypeConf : matchTypeDataIns.dataArray) {
            if (matchTypeConf.getBeDefault() == 1 && player.getUserAttr().getUnLockGameModeSet().contains(matchTypeConf.getId())) {
                if (matchTypeConf.getSettleProc() == MatchTypeSettleProc.MTSC_Rank) {
                    qualifyingMatchTypeId = matchTypeConf.getId();
                }
                if (matchTypeConf.getSettleProc() == MatchTypeSettleProc.MTSC_Common) {
                    defaultMatchTypeId = matchTypeConf.getId();
                }
            }
        }
        var labelAttr = player.getLabelMgr().checkGetValid(UserLabelConfId);
        if (labelAttr != null && labelAttr.getNumValsSize() > 0) {
            labelMatchTypeId = Math.toIntExact(labelAttr.getNumVals(0));
            LOGGER.debug("get player uid:{} labelMatchTypeId:{}", player.getUid(), labelMatchTypeId);
        }
        if (labelMatchTypeId > 0 && player.getUserAttr().getUnLockGameModeSet().contains(labelMatchTypeId) && !PlayerRoomMgr.isMatchTypeUrgentClose(labelMatchTypeId)) {
            MatchType matchTypeConf = matchTypeDataIns.get(labelMatchTypeId);
            NKPair<MatchStatus, MatchDate> matchTypeDateStatus = matchDateDataIns.getMatchDateStatus(labelMatchTypeId, currentTime);
            if ((matchTypeConf != null && matchTypeConf.getIsPermanent()) || (matchTypeDateStatus.getKey() == MatchStatus.MS_Open)) {
                return labelMatchTypeId;
            }
        }
        if (qualifyingMatchTypeId > 0) {
            return qualifyingMatchTypeId;
        }
        return defaultMatchTypeId;
    }

    private boolean checkGrayMatchTypeWhitelist(MatchType matchTypeConf, Player player) {
        if (matchTypeConf == null || matchTypeConf.getWhitelistModuleIdList().isEmpty()) {
            return true;
        }
        for (String moduleId : matchTypeConf.getWhitelistModuleIdList()) {
            XlsWhiteList whiteList = player.getUserAttr().getXlsWhiteList().get(moduleId);
            if (whiteList != null && whiteList.getStatus() == 1) {
                return true;
            }
        }
        return false;
    }

    private Timestamp genTimestamp(long time) {
        Timestamp.Builder timestamp = Timestamp.newBuilder();
        timestamp.setSeconds(time);
        return timestamp.build();
    }

    private void getMatchTypeListUgc(Player player, List<MatchType> matchTypeListUgc, List<Integer> newRecommendIds,
            CsMatch.MatchTypeList_S2C_Msg.Builder rspMsg) {
        boolean ugcMatchTypeListGetSwitch = PropertyFileReader.getRealTimeBooleanItem("ugcMatchTypeListGetSwitch",
                true);
        if (!ugcMatchTypeListGetSwitch) {
            // 这里使用realtime开关做个控制
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("skip ugc match type list get, uid:{}", player.getUid());
            }
            return;
        }
        // 下面为逻辑
        if (matchTypeListUgc == null || matchTypeListUgc.isEmpty()) {
            return;
        }
        Map<Integer, UgcMatchModeSortInfo> modeSortInfoMap = player.getUgcMatchLobbyMgr().getUgcMatchModeSortInfo(matchTypeListUgc);
        MatchNewTagDateData matchNewTagDateDataIns = MatchNewTagDateData.getInstance();
        MatchRecommendData matchRecommendDataIns = MatchRecommendData.getInstance();
        for (MatchType matchTypeConf : matchTypeListUgc) {
            UgcMatchModeSortInfo modeSortInfo = null;
            if (matchTypeConf.getModeID() != 14) {
                modeSortInfo = modeSortInfoMap.get(matchTypeConf.getId());
            } else if (UgcMatchMiniGameAreaIdDataConf.getInstance() != null) {
                modeSortInfo = UgcMatchMiniGameAreaIdDataConf.getInstance()
                        .genUgcMatchModeSortInfo(matchTypeConf.getId());
            }
            if (modeSortInfo == null) {
                continue;
            }
            MatchTypeInfo.Builder matchTypeInfo = MatchTypeInfo.newBuilder();
            matchTypeInfo.setMatchTypeId(matchTypeConf.getId());
            if (player.getUserAttr().getUnLockGameModeSet().contains(matchTypeConf.getId())) {
                matchTypeInfo.setIsUnLock(true);
            }
            matchTypeInfo.setIsNew(matchNewTagDateDataIns.isNewMatchTag(matchTypeConf.getId()));
            if (modeSortInfo.hasStartTime()) {
                matchTypeInfo.setStartTime(genTimestamp(modeSortInfo.getStartTime()));
            }
            if (modeSortInfo.hasCloseTime()) {
                matchTypeInfo.setEndTime(genTimestamp(modeSortInfo.getCloseTime()));
            }
            matchTypeInfo.setStatus(MatchStatus.MS_Open);
            matchTypeInfo.setSort(modeSortInfo.getSort());
            matchTypeInfo.setIsShowMiniGame(modeSortInfo.getIsShowMiniGame());
            matchTypeInfo.addAllMatchUnlockType(
                    player.getPlayerRoomMgr().getMatchTypeUnlockType(matchTypeConf.getId()));
            matchTypeInfo.setIsUgcMode(matchTypeConf.getGameModeType() == GameModeType.GMT_UgcMatch_VALUE);
            matchTypeInfo.setSpecJumpId(modeSortInfo.getUgcLobbyJumpId());
            rspMsg.addMatchTypeList(matchTypeInfo);
        }
        // 999999 mark-glueli 这个是ugc的统一推荐标志，这边需要处理下
        newRecommendIds.addAll(matchRecommendDataIns.getMatchTypeNewRecommendList(999999));
    }

    public boolean isForceOpenMatchType(MatchType matchTypeConf, Player player) {
        return player.getUserAttr().getForceOpenMatchType().contains(matchTypeConf.getId());
    }
}