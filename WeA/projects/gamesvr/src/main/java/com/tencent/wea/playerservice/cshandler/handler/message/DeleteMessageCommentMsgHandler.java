package com.tencent.wea.playerservice.cshandler.handler.message;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.CsMessage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DeleteMessageCommentMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(DeleteMessageCommentMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsMessage.DeleteMessageComment_C2S_Msg reqMsg = (CsMessage.DeleteMessageComment_C2S_Msg)request;
        CsMessage.DeleteMessageComment_S2C_Msg.Builder rspBuilder = CsMessage.DeleteMessageComment_S2C_Msg.newBuilder();
        player.getPlayerMessageSlipMgr().handleDeleteMessageComment(reqMsg, rspBuilder)
                .throwErrorNoStackIfNotOk();
        return rspBuilder;
    }
}