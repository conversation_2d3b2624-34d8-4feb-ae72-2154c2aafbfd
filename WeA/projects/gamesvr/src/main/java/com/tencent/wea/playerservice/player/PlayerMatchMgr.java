package com.tencent.wea.playerservice.player;

import com.tencent.ailab.metaai.match.MetaAiRpcManager;
import com.tencent.ipdb.IpDbApi;
import com.tencent.match.qualify.QualifyingUtils;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.timer.TimerType;
import com.tencent.nk.timer.TimerUtil;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.RegionalConf;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.math.Constants;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
// import com.tencent.util.QualifyingUtil;
import com.tencent.wea.attr.AttrRoomInfo;
import com.tencent.wea.framework.GSConfig;
import com.tencent.wea.framework.GSEngine;
import com.tencent.wea.other.meta_ai.match.MetaAiMatch;
import com.tencent.wea.playerservice.event.common.battle.PlayerStartBattleEvent;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.match.PlayerMatchWarmRoundManager;
import com.tencent.wea.playerservice.metaai.GsMetaAiRpcManager;
import com.tencent.wea.playerservice.numericattr.PlayerNumericAttrMgr;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.AttrBattleInfo;
import com.tencent.wea.protocol.AttrChatGroupKey.proto_ChatGroupKey;
import com.tencent.wea.protocol.CsBattle;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.CsRoom;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsCommon;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.starp.StarPConfs;
import com.tencent.wea.protocol.common.BattleCreateReason;
import com.tencent.wea.protocol.common.MatchDynamicConfigData;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.PlayerNoticeMsgType;
import com.tencent.wea.protocol.common.WarmRoundInfo;
import com.tencent.wea.protocol.common.WarmRoundType;
import com.tencent.wea.protocol.common.RoomExitType;
import com.tencent.wea.room.RoomUtil.UpdateMemberBaseInfoSource;
import com.tencent.wea.starp.StarPConfs;
import com.tencent.wea.xlsRes.ResRegional;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.MMRScoreType;
import com.tencent.wea.xlsRes.keywords.MetaAiLabMatchDyeGroupType;
import com.tencent.wea.xlsRes.keywords.QualifyingDegreeInfo;
import com.tencent.wea.xlsRes.keywords.RoomStateType;
import com.tencent.wea.xlsRes.keywords.RoomStatus;
import com.tencent.wea.xlsRes.keywords.WarmRoundScoreType;
import ipdb.jni.IpInfo;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 玩家匹配管理
 *
 * <AUTHOR>
 * @date 2021/07/02
 */
public class PlayerMatchMgr extends PlayerModule {

    /**
     * 日志记录器
     */
    private static final Logger LOGGER = LogManager.getLogger(PlayerMatchMgr.class);

    /**
     * 玩家匹配管理
     *
     * @param player 玩家
     */
    public PlayerMatchMgr(Player player) {
        super(GameModuleId.GMI_PlayerMatchManager, player);
    }

    /**
     * 通知比赛成功
     *
     * @param status        战斗状态
     * @param battleInfo    战斗信息
     * @param warmRoundType
     */
    public void doInformMatchSuccess(RoomStatus status, int gameId, int modeId,
                                     AttrBattleInfo.proto_BattleInfo battleInfo, WarmRoundType warmRoundType,
                                     MatchDynamicConfigData matchDynamicConfigData) {
        clearPlayerGiveUpBattleId(battleInfo);
        RoomStatus prestatus = player.getUserAttr().getRoomInfo().getStatus(); // @Deprecated
        updateMatchData(status, battleInfo, matchDynamicConfigData);
        RoomStatus setstatus = player.getUserAttr().getRoomInfo().getStatus(); // @Deprecated
        LOGGER.debug("roomstatus1 match uid:{} setstatus {},prestatus {},roomstatus{}", player.getUid(), setstatus,
                prestatus,
                status);
        LOGGER.info("player match succeed, player:{}, battleId:{}, dsAddr:{}", player.getUid(), battleInfo.getBattleid(),
                battleInfo.getDsAddr());
        player.getPlayerStateMgr().enterBattleScene();
        // dispatch event
        new PlayerStartBattleEvent(player).setModeType(modeId).dispatch();

        // modeId填充的是MatchTypeId
        onWarmRoundMatchRet(NKErrorCode.OK.getValue(), warmRoundType, modeId, battleInfo.getBattleid());

        player.getIdipMgr().decreaseDsReplayRecordCount();
    }

    /**
     * 玩家匹配成功后清理调玩家放弃数据
     * @param battleInfo
     */
    public void clearPlayerGiveUpBattleId(AttrBattleInfo.proto_BattleInfo battleInfo) {
        player.getNumericAttrMgr().set(PlayerNumericAttrMgr.Key.GIVE_UP_BATTLE_ID, 0);
        LOGGER.info("player match succeed, player:{},  clearPlayerGiveUpBattleId, curBattle:{} curMatchType: {} ",
                player.getUid(), battleInfo.getBattleid(), battleInfo.getMatchType());
    }

    public void doInformBattleDSJumpSuccess(RoomStatus status, int gameId, int modeId,
                                            AttrBattleInfo.proto_BattleInfo battleInfo, WarmRoundType warmRoundType,
                                            MatchDynamicConfigData matchDynamicConfigData, BattleCreateReason createReason) {
        RoomStatus preStatus = player.getUserAttr().getRoomInfo().getStatus(); // @Deprecated
        updateMatchData(status, battleInfo, matchDynamicConfigData);
        RoomStatus roomStatus = player.getUserAttr().getRoomInfo().getStatus(); // @Deprecated
        LOGGER.info("In doInformBattleDSJumpSuccess match uid:{} roomStatus:{}, preStatus {}, status:{} createReason:{}",
                player.getUid(), roomStatus, preStatus, status, createReason);
        LOGGER.info("In doInformBattleDSJumpSuccess player match succeed, player:{}, battleId:{}, dsAddr:{} createReason:{}",
                player.getUid(), battleInfo.getBattleid(), battleInfo.getDsAddr(), createReason);
        //ntf client

        CsBattle.BattleDsInfo.Builder battleDsInfoBuilder = CsBattle.BattleDsInfo.newBuilder();
        battleDsInfoBuilder.getBattleInfoBuilder().setBattleid(battleInfo.getBattleid())
                .setDsAddr(battleInfo.getDsAddr()).setSide(battleInfo.getSide())
                .setDsAuthToken(battleInfo.getDsAuthToken())
                .setDesModInfo(battleInfo.getDesModInfo())
                .setGlobalChatGroupKey(convertChatGroupKey(battleInfo.getGlobalChatGroupKey()))
                .setSideChatGroupKey(convertChatGroupKey(battleInfo.getSideChatGroupKey()))
                .setMatchType(battleInfo.getMatchType()).setSceneId(battleInfo.getSceneId())
                //.setPakType(battleInfo.getPakType()) 废弃
                .addAllPakGroupIdList(battleInfo.getPakGroupIdListList());

        CsBattle.DsJumpNtf.Builder ntfBuilder = CsBattle.DsJumpNtf.newBuilder();
        ntfBuilder.setJumpReason(createReason);
        CsBattle.DsJumpInfo.Builder dsJumpInfoBuilder = CsBattle.DsJumpInfo.newBuilder();
        dsJumpInfoBuilder.setDsBasicInfo(CsBattle.DsBasicInfo.newBuilder()
                .setId(battleInfo.getBattleid())
                .setDsAddr(battleInfo.getDsAddr())
                .setDsAuthToken(battleInfo.getDsAuthToken()));
        dsJumpInfoBuilder.setBattleDsInfo(battleDsInfoBuilder);
        ntfBuilder.setDsJumpInfo(dsJumpInfoBuilder);
        ntfBuilder.setResult(0);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_DSJUMPNTF, ntfBuilder);
        if(LOGGER.isDebugEnabled()) {
            LOGGER.info("Success to doInformBattleDSJumpSuccess:{}", ntfBuilder.build());
        }
        // dispatch event
        /*new PlayerStartBattleEvent(player).setModeType(modeId).dispatch();
        if (isWarmRound) {
            // modeId填充的是MatchTypeId
            onWarmRoundMatchSucc(modeId, battleInfo);
        }*/
        // 非window本地ds模式，离开当前大厅
        /*if (!ServerEngine.getInstance().isDsStartToken()) {
            player.getPlayerLobbyMgr().procExitCurrentLobby(
                    G6Common.ExitLobbyCode.EXIT_LOBBY_CODE_MATCH_SUCC.getNumber());
        }*/
    }

//    private void tryCompleteLevelGuideTask(int modeId) {
//        ResMisc.MiscGuideConf dsGuideConf = MiscConf.getInstance().getMiscConf().getGuideConf();
//        for (ResMisc.DsGuideConf config : dsGuideConf.getDsGuideList()) {
//            if (config.getGuideModeId() == modeId) {
//                player.getTaskManager().setGuideTaskComplete(config.getGuideTaskId());
//                player.getUserAttr().getLevelGuideABTestInfo()
//                        .setLevelGuideCount(player.getUserAttr().getLevelGuideABTestInfo().getLevelGuideCount() + 1);
//                // for next match
//                player.getPlayerRoomMgr().updateRoomMemberBaseInfo();
//                return;
//            }
//        }
//        // 改成强制完成新手引导，除非主动跳过
//        // 非新手引导模式, 直接完成最终新手引导任务
//        // player.getTaskManager().setGuideTaskComplete(dsGuideConf.getGuideTaskId());
//        return;
//    }

    /**
     * 做通知比赛失败
     */
    public void doInformMatchFailure() {
        //updateMatchData(MatchStatusType.MST_NotMatching, 0, "", 0);
    }

    public void doInformMatchFailureForStarP(int result, long battleId, int matchTypeId) {
        CsRoom.MatchSuccNtf.Builder ntfBuilder = CsRoom.MatchSuccNtf.newBuilder();
        ntfBuilder.setResCode(result);
        ntfBuilder.getBattleInfoBuilder().setMatchType(matchTypeId).setBattleid(battleId);
        if (result == NKErrorCode.MatchServicesBusy.getValue() ||
                result == NKErrorCode.MatchMaxTimeoutFail.getValue() ||
                result == NKErrorCode.MatchServicesClose.getValue() ||
                result == NKErrorCode.RoomUgcCoPlayMatchAbort.getValue() ||
                result == NKErrorCode.MatchMidJoinFailError.getValue() ||
                result == NKErrorCode.BattleUGCSingleDSLoadHighBusy.getValue()) {
            player.sendNtfMsgWhenFail(MsgTypes.MSG_TYPE_MATCHSUCCNTF, result, ntfBuilder);
        } else {
            player.sendNtfMsgWhenFail(MsgTypes.MSG_TYPE_MATCHSUCCNTF, NKErrorCode.BattleCreateModSessionFailure.getValue(), ntfBuilder);
        }
        LOGGER.warn("doInformMatchFailureForStarP, {}, {}, {}", player.getUid(), result, battleId);
    }
    /**
     * 做通知比赛失败
     */
    public void doInformMatchFailure(int result, long battleId, int modeId,
                                     String errMsg, WarmRoundType warmRoundType) {
        // modeId填充的是MatchTypeId
        onWarmRoundMatchRet(result, warmRoundType, modeId, battleId);
        if (StarPConfs.isStarPGame(modeId)) {
            doInformMatchFailureForStarP(result, battleId, modeId);
            return;
        }

        if (GSEngine.getInstance().isBusiness()) {
            if (result == NKErrorCode.MatchServicesBusy.getValue() ||
                    result == NKErrorCode.MatchMaxTimeoutFail.getValue() ||
                    result == NKErrorCode.MatchServicesClose.getValue() ||
                    result == NKErrorCode.RoomUgcCoPlayMatchAbort.getValue() ||
                    result == NKErrorCode.MatchMidJoinFailError.getValue() ||
                    result == NKErrorCode.BattleUGCSingleDSLoadHighBusy.getValue()) {
                player.sendError(MsgTypes.MSG_TYPE_MATCHSUCCNTF, result, "");
            } else {
                player.sendError(MsgTypes.MSG_TYPE_MATCHSUCCNTF, NKErrorCode.BattleCreateModSessionFailure.getValue(), "");
            }
            LOGGER.warn("doInformMatchFailure, {}, {}, {}, {}", player.getUid(), result, battleId, errMsg);
        } else {
            if (result == NKErrorCode.MatchServicesBusy.getValue() ||
                    result == NKErrorCode.MatchMaxTimeoutFail.getValue() ||
                    result == NKErrorCode.MatchServicesClose.getValue() ||
                    result == NKErrorCode.RoomUgcCoPlayMatchAbort.getValue() ||
                    result == NKErrorCode.MatchMidJoinFailError.getValue() ||
                    result == NKErrorCode.BattleUGCSingleDSLoadHighBusy.getValue()) {
                player.sendError(MsgTypes.MSG_TYPE_MATCHSUCCNTF, result,
                        NKStringFormater.format(" errorCode:{} battleId:{} {}", result, battleId, errMsg));
            } else {
                player.sendError(MsgTypes.MSG_TYPE_MATCHSUCCNTF, NKErrorCode.BattleCreateModSessionFailure.getValue(),
                        NKStringFormater.format(" errorCode:{} battleId:{} {}", result, battleId, errMsg));
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("doInformMatchFailure MSG_TYPE_MATCHSUCCNTF, {}, {}, {}, {}", player.getUid(), result, battleId, errMsg);
            }

        }

        if (StarPConfs.isStarPPvpGame(modeId))
        {
            AttrRoomInfo roomInfo = player.getUserAttr().getRoomInfo();
            int currMatchTypeId = player.getUserAttr().getRoomInfo().getCurrentMatchType();
            if (StarPConfs.isStarPPvpGame(currMatchTypeId)) {
                long roomId = roomInfo.getRoomid();

                LOGGER.warn("StarPPvp doInformMatchFailure exitRoom, uid:{} result:{} battleId:{} roomId:{} modeId:{} errMsg:{}",
                        player.getUid(), result, battleId, roomId, modeId, errMsg);

                // 如果失败需要退出房间
                NKErrorCode ret = player.getPlayerRoomMgr()
                        .exitRoom(roomId, RoomExitType.RET_SystemLogicExit);
                LOGGER.info("StarPPvp doInformMatchFailure exitRoom roomId:{}, ret:{}",
                        roomId, ret);
            }
        }
    }

    public void doInformBattleDSJumpFailure(int result, long battleId, String errMsg) {
        if (GSEngine.getInstance().isBusiness()) {
            player.sendError(MsgTypes.MSG_TYPE_DSJUMPNTF, NKErrorCode.BattleCreateModSessionFailure.getValue(), "");
            LOGGER.warn("doInformBattleDSJumpFailure, {}, {}, {}, {}", player.getUid(), result, battleId, errMsg);
        } else {
            player.sendError(MsgTypes.MSG_TYPE_DSJUMPNTF, NKErrorCode.BattleCreateModSessionFailure.getValue(),
                    NKStringFormater.format(" doInformBattleDSJumpFailure errorCode:{} battleId:{} {}", result, battleId, errMsg));
        }
    }

    /**
     * 做通知比赛开始
     */
    public void doInformMatchBegin() {
        /*
        if (getMatchData().getMatchStatus() == MatchStatusType.MST_NotMatching) {
            updateMatchData(MatchStatusType.MST_Matching, 0, "", 0);
        }
        */
    }

    /**
     * 更新匹配数据
     *
     * @param status 战斗状态
     * @param battleInfo 战斗信息
     */
    public void updateMatchData(RoomStatus status, AttrBattleInfo.proto_BattleInfo battleInfo, MatchDynamicConfigData matchDynamicConfigData) {
        // SP-爬塔不需要设置队伍状态
        if (!StarPConfs.isStarPRL(battleInfo.getMatchType())) {
            player.getUserAttr().getRoomInfo().setStatus(status);
            player.getUserAttr().getPlayerPublicLiveStatus().setRoomStatus(convertRoomState(status));
        }
        if (player.getPlayerBattleMgr().updateMatchData(battleInfo, matchDynamicConfigData)) {
            CsRoom.MatchSuccNtf.Builder ntfBuilder = CsRoom.MatchSuccNtf.newBuilder();
            ntfBuilder.getBattleInfoBuilder().setBattleid(battleInfo.getBattleid())
                    .setDsAddr(battleInfo.getDsAddr()).setSide(battleInfo.getSide())
                    .setDsAuthToken(battleInfo.getDsAuthToken())
                    .setDesModInfo(battleInfo.getDesModInfo())
                    .setGlobalChatGroupKey(convertChatGroupKey(battleInfo.getGlobalChatGroupKey()))
                    .setSideChatGroupKey(convertChatGroupKey(battleInfo.getSideChatGroupKey()))
                    .setMatchType(battleInfo.getMatchType())
                    .setAiGameType(battleInfo.getAiGameType())
                    .setCompetitionBasicInfo(battleInfo.getCompetitionBasicInfo())
                    .setMiscInfo(battleInfo.getMiscInfo())
                    .setDsaInstanceID(battleInfo.getDsaInstanceID())
                    .setSceneId(battleInfo.getSceneId())
                    //.setPakType(battleInfo.getPakType()) 废弃
                    .addAllPakGroupIdList(battleInfo.getPakGroupIdListList());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_MATCHSUCCNTF, ntfBuilder);
        }
    }

    private RoomStateType convertRoomState(RoomStatus status) {
        switch (status) {
            case RS_NORMAL:
            case RS_TEAM:
                return RoomStateType.RST_Wait;
            case RS_Matching:
                return RoomStateType.RST_Matching;
            case RS_BATTLE_READY:
            case RS_BATTLE_START:
                return RoomStateType.RST_Battle;
            default:
        }
        return RoomStateType.RST_Wait;
    }

    /**
     * 准备注册
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    /**
     * 在注册     *
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void onRegister() throws NKCheckedException {

    }

    /**
     * 注册后
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void afterRegister() throws NKCheckedException {

    }

    /**
     * 准备负载
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    /**
     * 在加载
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void onLoad() throws NKCheckedException {

    }

    /**
     * 后负荷
     */
    @Override
    public void afterLoad() {

    }

    /**
     * 准备登录
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    /**
     * 在登录
     *
     * @throws NKCheckedException nkchecked例外
     */
    @Override
    public void onLogin() throws NKCheckedException {
        if (GSConfig.isMultiIdcDsEnable()) {
            // 设置下登陆时玩家ip获取的区域
            setLoginCountryCode(player.getSession().getIpAddr());
        }
    }

    /**
     * 登录后
     *
     * @param todayFirstLogin 今天第一次登录
     */
    @Override
    public void afterLogin(boolean todayFirstLogin) {
        if (!player.isReLogin()) {
            asyncSendMetaAiMatchParamReq();
        }
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    /**
     * 在注销
     */
    @Override
    public void onLogout() {

    }

    /**
     * 在中期晚上
     */
    @Override
    public void onMidNight() {

    }

    /**
     * 周开始
     */
    @Override
    public void onWeekStart() {

    }

    private proto_ChatGroupKey convertChatGroupKey(proto_ChatGroupKey key) {
        return proto_ChatGroupKey.newBuilder().setChatType(key.getChatType()).setId(key.getId())
                .setSubID(key.getSubID()).build();
    }

    private void onWarmRoundMatchRet(int matchResult,
                                     WarmRoundType warmRoundType,
                                     int matchType,
                                     long battleId) {
        if (GSEngine.getInstance().canUseGmCmd()) {
            // 更新下debug信息
            player.getWarmRoundManager().gmUpdateDebugInfo(battleId, matchType, warmRoundType);
        }

        try {
            player.getWarmRoundManager().updateWarmScoreOnMatchRet(warmRoundType, matchResult, battleId, matchType);
        } catch (Exception e) {
            LOGGER.error("player {} battle {} updateWarmScoreOnWarmRoundMatchSucc failed catch {}", player.getUid(),
                    battleId, e);
        }
    }

    //将list里面的元素拼接成字符串最大长度maxLength, 用,隔开
    private<T> String convertListToStr(List<T> list, int maxLength) {
        if (list.isEmpty()) {
            return "";
        }
        StringBuilder strBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); ++i) {
            T value = list.get(i);
            strBuilder.append(value);
            // 如果当前长度已经超过 maxLength，跳出循环
            if (strBuilder.length() >= maxLength) {
                strBuilder.setLength(maxLength); // 将字符串长度设置为 maxLength
                break;
            }
            if (i < list.size() - 1) {
                strBuilder.append(",");
            }
        }
        return strBuilder.toString();
    }

    private<T> String convertListToStr(List<T> list, String decollator) {
        if (list.isEmpty()) {
            return "";
        }
        StringBuilder strBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); ++i) {
            T value = list.get(i);
            strBuilder.append(value);
            if (i < list.size() - 1) {
                strBuilder.append(decollator);
            }
        }
        return strBuilder.toString();
    }

    public void sendMatchBeginTlog(MemberBaseInfo memberBaseInfo, SsCommon.PlayerTLogMatchBegin matchBegin) {
        // memberStr字段最大长度
        final int MAX_MEMBER_STR_SZ = 96;

        StringBuilder memberStrBuilder = new StringBuilder();
        for (int i = 0; i < matchBegin.getMemberListCount(); ++i) {
            long uid = matchBegin.getMemberList(i);
            memberStrBuilder.append(uid).append(",").append(player.getFriendManager().getRelationTypeVal(uid));
            if (i < matchBegin.getMemberListCount() - 1) {
                memberStrBuilder.append(";");
            }
        }

        String memberStr = memberStrBuilder.toString();
        //越界了就截断
        if (memberStrBuilder.length() >= MAX_MEMBER_STR_SZ) {
            String str = memberStrBuilder.toString();
            memberStr = str.substring(0, MAX_MEMBER_STR_SZ - 1);
        }

        String mapIDStr = convertListToStr(matchBegin.getMapIDList(), 64);
        String mapPoolList = convertListToStr(matchBegin.getMapPoolListList(), ";");

        TlogFlowMgr.sendPlayerMatchingBeginFlow(player, memberBaseInfo, matchBegin, player.getQualifyingManager().getQualifyingInfoForTlog(),
                player.getQualifyingManager().getStar(), memberStr, mapIDStr, mapPoolList);
    }

    public void sendMatchResultTlog(MemberBaseInfo memberBaseInfo, SsCommon.PlayerTLogMatchResult matchResult) {
        // memberStr字段最大长度
        final int MAX_MEMBER_STR_SZ = 96;

        String memberStr = new String("");
        if (matchResult.getMemberListCount() > 0) {
            StringBuilder memberStrBuilder = new StringBuilder();
            for (int i = 0; i < matchResult.getMemberListCount(); ++i) {
                long uid = matchResult.getMemberList(i);
                memberStrBuilder.append(uid).append(",").append(player.getFriendManager().getRelationTypeVal(uid));
                if (i < matchResult.getMemberListCount() - 1) {
                    memberStrBuilder.append(";");
                }
            }

            memberStr = memberStrBuilder.toString();
            //越界了就截断
            if (memberStrBuilder.length() >= MAX_MEMBER_STR_SZ) {
                String str = memberStrBuilder.toString();
                memberStr = str.substring(0, MAX_MEMBER_STR_SZ - 1);
            }
        }

        String campStr = new String("");
        if (matchResult.getCampListCount() > 0) {
            StringBuilder campStrBuilder = new StringBuilder();
            for (int i = 0; i < matchResult.getCampListCount(); ++i) {
                long uid = matchResult.getCampList(i);
                if (uid == player.getUid()) {
                    continue;
                }
                campStrBuilder.append(uid).append(",").append(player.getFriendManager().getRelationTypeVal(uid));
                if (i < matchResult.getCampListCount() - 1) {
                    campStrBuilder.append(";");
                }
            }
            campStr = campStrBuilder.toString();
            //越界了就截断
            if (campStrBuilder.length() >= MAX_MEMBER_STR_SZ) {
                String str = campStrBuilder.toString();
                campStr = str.substring(0, MAX_MEMBER_STR_SZ - 1);
            }
        }

        String mapIDStr = convertListToStr(matchResult.getMapIDList(), 64);
        String mapPoolList = convertListToStr(matchResult.getMapPoolListList(), ";");

        TlogFlowMgr.sendPlayerMatchingResultFlow(player, memberBaseInfo, matchResult, player.getQualifyingManager().getQualifyingInfoForTlog(),
                player.getQualifyingManager().getStar(), memberStr, campStr, mapIDStr, mapPoolList);
    }

    private void setLoginCountryCode(String loginAddr) {
        final int DEFAULT_COUNTRY_CODE = 0;
        // 查不到就用DEFAULT_COUNTRY_CODE
        int countryCode = DEFAULT_COUNTRY_CODE;

        String countryName = null;
        // 查询
        do {
            IpInfo result = new IpInfo();
            int res = IpDbApi.getInstance().Query(loginAddr, result);
            if (0 != res) {
                LOGGER.error("player {} addr {} ipAddr query fail",
                        player.getUid(), loginAddr);
                break;
            }
            countryName = result.contry;
            ResRegional.Regional regionConf = RegionalConf.getInstance().getByName(countryName);
            if (null == regionConf) {
                // 未查询到相关地区
                LOGGER.error("player {} addr {} countryName {} get countryCode is null",
                        player.getUid(), loginAddr, result.contry);
                break;
            }

            countryCode = regionConf.getId();
        } while (false);

        player.getUserAttr().getMatchStatics().setLoginCountryCode(countryCode);
        LOGGER.debug("player {} loginAddr {} country {} map to countryCode {}",
                player.getUid(), loginAddr, countryName, countryCode);
    }

    public int getLoginCountryCode() {
        return player.getUserAttr().getMatchStatics().getLoginCountryCode();
    }

    public void gmDebugPrintLoginCountryCode() {
        if (!ServerEngine.getInstance().canUseGmCmd()) {
            return;
        }

        CurrentExecutorUtil.addTimer(TimerType.GmNotifyTimer, TimerUtil.msToTick(2000), true, () -> {
            String debugMsg = NKStringFormater.format("loginCountryCode {}",
                    getLoginCountryCode());
            CsPlayer.PlayerNoticeMsgNtf.Builder ntf = CsPlayer.PlayerNoticeMsgNtf.newBuilder()
                    .setType(PlayerNoticeMsgType.PNT_GM_DEBUG_TIPS).setNotice(debugMsg);

            ntf.addParams("loginCountryCode");
            ntf.addParams(String.valueOf(getLoginCountryCode()));
            player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, ntf);
        });
    }

    public void gmSetLoginCountryCode(int countryCode) {
        if (!ServerEngine.getInstance().canUseGmCmd()) {
            return;
        }

        player.getUserAttr().getMatchStatics().setLoginCountryCode(countryCode);
        LOGGER.info("player {} gmSetLoginCountryCode to {}",
                player.getUid(), countryCode);

        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.GM);
    }

    /**
     * 如开关开启 异步通知aiLab, 玩家登陆时匹配有关数据
     */
    private void asyncSendMetaAiMatchParamReq() {
        if (!GSConfig.GsMetaAiConfig.isMetaAiLabMatchOptimizeEnable()) {
            LOGGER.debug("player {} asyncSendMetaAiMatchParamReq !isMetaAiLabMatchOptimizeEnable",
                    player.getUid());
            return;
        }

        if (!GSConfig.GsMetaAiConfig.isMetaAiGameSvrGetParamEnable()) {
            LOGGER.debug("player {} asyncSendMetaAiMatchParamReq !isMetaAiGameSvrGetParamEnable",
                    player.getUid());
            return;
        }

        // 不能抛出异常 登陆中
        try {
            // 不异步请求 反正也不读取结果了
            int ret = GsMetaAiRpcManager.getInstance().sendMetaAINtf(buildMetaAiMatchGetParamReq());
            if (NKErrorCode.OK.getValue() != ret) {
                LOGGER.error("player {} sendMetaAINtf ret {}",
                        player.getUid(), ret);
                Monitor.getInstance().add.fail(MonitorId.attr_metaai_get_param, 1);
            } else {
                Monitor.getInstance().add.succ(MonitorId.attr_metaai_get_param, 1);
            }
        } catch (Exception e) {
            LOGGER.error("player {} asyncSendMetaAiMatchParamReq catch",
                    player.getUid(), e);
        }
    }

    private MetaAiMatch.Pkg.Builder buildMetaAiMatchGetParamReq() {
        MetaAiMatch.Pkg.Builder reqBuilder = MetaAiMatch.Pkg.newBuilder();

        MetaAiMatch.Head.Builder headerBuilder = MetaAiMatch.Head.newBuilder();
        headerBuilder.setCmdId(MetaAiMatch.CmdID.CMD_GET_PARAM_REQ);
        if (ServerEngine.getInstance().isPressTest()) {
            headerBuilder.addRouteBusid(Framework.getInstance().getWorldId());
        }

        MetaAiMatch.Body.Builder bodyBuilder = MetaAiMatch.Body.newBuilder();
        MetaAiMatch.GetParamReq.Builder getParamBuilder = MetaAiMatch.GetParamReq.newBuilder();
        getParamBuilder.setDteventtime(Framework.currentTimeSec());
        getParamBuilder.setSyncReason("login");

        MetaAiMatch.PlayerInfo.Builder playerInfoBuilder = MetaAiMatch.PlayerInfo.newBuilder();
        playerInfoBuilder.setUid(player.getUid());
        playerInfoBuilder.setVopenid(player.getOpenId());
        playerInfoBuilder.setIzoneareaid(Framework.getInstance().getWorldId());
        playerInfoBuilder.setDyeGroup(getMetaAiDyeType().getNumber());
        playerInfoBuilder.setRegisterTime(player.getRegisterTime() / Constants.THOUSAND);
        playerInfoBuilder.setLoginTime(Framework.currentTimeSec());
        playerInfoBuilder.setFriendNum(player.getFriendManager().getAllFriendNum());
        playerInfoBuilder.setBackTime(player.getWarmRoundManager().getReturningWarmRoundBeginMillSec() / Constants.THOUSAND);

        fillMetaAiMatchGetParamGameModeField(playerInfoBuilder);

        getParamBuilder.addPlayerInfos(playerInfoBuilder);
        bodyBuilder.setGetParamReq(getParamBuilder);

        reqBuilder.setHead(headerBuilder);
        reqBuilder.setBody(bodyBuilder);
        return reqBuilder;
    }

    private void fillMetaAiMatchGetParamGameModeField(MetaAiMatch.PlayerInfo.Builder playerInfoBuilder) {
        // 按ted要求 填两个gamemode 一个普通 一个排位
        // 都按四排

        // 排位
        MetaAiMatch.GameMode.Builder ladderMatchModeBuilder = MetaAiMatch.GameMode.newBuilder();
        ladderMatchModeBuilder.setMatchType(MetaAiMatch.EMatchType.LADDER);
        ladderMatchModeBuilder.setTeamType(MetaAiMatch.ETeamType.MATCH_TEAMTYPE_FOUR);
        // 这里没有表可以反向查询 特殊编码映射下
        final int LADDER_TEAM_TYPE_FOUR_MATCH_TYPE = 6;
        ladderMatchModeBuilder.setYmMatchTypeId(LADDER_TEAM_TYPE_FOUR_MATCH_TYPE);

        QualifyingDegreeInfo qualifyingInfo = player.getQualifyingManager().getQualifyingInfo();
        ladderMatchModeBuilder.setLadderLevel(QualifyingUtils.combineQualifyTypeAndDegreeToOneInt(
                qualifyingInfo.getQualifying(), qualifyingInfo.getDegree()));
        ladderMatchModeBuilder.setLadderScore(qualifyingInfo.getIntegral());
        ladderMatchModeBuilder.setLadderStar(qualifyingInfo.getStar());
        ladderMatchModeBuilder.setHiddenScore(player.getMatchMMRScoreManager()
                .getMMRScoreByType(MMRScoreType.MST_Core));

        fillMetaAiGameModeWarmPoint(ladderMatchModeBuilder);

        playerInfoBuilder.addGameMode(ladderMatchModeBuilder);

        // 普通
        MetaAiMatch.GameMode.Builder normalMatchModeBuilder = MetaAiMatch.GameMode.newBuilder();
        normalMatchModeBuilder.setMatchType(MetaAiMatch.EMatchType.MATCH);
        normalMatchModeBuilder.setTeamType(MetaAiMatch.ETeamType.MATCH_TEAMTYPE_FOUR);
        // 同样 映射下
        final int NORMAL_TEAM_TYPE_FOUR_MATCH_TYPE = 3;
        normalMatchModeBuilder.setYmMatchTypeId(NORMAL_TEAM_TYPE_FOUR_MATCH_TYPE);
        // 这些数据两边一致
        normalMatchModeBuilder.setLadderLevel(ladderMatchModeBuilder.getLadderLevel());
        normalMatchModeBuilder.setLadderScore(ladderMatchModeBuilder.getLadderScore());
        normalMatchModeBuilder.setLadderStar(ladderMatchModeBuilder.getLadderStar());
        normalMatchModeBuilder.setHiddenScore(ladderMatchModeBuilder.getHiddenScore());
        normalMatchModeBuilder.setMatchWarmpoint(ladderMatchModeBuilder.getMatchWarmpoint());
        normalMatchModeBuilder.setLadderWarmpoint(ladderMatchModeBuilder.getLadderWarmpoint());
        normalMatchModeBuilder.setLadderTwoWarmpoint(ladderMatchModeBuilder.getLadderTwoWarmpoint());
        normalMatchModeBuilder.setLadderFourWarmpoint(ladderMatchModeBuilder.getLadderFourWarmpoint());

        playerInfoBuilder.addGameMode(normalMatchModeBuilder);
    }

    private void fillMetaAiGameModeWarmPoint(MetaAiMatch.GameMode.Builder ladderMatchModeBuilder) {
        WarmRoundInfo warmRoundScore = player.getWarmRoundManager().getWarmRoundInfo();
        // 单排休闲
        ladderMatchModeBuilder.setMatchWarmpoint(PlayerMatchWarmRoundManager
                .getScoreFromWarmRoundScoreInfoByType(warmRoundScore, WarmRoundScoreType.WRST_QuickPlay));
        // 单排排位
        ladderMatchModeBuilder.setLadderWarmpoint(PlayerMatchWarmRoundManager
                .getScoreFromWarmRoundScoreInfoByType(warmRoundScore, WarmRoundScoreType.WRST_Qualifying));
        // 双排排位
        ladderMatchModeBuilder.setLadderTwoWarmpoint(PlayerMatchWarmRoundManager
                .getScoreFromWarmRoundScoreInfoByType(warmRoundScore, WarmRoundScoreType.WRST_QualifyingTwo));
        // 四排排位
        ladderMatchModeBuilder.setLadderFourWarmpoint(PlayerMatchWarmRoundManager
                .getScoreFromWarmRoundScoreInfoByType(warmRoundScore, WarmRoundScoreType.WRST_QualifyingFour));
    }

    /**
     * 获取uid在ailab匹配中所属的分组类型
     * @return ailab匹配中所属的分组类型
     */
    public MetaAiLabMatchDyeGroupType getMetaAiDyeType() {
        if (!GSConfig.GsMetaAiConfig.isMetaAiLabMatchOptimizeEnable()) {
            return MetaAiLabMatchDyeGroupType.MALMDGT_BaseGrp;
        }


        return MetaAiRpcManager.getUidMetaAiDyeType(player.getUid(),
                GSConfig.GsMetaAiConfig.getMetaAiDyeAGrpUidRangePair(),
                GSConfig.GsMetaAiConfig.getMetaAiDyeControlGrpUidRangePair());
    }

}
