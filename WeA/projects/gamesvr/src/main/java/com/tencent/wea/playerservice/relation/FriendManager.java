package com.tencent.wea.playerservice.relation;

import static com.tencent.wea.playerservice.relation.SimpleDataManager.PlayerPublicSourceFriendNtf;
import static com.tencent.wea.playerservice.relation.SimpleDataManager.PlayerPublicSourceFriendUpdate;
import static com.tencent.wea.protocol.common.PlayerPublicInfoField.INTIMATE_ONLINE_NOTICE;
import static com.tencent.wea.protocol.common.PlayerPublicInfoField.LOGOUT_TIME_MS;

import com.tencent.cache.Cache;
import com.tencent.cache.Cache.CacheResult;
import com.tencent.cache.CacheUtil;
import com.tencent.coRedis.CoRedisCmd;
import com.tencent.condition.event.player.common.AddGameFriendEvent;
import com.tencent.condition.event.player.common.DeleteGameFriendEvent;
import com.tencent.condition.event.player.common.IntimacyAddEvent;
import com.tencent.condition.event.player.common.ReturningFriendEvt;
import com.tencent.lru.LRUCache;
import com.tencent.nk.activity.ActivityTimeUtil;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.itop.ItopManager;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.Tuple;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.guid.BillNoIdGenerator;
import com.tencent.nk.util.random.util.RandomUtil;
import com.tencent.profile.ProfilePlayerPublicInfoFieldConst;
import com.tencent.resourceloader.resclass.ActivityMainConfig;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.ChatSystemMsgConfData;
import com.tencent.resourceloader.resclass.IntimateRelationLevelConfData;
import com.tencent.resourceloader.resclass.MailIniConfData;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.RelationBattleModeIntimacyConfData;
import com.tencent.resourceloader.resclass.RelationConfData;
import com.tencent.resourceloader.resclass.RelationMiscConfData;
import com.tencent.resourceloader.resclass.SeasonConfData;
import com.tencent.resourceloader.resclass.ServerIdipAreaConfig;
import com.tencent.rpc.RpcResult;
import com.tencent.seq.SeqUtil;
import com.tencent.tcaplus.dao.FriendHistoryInteractTableDao;
import com.tencent.tcaplus.dao.OpenIdToUidDao;
import com.tencent.tcaplus.dao.OpenIdToUidDao.OpenIdToUidInfo;
import com.tencent.tcaplus.dao.PlatFriendTableDao;
import com.tencent.tcaplus.dao.PlayerOnlineDao;
import com.tencent.tcaplus.dao.PlayerPublicDao;
import com.tencent.tcaplus.dao.PlayerPublicDao.PlayerPublicAttrKey;
import com.tencent.tcaplus.dao.RelationTableDao;
import com.tencent.tcaplus.dao.RelationTableDao.TdmRelationInfo;
import com.tencent.tcaplus.dao.RemovedFriendHistoryDao;
import com.tencent.tcaplusattr.FlashAttr;
import com.tencent.tconnd.TconndManager;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.util.SortedArrayList;
import com.tencent.util.TextUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.*;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.PlayerInteractionInvoker;
import com.tencent.wea.playerservice.ProfileGameSvrPlayerMgr;
import com.tencent.wea.playerservice.activity.implement.BaseActivity;
import com.tencent.wea.playerservice.activity.implement.TeamRankActivity;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.event.common.FriendAddIntimacyEvent;
import com.tencent.wea.playerservice.event.common.PlayerAddFriendCountEvent;
import com.tencent.wea.playerservice.event.common.PlayerFriendNumModifyEvent;
import com.tencent.wea.playerservice.event.common.PlayerIntimateRelationEvent;
import com.tencent.wea.playerservice.event.common.PlayerSendGoldCoinEvent;
import com.tencent.wea.playerservice.event.consumer.PlayerAddFriendEventConsumer;
import com.tencent.wea.playerservice.event.consumer.PlayerFarmCreateEventConsumer;
import com.tencent.wea.playerservice.event.consumer.PlayerFarmSendGiftConsumer;
import com.tencent.wea.playerservice.event.consumer.PlayerFarmVisitOtherConsumer;
import com.tencent.wea.playerservice.event.consumer.PlayerXiaowoVisitOthersXiaowoConsumer;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.player.PlayerStateMgr;
import com.tencent.wea.playerservice.profile.ProfileManager;
import com.tencent.wea.playerservice.report.PlayerReportUtil;
import com.tencent.wea.playerservice.returning.PlayerReturnActivityManager;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.uic.PlayerTssUtil;
import com.tencent.wea.protocol.AttrFriendInteractDetailAttr.proto_FriendInteractDetailAttr;
import com.tencent.wea.protocol.AttrSeasonFashion;
import com.tencent.wea.protocol.AttrSeasonFashionEquipBook;
import com.tencent.wea.protocol.CsLetsgo;
import com.tencent.wea.protocol.CsLetsgo.RelationSettlementInfo;
import com.tencent.wea.protocol.CsRelation;
import com.tencent.wea.protocol.CsRelation.FriendIntimacyChangeNtf;
import com.tencent.wea.protocol.CsRelation.RelationIntimacyNtf;
import com.tencent.wea.protocol.CsRelation.RelationStateInfo;
import com.tencent.wea.protocol.CsRelation.RelationStateNtf;
import com.tencent.wea.protocol.CsRelation.StickFriendScene;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SrFriendintimacy;
import com.tencent.wea.protocol.SsCommon;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.SsGamesvr.PlayerIntimateOnlineNoticeItemData;
import com.tencent.wea.protocol.SsGamesvr.PlayerSpecialData;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyPlayerOnlineStatusReq;
import com.tencent.wea.protocol.SsSeqsvr;
import com.tencent.wea.protocol.SsUgcplatsvr.RpcGetRecommendAddFriendReasonReq;
import com.tencent.wea.protocol.common.ChatGroupKey;
import com.tencent.wea.protocol.common.ChatMsgData;
import com.tencent.wea.protocol.common.ChatMsgType;
import com.tencent.wea.protocol.common.ChatType;
import com.tencent.wea.protocol.common.FriendHistoryInteractData;
import com.tencent.wea.protocol.common.FriendHistoryInteractData.Builder;
import com.tencent.wea.protocol.common.FriendIntimacyChangeReason;
import com.tencent.wea.protocol.common.G6Common.LevelDropItemInfo;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.KVArray;
import com.tencent.wea.protocol.common.KVEntry;
import com.tencent.wea.protocol.common.PlatFriendData;
import com.tencent.wea.protocol.common.PlatFriendList;
import com.tencent.wea.protocol.common.PlayerDressItemInfo;
import com.tencent.wea.protocol.common.PlayerPublicInfo;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.RecommendAddFriendReasonDesc;
import com.tencent.wea.protocol.common.RelationMsgExtraInfo;
import com.tencent.wea.protocol.idip.IdipFriendSearchType;
import com.tencent.wea.protocol.idip.IdipFriendSortType;
import com.tencent.wea.protocol.idip.IdipFriendType;
import com.tencent.wea.redis.FriendApplyCache;
import com.tencent.wea.redis.FriendIntimacyCache;
import com.tencent.wea.rpc.service.GameService;
import com.tencent.wea.rpc.service.SeqService;
import com.tencent.wea.rpc.service.UgcplatService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerPublic;
import com.tencent.wea.tcaplus.TcaplusDb.RelationTable;
import com.tencent.wea.tcaplus.TcaplusDbWrapper;
import com.tencent.wea.tcaplus.TcaplusDbWrapper.PlayerOnlineTable;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PiiIntimacyChangeParams;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionData;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionInstruction;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResActivity.ActivityTeamRankConfig;
import com.tencent.wea.xlsRes.ResBackpackItem;
import com.tencent.wea.xlsRes.ResChat.ChatSystemMsgConf;
import com.tencent.wea.xlsRes.ResRelation;
import com.tencent.wea.xlsRes.ResRelation.RelationConf;
import com.tencent.wea.xlsRes.ResSeason;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.BirthdayVisibleRange;
import com.tencent.wea.xlsRes.keywords.ChatSystemMsgConfEnum;
import com.tencent.wea.xlsRes.keywords.CommonLimitType;
import com.tencent.wea.xlsRes.keywords.FeatureOpenStatus;
import com.tencent.wea.xlsRes.keywords.FriendAddMotivationType;
import com.tencent.wea.xlsRes.keywords.FriendInteractDataType;
import com.tencent.wea.xlsRes.keywords.FriendOpType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.IntimateRelationFlowType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.PlayerStateType;
import com.tencent.wea.xlsRes.keywords.RelationApplyType;
import com.tencent.wea.xlsRes.keywords.RelationConfEnum;
import com.tencent.wea.xlsRes.keywords.RelationTypeEnum;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * @program: WEA
 * @description: 好友管理
 * @author: digoldzhang
 * @create: 2021-08-30 16:14
 **/

public class FriendManager extends RelationManager {

    private static final Logger LOGGER = LogManager.getLogger(FriendManager.class);
    private static final int INVALID_RELATION_APPLY_MODULE = -1;
    private HashMap<Long, String> platFriendNickName = new HashMap<>();
    private PlatFriendInfo platFriendInfo;
    private UgcFriendInfo ugcFriendInfo;
    private HashSet<PlayerPublicInfoField> changeFields = new HashSet<>();

    /* 由于社交相关改造, 该列表要与 profileSvr 共用
       为保证常量来源唯一, 遂迁移至 common 下的 ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFields
    ----
    private static final List<PlayerPublicInfoField> fields = Arrays.asList(UID, OPENID, PROFILE, FRIEND_NICKNAME,
            GENDER, QUALIFY_DEGREE_INFO, INTIMACT, DRESS_ITEM_INFOS, LABELS, MONTH_CARD, LOGOUT_TIME_MS,
            HIDE_PERSONALP_ROFILE, PLAT_PRIVILEGES, ACCOUNT_TYPE, HIDE_QQ_FRIEND_REQ, PLAYER_GEO_INFO, XIAOWO_ATXIAOWO,
            AUTH_TYPE, AUTH_DESC, PLAYER_GAME_TIMES, RETURNING, LAST_KEEP_ALIVE_TIME, PLAYER_STATE, SVR_ID, NAME,
            SHORT_UID, LEVEL, VIP_LEVEL, CLUB_INFO, SHOW_QUALIFY_TYPE, RECENT_ACTIVITY, LOBBY_MAP_ID, FARM_ATFARM,
            INTIMATE_ONLINE_NOTICE, ROOM_EXTRA_INFO, HIDE_ROOM_EXTRA_INFO, FARM_LEVEL, FARM_MONTHCARD_ENDTIME,
            DEMAND_SWITCH, HIDE_PROFILE_TO_FRIEND, HIDE_PROFILE_TO_STRANGER, CREATOR_ID, CUPS_NUM, HAS_HOUSE, STARP_ROLE_INFO);*/

    private static final Set<FriendInteractDataType> DAILY_INTERACT_STATISTIC_TYPE_SET = Set.of(
            FriendInteractDataType.FIDT_SendGoldCoin,
            FriendInteractDataType.FIDT_ChatCnt,
            FriendInteractDataType.FIDT_VisitXiaoWo,
            FriendInteractDataType.FIDT_VisitFarm,
            FriendInteractDataType.FIDT_TeamBattleCnt,
            FriendInteractDataType.FIDT_SendGift
    );
    private LRUCache<Long, Builder> friendInteractHistoryDataMap = new LRUCache<>(getFriendHistoryInteractCacheLimit());

    private static final Integer DEFAULT_FRIEND_LIST_NUM_PER_PAGE = 10;
    private boolean needNtfLogin = false;

    private boolean isNtfFriendDataFull = false;

    private long lastNtfChangeInfoTs = 0;

    private long lastGetFriendRentFashionListTimeMs = 0;

    public FriendManager(Player player) {
        super(player, GameModuleId.GMI_FriendManager);
        initRelationInfo();
        platFriendInfo = new PlatFriendInfo(player);
        ugcFriendInfo = new UgcFriendInfo(player);
        // 注册好友添加的事件监听器
        player.getEventSwitch().register(new PlayerAddFriendEventConsumer(player));
        player.getEventSwitch().register(new PlayerXiaowoVisitOthersXiaowoConsumer(player));
        player.getEventSwitch().register(new PlayerFarmVisitOtherConsumer(player));
        player.getEventSwitch().register(new PlayerFarmCreateEventConsumer(player));
        player.getEventSwitch().register(new PlayerFarmSendGiftConsumer(player));
    }


    /**
     * 检查时装租赁时间段限制
     *
     * @return
     */
    public static boolean checkFashionRentTime() {
        if (!PropertyFileReader.getRealTimeBooleanItem("friend_rent_fashion_enable", true)) {
            return false;
        }

        ResRelation.RelationMiscConf miscConf = RelationMiscConfData.getInstance().getMiscConf();
        if (null == miscConf) {
            return true;
        }

        if (!miscConf.getAllowRentFashion()) {
            return false;
        }

        if (!miscConf.hasAllowRentFashionStartTime() || !miscConf.hasAllowRentFashionEndTime()) {
            return false;
        }

        long curTimeMs = Framework.currentTimeMillis();
        long weekStartTimeMs = DateUtils.getThisWeekMondayZeroTime(curTimeMs).getTime();
        long startTimeMs =
                weekStartTimeMs
                        + ActivityTimeUtil.convertTimeInfoToSec(miscConf.getAllowRentFashionStartTime()) * 1000L;
        long endTimeMs =
                weekStartTimeMs + ActivityTimeUtil.convertTimeInfoToSec(miscConf.getAllowRentFashionEndTime()) * 1000L;
        if (startTimeMs >= endTimeMs) {
            LOGGER.error("item fashion rent time config error");
            return false;
        }

        if (curTimeMs < startTimeMs || curTimeMs > endTimeMs) {
            return false;
        }

        return true;
    }

    @Override
    public RelationTypeEnum getRelationType() {
        return RelationTypeEnum.RTE_FRIEND_RELATION;
    }

    // 金娟: RelationTypeEnum 优先级: 4 > 3 > 1
    public RelationTypeEnum getTlogFriendRelationType(long uid) {
        if (player.getIntimateManager().isRelation(uid)) {
            return player.getIntimateManager().getRelationType();
        }
        if (player.getFriendManager().isPlatFriend(uid)) {
            return RelationTypeEnum.RTE_PLAT_FRIEND_RELATION;
        }
        if (player.getFriendManager().isRelation(uid)) {
            return player.getFriendManager().getRelationType();
        }
        return RelationTypeEnum.RTE_UNKNOWN;
    }

    private void initRelationInfo() {
        dbRelationInfo = player.getUserAttr().getRelationMapInfo()
                .getRelationInfo(RelationTypeEnum.RTE_FRIEND_RELATION);
        if (dbRelationInfo == null) {
            player.getUserAttr().getRelationMapInfo().getRelationInfo()
                    .put(RelationTypeEnum.RTE_FRIEND_RELATION, new RelationInfo());
            dbRelationInfo = player.getUserAttr().getRelationMapInfo()
                    .getRelationInfo(RelationTypeEnum.RTE_FRIEND_RELATION);
        }
    }

    public String getPlatFriendNickNameIfPresent(long uid) {
        return platFriendNickName.get(uid);
    }

    @Override
    public void onMidNight() {
        super.onMidNight();
        player.getUserAttr().getRelationMapInfo().clearSendGoldCoinUid();
        for (DailyIntimacyData dailyIntimacyData : player.getUserAttr().getRelationMapInfo().getDailyIntimacy()
                .values()) {
            dailyIntimacyData.setTodayMsgNum(0);
        }

        checkAndRefreshDailyInteract();
        //同步一次回归好友
        int count = getReturningFriendCount();
        player.getPlayerEventManager().dispatch(new ReturningFriendEvt(player.getConditionMgr())
                .setCount(count));
        LOGGER.debug("post player {} ReturningFriendEvt count : {}", player.getUid(), count);
    }

    @Override
    public void onWeekStart() {
        player.getUserAttr().getPlayerPublicGameSettings().setHidePlayerStatusWeekCnt(0);
    }

    @Override
    public void afterLoad() {
        if (player.isPersonalizedRecommendationDisabled()) {
            for (DBApplyRelation relation : dbRelationInfo.getReceiveApplyInfo().values()) {
                // -1均代表无效, 不单独定义了
                relation.setAddMotivation(-1);
                relation.setMotivationParams("");
                relation.deleteAlgoInfo();
                relation.setRecommendReason(-1);
                relation.setRecommendReasonText("");
            }
        }
    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        super.afterLogin(todayFirstLogin);

        needNtfLogin = true;
        isNtfFriendDataFull = false;
        ProfileGameSvrPlayerMgr.getInstance().addNeedNtfLogin(player.getUid());

        // 在redis中记录当前关系数量
        cacheRelationNum(getRelationType(), player.getUid(), dbRelationInfo.getModRelationInfoSize());
        // 在redis缓存sendapply数据
        updateSendApplyInfoToRedis();

        if (PropertyFileReader.getRealTimeBooleanItem("use_async_friend_mgr_after_login", false)) {
            try {
                CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                    procAfterLogin(todayFirstLogin);
                    return null;
                }, "FriendManagerAfterLogin", true);
            } catch (NKCheckedException ex) {
                LOGGER.error("procAfterLogin async call fail", ex);
            }
        } else {
            procAfterLogin(todayFirstLogin);
        }

        // 隐身相关
        refreshHidePlayerStatusSwitch();
        resetHidePlayerStatus();

        checkAndRemoveIntimateNoticeAttr();

        FriendIntimacyCache.clearFriendIntimacyUpdateNtfCache(player.getUid());

        checkAndRefreshDailyInteract();
        transferFriendVisitData();

        // 高低版本兼容
        if (VersionUtil.checkClientVersion("", "1.3.7.27", player.getClientVersion64())) {
            PlayerPublicGameSettings publicSettings = player.getUserAttr().getPlayerPublicGameSettings();
            publicSettings.setHideProfileToFriend(publicSettings.getHidePersonalProfile());
            publicSettings.setHideProfileToStranger(publicSettings.getHidePersonalProfile());
            addChangeField(PlayerPublicInfoField.HIDE_PROFILE_TO_FRIEND);
            addChangeField(PlayerPublicInfoField.HIDE_PROFILE_TO_STRANGER);
        }
        notifyAllOnlineFriendPlayerInfoAsync();

    }

    @Override
    public void onReload() {
        refreshHidePlayerStatusSwitch();
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {
        //        Map<Long, PlayerColdData.Builder> coldDataMap = SimpleDataManager.batchGetPlayerColdDataWithFriendNickName(
        //                dbRelationInfo.getModRelationInfo().keySet(), ColdDataType.CDT_Relation, player);
        //        for (long uid : coldDataMap.keySet()) {
        //            setAttrPlayerNameImpl(dbRelationInfo.getModRelationInfo().get(uid), coldDataMap.get(uid));
        //        }
        //        Map<Long, PlayerColdData.Builder> platColdDataMap = SimpleDataManager.batchGetPlayerColdDataWithFriendNickName(
        //                platFriendInfo.getAllPlatFriendUid(), ColdDataType.CDT_Relation, player);
        //        for (long uid: platColdDataMap.keySet()) {
        //            setAttrPlayerNameImpl(platFriendInfo.getPlatFriendInfo(uid), platColdDataMap.get(uid));
        //        }

        MapAttrObj<Integer, StickFriendInfo> pubFriends = player.getUserAttr().getPlayerPublicProfileInfo()
                .getStickFriends();
        Collection<StickFriendInfo> friendInfos = player.getUserAttr().getStickFriends().values();
        if (pubFriends.isEmpty() && !friendInfos.isEmpty()) {
            updatePlayerPublishStickFriend();
        }
        //同步一次回归好友
        int count = getReturningFriendCount();
        player.getPlayerEventManager().dispatch(new ReturningFriendEvt(player.getConditionMgr())
                .setCount(count));
        LOGGER.debug("post player ReturningFriendEvt count : {}", count);
    }

    void procAfterLogin(boolean todayFirstLogin) {
        TxStopWatch stopWatch = NKStopWatch.SW_FriendMgrAfterLogin.getStopWatch("procAfterLogin");
        // 测试时平台好友加载为 gamesvr在线玩家，仅测试时使用
        //        if (PropertyFileReader.getRealTimeIntItem("is_take_online_player_as_plat_friend", 0) == 0) {
        //            testUpdatePlatFriend();
        //            stopWatch.mark("procAfterLogin.testUpdatePlatFriend");
        //        } else {
        if (!player.isReLogin()) {
            try {
                updatePlatFriendShip(todayFirstLogin, stopWatch);
            } catch (Exception e) {
                LOGGER.error("updatePlatFriendShip fail, {}", player.getUid(), e);
            }
            stopWatch.mark("procAfterLogin.updatePlatFriendShip");
        } else {
            LOGGER.info("not updatePlatFriendShip because player-{} isUseCache", player.getUid());
        }

        var fields = player.getPlayerStarPMgr().needSyncFriendPublicInfoStarPPart() ?
                ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFieldsAll :
                ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFields;
        updateFriendData(fields);

        stopWatch.mark("procAfterLogin.updateFriendData");
        //        stopWatch.mark("procAfterLogin.updatePlatFriendShip2");
        //        }
        //        updateFriendOnlineStatus();
        //        stopWatch.mark("procAfterLogin.updateFriendOnlineStatus");
        // 通知所有在线好友我上线了
        //        notifyAllOnlineFriendPlayerStatus(player.getPlayerStateMgr().getPlayerState());
        //        stopWatch.mark("procAfterLogin.notifyAllOnlineFriendPlayerStatus");
        stopWatch.dump(500, "procAfterLogin.updateFriend");

        player.getPlayerSnsManager().setStartPing(true);
    }

    public void updateFriendData(long targetUid) {
        DBRelation info = dbRelationInfo.getModRelationInfo().get(targetUid);
        if (info == null) {
            info = platFriendInfo.getPlatFriendInfo(targetUid);
        }
        if (info == null) {
            return;
        }
        Map<Long, PlayerPublicInfo.Builder> publicInfoMap = SimpleDataManager.batchGetPlayerPublic(player,
                Collections.singletonList(targetUid), ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFields, true,
                PlayerPublicSourceFriendUpdate);
        PlayerPublicInfo.Builder publicInfo = publicInfoMap.get(targetUid);
        if (publicInfo == null) {
            return;
        }
        publicInfo.setLogoutTimeMs(publicInfo.getLastKeepAliveTime());
        setAttrPlayerOnline(info, publicInfo);
        setAttrPlayerStealth(info, publicInfo);
        info.getNameData().setName(publicInfo.getName());
        info.getHotData().setCreatorId(publicInfo.getCreatorId());
        info.setReturning(publicInfo.getReturning());
        info.setReturnExpiredSec(publicInfo.getReturnExpiredSec());
        info.setBirthdayMonthDay(publicInfo.getBirthdayMonthDay());
        info.setBirthdayVisibleRange(publicInfo.getBirthdayVisibleRange());
        info.getHotData().setHideProfileToFriend(publicInfo.getHideProfileToFriend());
        // 异步分批向客户端同步所有好友信息
        syncFriendPlayerInfoAsync(publicInfoMap);
    }

    private void updateFriendData(Collection<PlayerPublicInfoField> fields) {
        Set<Long> uidSet = new HashSet<>();
        uidSet.addAll(platFriendInfo.getPlatFriendInfo().keySet());
        uidSet.addAll(dbRelationInfo.getModRelationInfo().keySet());

        Map<Long, PlayerPublicInfo.Builder> publicInfoMap = SimpleDataManager.batchGetPlayerPublic(player,
                uidSet, fields, true, PlayerPublicSourceFriendNtf);
        publicInfoMap.forEach((uid, data) -> {
            data.setLogoutTimeMs(data.getLastKeepAliveTime());
        });
        platFriendInfo.getPlatFriendInfo().values().forEach((info) -> {
            PlayerPublicInfo.Builder publicInfo = publicInfoMap.get(info.getUid());
            //            LOGGER.debug("paltf riend online data:{}-{}-{}", onlineTable.getUid(), onlineTable.getSvrId(),
            //                    onlineTable.getLastKeepAliveTime());
            if (publicInfo != null) {
                setAttrPlayerOnline(info, publicInfo);
                setAttrPlayerStealth(info, publicInfo);
                info.getNameData().setName(publicInfo.getName());
                info.getHotData().setCreatorId(publicInfo.getCreatorId());
                info.setReturning(publicInfo.getReturning());
                info.setReturnExpiredSec(publicInfo.getReturnExpiredSec());
                info.setBirthdayMonthDay(publicInfo.getBirthdayMonthDay());
                info.setBirthdayVisibleRange(publicInfo.getBirthdayVisibleRange());
                info.getHotData().setHideProfileToFriend(publicInfo.getHideProfileToFriend());
            } else {
                LOGGER.error("publicInfo is null.friendUid:{}", info.getUid());
            }
            if (platFriendNickName.containsKey(info.getUid())) {
                info.getNameData().setFriendNickname(platFriendNickName.get(info.getUid()));
            }
        });
        dbRelationInfo.getModRelationInfo().values().forEach((info) -> {
            PlayerPublicInfo.Builder publicInfo = publicInfoMap.get(info.getUid());
            //            LOGGER.debug("game friend online data:{}-{}-{}", onlineTable.getUid(), onlineTable.getSvrId(),
            //                    onlineTable.getLastKeepAliveTime());
            if (publicInfo != null) {
                setAttrPlayerOnline(info, publicInfo);
                setAttrPlayerStealth(info, publicInfo);
                info.getNameData().setName(publicInfo.getName());
                info.getHotData().setCreatorId(publicInfo.getCreatorId());
                info.setReturning(publicInfo.getReturning());
                info.setReturnExpiredSec(publicInfo.getReturnExpiredSec());
                info.setBirthdayMonthDay(publicInfo.getBirthdayMonthDay());
                info.setBirthdayVisibleRange(publicInfo.getBirthdayVisibleRange());
                info.getHotData().setHideProfileToFriend(publicInfo.getHideProfileToFriend());
            } else {
                LOGGER.error("publicInfo is null.friendUid:{}", info.getUid());
            }

        });
        // 异步分批向客户端同步所有好友信息
        syncFriendPlayerInfoAsync(publicInfoMap);
        isNtfFriendDataFull = true;
    }

    @Override
    public void onLogout() {
        super.onLogout();
    }

    @Override
    public void onDisconnect() {
        super.onDisconnect();
        addChangeField(LOGOUT_TIME_MS);
        if (PropertyFileReader.getRealTimeBooleanItem("snssvr_enable", true)) {
            // 切换到上报profilesvr
            ProfileGameSvrPlayerMgr.getInstance().forceSyncPlayerPublicChanged(player.getUid());
        } else {
            notifyAllOnlineFriendPlayerStatus();
        }
        player.getUserAttr().getBasicInfo().setLimitNotifyTimes(true);
    }

    public int getAllFriendNum() {
        return dbRelationInfo.getModRelationInfoSize() + platFriendInfo.getPlatFriendNum();
    }

    public int getGameFriendNum() {
        return dbRelationInfo.getModRelationInfoSize();
    }

    public int getReturningFriendCount() {
        int count = 0;
        long currSec = Framework.currentTimeSec();
        for (var info : dbRelationInfo.getModRelationInfo().values()) {
            if (PlayerReturnActivityManager.isReturningRelation(info)) {
                count++;
            }
        }
        for (var info : platFriendInfo.getPlatFriendInfo().values()) {
            if (PlayerReturnActivityManager.isReturningRelation(info)) {
                count++;
            }
        }
        return count;
    }

    public static int getMaxNum(RelationConfEnum type) {
        ResRelation.RelationConf relationConf = RelationConfData.getInstance().get(type);
        switch (type.getNumber()) {
            case RelationConfEnum.RCE_FRIEND_MAX_NUM_LIMIT_VALUE:
                int maxRelationNum = 100;
                maxRelationNum = relationConf.getValue();
                if (maxRelationNum < 1) {
                    maxRelationNum = 1;
                } else if (maxRelationNum > 500) {
                    maxRelationNum = 500;
                }
                return maxRelationNum;
            case RelationConfEnum.RCE_FRIEND_APPLY_MAX_NUM_LIMIT_VALUE:
                int maxApplyNum = 10;
                maxApplyNum = relationConf.getValue();
                if (maxApplyNum < 1) {
                    maxApplyNum = 1;
                } else if (maxApplyNum > 100) {
                    maxApplyNum = 100;
                }
                return maxApplyNum;
            case RelationConfEnum.RCE_FRIEND_ADD_MAX_NUM_LIMIT_VALUE:
                int maxAddNum = 10;
                maxAddNum = relationConf.getValue();
                if (maxAddNum < 1) {
                    maxAddNum = 1;
                } else if (maxAddNum > 100) {
                    maxAddNum = 100;
                }
                return maxAddNum;
            default:
                return 0;
        }
    }

    @Override
    public int getMaxRelationNum(long uid) {
        return getMaxNum(RelationConfEnum.RCE_FRIEND_MAX_NUM_LIMIT);
    }

    @Override
    public int getMaxReceiveApplyNum() {
        return getMaxNum(RelationConfEnum.RCE_FRIEND_APPLY_MAX_NUM_LIMIT);
    }

    @Override
    public int getMaxSendApplyNum() {
        return getMaxNum(RelationConfEnum.RCE_FRIEND_ADD_MAX_NUM_LIMIT);
    }

    @Override
    public long getAddInfoValidTime() {
        ResRelation.RelationConf relationConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_FRIEND_ADD_VALID_TIME);
        if (relationConf != null && relationConf.getValue() != 0) {
            return relationConf.getValue() * 1000;
        }
        return Long.MAX_VALUE;
    }

    @Override
    public long getApplyInfoValidTime() {
        ResRelation.RelationConf relationConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_FRIEND_APPLY_VALID_TIME);
        if (relationConf != null && relationConf.getValue() != 0) {
            return relationConf.getValue() * DateUtils.ONE_DAY_MILLIS;
        }
        return Long.MAX_VALUE;
    }

    public static int getConfValueOrDefault(RelationConfEnum type, int defaultValue) {
        ResRelation.RelationConf relationConf = RelationConfData.getInstance().get(type);
        return relationConf != null ? relationConf.getValue() : defaultValue;
    }

    public static int getSendGoldCoinIntimacy() {
        return getConfValueOrDefault(RelationConfEnum.RCE_INTIMACY_VALUE_SEND_GOLD_COIN, 0);
    }

    public static int getSendGoldCoinDailyLimit() {
        return getConfValueOrDefault(RelationConfEnum.RCE_FRIEND_SEND_GOLD_COIN_DAILY_LIMIT, 0);
    }

    public static int getSendGoldCoinMailTemplateId() {
        return getConfValueOrDefault(RelationConfEnum.RCE_FRIEND_SEND_GOLD_COIN_MAIL_ID, 0);
    }

    public static int getIntimacyDailyLimit() {
        return getConfValueOrDefault(RelationConfEnum.RCE_FRIEND_BATTLE_INTIMACY_DAILY_LIMIT, 0);
    }

    public static int getDailyMsgNum() {
        return getConfValueOrDefault(RelationConfEnum.RCE_DAILY_MSG_NUM, 0);
    }

    public static int getDailyMsgIntimacy() {
        return getConfValueOrDefault(RelationConfEnum.RCE_DAILY_MSG_INTIMACY, 0);
    }

    public static int getDefaultBattleIntimacy() {
        return getConfValueOrDefault(RelationConfEnum.RCE_INTIMACY_VALUE_DEFAULT_BATTLE, 0);
    }

    @Override
    public void afterInsertAttrRelation(DBRelation attr) {
        super.afterInsertAttrRelation(attr);
        //        TcaplusDbWrapper.PlayerOnlineTable onlineTable = PlayerOnlineDao.getPlayerOnlineTable(attr.getUid());
        //        if (onlineTable != null) {
        //            setAttrPlayerOnline(attr, onlineTable);
        //        }
        //        TcaplusDb.PlayerPublic publicTable = PlayerPublicDao.getPlayerPublicFromTcaplus(attr.getUid(),
        //                PlayerPublicDao.PlayerPublicAttrKey.PublicGameSettings);
        //        if (publicTable != null) {
        //            setAttrPlayerStealth(attr, publicTable);
        //        }
        //        setAttrPlayerName(attr);

        // 尝试恢复历史亲密度
        long historyIntimacy = getHistoryIntimacy(attr.getUid(), attr.getAddTime());
        if (historyIntimacy > 0) {
            attr.getHotData().setIntimacy(historyIntimacy);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player {} add friend {}, init intimacy use history data {}",
                        player.getUid(), attr.getUid(), attr.getHotData().getIntimacy());
            }
        }

        var fields = player.getPlayerStarPMgr().needSyncFriendPublicInfoStarPPart() ?
                ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFieldsAll :
                ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFields;

        Map<Long, PlayerPublicInfo.Builder> publicInfoMap = SimpleDataManager.batchGetPlayerPublic(player,
                Collections.singletonList(attr.getUid()), fields,
                true, PlayerPublicSourceFriendNtf);
        publicInfoMap.forEach((uid, data) -> {
            data.setLogoutTimeMs(data.getLastKeepAliveTime());
            setAttrPlayerOnline(attr, data);
            setAttrPlayerStealth(attr, data);
            attr.getNameData().setName(data.getName());
            attr.getHotData().setCreatorId(data.getCreatorId());

            DBRelation dbData = dbRelationInfo.getModRelationInfo().get(uid);
            if (dbData != null) {
                dbData.setReturning(data.getReturning());
                dbData.setReturnExpiredSec(data.getReturnExpiredSec());
                dbData.setBirthdayMonthDay(data.getBirthdayMonthDay());
                dbData.setBirthdayVisibleRange(data.getBirthdayVisibleRange());
                dbData.getHotData().setHideProfileToFriend(data.getHideProfileToFriend());
            }
            DBRelation platData = platFriendInfo.getPlatFriendInfo().get(uid);
            if (platData != null) {
                platData.setReturning(data.getReturning());
                platData.setReturnExpiredSec(data.getReturnExpiredSec());
                platData.setBirthdayMonthDay(data.getBirthdayMonthDay());
                platData.setBirthdayVisibleRange(data.getBirthdayVisibleRange());
                platData.getHotData().setHideProfileToFriend(data.getHideProfileToFriend());
            }
        });
        player.getPlayerEventManager().dispatch(
            new AddGameFriendEvent(player.getConditionMgr()).setUid(attr.getUid())
        );
        syncFriendPlayerInfo(publicInfoMap);
    }

    /**
     * @Description: 处理申请添加好友cs. 不放在RelationManager里.
     * @Author: digoldzhang
     * @Date: 2021/8/14
     */
    public void applyAddRelation(long targetUid, int reason, int subReason, boolean isExactSearch, boolean isFromPlat,
                                 String rcmdInfo, String abTestInfo, int exposePos) {
        if (platFriendInfo.isPlatFriend(targetUid)) {
            // 平台好友，直接加为游戏好友，不需要玩家同意
            translatePlatFriendToGameFriend(targetUid);
            return;
        }

        addRelationCheck(targetUid);

        // 对方也申请了，直接同意
        if (dbRelationInfo.getReceiveApplyInfo(targetUid) != null) {
            agreeRelationProcess(targetUid);
        }

        RelationMsgExtraInfo.Builder extraInfo = RelationMsgExtraInfo.newBuilder()
                .setReason(reason)
                .setSubReason(subReason);
        // 这里没再去取对方的设置, 对方加载出来后根据设置清掉(见afterLoad)
        if (!player.isPersonalizedRecommendationDisabled()) {
            if (isFromPlat) {
                extraInfo.setAddMotivationEnumVal(FriendAddMotivationType.FAMT_ADD_FROM_PLATFORM_VALUE);
            } else if (isExactSearch) {
                extraInfo.setAddMotivationEnumVal(FriendAddMotivationType.FAMT_EXACT_SEARCH_VALUE);
            } else if (reason == 17 || reason == 18) {
                // UGC结算
                extraInfo.setAddMotivationEnumVal(FriendAddMotivationType.FAMT_UGC_BATTLE_VALUE);
            } else if (reason == 22) {
                // SP模式
                extraInfo.setAddMotivationEnumVal(FriendAddMotivationType.FAMT_STARP_MODE_VALUE);
            } else {
                long sameClubId = 0;
                if (PropertyFileReader.getRealTimeBooleanItem("friend_recommend_same_club_spec", true) &&
                        ((sameClubId = player.getClubMgr().findSameClubId(targetUid)) != 0)) {
                    LOGGER.info("{} and targetUid:{} are both in club:{}", player.getUid(), targetUid, sameClubId);
                    extraInfo.setAddMotivationEnumVal(FriendAddMotivationType.FAMT_SAME_CLUB_VALUE);
                } else {
                    RecommendAddFriendReasonDesc recommendAddDesc = getRecommendAddDesc(targetUid, reason, subReason);
                    extraInfo.setAddMotivationEnumVal(recommendAddDesc.getReasonId());
                    extraInfo.setRecommendAddDesc(recommendAddDesc);
                }
            }
        } else {
            // 关闭个性化推荐并且加好友非高频, 记录个日志好查
            LOGGER.info("player:{}|{} disable personalized recomm, apply {} no reason",
                    player.getUid(), player.getOpenId(), targetUid);
        }

        if (relationApply(targetUid, RelationApplyType.RAT_Add, extraInfo.build())) {
            addRelationAfter(targetUid);
            TlogFlowMgr.sendFriendFLow(player, FriendOpType.FOT_SendApp_VALUE, targetUid, reason, subReason, rcmdInfo,
                    abTestInfo, exposePos);
        }
    }

    @NotNull
    private RecommendAddFriendReasonDesc getRecommendAddDesc(long targetUid, int reason, int subReason) {
        var recommendAddDesc = RecommendAddFriendReasonDesc.newBuilder();
        var algoRound = player.getFriendRecommendManager().getCurrentAgloRoundInfo();
        var candidate = (algoRound != null &&
                Framework.currentTimeMillis() - algoRound.getSessionStartTime() < DateUtils.ONE_MINUTE_MILLIS * 2)
                ? algoRound.getCandidate(targetUid) : null;
        if (algoRound != null && candidate != null) {
            recommendAddDesc.setReasonId(FriendAddMotivationType.FAMT_ADD_FROM_RECOMMEND_LIST_VALUE);
            recommendAddDesc.setReasonExtra(candidate.getReasonExtra());
            recommendAddDesc.setRecommendReason(candidate.getReason());
            recommendAddDesc.setRecommendReasonText(candidate.getReasonText());
            if (algoRound.getAlgoInfo() != null) {
                recommendAddDesc.setAlgoInfo(algoRound.getAlgoInfo());
            }
        } else if (player.getFriendRecommendManager().isAlgorithmEnabled()) {
            try {
                var req = RpcGetRecommendAddFriendReasonReq.newBuilder().setUid(player.getUid())
                        .setOpenId(player.getOpenId()).setDstUid(targetUid)
                        .setModule(reason > 0 ? reason : INVALID_RELATION_APPLY_MODULE);
                if (reason == 13 && subReason <= 0) {
                    // 大厅场景 传递场景mapId
                    req.setSubModule(player.getUserAttr().getPlayerPublicSceneData().getLobbyInfo().getMapId());
                } else {
                    req.setSubModule(subReason > 0 ? subReason : INVALID_RELATION_APPLY_MODULE);
                }
                var rpcRet = UgcplatService.get().rpcGetRecommendAddFriendReason(req);
                if (!rpcRet.isOK()) {
                    LOGGER.error("rpc fail: {}", rpcRet.getRet());
                } else if (rpcRet.getData().hasRecommendInfo()) {
                    var algoRecommedAddInfo = rpcRet.getData().getRecommendInfo();
                    recommendAddDesc.setReasonId(algoRecommedAddInfo.getReasonId());
                    recommendAddDesc.setAlgoInfo(algoRecommedAddInfo.getAlgoInfo());
                    recommendAddDesc.setReasonExtra(algoRecommedAddInfo.getReasonExtra());
                    recommendAddDesc.setRecommendReasonText(algoRecommedAddInfo.getReasonText());
                }
            } catch (RpcException | NKTimeoutException e) {
                LOGGER.error("GetRecommendAddFriendReason exception, targetUid:{}", targetUid, e);
                recommendAddDesc.clear();
            }
        }
        return recommendAddDesc.build();
    }

    @Override
    protected void addRelationCheck(long targetUid) {
        super.addRelationCheck(targetUid);
        // 黑名单检查
        if (player.getBlackManager().isBlack(targetUid)) {
            NKErrorCode.FriendManagerIllegalOpWhenBlocked
                    .throwError("IllegalOpWhenBlocked,{},{}", player.getUid(), targetUid);
        }
        if (player.getBlackManager().isBeBlack(targetUid)) {
            LOGGER.info("IllegalOpWhenBeBlocked,{},{}", player.getUid(), targetUid);
            return;
        }

        // 检查目标是否已设置拒绝好友申请
        PlayerPublic targetPlayerPublic = PlayerPublicDao.getTcaplusPlayerPublic(targetUid,
                PlayerPublicDao.PlayerPublicAttrKey.PublicGameSettings);
        if (targetPlayerPublic == null) {
            NKErrorCode.UserSimpleNotFound.throwError("CantFindPlayerPublic, {}", targetUid);
        } else {
            if (targetPlayerPublic.getPublicGameSettings().getHideFriendReq()) {
                NKErrorCode.FriendManagerUserDenyFriendReq.throwError("FriendManagerPlayerDenyFriendReq");
            }
        }
    }

    private void addRelationAfter(long targetUid) {
        // 记录已申请好友记录
        DBApplyRelation dbRelation = new DBApplyRelation()
                .setUid(targetUid)
                .setApplyTime(Framework.currentTimeMillis())
                .setApplyType(RelationApplyType.RAT_Add);
        dbRelationInfo.getSendApplyInfo().put(targetUid, dbRelation);

        updateSendApplyInfoToRedis();

        TlogFlowMgr.sendSecSNSFlow(player, targetUid, 1);
    }

    @Override
    public void agreeRelationProcess(long targetUid) {
        // 先获取到apply信息，agreeRelationAfter里会删掉
        DBApplyRelation apply = dbRelationInfo.getReceiveApplyInfo(targetUid);

        agreeRelationAfter(targetUid);

        try {
            interactionSendRelationAgree(targetUid, true);
        } catch (Exception e) {
            // 其余错误的交给修复逻辑
            LOGGER.error("notifyTargetHandleAgreeMsg error", e);
        }
        // 发送一条私聊消息给添加者
        try {
            CurrentExecutorUtil.runJob(() -> {
                ChatSystemMsgConf msgConf = ChatSystemMsgConfData.getInstance().get(ChatSystemMsgConfEnum.CSMCE_FRIEND_ADD_MSG);
                String text = msgConf != null ? msgConf.getStringFormat() : "我们已经成为好友啦，快来和我一起玩耍吧！";
                ChatGroupKey key = player.getPlayerChatManager().genP2PChatGroupKey(ChatType.CT_Private, targetUid);
                if (apply != null) {
                    KVArray helloKv = KVArray.newBuilder()
                            .addArray(KVEntry.newBuilder()
                                    .setKey("recommendReason").setValue(Integer.toString(apply.getRecommendReason())).build())
                            .addArray(KVEntry.newBuilder()
                                    .setKey("recommendReasonText").setValue(apply.getRecommendReasonText()).build())
                            .addArray(KVEntry.newBuilder()
                                    .setKey("addMotivation").setValue(Integer.toString(apply.getAddMotivation())).build())
                            .addArray(KVEntry.newBuilder()
                                    .setKey("motivationParams").setValue(apply.getMotivationParams()).build())
                            .build();
                    ChatMsgData helloMsg = ChatMsgData.newBuilder().setMsgType(ChatMsgType.CMT_AddPlayerHello)
                            .setFromId(player.getUid())
                            .setKvArray(helloKv)
                            .build();
                    try {
                        player.getPlayerChatManager().sendMsg(key, helloMsg, DateUtils.currentTimeMillis(), true, true);
                    } catch (Exception e) {
                        LOGGER.error("player {} add friend {} send hello msg error:", player.getUid(), targetUid, e);
                    }
                } else {
                    LOGGER.error("player {} add friend {} get apply null", player.getUid(), targetUid);
                }
                ChatMsgData msg = ChatMsgData.newBuilder().setMsgType(ChatMsgType.CMT_Text)
                        .setFromId(player.getUid())
                        .setText(text).build();
                try {
                    // 不做安全检查
                    player.getPlayerChatManager().sendMsg(key, msg, DateUtils.currentTimeMillis(), false, true);
                } catch (Exception e) {
                    LOGGER.error("player {} add friend {} send msg error:", player.getUid(), targetUid, e);
                }
                return null;
            }, "friendSendChatMsg", false);
        } catch (NKCheckedException e) {
            LOGGER.error("reload runJob reloadUpdateMetaAiHost catch", e);
        }

        TlogFlowMgr.sendFriendFLow(player, FriendOpType.FOT_AgreeApp_VALUE, targetUid, 0, 0, "", "", 0);

    }

    public void addDailyMsgIntimacy(long uid) {
        DailyIntimacyData dailyIntimacy = player.getUserAttr().getRelationMapInfo().getDailyIntimacy(uid);
        if (dailyIntimacy == null) {
            dailyIntimacy = new DailyIntimacyData();
            dailyIntimacy.setUid(uid);
            player.getUserAttr().getRelationMapInfo().putDailyIntimacy(uid, dailyIntimacy);
        }
        dailyIntimacy.addTodayMsgNum(1);
        if (dailyIntimacy.getTodayMsgNum() == getDailyMsgNum()) {
            long addIntimacy = increaseDailyLimitIntimacy(uid, getDailyMsgIntimacy(), DateUtils.currentTimeMillis(),
                    FriendIntimacyChangeReason.RUR_Chat);
            if (addIntimacy > 0) {
                sendIntimacyChangeNtf(List.of(uid), (int) addIntimacy, FriendIntimacyChangeReason.RUR_Chat);
            }
        }

        increaseFriendInteractStatistic(uid, FriendInteractDataType.FIDT_ChatCnt, 1);
    }

    @Override
    public NKErrorCode handleAddRelationMsg(long applicantUid, RelationMsgExtraInfo extraInfo, long time) {
        LOGGER.info("player {} receive player {} add friend application: {}", player.getUid(), applicantUid, extraInfo);
        NKErrorCode errorCode = checkAddMsgPreCondition(applicantUid);
        if (!errorCode.isOk()) {
            return errorCode;
        }
        errorCode = checkApplicantList(applicantUid);
        if (!errorCode.isOk()) {
            LOGGER.warn("Player handle add relation: check applicant list {} error: {}",
                    applicantUid, errorCode.toString());
            return errorCode;
        }

        // 插入被申请者的申请列表
        insertAttrApply(applicantUid, RelationApplyType.RAT_Add, extraInfo, time);

        // 自己也申请了，直接同意
        if (dbRelationInfo.getSendApplyInfo(applicantUid) != null) {
            agreeRelationProcess(applicantUid);
        }

        return NKErrorCode.OK;
    }

    @Override
    public void agreeRelationAfter(long targetUid) {
        super.agreeRelationAfter(targetUid);
        if (dbRelationInfo.getModRelationInfo().size() >= getMaxRelationNum(player.getUid())) {
            Monitor.getInstance().add.total(MonitorId.attr_relation_friend_limit_count, 1);
        }
        new PlayerAddFriendCountEvent(player).dispatch();
        player.getPlayerEventManager().dispatch(new ReturningFriendEvt(player.getConditionMgr())
                .setCount(getReturningFriendCount()));
    }

    @Override
    public boolean handleAgreeRelationMsg(long uid, long time) {
        super.handleAgreeRelationMsg(uid, time);
        new PlayerAddFriendCountEvent(player).dispatch();
        player.getPlayerEventManager().dispatch(new ReturningFriendEvt(player.getConditionMgr())
                .setCount(getReturningFriendCount()));
        return true;
    }

    @Override
    public void clearInvalidInfo() {
        super.clearInvalidInfo();
        //        clearInvalidRecentPlayInfo();
    }

    public void translatePlatFriendToGameFriend(long uid) {
        if (uid == player.getUid()) {
            return;
        }
        if (isRelation(uid)) {
            return;
        }
        if (isPlatFriend(uid)) {
            // 检查我的好友数量是否超过上限
            if (dbRelationInfo.getModRelationInfoSize() >= getMaxRelationNum(player.getUid())) {
                LOGGER.error("friend num:" + dbRelationInfo.getModRelationInfoSize() + ", conf num" + getMaxRelationNum(
                        player.getUid()));
                NKErrorCode.FriendManagerSelfFriendNumOverLimit.throwError(
                        "friend num:" + dbRelationInfo.getModRelationInfoSize() + ", conf num" + getMaxRelationNum(
                                player.getUid()));
            }
            // 检查对方好友数量是否超过上限
            int targetFriendCount = getRelationCount(dbRelationInfo.getRelationType(), uid);
            if (targetFriendCount >= getMaxRelationNum(uid)) {
                LOGGER.error("the num of applicant's friend over limit, uid:{}, targetFriendCount:{}", uid,
                        targetFriendCount);
                NKErrorCode.FriendManagerApplicantFriendNumOverLimit.throwError(
                        "the num of applicant's friend over limit");
            }
            insertRelationWithoutNtf(uid);
            new PlayerAddFriendCountEvent(player).dispatch();
            player.getPlayerEventManager().dispatch(new ReturningFriendEvt(player.getConditionMgr())
                    .setCount(getReturningFriendCount()));

            TlogFlowMgr.sendFriendFLow(player, FriendOpType.FOT_PlatTransFriend_VALUE, uid, 0, 0, "", "", 0);
            try {
                interactionSendRelationApply(uid, RelationApplyType.RAT_TransferGameFriend, null);
                doStickInRelationTranslate(uid);
            } catch (Exception e) {
                LOGGER.error("interactionSendRelationApply error", e);
            }
        }
    }

    private void doStickInRelationTranslate(long uid) {
        //如果玩家在平台好友里置顶，则需要在这里也要加上置顶信息
        int platScene = StickFriendScene.SFS_PlatFriend_VALUE;
        ArrayAttrObj<Long> platFriends = player.getUserAttr().getStickFriends().get(platScene).getFriends();
        if (platFriends.contains(uid)) {
            addAnotherRelationStick(uid, StickFriendScene.SFS_FriendList);
            LOGGER.warn("addAnotherRelationStick success, player:{}, targetUid:{}", player.getUid(), uid);
        }
    }

    private void insertRelationWithoutNtf(long targetUid) {
        // 平台好友转成游戏好友，不需要通知
        TdmRelationInfo tdmInfo = new TdmRelationInfo(player.getOpenId(), player.getPlatId());
        DBApplyRelation apply = dbRelationInfo.getReceiveApplyInfo(targetUid);
        if (apply != null) {
            tdmInfo.setReason(apply.getReason());
            tdmInfo.setSubReason(apply.getSubReason());
        }
        RelationTable.Builder record = RelationTable.newBuilder()
                .setType(dbRelationInfo.getRelationType().getNumber())
                .setUid1(player.getUid())
                .setUid2(targetUid);

        // 恢复亲密度
        fillRelationRecoverData(targetUid, record);

        RelationTableDao.addRelation(record, tdmInfo);

        insertAttrRelationImpl(targetUid, System.currentTimeMillis(), false);
    }

    @Override
    public boolean handleTranslateToGameFriendMsg(long uid) {
        LOGGER.debug("my friend:{} TranslateToGameFriend", uid);
        if (uid == 0) {
            return false;
        }
        insertAttrRelationImpl(uid, DateUtils.currentTimeMillis(), false);
        new PlayerAddFriendCountEvent(player).dispatch();
        player.getPlayerEventManager().dispatch(new ReturningFriendEvt(player.getConditionMgr())
                .setCount(getReturningFriendCount()));
        TlogFlowMgr.sendFriendFLow(player, FriendOpType.FOT_PlatTransFriend_VALUE, uid, 0, 0, "", "", 0);
        return true;
    }

    public SsGamesvr.RpcGetPlayerHotDataRes.Builder handleRpcGetPlayerHotDataReq(
            SsGamesvr.RpcGetPlayerHotDataReq reqMsg) {
        SsGamesvr.RpcGetPlayerHotDataRes.Builder res = SsGamesvr.RpcGetPlayerHotDataRes.newBuilder();
        res.setStatus(player.getUserAttr().getRoomInfo().getStatus()); // @Deprecated
        LOGGER.debug("handleRpcGetPlayerHotDataReq, uid:{}, status:{}", player.getUid(),
                player.getUserAttr().getRoomInfo().getStatus()); // @Deprecated
        return res;
    }

    /**
     * 注意：切换到snssvr后此函数不再生效
     */
    public void notifyAllOnlineFriendPlayerInfoAsync() {
        if (PropertyFileReader.getRealTimeBooleanItem("snssvr_enable", true)) {
            // 切换到上报profilesvr
            return;
        }
        if (!PropertyFileReader.getRealTimeBooleanItem("enable_friend_hot_data", true)) {
            LOGGER.debug("enable_friend_hot_data is disable");
            return;
        }
        if (changeFields.isEmpty()) {
            return;
        }
        long interval = PropertyFileReader.getRealTimeIntItem("ntf_online_friend_change_info_interval", 3) * 1000L;
        if (lastNtfChangeInfoTs + interval >= Framework.currentTimeMillis()) {
            return;
        }
        lastNtfChangeInfoTs = Framework.currentTimeMillis();
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                notifyAllOnlineFriendPlayerStatus();
                return null;
            }, "notifyAllOnlineFriendPlayerInfoAsync", true);
        } catch (NKCheckedException ex) {
            LOGGER.error("notifyAllOnlineFriendPlayerInfoAsync call fail", ex);
        }
    }

    private void syncFriendPlayerInfoAsync(Map<Long, PlayerPublicInfo.Builder> publicInfoMap) {
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                syncFriendPlayerInfo(publicInfoMap);
                return null;
            }, "syncFriendPlayerInfoAsync", true);
        } catch (NKCheckedException ex) {
            LOGGER.error("syncFriendPlayerInfoAsync call fail", ex);
        }
    }

    private void syncFriendPlayerInfo(Map<Long, PlayerPublicInfo.Builder> publicInfoMap) {
        RelationStateNtf.Builder builder = RelationStateNtf.newBuilder();
        int batchNum = PropertyFileReader.getRealTimeIntItem("login_sync_friend_batch_size", 50);
        publicInfoMap.forEach((uid, info) -> {
            boolean isGameFriend = dbRelationInfo.getModRelationInfo().containsKey(uid);
            boolean isPlatFriend = platFriendInfo.getPlatFriendInfo().containsKey(uid);
            if (isGameFriend) {
                builder.addInfo(newRelationStateInfoBuilderFromPublicInfo(
                        info, RelationTypeEnum.RTE_FRIEND_RELATION, isPlatFriend));
            } else if (isPlatFriend) {
                builder.addInfo(newRelationStateInfoBuilderFromPublicInfo(
                        info, RelationTypeEnum.RTE_PLAT_FRIEND_RELATION, false));
            }
            if (builder.getInfoList().size() == batchNum) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("batch sync friend info: size:{} info:{}", batchNum, builder.toString());
                }
                player.sendNtfMsg(MsgTypes.MSG_TYPE_RELATIONSTATENTF, builder);
                builder.clearInfo();
            }
        });
        if (!builder.getInfoList().isEmpty()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("batch sync friend info: size:{} info:{}",
                        builder.getInfoList().size(), builder.toString());
            }
        }
        builder.setLastBatch(true);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_RELATIONSTATENTF, builder);
    }

    private RelationStateInfo.Builder newRelationStateInfoBuilderFromPublicInfo(
            PlayerPublicInfo.Builder playerPublicInfo,
            RelationTypeEnum relationType,
            boolean isDualFriend) {
        RelationStateInfo.Builder info = RelationStateInfo.newBuilder()
                .setType(relationType)
                .setUid(playerPublicInfo.getUid())
                .setIsOnline(PlayerStateMgr.isOnline(playerPublicInfo.getPlayerState()))
                .setHidePlayerStatus(playerPublicInfo.getHidePlayerStatus())
                .setIsLoginSuc(false)
                .setPlayerPublicInfo(playerPublicInfo)
                .setIsDualFriend(isDualFriend)
                .setIntimateOnlineNotice(getIntimateOnlineNoticeByPublicInfo(playerPublicInfo));
        if (playerPublicInfo.getHidePlayerStatus()) {
            info.setLogoutTime(playerPublicInfo.getHidePlayerStatusTime());
        } else if (PlayerStateMgr.isOnline(playerPublicInfo.getPlayerState())) {
            info.setLogoutTime(Framework.currentTimeMillis());
        }
        return info;
    }

    /**
     * 通知所有好友我的在线状态
     * 注意：切换到snssvr后此函数不再生效
     */
    private void notifyAllOnlineFriendPlayerStatus() {
        notifyAllOnlineFriendPlayerStatus(false);
    }

    /**
     * 通知所有好友我的在线状态 (仅用于好友登入加载完以后通知好友已经稳健在线)  每次登入只能发送一次
     *
     */
    public void notifyAllOnlineFriendPlayerStatus(boolean finishedLoad) {
        if (changeFields.isEmpty() && !finishedLoad) {
            LOGGER.info("changeFields finishedLoad is {} {}",changeFields,finishedLoad);
            return;
        }
        PlayerPublicInfo.Builder changeInfo = collectChangeField();
        Map<Integer, Map<Long, PlayerSpecialData.Builder>> svrIdMap = new HashMap<>();
        for (DBRelation info : dbRelationInfo.getModRelationInfo().values()) {
            int svrId = info.getHotData().getSvrId();
            if (PlayerStateMgr.isOnline(info.getHotData().getPlayerStatus())) {
                var uidMap = svrIdMap.computeIfAbsent(svrId, v -> new HashMap<>());
                PlayerSpecialData.Builder speData = uidMap.computeIfAbsent(info.getUid(),
                        v -> PlayerSpecialData.newBuilder().setTargetUid(info.getUid()));
                speData.setRelationType(speData.getRelationType() + 1);

            }
        }
        for (DBRelation info : platFriendInfo.getAllPlatFriendData()) {
            int svrId = info.getHotData().getSvrId();
            if (PlayerStateMgr.isOnline(info.getHotData().getPlayerStatus())) {
                var uidMap = svrIdMap.computeIfAbsent(svrId, v -> new HashMap<>());
                PlayerSpecialData.Builder speData = uidMap.computeIfAbsent(info.getUid(),
                        v -> PlayerSpecialData.newBuilder().setTargetUid(info.getUid()));
                speData.setRelationType(speData.getRelationType() + 2);

            }
        }
        svrIdMap.forEach((svrId, uidMap) -> {
            notifyFriendPlayerOnlineStatus(svrId, uidMap, changeInfo, finishedLoad);
        });
        if (finishedLoad) {
            //关闭稳健登入后通知好友
            player.getUserAttr().getBasicInfo().setLimitNotifyTimes(false);
            LOGGER.info("limitNotifyTimes is change");
        }
        needNtfLogin = false;
    }

    /**
     * 注意：切换到snssvr后此函数不再生效
     */
    private void notifyFriendPlayerOnlineStatus(int svrId, Map<Long, PlayerSpecialData.Builder> targetMap,
                                                PlayerPublicInfo.Builder changeInfo, boolean finishedLoad) {
        if (svrId == 0 || targetMap.size() == 0) {
            return;
        }
        try {
            RpcNotifyPlayerOnlineStatusReq.Builder ssReq = SsGamesvr.RpcNotifyPlayerOnlineStatusReq.newBuilder()
                    .setServerId(svrId).setReqUid(player.getUid())
                    .setOriginSvrId(Framework.getInstance().getServerId())
                    .setStatus(player.getPlayerStateMgr().getPlayerState())
                    .setHidePlayerStatus(player.getPlayerStateMgr().isStealth())
                    .setHidePlayerStatusTime(player.getPlayerStateMgr().getStealthTime())
                    .setChangeInfo(changeInfo)
                    .setIsLoginSuc(needNtfLogin)
                    .setIntimateOnlineNotice(getIntimateOnlineNotice())
                    .setFinishedLoad(finishedLoad);
            for (var builder : targetMap.values()) {
                ssReq.putTargetFriendData(builder.getTargetUid(), builder.build());
            }
            fillIntimateOnlineNoticeDressInfo(targetMap.keySet(), ssReq);
            GameService.get().rpcNotifyPlayerOnlineStatus(ssReq);
        } catch (RpcException e) {
            LOGGER.error("notifyFriendPlayerLogin{} error,e: {}", svrId, e.getEnumErrCode());
        }
    }

    private void fillIntimateOnlineNoticeDressInfo(Set<Long> targetUidList,
                                                   RpcNotifyPlayerOnlineStatusReq.Builder ssReq) {
        if (needNtfLogin && ssReq.getIntimateOnlineNotice().getNumber() == FeatureOpenStatus.FOS_Open_VALUE
                && player.getUserAttr().getRelationMapInfo().getIntimateNoticeAttr().getNoticeDressItemSize() > 0) {
            for (long uid : targetUidList) {
                if (player.getIntimateManager().isRelation(uid)) {
                    var noticeItemData = PlayerIntimateOnlineNoticeItemData.newBuilder().setUid(uid);
                    for (IntimateRelationOnlineNoticeDressItemAttr dressItemAttr : player.getUserAttr()
                            .getRelationMapInfo().getIntimateNoticeAttr().getNoticeDressItem().values()) {
                        var singleDressItem = dressItemAttr.getSingleDressItem(uid);
                        int itemId = 0;
                        if (singleDressItem != null && singleDressItem.getItemId() != 0) {
                            itemId = singleDressItem.getItemId();
                        } else {
                            itemId = dressItemAttr.getAllDressItem();
                        }
                        if (itemId <= 0) {
                            continue;
                        }
                        // 判断是否拥有这个道具
                        if (player.getItemManager().GetItemNumByItemId(itemId) <= 0) {
                            LOGGER.debug("not has this item,uid:{},itemId:{}", player.getUid(), itemId);
                            continue;
                        }
                        ResBackpackItem.Item_BackpackItem conf = BackpackItem.getInstance().get(itemId);
                        if (conf == null) {
                            LOGGER.debug("ResBackpackItem not found, uid:{},itemId:{}", player.getUid(), itemId);
                            continue;
                        }
                        noticeItemData.addItems(
                                PlayerDressItemInfo.newBuilder().setItemId(itemId).setDressUpType(conf.getType()));
                    }
                    ssReq.putIntimateDressItems(uid, noticeItemData.build());
                }
            }
        }
    }

    public void refreshIntimateOnlineNoticeDressItem() {
        int itemId = PropertyFileReader.getRealTimeIntItem("intimate_online_notice_default_itemid", 890001);
        for (IntimateRelationOnlineNoticeDressItemAttr dressItemAttr : player.getUserAttr()
                .getRelationMapInfo().getIntimateNoticeAttr().getNoticeDressItem().values()) {
            if (dressItemAttr.getAllDressItem() != 0) {
                // 判断是否拥有这个道具
                if (player.getItemManager().GetItemNumByItemId(dressItemAttr.getAllDressItem()) <= 0) {
                    dressItemAttr.setAllDressItem(itemId);
                }
            }
            for (var singleDressItem : dressItemAttr.getSingleDressItem().values()) {
                // 判断是否拥有这个道具
                if (player.getItemManager().GetItemNumByItemId(singleDressItem.getItemId()) <= 0) {
                    singleDressItem.setItemId(itemId);
                }
            }
        }
    }

    // 服务器向客户端同步好友信息中的sp部分
    public void syncStarPPartPublicInfo() {
        try {
            updateFriendData(ProfilePlayerPublicInfoFieldConst.ClientPublicInfoFieldsStarPPart);
        } catch (Exception e) {
            LOGGER.error("syncStarPPartPublicInfo error:{}", e.toString());
        }
    }

    public void addChangeField(PlayerPublicInfoField field) {
        changeFields.add(field);
        if (PropertyFileReader.getRealTimeBooleanItem("snssvr_enable", true)) {
            ProfileGameSvrPlayerMgr.getInstance().addChangeField(player.getUid(), field);
        }
    }

    private PlayerPublicInfo.Builder collectChangeField() {
        Collection<PlayerPublicInfoField> fields = new HashSet<>(changeFields);
        changeFields.clear();

        PlayerPublicInfo.Builder info = com.tencent.wea.simpleData.PlayerPublic.GetPlayerPublicInfoByFields(
                player.getUserAttr(), fields);

        return info;
    }

    @Override
    public void removeRelationAfter(long targetUid) {
        long intimacy = getFriendIntimacy(targetUid);
        DBRelation intimateRelation = player.getIntimateManager().dbRelationInfo.getModRelationInfo(targetUid);
        int intimateId = (intimateRelation == null ? 0 : intimateRelation.getIntimateId());
        super.removeRelationAfter(targetUid);
        // 删除好友同时删除亲密关系
        player.getIntimateManager().directRemoveIntimateRelation(targetUid, DateUtils.currentTimeMillis());
        // 删除好友同时删除亲密关系申请和亲密度上限
        player.getIntimateManager().removeAttrRelationApply(List.of(targetUid));
        /* M10新增功能，一定时间加回好友可以恢复亲密度，移除好友不要清理每日亲密度数据，否则会通过移除再加好友刷数据 */
        player.getUserAttr().getRelationMapInfo().removeRecentIntimacy(targetUid);
        TlogFlowMgr.sendIntimacyFLow(player, targetUid, intimacy, 0, 0, 0, true);
        // 通知营地
        PlayerReportUtil.reportPlayerIntimacy(IntimateRelationFlowType.IRFT_Change_VALUE, player, targetUid, 0,
                intimateId);
        // 删除好友排行榜
        player.getRankManager().onFriendRemoved(targetUid);

        RemovedFriendHistoryDao.saveRemoveFriendHistory(player.getUid(), targetUid, intimacy);
        TlogFlowMgr.sendRemoveFriendIntimacyFlow(player, targetUid, 0, intimacy);

        player.getTaskManager().onFriendRemoved(targetUid);

        player.getPlayerEventManager().dispatch(
                new DeleteGameFriendEvent(player.getConditionMgr()).setUid(targetUid)
        );
    }

    @Override
    public NKErrorCode handleRemoveRelationMsg(long uid, long time) {
        long intimacy = getFriendIntimacy(uid);
        DBRelation intimateRelation = player.getIntimateManager().dbRelationInfo.getModRelationInfo(uid);
        int intimateId = (intimateRelation == null ? 0 : intimateRelation.getIntimateId());
        super.handleRemoveRelationMsg(uid, time);
        // 删除好友同时删除亲密关系
        player.getIntimateManager().directRemoveIntimateRelation(uid, time);
        TlogFlowMgr.sendIntimacyFLow(player, uid, intimacy, 0, 0, 0, true);
        // 删除好友同时删除亲密关系申请和亲密度上限
        player.getIntimateManager().removeAttrRelationApply(List.of(uid));
        /* M10新功能移除好友7天内加回可以恢复亲密度，此处不要清理每日亲密度，避免刷亲密度 */
        player.getUserAttr().getRelationMapInfo().removeRecentIntimacy(uid);
        // 通知营地
        PlayerReportUtil.reportPlayerIntimacy(IntimateRelationFlowType.IRFT_Change_VALUE, player, uid, 0, intimateId);
        // 删除好友排行榜
        player.getRankManager().onFriendRemoved(uid);
        // 删除好友置顶
        removeStickFriendAllScene(uid);

        player.getTaskManager().onFriendRemoved(uid);
        // 删除对农场好友的置顶/拉黑标记
        player.getPlayerFarmMgr().removeFarmTagFriend(uid);

        return NKErrorCode.OK;
    }

    /**
     * @Description: 更新好友在线状态
     * @Author: digoldzhang
     * @Date: 2021/11/2
     */
    private Map<Long, PlayerOnlineTable> updateFriendOnlineStatus() {
        boolean enableFriendOnlineStatus = PropertyFileReader.getRealTimeBooleanItem("enable_friend_online_status",
                true);
        if (!enableFriendOnlineStatus) {
            LOGGER.debug("friend online status is disable");
            return null;
        }
        LOGGER.debug("updateFriendOnlineStatus, uid:{}", player.getUid());
        Set<Long> uidSet = new HashSet<>();
        uidSet.addAll(platFriendInfo.getPlatFriendInfo().keySet());
        uidSet.addAll(dbRelationInfo.getModRelationInfo().keySet());
        //        uidSet.addAll(dbRelationInfo.getApplyInfo().keySet());
        Map<Long, TcaplusDbWrapper.PlayerOnlineTable> svrIdMap = PlayerOnlineDao.batchGetPlayerSvrIdFromUid(uidSet);
        if (svrIdMap == null || svrIdMap.size() == 0) {
            LOGGER.info("updateFriendOnlineStatus, svrIdMap is null, uid:{}", player.getUid());
            return null;
        }
        LOGGER.debug("updateFriendOnlineStatus, uid:{}, {}", player.getUid(), svrIdMap.size());
        Monitor.getInstance().set.total(MonitorId.attr_relation_friend_online_status_update_count, svrIdMap.size());

        Map<Long, PlayerPublic> playerPublicMap = PlayerPublicDao.batchGetPlayerPublicMap(uidSet,
                PlayerPublicDao.PlayerPublicAttrKey.PublicGameSettings);
        platFriendInfo.getPlatFriendInfo().values().forEach((info) -> {
            TcaplusDbWrapper.PlayerOnlineTable onlineTable = svrIdMap.get(info.getUid());
            //            LOGGER.debug("paltf riend online data:{}-{}-{}", onlineTable.getUid(), onlineTable.getSvrId(),
            //                    onlineTable.getLastKeepAliveTime());
            setAttrPlayerOnline(info, onlineTable);
            if (playerPublicMap != null && playerPublicMap.containsKey(info.getUid())) {
                setAttrPlayerStealth(info, playerPublicMap.get(info.getUid()));
            }
        });
        dbRelationInfo.getModRelationInfo().values().forEach((info) -> {
            TcaplusDbWrapper.PlayerOnlineTable onlineTable = svrIdMap.get(info.getUid());
            //            LOGGER.debug("game friend online data:{}-{}-{}", onlineTable.getUid(), onlineTable.getSvrId(),
            //                    onlineTable.getLastKeepAliveTime());
            setAttrPlayerOnline(info, onlineTable);
            if (playerPublicMap != null && playerPublicMap.containsKey(info.getUid())) {
                setAttrPlayerStealth(info, playerPublicMap.get(info.getUid()));
            }
        });
        return svrIdMap;
    }


    // 请求平台好友, 返回值中已经排除掉自己
    private Map<String, JSONObject> reqPlatFriend(int maxNumber) throws NKCheckedException {
        if (player.isRobot()) {
            //            Map<String, JSONObject> dummyFriends = PressTestOpenIdData.getInstance().getAllPressInfo();
            //            if (dummyFriends != null) {
            //                dummyFriends.remove(player.getOpenId());
            //            }
            //            return dummyFriends;
            return null;
        }
        if (!PropertyFileReader.getRealTimeBooleanItem("enable_update_plat_friend", true)) {
            LOGGER.debug("enable_update_plat_friend is disable");
            return null;
        }

        if (!ServerEngine.getInstance().isBusiness()
                && player.getUserAttr().getPlayerProfileInfo().getAccessToken().length() == 0) {
            return null;
        }
        JSONObject result = ItopManager.getInstance()
                .GetFriendList(player.getPlatId(), player.getAccountType().getNumber(), player.getOpenId(),
                        player.getUserAttr().getPlayerProfileInfo().getAccessToken());
        if (!result.has("ret") || 0 != result.getInt("ret")) {
            NKErrorCode.FriendManagerReqPlatFriendFail.throwError("req platfriend fail");
        }
        JSONArray platFriendList = null;
        if (result.has("lists")) {
            platFriendList = result.getJSONArray("lists");
        }
        if (platFriendList == null || platFriendList.length() == 0) {
            LOGGER.info("player plat friend is null, uid:{}, openId:{}", player.getUid(), player.getOpenId());
            return null;
        }
        if (maxNumber <= 0 || maxNumber >= platFriendList.length()) {
            maxNumber = platFriendList.length();
        }
        Map<String, JSONObject> platFriends = new HashMap<>(maxNumber);
        for (Object ob : platFriendList) {
            JSONObject jo = (JSONObject) ob;
            String openId = jo.getString("openid");
            if (openId != null && !openId.equals(player.getOpenId())) {
                platFriends.put(openId, jo);
                if (platFriends.size() >= maxNumber) {
                    break;
                }
            }
        }

        return platFriends;
    }

    /**
     * @Description: 更新平台好友, 仅登录流程调用
     * @Author: digoldzhang
     * @Date: 2021/11/10
     */
    private void updatePlatFriendShip(boolean todayFirstLogin, TxStopWatch stopWatch) {
        LOGGER.debug("updatePlatFriendShip, uid: {}", player.getUid());
        try {
            Monitor.getInstance().add.total(MonitorId.attr_relation_plat_friend_update, 1);
            if (!ServerEngine.getInstance().isBusiness()) {
                CacheResult<String> result = CacheUtil.UseBackUpPlatFriend.getCacheString(player.getUid());
                if (result.isOk()) {
                    if (!result.val.isEmpty()) {
                        LOGGER.info("UseBackUpPlatFriend, {}", player.getUid());
                        NKErrorCode.FriendManagerReqPlatFriendFail.throwError(
                                "req platfriend fail, UseBackUpPlatFriend");
                    }
                }
            }
            // 请求平台好友数据
            Map<String, JSONObject> platFriends = reqPlatFriend(PlatFriendInfo.getMaxRelationNum());
            if (platFriends == null) {
                return;
            }
            // openid 转换为 uid
            Map<String, List<OpenIdToUidInfo>> uidMap = new HashMap<>();
            if (stopWatch != null) {
                stopWatch.mark("updatePlatFriendShip.getFriendFromPlat");
            }
            if (platFriends.size() > 0) {
                uidMap = OpenIdToUidDao.batchGetUidListFromOpenID(platFriends.keySet());
            }
            if (stopWatch != null) {
                stopWatch.mark("updatePlatFriendShip.batchGetUidListFromOpenID");
            }
            if (uidMap.size() == 0) {
                LOGGER.info("can not get any uid from plat friend openid, uid:{}, openid:{}, openIdList:{}",
                        player.getUid(), player.getOpenId(), platFriends.keySet());
            }
            StringBuilder tlogFriendStr = null;
            if (todayFirstLogin) {
                tlogFriendStr = new StringBuilder();
            }

            PlatFriendList.Builder backupPlatFriend = PlatFriendList.newBuilder();
            for (Map.Entry<String, List<OpenIdToUidInfo>> entry : uidMap.entrySet()) {
                final String openId = entry.getKey();
                JSONObject jsonInfo = platFriends.getOrDefault(openId, null);
                if (jsonInfo == null) {
                    LOGGER.error("unexpected openid:{} from OpenIdToUidDao", openId);
                    continue;
                }
                for (OpenIdToUidInfo info : entry.getValue()) {
                    if (tlogFriendStr != null) {
                        tlogFriendStr
                                .append(openId)
                                .append(",")
                                .append(info.getPlatId())
                                .append(";");
                    }
                    long uid = info.getUid();
                    String platNickName = jsonInfo.getString("user_name");
                    if (!platFriendInfo.getPlatFriendInfo().containsKey(uid)) {
                        DBRelation dbRelation = new DBRelation().setUid(uid);
                        platFriendInfo.getPlatFriendInfo().put(uid, dbRelation);
                        platFriendNickName.put(uid, platNickName);
                    }
                    backupPlatFriend.addData(
                            PlatFriendData.newBuilder()
                                    .setUid(uid)
                                    .setOpenid(openId)
                                    .setPlatName(platNickName));
                }
            }

            new PlayerFriendNumModifyEvent(player, getAllFriendNum()).dispatch();
            if (tlogFriendStr != null) {
                TlogFlowMgr.sendSocialFriendFlow(player, tlogFriendStr.toString());
            }
            // 备份
            PlatFriendTableDao.replacePlatFriendList(player.getUid(), backupPlatFriend);
            if (stopWatch != null) {
                stopWatch.mark("updatePlatFriendShip.PlayerFriendNumModifyEvent");
            }
        } catch (Exception e) {
            LOGGER.info("upload PlatFriendShip from plat failed,uid:{}, msg:{}", player.getUid(), e.getMessage());
            Monitor.getInstance().add.fail(MonitorId.attr_relation_plat_friend_update, 1);
            // 用备份的plat uid list
            PlatFriendList list = PlatFriendTableDao.getPlatFriendList(player.getUid());
            if (list == null) {
                LOGGER.info("{}-backup plat friend is null", player.getUid());
                return;
            }
            for (PlatFriendData data : list.getDataList()) {
                if (data.getUid() == player.getUid()) {
                    continue;
                }
                if (!platFriendInfo.getPlatFriendInfo().containsKey(data.getUid())) {
                    DBRelation dbRelation = new DBRelation().setUid(data.getUid());
                    platFriendInfo.getPlatFriendInfo().put(data.getUid(), dbRelation);
                    platFriendNickName.put(data.getUid(), data.getPlatName());
                }
            }
        }
    }

    /**
     * 增加亲密度（有每日上限）
     *
     * @param friendUid 好友uid
     * @param intimacy 亲密度
     * @param reason 理由
     */
    public long increaseDailyLimitIntimacy(long friendUid, long intimacy, FriendIntimacyChangeReason reason,
                                           int subReason) {
        long realAddIntimacy = increaseDailyLimitIntimacy(friendUid, intimacy, DateUtils.currentTimeMillis(), reason,
                subReason);
        if (realAddIntimacy > 0) {
            sendIntimacyChangeNtf(List.of(friendUid), (int) realAddIntimacy, reason);
        }
        return realAddIntimacy;
    }

    private long increaseDailyLimitIntimacy(long friendUid, long intimacy, long time,
                                            FriendIntimacyChangeReason reason) {
        return increaseDailyLimitIntimacy(friendUid, intimacy, time, reason, 0);
    }

    /**
     * @param friendUid
     * @param intimacy
     * @param time
     * @param reason
     * @param subReason
     * @return 实际增加的亲密度(每日上限)
     */
    private long increaseDailyLimitIntimacy(long friendUid, long intimacy, long time,
                                            FriendIntimacyChangeReason reason, int subReason) {
        DailyIntimacyData dailyIntimacyInfo = player.getUserAttr().getRelationMapInfo().getDailyIntimacy(friendUid);
        if (dailyIntimacyInfo == null) {
            dailyIntimacyInfo = new DailyIntimacyData().setUid(friendUid);
            player.getUserAttr().getRelationMapInfo().putDailyIntimacy(friendUid, dailyIntimacyInfo);
        }
        // 如果是今天第一次加，清零
        if (DateUtils.getDayBeginTimeMs(time) > dailyIntimacyInfo.getLastIncreaseTime()) {
            dailyIntimacyInfo.setTodayIntimacy(0);
        }
        dailyIntimacyInfo.setLastIncreaseTime(time);

        LOGGER.info("increaseDailyLimitIntimacy: player {} add friend {} intimacy {}, set daily intimacy to {}",
                player.getUid(), friendUid, intimacy, dailyIntimacyInfo.getTodayIntimacy());

        long dailyNum = getIntimacyDailyLimit();
        if (dailyIntimacyInfo.getTodayIntimacy() >= dailyNum) {
            LOGGER.info("{}, {} today battle intimacy limit", player.getUid(), friendUid);
            return 0;
        }
        if (dailyIntimacyInfo.getTodayIntimacy() + intimacy > dailyNum) {
            intimacy = dailyNum - dailyIntimacyInfo.getTodayIntimacy();
        }
        dailyIntimacyInfo.addTodayIntimacy(intimacy);

        increaseFriendIntimacy(friendUid, intimacy, time, reason, subReason, false);
        return intimacy;
    }

    public void sendGoldCoin(long uid) {
        // 检查，是否游戏好友，如果不是，是否平台好友，平台好友则加为游戏好友
        if (!isFriend(uid)) {
            NKErrorCode.FriendManagerIllegalFriendRelation.throwError("not friend,{},{}", player.getUid(), uid);
        }
        translatePlatFriendToGameFriend(uid);
        // 检查，今天是否给该好友送过
        DBRelation friend = dbRelationInfo.getModRelationInfo(uid);
        if (friend == null) {
            NKErrorCode.FriendManagerIllegalFriendRelation.throwError("not friend,{},{}", player.getUid(), uid);
        }
        if (player.getUserAttr().getRelationMapInfo().getSendGoldCoinUid().contains(uid)) {
            NKErrorCode.FriendManagerTodaySendGoldCoinOverLimit.throwError("send gold coin limit");
        }
        // 检查，今天给多少好友送过
        long todayCount = player.getUserAttr().getRelationMapInfo().getSendGoldCoinUidSize();
        if (todayCount >= getSendGoldCoinDailyLimit()) {
            NKErrorCode.FriendManagerTodaySendGoldCoinNumOverLimit.throwError("send gold coin num limit");
        }
        long recved = FlashAttr.incr(uid, FlashAttr.FlashAttrKey.DAILY_RECV_GOLD_MAIL_COUNT);
        if (recved > MailIniConfData.getInstance().getMaxFriendGiftMailRecvedPerDay()) {
            NKErrorCode.FriendRecvedTooMuchGoldGiftMailToday.throwError("friend recved too much:{}/{}",
                    recved, MailIniConfData.getInstance().getMaxFriendGiftMailRecvedPerDay());
        }
        // 记录赠送时间
        player.getUserAttr().getRelationMapInfo().addSendGoldCoinUid(uid);
        // 发送邮件
        int mailTemplateId = getSendGoldCoinMailTemplateId();
        long ret = MailInteraction.sendTemplateMail(uid, player.getUid(), mailTemplateId, null,
                MailInteraction.TlogSendReason.friendSendGift,
                player.getUserAttr().getPlayerPublicProfileInfo().getNickname());
        if (ret < 0) {
            LOGGER.error("sendGoldCoin send template mail failed, uid:{}", player.getUid());
        }
        // dispatch event
        new PlayerSendGoldCoinEvent(player).dispatch();
        // 增加亲密度
        long intimacy = getSendGoldCoinIntimacy();
        long isAdd = increaseDailyLimitIntimacy(uid, intimacy, DateUtils.currentTimeMillis(),
                FriendIntimacyChangeReason.RUR_GOLD_COIN);
        if (isAdd > 0) {
            sendIntimacyChangeNtf(List.of(uid), (int) intimacy, FriendIntimacyChangeReason.RUR_GOLD_COIN);
        }

        increaseFriendInteractStatistic(uid, FriendInteractDataType.FIDT_SendGoldCoin, 1);
    }


    private void sendIntimacyChangeNtf(List<Long> uidList, int value, FriendIntimacyChangeReason changeReason) {
        sendIntimacyChangeNtf(uidList, value, false, changeReason);
    }

    private void sendIntimacyChangeNtf(List<Long> uidList, int value, boolean isBattleSettlement, FriendIntimacyChangeReason changeReason) {
        if (!uidList.isEmpty()) {
            FriendIntimacyChangeNtf.Builder ntfBuilder = FriendIntimacyChangeNtf.newBuilder()
                    .addAllUid(isBattleSettlement ? List.of(RandomUtil.randomGetFromNonEmptyList(uidList)) : uidList)
                    .setChangeReason(changeReason)
                    .setAddValue(value);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_FRIENDINTIMACYCHANGENTF, ntfBuilder);
        }
        // 兼容客户端处理
        RelationStateNtf.Builder stateNtf = RelationStateNtf.newBuilder();
        uidList.forEach(uid -> {
            RelationStateInfo.Builder info = RelationStateInfo.newBuilder();
            info.setType(RelationTypeEnum.RTE_FRIEND_RELATION)
                    .setUid(uid)
                    .setPlayerPublicInfo(PlayerPublicInfo.newBuilder().setIntimacy(getFriendIntimacy(uid)));
            stateNtf.addInfo(info);
        });
        player.sendNtfMsg(MsgTypes.MSG_TYPE_RELATIONSTATENTF, stateNtf);
    }

    //处理默契之心发放和亲密度改变通知
    private void sendIntimacyChangeNtf(List<Long> uidList, int value, boolean isBattleSettlement, FriendIntimacyChangeReason changeReason, int totalTacitIntimacy) {
        if (!uidList.isEmpty()) {
            FriendIntimacyChangeNtf.Builder ntfBuilder = FriendIntimacyChangeNtf.newBuilder()
                    .addAllUid(isBattleSettlement ? List.of(RandomUtil.randomGetFromNonEmptyList(uidList)) : uidList)
                    .setChangeReason(changeReason)
                    .setAddValue(value)
                    .setTotalTacitIntimacy(totalTacitIntimacy);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_FRIENDINTIMACYCHANGENTF, ntfBuilder);
        }
        // 兼容客户端处理
        RelationStateNtf.Builder stateNtf = RelationStateNtf.newBuilder();
        uidList.forEach(uid -> {
            RelationStateInfo.Builder info = RelationStateInfo.newBuilder();
            info.setType(RelationTypeEnum.RTE_FRIEND_RELATION)
                    .setUid(uid)
                    .setPlayerPublicInfo(PlayerPublicInfo.newBuilder().setIntimacy(getFriendIntimacy(uid)));
            stateNtf.addInfo(info);
        });
        player.sendNtfMsg(MsgTypes.MSG_TYPE_RELATIONSTATENTF, stateNtf);
    }

    public void increaseFriendIntimacy(long friendUid, long intimacy, long time, FriendIntimacyChangeReason reason)
            throws NKRuntimeException {
        increaseFriendIntimacy(friendUid, intimacy, time, reason, true);
    }

    public void increaseFriendIntimacy(long friendUid, long intimacy, long time, FriendIntimacyChangeReason reason,
                                       boolean changeNtf) throws NKRuntimeException {
        increaseFriendIntimacy(friendUid, intimacy, time, reason, 0, changeNtf);
    }

    /**
     * @Description: 同时增加玩家及其好友的亲密度
     * @Author: digoldzhang
     * @Date: 2023/5/25
     */
    public void increaseFriendIntimacy(long friendUid, long intimacy, long time, FriendIntimacyChangeReason reason,
                                       int subReason, boolean changeNtf) throws NKRuntimeException {
        if (!isFriend(friendUid)) {
            NKErrorCode.FriendManagerIllegalFriendRelation.throwError("not friend,{},{}", player.getUid(), friendUid);
        }
        translatePlatFriendToGameFriend(friendUid);
        DBRelation dbRelation = dbRelationInfo.getModRelationInfo(friendUid);
        long oldIntimacy = dbRelation.getHotData().getIntimacy();
        long newIntimacy = RelationTableDao.increaseFriendIntimacy(player.getUid(), friendUid, intimacy, reason);
        // 更新缓存的亲密度
        dbRelation.getHotData().setIntimacy(newIntimacy);

        LOGGER.info(
                "increaseFriendIntimacy: player {} add friend {} intimacy {}, old: {}, new: {}, reason: {}, subReason: {}",
                player.getUid(), friendUid, intimacy, oldIntimacy, newIntimacy, reason, subReason);

        player.getPlayerEventManager().dispatch(new IntimacyAddEvent(player.getConditionMgr()).setIntimacy(intimacy));
        player.getIntimateManager().updatePublicProfileIntimate(friendUid, false);
        // 通知对方，对局亲密度不需要通知对方
        if (reason != FriendIntimacyChangeReason.RUR_BATTLE_INTIMACY) {
            PiiIntimacyChangeParams.Builder ntf = PiiIntimacyChangeParams.newBuilder().setReqUid(player.getUid())
                    .setChangeIntimacy(intimacy).setReason(reason).setTime(time);
            PlayerInteractionData.Builder data = PlayerInteractionData.newBuilder();
            data.setInstruction(PlayerInteractionInstruction.PII_INTIMACY_CHANGE)
                    .setIntimacyChangeParams(ntf);
            PlayerInteractionInvoker.interact(friendUid, data);
        }
        TlogFlowMgr.sendIntimacyFLow(player, friendUid, intimacy, newIntimacy, reason.getNumber(), subReason, false);
        // 通知营地
        DBRelation intimateRelation = player.getIntimateManager().dbRelationInfo.getModRelationInfo(friendUid);
        if (intimateRelation != null) {
            FriendIntimacyCache.modifyFriendIntimacy(friendUid,
                    player.getIntimateManager().getFriendIntimatePlayerInfo(friendUid, intimateRelation.getIntimateId())
                            .build());
        }
        int intimateId = (intimateRelation == null ? 0 : intimateRelation.getIntimateId());
        PlayerReportUtil.reportPlayerIntimacy(IntimateRelationFlowType.IRFT_Change_VALUE, player, friendUid,
                newIntimacy, intimateId);
        // 更新好友最近亲密度变化信息
        player.getIntimateManager().afterAddFriendIntimacy(friendUid, oldIntimacy, newIntimacy, time);
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
        sendIntimacyNtf(friendUid, newIntimacy, oldIntimacy);
        if (changeNtf) {
            sendIntimacyChangeNtf(List.of(friendUid), (int) intimacy, reason);
        }
        // 更新最后亲密度变化时间
        updateRecentInteractTs(friendUid, time, reason);

        new FriendAddIntimacyEvent(player, friendUid, intimacy).dispatch();

        // 如果是组队开黑，记录一下开黑次数
        if (reason == FriendIntimacyChangeReason.RUR_BATTLE_INTIMACY) {
            incTogetherBattleCount(friendUid);
        }

        // 建立亲密关系引导
        if (null == intimateRelation && intimacy > 0) {
            CurrentExecutorUtil.runJobNoException("tryGuideAddIntimateRelation", () -> {
                player.getIntimateManager().tryGuideAddIntimateRelation(friendUid);
                return null;
            });
        }

        for (int boundary : MiscConf.getInstance().getMiscConf().getChatNotifyIntimacyLevelList()) {
            if (oldIntimacy < boundary && newIntimacy >= boundary) {
                asyncSendChatNotifyIntimacyLevel(friendUid, boundary, newIntimacy);
                break;
            }
        }
    }

    private void asyncSendChatNotifyIntimacyLevel(long friendUid, int boundary, long current) {
        try {
            CurrentExecutorUtil.runJob(() -> {
                ChatGroupKey key = player.getPlayerChatManager().genP2PChatGroupKey(ChatType.CT_Private, friendUid);
                KVArray kv = KVArray.newBuilder()
                        .addArray(KVEntry.newBuilder()
                                .setKey("boundaryIntimacy").setValue(Integer.toString(boundary)))
                        .addArray(KVEntry.newBuilder()
                                .setKey("currentIntimacy").setValue(Long.toString(current)).build())
                        .build();
                ChatMsgData msg = ChatMsgData.newBuilder().setMsgType(ChatMsgType.CMT_IntimacyLevelNtf)
                        .setFromId(player.getUid())
                        .setKvArray(kv)
                        .build();
                try {
                    player.getPlayerChatManager().sendMsg(key, msg, DateUtils.currentTimeMillis(), true, true);
                } catch (Exception e) {
                    LOGGER.error("player {} add friend {} send hello msg error:", player.getUid(), friendUid, e);
                }
                return null;
            }, "chatNotifyIntimacyLevel", true);
        } catch (NKCheckedException e) {
            LOGGER.error("send chatNotifyIntimacyLevel error", e);
        }
    }

    public int getFriendIntimacyLevel(long uid) {
        DBRelation dbRelation = dbRelationInfo.getModRelationInfo(uid);
        if (dbRelation == null) {
            return 0;
        }
        return getIntimacyLevel(dbRelation.getHotData().getIntimacy());
    }

    private int getIntimacyLevel(long intimacy) {
        return IntimateRelationLevelConfData.getInstance().getIntimacyLevel(intimacy);
    }

    private void sendIntimacyNtf(long uid, long curIntimacy, long preIntimacy) {
        RelationIntimacyNtf.Builder ntf = RelationIntimacyNtf.newBuilder();
        int preLevel = getIntimacyLevel(preIntimacy);
        int curLevel = getIntimacyLevel(curIntimacy);
        // 亲密关系升级触发事件
        DBRelation relation = player.getIntimateManager().getRelation(uid);
        if (relation != null) {
            IntimatePlayerInfo intimatePlayerInfo = new IntimatePlayerInfo();
            intimatePlayerInfo.setIntimateId(relation.getIntimateId())
                    .setUid(relation.getUid())
                    .setLevel(player.getFriendManager().getFriendIntimacyLevel(uid));
            new PlayerIntimateRelationEvent(player).setIntimateRelationInfo(intimatePlayerInfo).dispatch();
        }
        ntf.setUid(uid).setPreLevel(preLevel).setCurLevel(curLevel);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_RELATIONINTIMACYNTF, ntf);
    }

    public void handleIntimacyChangeInteraction(long friendUid, long intimacy, long time,
                                                FriendIntimacyChangeReason reason) {
        try {
            DBRelation dbRelation = dbRelationInfo.getModRelationInfo(friendUid);
            if (Objects.isNull(dbRelation)) {
                LOGGER.info(
                        "handleIntimacyChangeInteraction: getModRelationInfoNull, maybe not friend. uid:{} friendUid:{} intimacy:{}",
                        player.getUid(), friendUid, intimacy);
                return;
            }
            long oldIntimacy = dbRelation.getHotData().getIntimacy();
            // long oldIntimacy = Optional.ofNullable(dbRelationInfo.getModRelationInfo(friendUid)).map(e -> e.getHotData().getIntimacy()).orElse(0L);

            long newIntimacy = RelationTableDao.increaseFriendIntimacy(player.getUid(), friendUid, intimacy, reason);
            LOGGER.info("handleIntimacyChangeInteraction: player {} add friend {} intimacy {}, old: {}, new {}",
                    player.getUid(), friendUid, intimacy, oldIntimacy, newIntimacy);
            // 更新缓存的亲密度
            dbRelation.getHotData().setIntimacy(newIntimacy);

            player.getIntimateManager().updatePublicProfileIntimate(friendUid, false);
            if (reason == FriendIntimacyChangeReason.RUR_GOLD_COIN
                    || reason == FriendIntimacyChangeReason.RUR_BATTLE_INTIMACY) {
                DailyIntimacyData dailyIntimacyInfo = player.getUserAttr().getRelationMapInfo()
                        .getDailyIntimacy(friendUid);
                if (dailyIntimacyInfo == null) {
                    dailyIntimacyInfo = new DailyIntimacyData().setUid(friendUid);
                    player.getUserAttr().getRelationMapInfo().putDailyIntimacy(friendUid, dailyIntimacyInfo);
                }
                // 如果是今天第一次加，清零
                if (DateUtils.getDayBeginTimeMs(time) > dailyIntimacyInfo.getLastIncreaseTime()) {
                    dailyIntimacyInfo.setTodayIntimacy(0);
                }
                dailyIntimacyInfo.addTodayIntimacy(newIntimacy - oldIntimacy);
                dailyIntimacyInfo.setLastIncreaseTime(time);
                LOGGER.info(
                        "handleIntimacyChangeInteraction: player {} add friend {} intimacy {}, new dailyIntimacy {}",
                        player.getUid(), friendUid, intimacy, dailyIntimacyInfo.getTodayIntimacy());
            }
            TlogFlowMgr.sendIntimacyFLow(player, friendUid, intimacy, newIntimacy, reason.getNumber(), 0, false);
            // 通知营地
            DBRelation intimateRelation = player.getIntimateManager().dbRelationInfo.getModRelationInfo(friendUid);
            int intimateId = (intimateRelation == null ? 0 : intimateRelation.getIntimateId());
            PlayerReportUtil.reportPlayerIntimacy(IntimateRelationFlowType.IRFT_Change_VALUE, player, friendUid,
                    newIntimacy, intimateId);
            // 更新好友最近亲密度变化信息
            player.getIntimateManager().afterAddFriendIntimacy(friendUid, oldIntimacy, newIntimacy, time);
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
            sendIntimacyNtf(friendUid, newIntimacy, oldIntimacy);
            // 更新最后亲密度变化时间
            updateRecentInteractTs(friendUid, time, reason);

            new FriendAddIntimacyEvent(player, friendUid, intimacy).dispatch();
            player.getPlayerEventManager()
                    .dispatch(new IntimacyAddEvent(player.getConditionMgr()).setIntimacy(intimacy));
        } catch (Exception e) {
            LOGGER.error("increaseFriendIntimacy fail,{}, friendUid:{}, {}, {}", player.getUid(), friendUid, intimacy,
                    reason, e);
            // todo 亲密度监控
        }
    }

    private int useTeamRankActivityEffectIntimacyMag(int matchTypeId, int teamMemberCount) {
        List<ResActivity.ActivityMainConfig> runningActivity = ActivityMainConfig.getInstance()
                .getActivityOpenByType(ActivityType.ATTeamRank);
        if (!runningActivity.isEmpty()) {
            for (ResActivity.ActivityMainConfig teamRankConfig : runningActivity) {
                BaseActivity activityBase = player.getActivityManager().getRunningActivity(teamRankConfig.getId());
                if (activityBase == null) {
                    LOGGER.error("get getRunningActivity err, uid:{} activityId:{}", player.getUid(),
                            teamRankConfig.getId());
                    continue;
                }
                if (!(activityBase instanceof com.tencent.wea.playerservice.activity.implement.TeamRankActivity)) {
                    continue;
                }
                com.tencent.wea.playerservice.activity.implement.TeamRankActivity teamRankActivity = (TeamRankActivity) activityBase;
                ActivityTeamRankConfig effectConfig = teamRankActivity.getIntimacyEffect(matchTypeId, teamMemberCount);
                if (effectConfig != null && effectConfig.getParamsCount() > 0) {
                    teamRankActivity.addTeamRankEffectTimes(effectConfig);
                    return effectConfig.getParams(0);
                }
            }
        }
        return 0;
    }

    public void refreshFriendIntimacy(long friendUid) {
        TcaplusDb.RelationTable dbData = RelationTableDao
                .getPlayerRelation(RelationTypeEnum.RTE_FRIEND_RELATION, player.getUid(), friendUid);
        if (dbData == null) {
            LOGGER.error("getPlayerRelation fail, {},friendUid:{}", player.getUid(), friendUid);
            return;
        }
        DBRelation dbRelation = dbRelationInfo.getModRelationInfo(friendUid);
        if (dbRelation == null) {
            dbRelation = new DBRelation().setUid(friendUid).setAddTime(dbData.getAddTime());
            dbRelationInfo.putModRelationInfo(friendUid, dbRelation);
            // 在redis中记录当前关系数量
            cacheRelationNum(getRelationType(), player.getUid(), dbRelationInfo.getModRelationInfoSize());
        }
        dbRelation.getHotData().setIntimacy(dbData.getIntimacy());
    }

    public void handleBattleIntimacy(List<Long> roomMembers, long battleStartTime, int matchType,
                                     CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        handleBattleIntimacy(roomMembers, battleStartTime, matchType, ntfBuilder, false);
    }

    public void handleBattleIntimacy(List<Long> roomMembers, long battleStartTime, int matchType,
                                     CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, boolean isBattleSettlement) {
        if (roomMembers == null || roomMembers.isEmpty()) {
            return;
        }
        ArrayList<Long> sortRoomMembers = new ArrayList<>(roomMembers);
        sortRoomMembers.sort((o1, o2) -> (int) (getFriendIntimacy(o2) - getFriendIntimacy(o1)));
        long intimacy = RelationBattleModeIntimacyConfData.getInstance().getModeIntimacy(matchType);
        if (intimacy == 0) {
            intimacy = getDefaultBattleIntimacy();
        }

        int intimacyMag = useTeamRankActivityEffectIntimacyMag(matchType, roomMembers.size());
        if (intimacyMag > 0) {
            LOGGER.debug("player {} battle matchType {} add intimacy use mag {} * {}",
                    player.getUid(), matchType, intimacyMag, intimacy);
            intimacy = (100 + intimacyMag) / 100 * intimacy;
        }

        int totalTacitIntimacy = 0;
        int addTacitIntimacy = 0;
        int tacitIntimacyItemId = 0;
        int tacitIntimacyDailyLimit = 0;
        ResRelation.RelationConf relationBattleTacitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_INTIMACY_RELATION_BATTLE_TACIT_COIN_NUM);
        if (relationBattleTacitConf != null) {
            addTacitIntimacy = relationBattleTacitConf.getValue();
        }

        ResRelation.RelationConf relationBattleTacitCoinConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_INTIMACY_RELATION_BATTLE_TACIT_COIN_TYPE);
        if (relationBattleTacitCoinConf != null) {
            tacitIntimacyItemId = relationBattleTacitCoinConf.getValue();
        }

        ResRelation.RelationConf tacitIntimacyDailyLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_INTIMACY_RELATION_BATTLE_TACIT_COIN_DAILY_LIMIT);
        if (tacitIntimacyDailyLimitConf != null) {
            tacitIntimacyDailyLimit = tacitIntimacyDailyLimitConf.getValue();
        }
        ArrayList<Long> batchNtfUidList = new ArrayList<>();
        for (long uid : sortRoomMembers) {
            DBRelation friend = getFriend(uid);
            if (friend == null) {
                continue;
            }
            RelationSettlementInfo.Builder relationSettlementInfo = RelationSettlementInfo.newBuilder();
            relationSettlementInfo.setFriendUid(uid);
            try {
                // 实际增加的亲密度
                long actAddIntimacy = increaseDailyLimitIntimacy(uid, intimacy, battleStartTime,
                        FriendIntimacyChangeReason.RUR_BATTLE_INTIMACY, matchType);
                if (actAddIntimacy > 0) {
                    if (actAddIntimacy == intimacy) {
                        batchNtfUidList.add(uid);
                    } else {
                        sendIntimacyChangeNtf(List.of(uid), (int) actAddIntimacy, isBattleSettlement, FriendIntimacyChangeReason.RUR_BATTLE_INTIMACY);
                    }
                    relationSettlementInfo.setIntimacy((int) actAddIntimacy);
                }

                increaseFriendInteractStatistic(uid, FriendInteractDataType.FIDT_TeamBattleCnt, 1);

                // 是否亲密关系
                IntimatePlayerInfo intimatePlayerInfo = player.getPlayerPublicProfileInfo().getIntimateRelationInfo()
                        .getIntimatePlayer(uid);
                if (intimatePlayerInfo != null) {
                    int intimatePlayerInfoLevel = intimatePlayerInfo.getLevel();
                    LOGGER.debug("player {} current intimatePlayerInfo level {}",uid,intimatePlayerInfoLevel);
                    ResRelation.IntimateRelationLevelConf intimateRelationLevelConf = IntimateRelationLevelConfData.getInstance().get(intimatePlayerInfoLevel);
                    float intimacyFactor = intimateRelationLevelConf.getIntimacyFactor();
                    LOGGER.debug("player {} current intimacyFactor  {}",uid,intimacyFactor);
                    if (addTacitIntimacy!=10){
                        addTacitIntimacy=10;   //重置
                    }
                    addTacitIntimacy= (int) (addTacitIntimacy*intimacyFactor);
                    LOGGER.debug("player {} current need add {}",uid,addTacitIntimacy);
                    totalTacitIntimacy += addTacitIntimacy;
                    relationSettlementInfo.setIntimateId(intimatePlayerInfo.getIntimateId());
                }
                if (ntfBuilder != null) {
                    ntfBuilder.addRelationSettlementInfo(relationSettlementInfo);
                }
            } catch (Exception e) {
                LOGGER.error("player {} increase {} intimacy after battle error:", player.getUid(), uid, e);
            }
        }
        if (ntfBuilder != null && ntfBuilder.getIsGiveUp() == 0) {
            // 默契之心发放
            long intimateCoinOpenTimestamp = RelationMiscConfData.getInstance().getMiscConf()
                    .getIntimateCoinOpenTimestamp().getSeconds();
            if (totalTacitIntimacy > 0 && DateUtils.currentTimeSec() >= intimateCoinOpenTimestamp) {
                int currentNum = (int) player.getLimitManager()
                        .getValue(CommonLimitType.CLT_BattleTacitCoinDailyLimit, 0L);
                if (currentNum + totalTacitIntimacy > tacitIntimacyDailyLimit) {
                    totalTacitIntimacy = Math.max(0, tacitIntimacyDailyLimit - currentNum);
                }
                player.getBagManager()
                        .AddItems2(tacitIntimacyItemId, totalTacitIntimacy, ItemChangeReason.ICR_BattleDrop, false);
                long currentTimeMs = DateUtils.currentTimeMillis();
                player.getLimitManager().addValue(CommonLimitType.CLT_BattleTacitCoinDailyLimit, 0L, totalTacitIntimacy,
                        DateUtils.getDayEndTimeMs(currentTimeMs), currentTimeMs);
                // 亲密度满了100 batchNtfUidList为空
                if (batchNtfUidList.isEmpty()){
                    batchNtfUidList.add(player.getUid());
                    intimacy=0;
                }
                ntfBuilder.addItemList(LevelDropItemInfo.newBuilder().setItemId(tacitIntimacyItemId)
                        .setItemCount(totalTacitIntimacy).setCurrentCount(currentNum)
                        .setLimitCount(tacitIntimacyDailyLimit).build());
            }
        }else {
            //这里没有完成比赛，信息中传递的 默契币增加值应该是 0
            totalTacitIntimacy = 0;
        }
        sendIntimacyChangeNtf(batchNtfUidList, (int) intimacy, isBattleSettlement, FriendIntimacyChangeReason.RUR_BATTLE_INTIMACY , totalTacitIntimacy);
    }

    /**
     * @Description: 是否平台好友
     * @Author: digoldzhang
     * @Date: 2021/10/18
     */
    public boolean isPlatFriend(long uid) {
        return platFriendInfo.getPlatFriendInfo().containsKey(uid);
    }

    /**
     * @Description: 是否游戏内好友
     * @Author: digoldzhang
     * @Date: 2023/03/18
     */
    public boolean isGameFriend(long uid) {
        return isRelation(uid);
    }

    /**
     * @Description: 是好友？包括游戏好友和平台好友
     * @Author: digoldzhang
     * @Date: 2021/10/11
     */
    public boolean isFriend(long uid) {
        return dbRelationInfo.getModRelationInfo().containsKey(uid) || platFriendInfo.getPlatFriendInfo()
                .containsKey(uid);
    }

    // 是回归好友
    public boolean isReturningFriend(long uid) {
        DBRelation friendInfo = dbRelationInfo.getModRelationInfo().get(uid);
        if (friendInfo == null) {
            friendInfo = platFriendInfo.getPlatFriendInfo().get(uid);
        }
        if (friendInfo == null) {
            return false;
        }
        return PlayerReturnActivityManager.isReturningRelation(friendInfo);
    }

    /** 好友的生日数据 */
    public Tuple.T2<Integer, BirthdayVisibleRange> getFriendBirthdayData(long friendUid) {
        DBRelation friendInfo = dbRelationInfo.getModRelationInfo().get(friendUid);
        if (Objects.isNull(friendInfo)) {
            friendInfo = platFriendInfo.getPlatFriendInfo().get(friendUid);
        }
        if (Objects.isNull(friendInfo)) {
            return null;
        }
        int birthdayMonthDay = friendInfo.getBirthdayMonthDay();
        BirthdayVisibleRange birthdayVisibleRange = friendInfo.getBirthdayVisibleRange();
        return Tuple.New(birthdayMonthDay, birthdayVisibleRange);
    }

    public int getFriendType(long uid) {
        if (!isFriend(uid)) {
            return 0;
        }
        if (isReturningFriend(uid)) {
            return 2;
        }
        return 1;
    }

    // 返回1，游戏好友，2，平台好友，3 亲密关系，0，非好友
    public int getFriendTypeTlog(long uid){
        boolean  platFriend = isPlatFriend(uid);
        boolean  gameFriend = isGameFriend(uid);
        if (!platFriend && !gameFriend){
            return 0;
        }
        for (DBRelation dbRelation : player.getUserAttr().getRelationMapInfo()
                .getRelationInfo(RelationTypeEnum.RTE_INTIMATE_RELATION).getModRelationInfo().values()) {
            if (dbRelation.getUid() == uid) {
                return 3;
            }
        }
        return gameFriend?1:2;
    }


    /**
     * @Description: 获取平台好友列表
     * @Author: digoldzhang
     * @Date: 2023/3/3
     */
    public Collection<DBRelation> getAllPlatFriend() {
        return platFriendInfo.getAllPlatFriendData();
    }

    public Collection<Long> getAllPlatFriendUid() {
        return platFriendInfo.getAllPlatFriendUid();
    }

    /**
     * @Description: 获取游戏内好友列表
     * @Author: digoldzhang
     * @Date: 2023/3/3
     */
    public Collection<DBRelation> getAllGameFriend() {
        return dbRelationInfo.getModRelationInfo().values();
    }

    /**
     * @Description: 获取所有好友Uid(包括游戏内和平台好友, 取并集)
     * @Author: digoldzhang
     * @Date: 2023/3/3
     */
    public Collection<Long> getAllFriendUid() {
        HashSet<Long> allFriend = new HashSet<>(dbRelationInfo.getModRelationInfo().keySet());
        allFriend.addAll(platFriendInfo.getAllPlatFriendUid());
        return allFriend;
    }

    /**
     * @Description: 获取所有在线好友
     * @Author: digoldzhang
     * @Date: 2023/5/31
     */
    public HashMap<Long, DBRelation> getAllOnlineFriend() {
        HashMap<Long, DBRelation> allFriend = new HashMap<>();
        dbRelationInfo.getModRelationInfo().values().forEach(info -> {
            if (PlayerStateMgr.isOnline(info.getHotData().getPlayerStatus())) {
                allFriend.put(info.getUid(), info);
            }
        });
        platFriendInfo.getAllPlatFriendData().forEach(info -> {
            if (PlayerStateMgr.isOnline(info.getHotData().getPlayerStatus())) {
                allFriend.put(info.getUid(), info);
            }
        });
        return allFriend;
    }

    /**
     * @Description: 获取好友
     * @Author: digoldzhang
     * @Date: 2023/6/12
     */
    public DBRelation getFriend(long uid) {
        DBRelation relation = dbRelationInfo.getModRelationInfo(uid);
        if (relation == null) {
            relation = platFriendInfo.getPlatFriendInfo(uid);
        }
        return relation;
    }

    /**
     * @Description: 获取游戏好友
     * @Author: digoldzhang
     * @Date: 2023/6/12
     */
    public DBRelation getGameFriend(long uid) {
        return dbRelationInfo.getModRelationInfo(uid);
    }


    /**
     * @Description: 获取好友
     * @Author: digoldzhang
     * @Date: 2023/6/12
     */
    public DBRelation getPlatFriend(long uid) {
        return platFriendInfo.getPlatFriendInfo(uid);
    }

    /**
     * @Description: 获取好友亲密度
     * @Author: digoldzhang
     * @Date: 2023/6/12
     */
    public long getFriendIntimacy(long uid) {
        DBRelation relation = getGameFriend(uid);
        if (relation != null) {
            return relation.getHotData().getIntimacy();
        }
        return 0;
    }

    /**
     * @Description: 获取亲密度最高的一定数量的在大厅的好友
     * @Author: digoldzhang
     * @Date: 2023/3/23
     */
    public Collection<DBRelation> getTopIntimacyFriendInLobby(int num) {
        HashSet<Long> res = new HashSet<>();
        SortedArrayList<DBRelation> sortedList = new SortedArrayList<>(
                (o1, o2) -> (int) (o1.getHotData().getIntimacy() - o2.getHotData().getIntimacy()));
        for (DBRelation relation : dbRelationInfo.getModRelationInfo().values()) {
            if (PlayerStateMgr.isOnline(relation.getHotData().getPlayerStatus())) {
                res.add(relation.getUid());
                sortedList.add(relation);
            }
        }
        if (sortedList.size() > num) {
            return sortedList.subList(0, num - 1);
        }
        for (DBRelation relation : platFriendInfo.getPlatFriendInfo().values()) {
            if (!res.contains(relation.getUid()) && PlayerStateMgr
                    .isOnline(relation.getHotData().getPlayerStatus())) {
                sortedList.add(relation);
                if (sortedList.size() >= num) {
                    return sortedList;
                }
            }
        }
        return sortedList;
    }

    // 增加好友开黑次数 无需抛异常
    public void incTogetherBattleCount(long friendUid) {
        LOGGER.info("incTogetherBattleCount: player {} add friend {} ",
                player.getUid(), friendUid);
        try {
            if (!isGameFriend(friendUid)) {
                NKErrorCode.FriendManagerIllegalFriendRelation.throwError("not friend,{},{}", player.getUid(),
                        friendUid);
            }
            DBRelation dbRelation = dbRelationInfo.getModRelationInfo(friendUid);
            int oldValue = dbRelation.getHotData().getTogetherBattleCount();
            int newValue = RelationTableDao.incTogetherBattleCount(player.getUid(), friendUid, 1);
            // 更新缓存
            dbRelation.getHotData().setTogetherBattleCount(newValue);

            LOGGER.info("incTogetherBattleCount suc: player {} add friend {} change {}, old: {}, new: {}",
                    player.getUid(), friendUid, 1, oldValue, newValue);

            player.getUserAttrMgr().collectAndSyncDirtyToClient();
        } catch (Exception e) {
            LOGGER.error("incTogetherBattleCount error {} player {} friend {}", e, player.getUid(),
                    friendUid);
        }
    }

    // 更新亲密度变化时间
    public void updateRecentInteractTs(long friendUid, long recentInteractTs, FriendIntimacyChangeReason reason) {
        LOGGER.debug("updateRecentInteractTs: player {} friend {} reason {}", player.getUid(), friendUid, reason);
        try {
            if (!isGameFriend(friendUid)) {
                NKErrorCode.FriendManagerIllegalFriendRelation.throwError("not friend,{},{}", player.getUid(),
                        friendUid);
            }
            DBRelation dbRelation = dbRelationInfo.getModRelationInfo(friendUid);
            if (dbRelation == null) {
                NKErrorCode.FriendManagerIllegalFriendRelation.throwError("not friend,{},{}", player.getUid(),
                        friendUid);
            }
            long oldValue = dbRelation.getHotData().getRecentInteractTs();
            if (recentInteractTs == oldValue) {
                return;
            }
            RelationTableDao.setRecentInteractTs(player.getUid(), friendUid, recentInteractTs);
            // 更新缓存
            dbRelation.getHotData().setRecentInteractTs(recentInteractTs);
            LOGGER.debug("updateRecentInteractTs suc: player {} friend {} reason {}", player.getUid(), friendUid,
                    reason);

            player.getUserAttrMgr().collectAndSyncDirtyToClient();
        } catch (Exception e) {
            LOGGER.error("incTogetherBattleCount error {} player {} friend {}", e, player.getUid(),
                    friendUid);
        }
    }

    public void GmAddPlatFriend(Set<Long> uidSet) {
        platFriendInfo.batchAddPlatFriend(player, uidSet);
        updateFriendOnlineStatus();
        // 通知所有在线好友我上线了
        //        notifyAllOnlineFriendPlayerStatus(player.getPlayerStateMgr().getPlayerState());
    }


    public DBRelation addPlatFriend(long uid) {
        if (uid == player.getUid()) {
            return null;
        }
        DBRelation dbRelation = new DBRelation().setUid(uid).setAddTime(Framework.currentTimeMillis());
        platFriendInfo.getDbRelationInfo().putModRelationInfo(uid, dbRelation);
        updateFriendData(uid);
        return dbRelation;
    }

    /**
     * @Description: 测试环境使用：将该gamesvr在线用户作为平台好友下发
     * @Author: digoldzhang
     * @Date: 2021/9/30
     */
    public void testUpdatePlatFriend() {
        Set<Long> uidSet = TconndManager.getInstance().getAllSessionUid();
        platFriendInfo.batchAddPlatFriend(player, uidSet);
    }

    // 把好友分为三种: 游戏和平台好友、仅游戏好友及仅平台好友, 组播通知相应好
    // 友, 减少消息序列化的开销
    public static void batchNotifyPlayerOnlineStatusChange(
            Collection<Player> players,
            SsGamesvr.RpcNotifyPlayerOnlineStatusReq.Builder reqMsg) {
        if (players == null || players.size() == 0) {
            return;
        }

        // 这里和之前语义保持一致, 同时是游戏和平台好友的单独一组. 三个平铺变量
        // 和下面的检查丑陋了点, 但又不想再来个relation type的容器
        List<Long> dualFriendUidList = null;
        List<Long> gameFriendUidList = null;
        List<Long> platFriendUidList = null;
        for (Player player : players) {
            if (!player.getFriendManager().isNtfFriendDataFull) {
                continue;
            }
            DBRelation modFriend = player.getFriendManager().dbRelationInfo.getModRelationInfo(reqMsg.getReqUid());
            DBRelation platFriend = player.getFriendManager().platFriendInfo.getPlatFriendInfo(reqMsg.getReqUid());
            boolean relation = player.getIntimateManager().isRelation(reqMsg.getReqUid());
            // 如果在对方的平台好友中，但不在自己的平台好友中，尝试加为平台好友
            if (PropertyFileReader.getRealTimeBooleanItem("plat_friend_ntf_add_switch", true)) {
                if (player.getFriendManager().platFriendInfo.getPlatFriendNum() < PlatFriendInfo.getMaxRelationNum()) {
                    PlayerSpecialData specialData = reqMsg.getTargetFriendDataMap().get(player.getUid());
                    if (platFriend == null && specialData != null && specialData.getRelationType() >= 2) {
                        LOGGER.info("addPlatFriend, {}, {}", player.getUid(), reqMsg.getReqUid());
                        platFriend = player.getFriendManager().addPlatFriend(reqMsg.getReqUid());
                    }
                }

            }
            List<Long> groupUidList = null;
            if (modFriend != null) {
                if (!reqMsg.getOnlyClient()) {
                    refreshFriendState(modFriend, reqMsg, player);
                }

                if (platFriend != null) {
                    if (dualFriendUidList == null) {
                        dualFriendUidList = new ArrayList<>(players.size());
                    }
                    groupUidList = dualFriendUidList;
                } else {
                    if (gameFriendUidList == null) {
                        gameFriendUidList = new ArrayList<>(players.size());
                    }
                    groupUidList = gameFriendUidList;
                }
            }
            if (platFriend != null) {
                if (!reqMsg.getOnlyClient()) {
                    refreshFriendState(platFriend, reqMsg, player);
                }
                if (modFriend == null) {
                    if (platFriendUidList == null) {
                        platFriendUidList = new ArrayList<>(players.size());
                    }
                    groupUidList = platFriendUidList;
                }
            }
            if (groupUidList != null) {
                groupUidList.add(player.getUid());
            }
        }

        LOGGER.debug(
                "reqUid:{} dualFriends: {} gameFriends: {} platFriends:{}",
                reqMsg.getReqUid(),
                (dualFriendUidList != null ? dualFriendUidList.size() : 0),
                (gameFriendUidList != null ? gameFriendUidList.size() : 0),
                (platFriendUidList != null ? platFriendUidList.size() : 0));

        RelationStateInfo.Builder gameRelationBuilder =
                newRelationStateInfoBuilderFromRpcNotify(
                        reqMsg, RelationTypeEnum.RTE_FRIEND_RELATION);
        RelationStateInfo.Builder platRelationBuilder =
                newRelationStateInfoBuilderFromRpcNotify(
                        reqMsg, RelationTypeEnum.RTE_PLAT_FRIEND_RELATION);
        if (reqMsg.getFinishedLoad()){
            gameRelationBuilder.setFinishedLoad(true);
            platRelationBuilder.setFinishedLoad(true);
        }
        // 有亲密好友头像框的，单独同步
        for (var dressItemEntry : reqMsg.getIntimateDressItemsMap().entrySet()) {
            RelationStateInfo.Builder intimateRelationBuilder = newRelationStateInfoBuilderFromRpcNotify(
                    reqMsg, RelationTypeEnum.RTE_FRIEND_RELATION);
            if (reqMsg.getFinishedLoad()){
                intimateRelationBuilder.setFinishedLoad(true);
            }
            intimateRelationBuilder.addAllIntimateOnlineNoticeDressItems(dressItemEntry.getValue().getItemsList());
            RelationStateNtf.Builder ntf = RelationStateNtf.newBuilder();
            ntf.addInfo(intimateRelationBuilder);
            TconndManager.getInstance().sendNtfMsg(dressItemEntry.getKey(), MsgTypes.MSG_TYPE_RELATIONSTATENTF, ntf);
            if (dualFriendUidList != null) {
                dualFriendUidList.remove(dressItemEntry.getKey());
            }
            if (gameFriendUidList != null) {
                gameFriendUidList.remove(dressItemEntry.getKey());
            }
        }
        // 非null必然非空
        if (dualFriendUidList != null && dualFriendUidList.size() > 0) {
            // todo 是游戏好友又是平台好友，应该只需要同步游戏好友就行
            RelationStateNtf.Builder ntf = RelationStateNtf.newBuilder();
            ntf.addInfo(gameRelationBuilder);
            ntf.addInfo(platRelationBuilder);
            TconndManager.getInstance()
                    .uidMultiCastMsg(dualFriendUidList, MsgTypes.MSG_TYPE_RELATIONSTATENTF, ntf);
        }
        if (gameFriendUidList != null && gameFriendUidList.size() > 0) {
            RelationStateNtf.Builder ntf = RelationStateNtf.newBuilder();
            ntf.addInfo(gameRelationBuilder);
            TconndManager.getInstance()
                    .uidMultiCastMsg(gameFriendUidList, MsgTypes.MSG_TYPE_RELATIONSTATENTF, ntf);
        }
        if (platFriendUidList != null && platFriendUidList.size() > 0) {
            RelationStateNtf.Builder ntf = RelationStateNtf.newBuilder();
            ntf.addInfo(platRelationBuilder);
            TconndManager.getInstance()
                    .uidMultiCastMsg(platFriendUidList, MsgTypes.MSG_TYPE_RELATIONSTATENTF, ntf);
        }
    }

    private static void refreshFriendState(DBRelation friend, SsGamesvr.RpcNotifyPlayerOnlineStatusReq.Builder reqMsg,
                                           Player player) {
        friend.getHotData()
                .setSvrId(reqMsg.getOriginSvrId())
                .setPlayerStatus(reqMsg.getStatus())
                .setHideProfileToFriend(reqMsg.getChangeInfo().getHideProfileToFriend())
                .setHidePlayerStatus(reqMsg.getHidePlayerStatus());
        player.getFriendManager().ugcFriendInfo.refreshFriendPlayMap(reqMsg.getChangeInfo().getBattleUgcId(),
                friend.getUid(), Framework.currentTimeMillis());
        if (reqMsg.getHidePlayerStatus()) {
            friend.getHotData().setLastLogoutTime(reqMsg.getHidePlayerStatusTime());
        } else if (reqMsg.getStatus() == PlayerStateType.PST_Offline) {
            friend.getHotData().setLastLogoutTime(Framework.currentTimeMillis());
        }
        if (reqMsg.hasChangeInfo()) {
            if (reqMsg.getChangeInfo().hasBirthdayMonthDay()) {
                friend.setBirthdayMonthDay(reqMsg.getChangeInfo().getBirthdayMonthDay());
            }
            if (reqMsg.getChangeInfo().hasBirthdayVisibleRange()) {
                friend.setBirthdayVisibleRange(reqMsg.getChangeInfo().getBirthdayVisibleRange());
            }
        }
    }

    private static RelationStateInfo.Builder newRelationStateInfoBuilderFromRpcNotify(
            SsGamesvr.RpcNotifyPlayerOnlineStatusReq.Builder reqMsg,
            RelationTypeEnum relationType) {
        RelationStateInfo.Builder info = RelationStateInfo.newBuilder()
                .setType(relationType)
                .setUid(reqMsg.getReqUid())
                .setIsOnline(reqMsg.getStatus() != PlayerStateType.PST_Offline)
                .setHidePlayerStatus(reqMsg.getHidePlayerStatus())
                .setIsLoginSuc(reqMsg.getIsLoginSuc())
                .setIntimateOnlineNotice(reqMsg.getIntimateOnlineNotice());
        if (reqMsg.getFinishedLoad()){
            info.setFinishedLoad(true);
        }
        if (reqMsg.getHidePlayerStatus()) {
            info.setLogoutTime(reqMsg.getHidePlayerStatusTime());
        } else if (reqMsg.getStatus() == PlayerStateType.PST_Offline) {
            info.setLogoutTime(Framework.currentTimeMillis());
        }
        info.setPlayerPublicInfo(reqMsg.getChangeInfo());
        return info;
    }

    @Override
    protected void updateSendApplyInfoToRedis() {
        FriendApplyCache.addFriendApplyToCache(player.getUid(), dbRelationInfo.getSendApplyInfo().keySet());
    }

    /*************************************************** 隐身相关 ***************************************************/
    // 七彩石隐身开关
    public void refreshHidePlayerStatusSwitch() {
        boolean stealthSwitch = PropertyFileReader.getRealTimeBooleanItem("is_open_stealth_switch", true);
        player.getUserAttr().getPlayerPublicGameSettings().setHidePlayerStatusSwitch(stealthSwitch);
        sendSettingsChangeNtf();
    }

    // 设置隐身状态
    public void setHidePlayerStatus(boolean hidePlayerStatus) {
        PlayerPublicGameSettings publicSettings = player.getUserAttr().getPlayerPublicGameSettings();
        if (publicSettings.getHidePlayerStatus() == hidePlayerStatus) {
            // 没有变化
            return;
        }
        if (hidePlayerStatus) {
            // 超过次数
            int weekCnt = MiscConf.getInstance().getMiscConf().getHidePlayerStatusWeekCnt();
            if (publicSettings.getHidePlayerStatusWeekCnt() >= (weekCnt != 0 ? weekCnt : 5)) {
                NKErrorCode.PlayerStealthCntLimit.throwError("PlayerStealthCntLimit, uid={}", player.getUid());
            }
            publicSettings.addHidePlayerStatusWeekCnt(1);
            publicSettings.setHidePlayerStatusTime(Framework.currentTimeMillis());
            player.getPlayerHouseMgr().removeFromAtHomePool();
        }
        publicSettings.setHidePlayerStatus(hidePlayerStatus);
        addChangeField(PlayerPublicInfoField.PLAYER_STATE);
        if (!hidePlayerStatus) {
            needNtfLogin = true;
            ProfileGameSvrPlayerMgr.getInstance().addNeedNtfLogin(player.getUid());
        }

        player.getClubMgr().noticeClubSvr();
    }

    // 重置隐身状态
    public void resetHidePlayerStatus() {
        if (player.getUserAttr().getPlayerPublicGameSettings().getHidePlayerStatus()) {
            long limitTimeSec = MiscConf.getInstance().getMiscConf().getHidePlayerStatusOfflineLimit();
            long logoutTimeMs = player.getUserAttr().getBasicInfo().getLogoutTimeMs();
            long offlineTimeMs = Framework.currentTimeMillis() - logoutTimeMs;
            if (offlineTimeMs >= limitTimeSec * 1000) {
                setHidePlayerStatus(false);
                sendSettingsChangeNtf();
                player.getClubMgr().noticeClubSvr();
                LOGGER.info("resetHidePlayerStatus, uid={} offlineTimeMs={}", player.getUid(), offlineTimeMs);
            }
        }
    }

    // setting change ntf
    public void sendSettingsChangeNtf() {
        try {
            CurrentExecutorUtil.runJob(() -> {
                CsLetsgo.LetsGoSettingsChangeNtf.Builder ntf = CsLetsgo.LetsGoSettingsChangeNtf.newBuilder();
                player.sendNtfMsg(MsgTypes.MSG_TYPE_LETSGOSETTINGSCHANGENTF, ntf);
                return 0;
            }, "sendSettingsChangeNtf", false);
        } catch (Exception e) {
            LOGGER.error("sendSettingsChangeNtf error", e);
        }
    }

    /**
     * 处理设置好友备注的消息
     **/
    private static int getMaxRemarkNameLength() {
        return PropertyFileReader.getRealTimeIntItem("friend_remark_name_length_limit", 14);
    }

    public NKErrorCode handleSetRelationRemarkName(long uid, String remarkName, int scene) {
        LOGGER.debug("handleSetRelationRemarkName :{},{},{}", player.getUid(), uid, remarkName);
        try {
            if (!isGameFriend(uid)) {
                LOGGER.error("not game friend,{},{}", player.getUid(), uid);
                return NKErrorCode.FriendNotGameFriend;
            }
            if (TextUtil.textLength(remarkName) > getMaxRemarkNameLength()) {
                LOGGER.error("remarkName len too large,{},{}, {}",
                        player.getUid(), remarkName, getMaxRemarkNameLength());
                return NKErrorCode.FriendRemarkNameTooLong;
            }
            DBRelation friend = dbRelationInfo.getModRelationInfo(uid);
            if (friend == null) {
                LOGGER.error("not game friend,{},{}", player.getUid(), uid);
                return NKErrorCode.FriendNotGameFriend;
            }
            if (friend.getNameData().getRemarkName().equals(remarkName)) {
                LOGGER.error("remarkName is same,{},{}, {}",
                        player.getUid(), uid, remarkName);
                return NKErrorCode.FriendRemarkNameIsSame;
            }
            if (remarkName.length() > 0) {
                boolean isLawful = PlayerTssUtil.checkInformationLawful(player, scene, remarkName, new HashMap<>());
                if (!isLawful) {
                    LOGGER.error("remarkName invalid,{},{}", player.getUid(),
                            remarkName);
                    return NKErrorCode.FriendRemarkNameInvalid;
                }
            }
            RelationTableDao.setFriendRemarkName(player.getUid(), uid, remarkName);
            friend.getNameData().setRemarkName(remarkName);
        } catch (Exception e) {
            LOGGER.error("handleSetRelationRemarkName error", e);
        }
        LOGGER.debug("handleSetRelationRemarkName suc:{},{},{}", player.getUid(), uid, remarkName);
        return NKErrorCode.OK;
    }

    /**
     * 获取idip使用的拉取用户好友列表数据
     *
     * @param req
     * @return
     */
    public SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder getPlayerFriendListForIdip(
            SsGamesvr.RpcGetPlayerFriendListForIdipReq.Builder req) {

        SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder res = SsGamesvr.RpcGetPlayerFriendListForIdipRes.newBuilder();

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("query player friend list, openId:{}, platId:{}, uid:{}, friendType:{}, page:{}",
                    player.getOpenId(), player.getPlatId(), player.getUid(), req.getFriendType(), req.getPage());
        }

        if (req.hasSearchType() && req.hasOriginalData() && !req.getOriginalData().isEmpty()) {
            // 处理基于昵称/短号搜索好友信息场景
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player uid:{}, friendType:{}, searchType:{}, originalData:{}",
                        player.getUid(), req.getFriendType(), req.getSearchType(), req.getOriginalData());
            }

            Set<Long> uidSet = new HashSet<>();
            // 基于搜索数据获取搜索目标用户的uid数据
            if (req.getSearchType() == IdipFriendSearchType.EN_FRIEND_SEARCH_TYPE_KEYWORD_VALUE) {
                // 先基于昵称搜索, 然后再基于短号搜索
                Long targetUid = getUidByNickname(req.getOriginalData());
                if (targetUid != 0L) {
                    uidSet.add(targetUid);
                }
                targetUid = getUidByShortUid(player.getUid(), req.getOriginalData());
                if (targetUid != 0L) {
                    uidSet.add(targetUid);
                }
            } else if (req.getSearchType() == IdipFriendSearchType.EN_FRIEND_SEARCH_TYPE_UID_VALUE) {
                try {
                    Long targetUid = Long.parseLong(req.getOriginalData());
                    uidSet.add(targetUid);
                } catch (Exception ex) {
                    LOGGER.error("parse search target uid failed, uid:{}, originalData:{}",
                            player.getUid(), req.getOriginalData());
                }
            }
            if (uidSet.isEmpty()) {
                return fillResponse(res, 0, 0);
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player uid:{}, targetUid:{}", player.getUid(), uidSet);
            }

            // 基于好友类型获取好友的uid->亲密度的映射关系
            Map<Long, Long> friendUidToIntimacyMap = getFriendUidToIntimacyMap(player, req.getFriendType());
            if (friendUidToIntimacyMap == null || friendUidToIntimacyMap.isEmpty()) {
                return fillResponse(res, 0, 0);
            }

            // 如果用户的好友数据中不包含搜索目标用户, 直接返回
            Set<Long> resultSet = uidSet.stream().filter(friendUidToIntimacyMap::containsKey)
                    .collect(Collectors.toSet());
            if (resultSet.isEmpty()) {
                return fillResponse(res, 0, 0);
            }

            // 获取搜索目标用户的用户信息
            List<Long> uidList = new ArrayList<>(resultSet);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player uid:{}, searchType:{}, originalData:{}, friend list size:{}",
                        player.getUid(), req.getSearchType(), req.getOriginalData(), uidList.size());
            }

            Map<Long, TcaplusDb.PlayerPublic> playerPublicList = getPlayerPublicInfo(uidList);
            if (playerPublicList == null || playerPublicList.isEmpty()) {
                return fillResponse(res, 0, 0);
            }

            // 填充好友信息
            res = fillFriendInfo(res, playerPublicList, friendUidToIntimacyMap, uidList);

            return fillResponse(res, 1, 1);
        } else {
            // 处理分页拉取好友数据场景
            // 基于好友类型获取好友的uid->亲密度的映射关系
            int sortType = req.hasSortType() ? req.getSortType() : IdipFriendSortType.EN_FRIEND_SORT_TYPE_DEFAULT_VALUE;
            Map<Long, Long> friendUidToIntimacyMap = getSortedFriendUidToIntimacyMap(player, req.getFriendType(),
                    sortType);
            if (friendUidToIntimacyMap == null || friendUidToIntimacyMap.isEmpty()) {
                return fillResponse(res, 0, 0);
            }

            int offset = (req.getPage() - 1) * req.getPageNum();

            if (offset < friendUidToIntimacyMap.size()) {
                // 获取待查询信息的uid列表
                List<Long> uidList = friendUidToIntimacyMap.keySet().stream()
                        .skip(offset)
                        .limit(req.getPageNum())
                        .collect(Collectors.toList());
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info("player uid:{}, friendType:{}, sortType:{}, page:{}, friend list:{}",
                            player.getUid(), req.getFriendType(), sortType, req.getPage(), uidList);
                }

                // 基于uid列表获取用户信息列表
                Map<Long, TcaplusDb.PlayerPublic> playerPublicList = getPlayerPublicInfo(uidList);
                if (playerPublicList == null || playerPublicList.isEmpty()) {
                    return fillResponse(res, 0, 0);
                }

                // 填充好友信息
                res = fillFriendInfo(res, playerPublicList, friendUidToIntimacyMap, uidList);
            }

            int totalPage = friendUidToIntimacyMap.size() % req.getPageNum() == 0 ?
                    friendUidToIntimacyMap.size() / req.getPageNum() :
                    friendUidToIntimacyMap.size() / req.getPageNum() + 1;
            return fillResponse(res, friendUidToIntimacyMap.size(), totalPage);
        }
    }

    /**
     * 通过短号id获取用户uid
     *
     * @param uid
     * @param originalData
     * @return
     */
    private Long getUidByShortUid(long uid, String originalData) {

        if (originalData == null) {
            return 0L;
        }

        // 解析短号uid
        long shortUid = 0L;
        try {
            shortUid = Long.parseLong(originalData);
        } catch (Exception ex) {
            LOGGER.error("parse search short uid failed, uid:{}, originalData:{}", uid, originalData);
        }
        if (shortUid == 0L) {
            return 0L;
        }

        // 拼接seqsvr请求结构体数据
        SsCommon.SeqType seqType = SeqUtil.getUserSeqTypeBySeqId(shortUid);
        SsSeqsvr.RpcQuerySeqInfoReq.Builder reqBuilder = SsSeqsvr.RpcQuerySeqInfoReq.newBuilder()
                .setSeqType(seqType.getNumber())
                .setSeqId(shortUid)
                .setVirtualUuid(SeqUtil.getVirtualHashKey(seqType, shortUid));
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("begin to query seq info, req:{}", reqBuilder);
        }

        Long targetUid = 0L;
        try {
            // 向seqsvr发起一次rpc请求来获取数据
            SeqService service = SeqService.get();
            RpcResult<SsSeqsvr.RpcQuerySeqInfoRes.Builder> rpcResult = service.rpcQuerySeqInfo(reqBuilder);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("end to query seq info, ret:{}, rsp:{}", rpcResult.getRet(), rpcResult.getData());
            }

            if (rpcResult.getRet() != NKErrorCode.OK.getValue() || rpcResult.getData().getSeqInfo() == null) {
                LOGGER.error("query seq info failed, ret:{}, seqInfo:{}",
                        rpcResult.getRet(), rpcResult.getData().getSeqInfo());
            } else {
                targetUid = Long.valueOf(rpcResult.getData().getSeqInfo().getDesc());
            }
        } catch (Exception ex) {
            LOGGER.error("query seq info catch unknown exception, shortUid:{}, uid:{}", shortUid, uid);
        }

        return targetUid;
    }

    /**
     * 通过昵称获取用户uid
     *
     * @param nickName
     * @return
     */
    private Long getUidByNickname(String nickName) {

        Long uid = 0L;

        // 从跨区redis中获取搜索的昵称信息
        NKPair<Long, Long> crossData = ProfileManager.NicknameRedis.searchUidByNickname(nickName);
        if (crossData != null && crossData.getKey() != 0L) {
            uid = crossData.getKey();
        }

        return uid;
    }

    /**
     * 获取好友的uid->亲密度的映射关系
     *
     * @param player
     * @param friendType
     * @return
     */
    private Map<Long, Long> getFriendUidToIntimacyMap(Player player, int friendType) {

        // 基于好友类型获取好友信息
        Collection<DBRelation> relations = null;

        if (friendType == IdipFriendType.EN_FRIEND_TYPE_GAME_VALUE) {
            relations = player.getFriendManager().getAllGameFriend();
        } else if (friendType == IdipFriendType.EN_FRIEND_TYPE_PLAT_VALUE) {
            relations = player.getFriendManager().getAllPlatFriend();
        }

        if (relations == null || relations.isEmpty()) {
            return null;
        }

        // 将好友信息转换成好友uid->亲密度的映射关系
        Map<Long, Long> uidToIntimacyMap = new LinkedHashMap<>();
        for (DBRelation relation : relations) {
            uidToIntimacyMap.put(relation.getUid(), relation.getHotData().getIntimacy());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("uid:{}, friendType:{}, friend num:{}",
                    player.getUid(), friendType, uidToIntimacyMap.size());
        }

        return uidToIntimacyMap;
    }

    /**
     * 获取排序规则下的好友的uid->亲密度的映射关系
     *
     * @param player
     * @param friendType
     * @param sortType
     * @return
     */
    private Map<Long, Long> getSortedFriendUidToIntimacyMap(Player player, int friendType, int sortType) {

        SrFriendintimacy.PlayerFriendSortInfoListReq.Builder sortInfoListBuilder = null;

        if (sortType == IdipFriendSortType.EN_FRIEND_SORT_TYPE_ONLINE_AND_Relation_VALUE) {
            // 基于redis获取排序规则下的好友排序信息列表
            sortInfoListBuilder = getPlayerFriendSortInfoListFromRedis(player.getUid(), friendType, sortType);
        }

        if (sortInfoListBuilder == null) {
            // 基于好友类型获取好友信息
            Collection<DBRelation> relations = null;

            if (friendType == IdipFriendType.EN_FRIEND_TYPE_GAME_VALUE) {
                relations = player.getFriendManager().getAllGameFriend();
            } else if (friendType == IdipFriendType.EN_FRIEND_TYPE_PLAT_VALUE) {
                relations = player.getFriendManager().getAllPlatFriend();
            }

            if (relations == null || relations.isEmpty()) {
                return null;
            }

            // 将好友信息转换成uid+在线状态+亲密度的结构体列表数据
            List<SrFriendintimacy.PlayerFriendSortInfo> sortInfoList = new ArrayList<>();
            for (DBRelation relation : relations) {
                SrFriendintimacy.PlayerFriendSortInfo.Builder sortInfoBuilder = SrFriendintimacy.PlayerFriendSortInfo.newBuilder()
                        .setUid(relation.getUid())
                        .setIsOnline(PlayerStateMgr.isOnline(relation.getHotData().getPlayerStatus()) ? 1 : 0)
                        .setIntimacy(relation.getHotData().getIntimacy());
                sortInfoList.add(sortInfoBuilder.build());
            }

            sortInfoListBuilder = SrFriendintimacy.PlayerFriendSortInfoListReq.newBuilder();
            if (sortType == IdipFriendSortType.EN_FRIEND_SORT_TYPE_ONLINE_AND_Relation_VALUE) {
                // 根据在线状态和亲密度排序
                sortInfoList.sort(Comparator.comparing(SrFriendintimacy.PlayerFriendSortInfo::getIsOnline,
                                Comparator.reverseOrder())
                        .thenComparing(SrFriendintimacy.PlayerFriendSortInfo::getIntimacy, Comparator.reverseOrder()));
                sortInfoListBuilder.addAllList(sortInfoList);

                // 将排序后的数据设置到redis中
                setPlayerFriendSortInfoListToRedis(player.getUid(), friendType, sortType, sortInfoListBuilder);
            } else {
                sortInfoListBuilder.addAllList(sortInfoList);
            }
        }

        if (sortInfoListBuilder == null || sortInfoListBuilder.getListList().isEmpty()) {
            return null;
        }

        // 将好友信息转换成好友uid->亲密度的映射关系
        Map<Long, Long> uidToIntimacyMap = new LinkedHashMap<>();
        for (SrFriendintimacy.PlayerFriendSortInfo sortInfoBuilder : sortInfoListBuilder.getListList()) {
            uidToIntimacyMap.put(sortInfoBuilder.getUid(), sortInfoBuilder.getIntimacy());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("uid:{}, friendType:{}, sortType:{}, friend:{}",
                    player.getUid(), friendType, sortType, uidToIntimacyMap);
        }

        return uidToIntimacyMap;
    }

    /**
     * 批量获取玩家信息
     *
     * @param uidList
     * @return
     */
    private Map<Long, TcaplusDb.PlayerPublic> getPlayerPublicInfo(List<Long> uidList) {

        if (uidList == null || uidList.isEmpty()) {
            return null;
        }

        return PlayerPublicDao.batchGetPlayerPublicMap(uidList, PlayerPublicDao.PlayerPublicAttrKey.PublicProfile,
                PlayerPublicDao.PlayerPublicAttrKey.PublicBasicInfo, PlayerPublicDao.PlayerPublicAttrKey.PublicGameData,
                PlayerPublicDao.PlayerPublicAttrKey.PublicLiveStatus);
    }

    /**
     * 填充好友信息数据
     *
     * @param playerPublicInfo
     * @param uidToIntimacyMap
     * @param uidList
     * @return
     */
    private SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder fillFriendInfo(
            SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder res,
            Map<Long, TcaplusDb.PlayerPublic> playerPublicInfo,
            Map<Long, Long> uidToIntimacyMap, List<Long> uidList) {

        if (res == null || playerPublicInfo == null || playerPublicInfo.isEmpty()) {
            return res;
        }

        Integer worldId = Framework.getInstance().getWorldId();
        String envFlag = PropertyFileReader.getItem("env_flag", "");

        // 遍历好友列表, 填充好友信息
        for (long uid : uidList) {
            TcaplusDb.PlayerPublic playerPublic = playerPublicInfo.get(uid);
            if (playerPublic == null) {
                continue;
            }

            int rankDegree = playerPublic.getPublicGameData().getQualifyingInfo().getDegreeTypeInt() != 0 ?
                    playerPublic.getPublicGameData().getQualifyingInfo().getDegreeTypeInt() :
                    playerPublic.getPublicGameData().getQualifyingInfo().getDegreeType().getNumber();
            SsGamesvr.RpcGetPlayerFriendListForIdipRes.FriendInfoForIdip.Builder builder =
                    SsGamesvr.RpcGetPlayerFriendListForIdipRes.FriendInfoForIdip.newBuilder()
                            .setOpenId(playerPublic.getPublicProfile().getOpenId())
                            .setUid(playerPublic.getUid())
                            .setNickname(playerPublic.getPublicProfile().getNickname())
                            .setProfile(playerPublic.getPublicProfile().getProfile())
                            .setRelation(uidToIntimacyMap.containsKey(playerPublic.getUid()) ?
                                    uidToIntimacyMap.get(playerPublic.getUid()) : 0)
                            .setIsOnline(
                                    playerPublic.getPublicLiveStatus().getStatus() == PlayerStateType.PST_Offline ? 0
                                            : 1)
                            .setLastOfflineTime(playerPublic.getPublicBasicInfo().getLogoutTimeMs() / 1000)
                            .setLevel(playerPublic.getPublicProfile().getLevel())
                            .setRankDegree(rankDegree)
                            .setRankDegreeID(playerPublic.getPublicGameData().getQualifyingInfo().getDegreeID())
                            .setRankDegreeStar(playerPublic.getPublicGameData().getQualifyingInfo().getDegreeStar())
                            .setArea(ServerIdipAreaConfig.getInstance().getFormalIdipArea(worldId, envFlag,
                                    playerPublic.getPublicBasicInfo().getAccountType().getNumber()))
                            .setShortUid(playerPublic.getPublicProfile().getShortUid())
                            .setPlatId(playerPublic.getPublicProfile().getPlatId())
                            .setGender(playerPublic.getPublicProfile().getGender())
                            .setPlatNickName(playerPublic.getPublicProfile().hasPlatNickName() ?
                                    playerPublic.getPublicProfile().getPlatNickName() : "");
            res.addFriendList(builder);
        }

        return res;
    }

    /**
     * 填充回包结构体数据
     *
     * @param res
     * @param totalNum
     * @param totalPage
     * @return
     */
    private SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder fillResponse(
            SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder res, int totalNum, int totalPage) {

        if (res == null) {
            return res;
        }

        res.setTotalNum(totalNum);
        res.setTotalPage(totalPage);
        res.setResult(0);

        return res;
    }

    /**
     * 从reids中获取玩家好友排序信息列表
     *
     * @param uid
     * @param friendType
     * @param sortType
     * @return
     */
    private SrFriendintimacy.PlayerFriendSortInfoListReq.Builder getPlayerFriendSortInfoListFromRedis(long uid,
                                                                                                      int friendType,
                                                                                                      int sortType) {
        String key = getPlayerFriendSortInfoRedisKey(uid, friendType, sortType);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("redis get player friend sort info list, redis key:{}", key);
        }

        try {
            CoRedisCmd<String, SrFriendintimacy.PlayerFriendSortInfoListReq.Builder> coRedisCmd = Cache.getCoRedisCmdForPb();
            String redisKey = CacheUtil.PlayerFriendSortInfo.getKey(key);
            return coRedisCmd.get(redisKey);
        } catch (Exception ex) {
            LOGGER.error("get player friend sort info list from redis error, redis key:{}", key, ex);
        }

        return null;
    }

    /**
     * 设置玩家好友排序信息列表到redis中
     *
     * @param uid
     * @param friendType
     * @param sortType
     * @param builder
     * @return
     */
    private boolean setPlayerFriendSortInfoListToRedis(long uid, int friendType, int sortType,
                                                       SrFriendintimacy.PlayerFriendSortInfoListReq.Builder builder) {

        if (builder == null) {
            return false;
        }

        String key = getPlayerFriendSortInfoRedisKey(uid, friendType, sortType);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("redis store player friend sort info list, redis key:{}", key);
        }

        try {
            CoRedisCmd<String, SrFriendintimacy.PlayerFriendSortInfoListReq.Builder> coRedisCmd = Cache.getCoRedisCmdForPb();
            String redisKey = CacheUtil.PlayerFriendSortInfo.getKey(key);
            String res = coRedisCmd.setnxex(redisKey, builder, 180);
            if (CacheUtil.REDIS_SET_RESULT_OK.equals(res)) {
                return true;
            }
        } catch (Exception ex) {
            LOGGER.error("setPlayerFriendSortInfoListToRedis catch exception, uid:{}, sortType:{}, ", key, ex);
        }

        return false;
    }

    /**
     * 获取玩家好友排序信息redis key
     *
     * @param uid
     * @param friendType
     * @param sortType
     * @return
     */
    private String getPlayerFriendSortInfoRedisKey(long uid, int friendType, int sortType) {

        StringBuilder stringBuilder = new StringBuilder().append(uid)
                .append("_").append(friendType)
                .append("_").append(sortType);
        return stringBuilder.toString();
    }

    /**
     * 设置亲密好友上线提醒
     *
     * @param openStatus
     */
    public void setIntimateOnlineNotice(FeatureOpenStatus openStatus) {
        player.getUserAttr().getPlayerPublicGameSettings().setIntimateOnlineNotice(openStatus);
        addChangeField(INTIMATE_ONLINE_NOTICE);
    }

    /**
     * 获取玩家设置的亲密好友在线提醒
     *
     * @return
     */
    public FeatureOpenStatus getIntimateOnlineNotice() {
        FeatureOpenStatus status = player.getUserAttr().getPlayerPublicGameSettings().getIntimateOnlineNotice();
        if (status != FeatureOpenStatus.FOS_None) {
            return status;
        }

        return getIntimateOnlineNoticeDefaultStatus();
    }

    /**
     * 通过PublicInfo获取亲密好友在线提醒开关
     *
     * @param publicInfo
     * @return
     */
    public FeatureOpenStatus getIntimateOnlineNoticeByPublicInfo(PlayerPublicInfo.Builder publicInfo) {
        FeatureOpenStatus status = publicInfo.getIntimateOnlineNotice();
        if (status != FeatureOpenStatus.FOS_None) {
            return status;
        }

        return getIntimateOnlineNoticeDefaultStatus();
    }

    /**
     * 获取亲密好友上线提醒默认值
     *
     * @return
     */
    private static FeatureOpenStatus getIntimateOnlineNoticeDefaultStatus() {
        RelationConf relationConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_INTIMATE_ONLINE_NOTICE_DEFAULT_VALUE);
        if (null != relationConf) {
            int defaultValue = relationConf.getValue();
            if (defaultValue >= FeatureOpenStatus.FOS_Open_VALUE && defaultValue <= FeatureOpenStatus.FOS_Close_VALUE) {
                return FeatureOpenStatus.forNumber(defaultValue);
            }
        }

        if (PropertyFileReader.getRealTimeBooleanItem("intimate_online_notice_enable", true)) {
            return FeatureOpenStatus.FOS_Open;
        } else {
            return FeatureOpenStatus.FOS_Close;
        }
    }

    /**
     * 记录亲密好友上线提示时间
     *
     * @param friendUid
     */
    public void recordIntimateRelationOnlineNotice(long friendUid) {
        if (!isFriend(friendUid)) {
            LOGGER.error("player {} record intimate friend {} online notice error, not friend", player.getUid(),
                    friendUid);
            return;
        }

        IntimateRelationOnlineNoticeAttr noticeAttr = player.getUserAttr().getRelationMapInfo().getIntimateNoticeAttr();
        noticeAttr.setLastNoticeTime(Framework.currentTimeMillis());

        IntimateRelationOnlineNoticeDetailAttr noticeDetailAttr = getOrCreateIntimateRelationOnlineNoticeDetail(
                friendUid);
        noticeDetailAttr.setLastNoticeTime(Framework.currentTimeMillis());

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} receive intimate friend {} online notice", player.getUid(), friendUid);
        }
    }

    /**
     * 禁用某个亲密好友上线提醒一段时间
     *
     * @param friendUid
     */
    public void rejectIntimateRelationOnlineNotice(long friendUid) {
        if (!isFriend(friendUid)) {
            LOGGER.error("player {} reject intimate friend {} online notice error, not friend", player.getUid(),
                    friendUid);
            return;
        }

        IntimateRelationOnlineNoticeDetailAttr noticeDetailAttr = getOrCreateIntimateRelationOnlineNoticeDetail(
                friendUid);
        noticeDetailAttr.setRejectNoticeTime(Framework.currentTimeMillis());

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} reject intimate friend {} online notice", player.getUid(), friendUid);
        }
    }

    private IntimateRelationOnlineNoticeDetailAttr getOrCreateIntimateRelationOnlineNoticeDetail(long friendUid) {
        IntimateRelationOnlineNoticeAttr noticeAttr = player.getUserAttr().getRelationMapInfo().getIntimateNoticeAttr();
        IntimateRelationOnlineNoticeDetailAttr noticeDetailAttr = noticeAttr.getNoticeDetail(friendUid);
        if (null == noticeDetailAttr) {
            noticeDetailAttr = new IntimateRelationOnlineNoticeDetailAttr();
            noticeDetailAttr.setUid(friendUid);
            noticeAttr.putNoticeDetail(friendUid, noticeDetailAttr);
        }

        return noticeDetailAttr;
    }

    /**
     * 检查并清理亲密好友上线提示信息
     */
    private void checkAndRemoveIntimateNoticeAttr() {
        IntimateRelationOnlineNoticeAttr noticeAttr = player.getUserAttr().getRelationMapInfo().getIntimateNoticeAttr();
        if (noticeAttr.getNoticeDetail().isEmpty()) {
            return;
        }

        if (!PropertyFileReader.getRealTimeBooleanItem("remove_friend_online_notice_attr_enable", true)) {
            return;
        }

        long noticeIntervalTimeMs = 0L;
        ResRelation.RelationConf noticeIntervalConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_INTIMATE_ONLINE_NOTICE_INTERVAL_MIN_FOR_ONE);
        if (null != noticeIntervalConf) {
            noticeIntervalTimeMs = noticeIntervalConf.getValue() * DateUtils.ONE_MINUTE_MILLIS;
        } else {
            noticeIntervalTimeMs =
                    PropertyFileReader.getRealTimeLongItem("intimate_relation_online_notice_interval_min", 30)
                            * DateUtils.ONE_MINUTE_MILLIS;
        }

        long rejectExpireTimeMs = 0L;
        ResRelation.RelationConf rejectNoticeConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCI_INTIMATE_ONLINE_NOTICE_REJECT_EXPIRE_MIN);
        if (null != rejectNoticeConf) {
            rejectExpireTimeMs = rejectNoticeConf.getValue() * DateUtils.ONE_MINUTE_MILLIS;
        } else {
            rejectExpireTimeMs =
                    PropertyFileReader.getRealTimeIntItem("intimate_relation_reject_online_notice_time_min", 4320)
                            * DateUtils.ONE_MINUTE_MILLIS;
        }

        List<Long> friendUidList = new ArrayList<>(noticeAttr.getNoticeDetail().keySet());
        for (long friendId : friendUidList) {
            IntimateRelationOnlineNoticeDetailAttr noticeDetailAttr = noticeAttr.getNoticeDetail(friendId);
            if (null == noticeDetailAttr) {
                noticeAttr.removeNoticeDetail(friendId);
                continue;
            }

            if (noticeDetailAttr.getLastNoticeTime() > 0 && (noticeDetailAttr.getLastNoticeTime() + noticeIntervalTimeMs
                    > Framework.currentTimeMillis())) {
                continue;
            }

            if (noticeDetailAttr.getRejectNoticeTime() > 0 && (
                    noticeDetailAttr.getRejectNoticeTime() + rejectExpireTimeMs > Framework.currentTimeMillis())) {
                continue;
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player {} intimate friend {} online notice detail remove {}-{}", player.getUid(),
                        noticeDetailAttr.getUid(), noticeAttr.getLastNoticeTime(),
                        noticeDetailAttr.getRejectNoticeTime());
            }

            noticeAttr.removeNoticeDetail(friendId);
        }
    }

    public void setIntimateOnlineNoticeDressItem(boolean setAll, int itemId, long friendUid) {
        // 判断是否拥有这个道具
        if (player.getItemManager().GetItemNumByItemId(itemId) <= 0) {
            LOGGER.error("not has this item,uid:{},itemId:{}", player.getUid(), itemId);
            NKErrorCode.ItemNotExist.throwError("not has this item,uid:{},itemId:{}", player.getUid(), itemId);
        }
        // 判断道具类型
        ResBackpackItem.Item_BackpackItem conf = BackpackItem.getInstance().get(itemId);
        if (conf == null) {
            LOGGER.error("ResBackpackItem not found, uid:{},itemId:{}", player.getUid(), itemId);
            NKErrorCode.ResNotFound.throwError("ResBackpackItem not found, uid:{},itemId:{}", player.getUid(), itemId);
        }
        if (conf.getType() != ItemType.ItemType_FriendOnlineReminder) {
            LOGGER.error("item type error, uid:{},itemId:{}", player.getUid(), itemId);
            NKErrorCode.ItemUnknownItemType.throwError("item type error, uid:{},itemId:{}", player.getUid(),
                    itemId);
        }
        IntimateRelationOnlineNoticeDressItemAttr noticeDressAttr = player.getUserAttr().getRelationMapInfo()
                .getIntimateNoticeAttr().getNoticeDressItem(conf.getType());
        if (noticeDressAttr == null) {
            noticeDressAttr = new IntimateRelationOnlineNoticeDressItemAttr().setItemType(conf.getType());
            player.getUserAttr().getRelationMapInfo().getIntimateNoticeAttr()
                    .putNoticeDressItem(conf.getType(), noticeDressAttr);
        }
        int beforeItem = noticeDressAttr.getAllDressItem();
        // 如果是setAll，清除单个亲密好友的设置
        if (setAll) {
            noticeDressAttr.clearSingleDressItem();
            noticeDressAttr.setAllDressItem(itemId);
        } else {
            // 非setAll，检查是否是亲密好友
            if (!player.getIntimateManager().isRelation(friendUid)) {
                LOGGER.error("NotIntimateRelation, uid:{},friend:{}", player.getUid(), friendUid);
                NKErrorCode.NotIntimateRelation.throwError("NotIntimateRelation, uid:{},friend:{}", player.getUid(),
                        friendUid);
            }
            IntimateRelationOnlineNoticeSingleDressItem oldDress = noticeDressAttr.getSingleDressItem().put(friendUid,
                    new IntimateRelationOnlineNoticeSingleDressItem().setUid(friendUid).setItemId(itemId));
            if (oldDress != null) {
                beforeItem = oldDress.getItemId();
            }
        }
        TlogFlowMgr.sendLoginFrameChangeFlow(player, itemId, beforeItem, true, setAll, friendUid);
    }

    //----------------------------------------- 时装租借相关 ---------------------------------------//

    /**
     * 租借好友时装
     *
     * @param friendUid
     * @param itemId
     */
    public void rentFriendFashion(long friendUid, int itemId) {
        int costItemId = checkRentFashionCondition(friendUid, itemId);

        ResRelation.RelationConf expireConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCI_FRIEND_RENT_FASHION_VALIDITY_DURATION_HOUR);
        if (null == expireConf || expireConf.getValue() <= 0) {
            NKErrorCode.RentFriendFashionExpireConfErr.throwError(
                    "player {} rent friend {} fashion {} error, expire config error",
                    player.getUid(), friendUid, itemId);
        }

        // 增加对方的租借次数
        long friendRentCnt = tryIncreaseFriendRentCount(friendUid, itemId);

        String billNo = BillNoIdGenerator.getBusinessBillNo("RentFashion");
        if (costItemId > 0) {
            player.getBagManager().MinItem(costItemId, 1, ItemChangeReason.ICR_RentFriendFashion, friendUid, billNo);
        }

        ItemInfo.Builder fashionItem = ItemInfo.newBuilder()
                .setItemId(itemId)
                .setItemNum(1)
                .setExpireTimeMs(Framework.currentTimeMillis() + expireConf.getValue() * DateUtils.ONE_HOUR_MILLIS)
                .setExpireType(ItemExpireType.IET_ABSOLUTE_VALUE)
                .setLessorUid(friendUid);
        NKPair<NKErrorCode, ItemChangeDetails> result = player.getBagManager()
                .AddItems2(Collections.singletonList(fashionItem.build()), ItemChangeReason.ICR_RentFriendFashion);

        TlogFlowMgr.sendRentFriendFashionFlow(player, friendUid, itemId, result.getKey().getValue(), result.getValue(),
                billNo, friendRentCnt);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} rent friend {} fashion item {} result {}, friend rent cnt {}",
                    player.getUid(), friendUid, itemId, result.getKey(), friendRentCnt);
        }
    }

    /**
     * 增加好友的租借次数
     *
     * @param friendUid
     * @param itemId
     * @return
     */
    private long tryIncreaseFriendRentCount(long friendUid, int itemId) {
        ResRelation.RelationConf limitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCI_FRIEND_RENT_FASHION_VALIDITY_DURATION_HOUR);
        if (null == limitConf) {
            NKErrorCode.FriendRentCountLimitExceed.throwError(
                    "player {} rent friend {} fashion {} error, get limit config null",
                    player.getUid(), friendUid, itemId);
        }

        if (limitConf.getValue() <= 0) {
            NKErrorCode.FriendRentCountLimitExceed.throwError(
                    "player {} rent friend {} fashion {} error, limit config invalid",
                    player.getUid(), friendUid, itemId);
        }

        ResSeason.SeasonConf seasonConf = SeasonConfData.getInstance().getCurrOrLatestSeason();
        if (null == seasonConf) {
            NKErrorCode.FriendRentCountLimitExceed.throwError(
                    "player {} rent friend {} fashion {} error, get season config null",
                    player.getUid(), friendUid, itemId);
        }

        int maxCount = limitConf.getValue();
        int seasonId = seasonConf.getSeasonId();
        // 计算赛季结束到当前时间的秒数，增加1天的buff，避免在失效时操作
        long expireSec = seasonConf.getEndTime().getSeconds() - Framework.currentTimeSec() + DateUtils.ONE_DAY_SECONDS;
        String key = "RentFashionCnt_" + friendUid + "_" + seasonId;
        CacheResult<Long> result = CacheUtil.RentFashion.incrWithExpire(key, 1, maxCount, expireSec);
        if (!result.isOk() || result.val == null || result.val <= 0) {
            NKErrorCode.FriendRentCountLimitExceed.throwError(
                    "player {} rent friend {} fashion {} error, friend rent count limit exceed {}, season id {}, expire sec {}",
                    player.getUid(), friendUid, itemId, maxCount, seasonId, expireSec);
        }

        return result.val;
    }

    /**
     * 检查租借时装条件
     *
     * @param friendUid
     * @param itemId
     * @return
     */
    private int checkRentFashionCondition(long friendUid, int itemId) {
        // 租借时间限制
        if (!checkFashionRentTime()) {
            NKErrorCode.RentFriendFashionRentTimeError.throwError(
                    "player {} rent friend {} fashion {} error, not in rent duration",
                    player.getUid(), friendUid, itemId);
        }

        if (!isFriend(friendUid)) {
            NKErrorCode.FriendNotGameFriend.throwError("player {} rent friend {} fashion {} error, not friend",
                    player.getUid(), friendUid, itemId);
        }

        // 亲密度等级
        int intimateLevel = getFriendIntimacyLevel(friendUid);
        ResRelation.RelationConf intimateLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCI_FRIEND_RENT_FASHION_NEED_INTIMATE_LEVEL);
        if (null != intimateLimitConf && intimateLevel < intimateLimitConf.getValue()) {
            NKErrorCode.RentFriendFashionIntimateLvlErr.throwError(
                    "player {} rent friend {} fashion {} error, intimate level {} error",
                    player.getUid(), friendUid, itemId, intimateLevel);
        }

        // 租借卡数量
        int costItemId = 0;
        ResRelation.RelationConf costItemConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCI_FRIEND_RENT_FASHION_ITEM_ID);
        if (null != costItemConf && costItemConf.getValue() > 0) {
            costItemId = costItemConf.getValue();
            if (player.getBagManager().getItemNumByItemId(costItemId) <= 0) {
                NKErrorCode.RentFriendFashionItemNotEnough.throwError(
                        "player {} rent friend {} fashion {} error, cost item {} not enough",
                        player.getUid(), friendUid, itemId, costItemId);
            }
        }

        // 玩家是否已经拥有该套装
        HashMap<Long, Item> fashionItemMap = player.getItemManager().getItemsByItemId(itemId);
        if (!fashionItemMap.isEmpty()) {
            NKErrorCode.RentFriendFashionAlreadyHaveSuit.throwError(
                    "player {} rent friend {} fashion {} error, already have this suit",
                    player.getUid(), friendUid, itemId);
        }

        checkFriendFashionCanRent(friendUid, itemId);

        return costItemId;
    }

    /**
     * 检查好友的时装是否可以被租借
     *
     * @param friendUid
     * @param itemId
     */
    private void checkFriendFashionCanRent(long friendUid, int itemId) {
        ResBackpackItem.Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (null == itemConf) {
            NKErrorCode.RentFriendFashionItemConfError.throwError(
                    "player {} rent friend {} fashion {} error, item conf null",
                    player.getUid(), friendUid, itemId);
        }

        if (!ItemType.ItemType_Suit.equals(itemConf.getType())) {
            NKErrorCode.RentFriendFashionTypeError.throwError(
                    "player {} rent friend {} fashion {} error, item type {} error",
                    player.getUid(), friendUid, itemId, itemConf.getType());
        }

        Map<Long, PlayerPublic> playerPubicMap = PlayerPublicDao.batchGetPlayerPublicMap(
                Collections.singletonList(friendUid),
                PlayerPublicAttrKey.PublicEquipments);
        if (null == playerPubicMap || playerPubicMap.isEmpty()) {
            NKErrorCode.RentFriendFashionGetPublicError.throwError("get player {} public error null", friendUid);
        }

        PlayerPublic playerPublic = playerPubicMap.get(friendUid);
        if (null == playerPublic) {
            NKErrorCode.RentFriendFashionGetPublicError.throwError("get player {} public error null2", friendUid);
        }

        // 检查对方是否有该套装
        if (!checkFriendHaveFashion(playerPublic, itemId)) {
            NKErrorCode.RentFriendFashionTargetNotHaveSuit.throwError(
                    "player {} rent friend {} fashion {} error, target not have this suit",
                    player.getUid(), friendUid, itemId);
        }

        // 检查对方租借数量限制
    }

    /**
     * 检查对方是否有某个时装
     *
     * @param playerPublic
     * @param itemId
     * @return
     */
    private boolean checkFriendHaveFashion(PlayerPublic playerPublic, int itemId) {
        for (AttrSeasonFashion.proto_SeasonFashion seasonFashion : playerPublic.getPublicEquipments()
                .getFashionValuesList()) {
            if (seasonFashion.getEquipBookList().isEmpty()) {
                continue;
            }

            for (AttrSeasonFashionEquipBook.proto_SeasonFashionEquipBook equipBook : seasonFashion.getEquipBookList()) {
                if (equipBook.getItemType() != ItemType.ItemType_Suit) {
                    continue;
                }

                for (int fashionItemId : equipBook.getItemIdList()) {
                    if (fashionItemId == itemId) {
                        return true;
                    }
                }
                break;
            }
        }

        return false;
    }

    /**
     * 检查获取好友租借时装列表频率
     */
    public void checkGetFriendRentFashionListFrequency() {
        long intervalMs = PropertyFileReader.getRealTimeLongItem("get_friend_rent_fashion_list_interval_ms", 2000L);
        if (lastGetFriendRentFashionListTimeMs > 0 && (lastGetFriendRentFashionListTimeMs + intervalMs
                > Framework.currentTimeMillis())) {
            NKErrorCode.GetFriendRentFashionListTooOften.throwError("player {} get friend rent fashion list too often",
                    player.getUid());
        }

        lastGetFriendRentFashionListTimeMs = Framework.currentTimeMillis();
    }

    /**
     * 获取历史亲密度
     *
     * @param friendUid
     * @return 移除前的亲密度
     */
    private long getHistoryIntimacy(long friendUid, long addFriendTime) {
        if (!PropertyFileReader.getRealTimeBooleanItem("recover_removed_friend_intimacy_enable", true)) {
            return 0;
        }

        SrFriendintimacy.RemovedFriendHistoryDataReq.Builder historyData = RemovedFriendHistoryDao.getRemoveFriendHistory(
                player.getUid(), friendUid);
        if (null == historyData) {
            return 0;
        }

        long limitTimeMs = 0;
        ResRelation.RelationConf limitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCI_RECOVER_FRIEND_REMOVE_INTIMACY_LIMIT_HOUR);
        if (null == limitConf) {
            limitTimeMs = PropertyFileReader.getRealTimeIntItem("recover_removed_friend_intimacy_limit_hour", 168)
                    * DateUtils.ONE_HOUR_MILLIS;
        } else {
            limitTimeMs = limitConf.getValue() * DateUtils.ONE_HOUR_MILLIS;
        }

        if (historyData.getRemoveTimeMs() + limitTimeMs < addFriendTime) {
            LOGGER.warn("player {} removed friend {} intimacy {} expire {}, add time {}", player.getUid(),
                    friendUid, historyData.getIntimacy(), historyData.getRemoveTimeMs(), addFriendTime);
            return 0;
        }

        LOGGER.info("get playr {} removed friend {} intimacy {} done, remove time {}",
                player.getUid(), friendUid, historyData.getIntimacy(), historyData.getRemoveTimeMs());

        return historyData.getIntimacy();
    }

    /**
     * 清理好友移除历史
     *
     * @param friendUid
     * @return
     */
    private void clearRemovedFriendHistory(long friendUid) {
        RemovedFriendHistoryDao.delRemoveFriendHistory(player.getUid(), friendUid);
    }

    /**
     * 填充需要恢复的数据
     *
     * @param friendUid
     * @param record
     */
    @Override
    protected void fillRelationRecoverData(long friendUid, RelationTable.Builder record) {
        super.fillRelationRecoverData(friendUid, record);

        record.setIntimacy(getHistoryIntimacy(friendUid, Framework.currentTimeMillis()));
        if (record.getIntimacy() > 0) {
            LOGGER.info("recover player {} deleted friend {} intimacy {} done",
                    player.getUid(), friendUid, record.getIntimacy());

            TlogFlowMgr.sendRemoveFriendIntimacyFlow(player, friendUid, 1, record.getIntimacy());
        }
    }

    private void preCheckAddStickFriend(CsRelation.StickFriendScene scene, long friendUid) {
        if (scene == StickFriendScene.SFS_Relationships) {
            DBRelation relation = getRelation(friendUid);
            // 判断当前 player 与对应的 friendUid 是否有亲密关系
            if (relation == null) {
                NKErrorCode.StickEmptyRelationship.throwError("player:{} has no relationship with:{}",
                        player.getUid(), friendUid);
                return;
            }
            // 判断当前 player 与 对应的 friendUid 的亲密关系是否为情侣
            if (player.getIntimateManager().isCoupleRelation(relation.getIntimateId())) {
                NKErrorCode.StickCoupleRelationship.throwError("player:{} has couple relationship with:{}",
                        player.getUid(), friendUid);
            }
        }
    }

    private List<Long> getNewStickFriends(StickFriendInfo stickFriendinfo, long friendUid) {
        List<Long> temporaryStickFriends = new ArrayList<>(stickFriendinfo.getFriendsList());
        if (!temporaryStickFriends.contains(friendUid)) {
            temporaryStickFriends.add(friendUid);
            if (stickFriendinfo.getScene()!=StickFriendScene.SFS_PlatFriend_VALUE){
                translatePlatFriendToGameFriend(friendUid);
            }else {
                // 平台好友执行不用转换关系
            }
        }

        List<Long> newStickFriends = new ArrayList<>();
        for (long uid : temporaryStickFriends) {
            if (stickFriendinfo.getScene() == StickFriendScene.SFS_FriendList_VALUE &&
                    !player.getFriendManager().isGameFriend(uid)) {
                continue;
            }
            if (player.getBlackManager().isBlack(uid) || player.getBlackManager().isBeBlack(uid)) {
                continue;
            }
            newStickFriends.add(uid);
        }
        return newStickFriends;
    }

    private void postCheckAddStickFriends(CsRelation.StickFriendScene scene, List<Long> newStickFriends, long friendUid) {
        int sumSize = getSumFriendAndPlatSize();
        switch (scene.getNumber()) {
            case StickFriendScene.SFS_PlatFriend_VALUE:
            case StickFriendScene.SFS_FriendList_VALUE: {
                //判断 是不是 既是平台好友又是游戏好友
                if (isGameFriend(friendUid) && isPlatFriend(friendUid)){
                    if (sumSize > MiscConf.getInstance().getMiscConf().getStickFriendMax()) {
                        NKErrorCode.StickFriendMax.throwError("uid {} stick plat game  FriendMax", player.getUid());
                    }
                }else {
                    if (sumSize > MiscConf.getInstance().getMiscConf().getStickFriendMax()-1) {
                        NKErrorCode.StickFriendMax.throwError("uid {} stickFriendMax", player.getUid());
                    }
                }
                break;
            }
            case StickFriendScene.SFS_Relationships_VALUE: {
                if (newStickFriends.size() > MiscConf.getInstance().getMiscConf().getStickRelationshipMax()) {
                    NKErrorCode.StickRelationshipsMax.throwError("uid {} stickRelationshipMax", player.getUid());
                }
                break;
            }
        }
    }
    
    private int getSumFriendAndPlatSize() {
        Set<Long>  sumSize = new HashSet<>();
        MapAttrObj<Integer, StickFriendInfo> stickFriends = player.getUserAttr().getStickFriends();
        StickFriendInfo stickFriendInfo = stickFriends.get(StickFriendScene.SFS_FriendList_VALUE);
        StickFriendInfo stickPlatFriendInfo = stickFriends.get(StickFriendScene.SFS_PlatFriend_VALUE);
        Optional.ofNullable(stickFriendInfo)
                .map(StickFriendInfo::getFriendsList)
                .ifPresent(sumSize::addAll);
        Optional.ofNullable(stickPlatFriendInfo)
                .map(StickFriendInfo::getFriendsList)
                .ifPresent(sumSize::addAll);
        return sumSize.size();
    }

    public void addStickFriend(CsRelation.StickFriendScene scene, long friendUid) {
        preCheckAddStickFriend(scene, friendUid);

        StickFriendInfo stickFriendInfo = player.getUserAttr().getStickFriends()
                .computeIfAbsent(scene.getNumber(), x -> new StickFriendInfo().setScene(x));
        List<Long> newStickFriends = getNewStickFriends(stickFriendInfo, friendUid);

        postCheckAddStickFriends(scene, newStickFriends, friendUid);

        stickFriendInfo.clearFriends();

        stickFriendInfo.getFriends().addAll(newStickFriends);

        updatePlayerPublishStickFriend();
        player.getUserAttrMgr().collectAndSyncDirtyToClient();

        doStickTwoRelationStation(scene, friendUid);
    }

    /**
     *  处理同时是游戏好友和平台好友的置顶情况
     * @param scene
     * @param friendUid
     */
    private void doStickTwoRelationStation(StickFriendScene scene, long friendUid) {
        int gameScene = StickFriendScene.SFS_FriendList_VALUE;
        int platScene = StickFriendScene.SFS_PlatFriend_VALUE;
        if (isGameFriend(friendUid) && isPlatFriend(friendUid)){
            if (scene.getNumber() == gameScene){
                //如果传进来是游戏好友，那么平台好友里有也要添加置顶
                addAnotherRelationStick( friendUid, StickFriendScene.SFS_PlatFriend);
                LOGGER.info("player {} add stick game friend and plat friend {}, scene {}", player.getUid(), friendUid, scene.getNumber());
            }else if (scene.getNumber() == platScene){
                //如果传进来是平台好友，那么游戏好友里有也要添加置顶
                addAnotherRelationStick( friendUid, StickFriendScene.SFS_FriendList);
                LOGGER.info("player {} add stick plat friend and game friend {}, scene {}", player.getUid(), friendUid, scene.getNumber());
            }
        }
    }

    private void addAnotherRelationStick( long friendUid, StickFriendScene anotherScene ) {
        StickFriendInfo stickFriendInfo = player.getUserAttr().getStickFriends()
                .computeIfAbsent(anotherScene.getNumber(), x -> new StickFriendInfo().setScene(anotherScene.getNumber()));
        List<Long> newStickFriends = getNewStickFriends(stickFriendInfo, friendUid);
        postCheckAddStickFriends(anotherScene, newStickFriends, friendUid);
        stickFriendInfo.clearFriends();
        stickFriendInfo.getFriends().addAll(newStickFriends);
        updatePlayerPublishStickFriend();
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
    }

    public void removeStickFriendAllScene(long friendUid) {
        for (int scene : player.getUserAttr().getStickFriends().keySet()) {
            StickFriendInfo sf = player.getUserAttr().getStickFriends().get(scene);
            if (sf == null) {
                continue;
            }
            for (int i = sf.getFriendsSize() - 1; i >= 0; i--) {
                if (sf.getFriends().get(i) == friendUid) {
                    sf.getFriends().remove(i);
                }
            }
        }
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
    }

    public void removeStickFriend(CsRelation.StickFriendScene scene, long friendUid) {
        StickFriendInfo stickFriendInfo = player.getUserAttr().getStickFriends().get(scene.getNumber());
        if (stickFriendInfo == null) {
            return;
        }
        for (int i = stickFriendInfo.getFriendsSize() - 1; i >= 0; i--) {
            if (stickFriendInfo.getFriends().get(i) == friendUid) {
                stickFriendInfo.getFriends().remove(i);
            }
        }
        doRemoveStickTwoRelationStation(scene, friendUid);

        updatePlayerPublishStickFriend();
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
    }

    /**
     * 删除置顶 处理同时是游戏好友和平台好友的情况
     * @param scene
     * @param friendUid
     */
    private void doRemoveStickTwoRelationStation(StickFriendScene scene, long friendUid) {
        int gameScene = StickFriendScene.SFS_FriendList_VALUE;
        int platScene = StickFriendScene.SFS_PlatFriend_VALUE;
        if (isGameFriend(friendUid) && isPlatFriend(friendUid)){
            if (scene.getNumber() == platScene){
                //如果传进来是平台好友，那么游戏好友里有也要移除
                removeAnotherRelationStick(friendUid, gameScene);
                LOGGER.info("player {} remove plat game friend scene {} {}", player.getUid(), friendUid, scene.getNumber());
            }else if (scene.getNumber() == gameScene){
                //如果传进来游戏好友，那么平台好友里有也要移除
                removeAnotherRelationStick(friendUid, platScene);
                LOGGER.info("player {} remove game plat friend scene {} {}", player.getUid(), friendUid, scene.getNumber());
            }
        }
    }

    private void removeAnotherRelationStick(long friendUid, int anotherScene) {
        StickFriendInfo stickGameFriendInfo = player.getUserAttr().getStickFriends().get(anotherScene);
        for (int i = stickGameFriendInfo.getFriendsSize() - 1; i >= 0; i--) {
            if (stickGameFriendInfo.getFriends().get(i) == friendUid) {
                stickGameFriendInfo.getFriends().remove(i);
            }
        }
    }

    // 更新玩家公开展示信息
    private void updatePlayerPublishStickFriend() {
        MapAttrObj<Integer, StickFriendInfo> stickFriends = player.getUserAttr().getPlayerPublicProfileInfo()
                .getStickFriends();
        for (StickFriendInfo value : player.getUserAttr().getStickFriends().values()) {
            stickFriends.put(value.getScene(), value.clone());
        }
    }

    public void onPlayerSendGift(long friendUid, int todayTotalTimes, int todayTimes) {
        int addIntimacy = 0;
        LOGGER.debug("today total times = {}, times ={},  friend = {}", todayTotalTimes, todayTimes, friendUid);
        // 每日首次送礼
        if (todayTimes == 1) {
            addIntimacy = getConfValueOrDefault(RelationConfEnum.RCI_SEND_FARM_GIFT_ADD_INTIMACY, 2);
        }
        LOGGER.debug("addIntimacy = {}", addIntimacy);
        if (addIntimacy <= 0) {
            return;
        }

        if (increaseDailyLimitIntimacy(friendUid, addIntimacy, Framework.currentTimeMillis(),
                FriendIntimacyChangeReason.RUR_SEND_GIFT) > 0) {
            sendIntimacyChangeNtf(List.of(friendUid), addIntimacy, FriendIntimacyChangeReason.RUR_SEND_GIFT);
        }
        LOGGER.info("player {} send gift {}, add intimacy {}", player.getUid(), friendUid, addIntimacy);
    }

    /**
     * 玩家访问好友
     *
     * @param friendUid
     * @param reason
     */
    public void onPlayerVisitFriend(long friendUid, FriendIntimacyChangeReason reason) {
        if (!isFriend(friendUid)) {
            return;
        }

        int dailyAddIntimacyCount = 0;
        int addIntimacy = 0;
        int todayInteractCount = 0;
        if (reason == FriendIntimacyChangeReason.RUR_VISIT_FARM) {
            dailyAddIntimacyCount = getConfValueOrDefault(
                    RelationConfEnum.RCI_VISIT_FRIEND_FARM_ADD_INTIMACY_DAILY_COUNT, 0);
            addIntimacy = getConfValueOrDefault(RelationConfEnum.RCI_VISIT_FRIEND_FARM_ADD_INTIMACY, 0);

            todayInteractCount = increaseFriendInteractStatistic(friendUid, FriendInteractDataType.FIDT_VisitFarm, 1);
        } else if (reason == FriendIntimacyChangeReason.RUR_VISIT_XIAOWO) {
            dailyAddIntimacyCount = getConfValueOrDefault(
                    RelationConfEnum.RCI_VISIT_FRIEND_XIAOWO_ADD_INTIMACY_DAILY_COUNT, 0);
            addIntimacy = getConfValueOrDefault(RelationConfEnum.RCI_VISIT_FRIEND_XIAOWO_ADD_INTIMACY, 0);

            todayInteractCount = increaseFriendInteractStatistic(friendUid, FriendInteractDataType.FIDT_VisitXiaoWo, 1);
        }

        if (dailyAddIntimacyCount <= 0 || addIntimacy <= 0) {
            return;
        }

        if (todayInteractCount <= dailyAddIntimacyCount) {
            increaseFriendIntimacy(friendUid, addIntimacy, Framework.currentTimeMillis(), reason);
            LOGGER.info("player {} visit friend {} {}, today visit count {}, add intimacy {}",
                    player.getUid(), friendUid, reason, todayInteractCount, addIntimacy);
        } else if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} visit friend {} {}, count {}", player.getUid(), friendUid, reason,
                    todayInteractCount);
        }
    }

    /**
     * 刷新每日交互数据
     */
    private void checkAndRefreshDailyInteract() {
        AllFriendInteractAttr allFriendInteractAttr = player.getUserAttr().getRelationMapInfo()
                .getAllFriendInteractAttr();
        if (DateUtils.isSameDay(allFriendInteractAttr.getDailyInteractRefreshTimeMs(), Framework.currentTimeMillis())) {
            return;
        }

        allFriendInteractAttr.setDailyInteractRefreshTimeMs(Framework.currentTimeMillis());
        allFriendInteractAttr.clearDailyInteractAttr();
        allFriendInteractAttr.clearDailyInteractStatistic();

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} refresh daily friend interact attr", player.getUid());
        }
    }

    /**
     * 转换好友的交互数据
     */
    private void transferFriendVisitData() {
        AllFriendInteractAttr allFriendInteractAttr = player.getUserAttr().getRelationMapInfo()
                .getAllFriendInteractAttr();
        if (allFriendInteractAttr.getDailyInteractAttrSize() <= 0) {
            return;
        }

        for (FriendInteractAttr interactAttr : allFriendInteractAttr.getDailyInteractAttr().values()) {
            if (interactAttr.getInteractDetailSize() <= 0) {
                continue;
            }

            for (FriendInteractDetailAttr detailAttr : interactAttr.getInteractDetail().values()) {
                FriendInteractDataType targetType = FriendInteractDataType.FIDT_None;
                if (detailAttr.getType() == FriendIntimacyChangeReason.RUR_VISIT_FARM.getNumber()) {
                    targetType = FriendInteractDataType.FIDT_VisitFarm;
                } else if (detailAttr.getType() == FriendIntimacyChangeReason.RUR_VISIT_XIAOWO.getNumber()) {
                    targetType = FriendInteractDataType.FIDT_VisitXiaoWo;
                }

                if (targetType != FriendInteractDataType.FIDT_None) {
                    increaseFriendInteractStatistic(interactAttr.getUid(), targetType, detailAttr.getInteractCount());
                    LOGGER.info("player {} transfer friend {} interact data {}-{} to {}", player.getUid(),
                            interactAttr.getUid(), detailAttr.getType(), detailAttr.getInteractCount(), targetType);
                }
            }
        }

        allFriendInteractAttr.clearDailyInteractAttr();
    }

    private FriendInteractDetailAttr getOrCreateInteractDetail(MapAttrObj<Long, FriendInteractAttr> attrMap,
                                                               long friendUid, int interactType) {
        FriendInteractAttr interactAttr = attrMap.get(friendUid);
        if (null == interactAttr) {
            interactAttr = new FriendInteractAttr();
            interactAttr.setUid(friendUid);
            attrMap.put(friendUid, interactAttr);
        }

        FriendInteractDetailAttr interactDetailAttr = interactAttr.getInteractDetail(interactType);
        if (null == interactDetailAttr) {
            interactDetailAttr = new FriendInteractDetailAttr();
            interactDetailAttr.setType(interactType);
            interactAttr.putInteractDetail(interactType, interactDetailAttr);
        }

        return interactDetailAttr;
    }

    public HashMap<Long, Long> getAllFriendUidToCreatorId() {
        HashMap<Long, Long> uidToCreatorIdMap = new HashMap<>();
        for (DBRelation info : dbRelationInfo.getModRelationInfo().values()) {
            uidToCreatorIdMap.put(info.getUid(), info.getHotData().getCreatorId());
        }

        for (DBRelation info : platFriendInfo.getPlatFriendInfo().values()) {
            uidToCreatorIdMap.put(info.getUid(), info.getHotData().getCreatorId());
        }
        return uidToCreatorIdMap;
    }

    public HashMap<Long, Long> getAllFriendCreatorIdToUid() {
        HashMap<Long, Long> creatorIdToUidMap = new HashMap<>();
        for (DBRelation info : dbRelationInfo.getModRelationInfo().values()) {
            creatorIdToUidMap.put(info.getHotData().getCreatorId(), info.getUid());
        }

        for (DBRelation info : platFriendInfo.getPlatFriendInfo().values()) {
            creatorIdToUidMap.put(info.getHotData().getCreatorId(), info.getUid());
        }
        return creatorIdToUidMap;
    }

    public UgcFriendInfo getUgcFriendInfo() {
        return ugcFriendInfo;
    }

    //-------------------------------- 亲密度提升计划 ----------------------------------------//

    /**
     * 增加好友交互次数
     */
    public int increaseFriendInteractStatistic(long friendUid, FriendInteractDataType dataType, int addCnt) {
        // 组队游戏只增加一次，由uid小的一方增加
        if (dataType != FriendInteractDataType.FIDT_TeamBattleCnt || player.getUid() < friendUid) {
            increaseFriendInteractHistoryStatistic(friendUid, dataType, addCnt);
        }

        return increaseFriendDailyInteractStatistic(friendUid, dataType, addCnt);
    }

    /**
     * 增加每日交互次数统计
     */
    protected int increaseFriendDailyInteractStatistic(long friendUid, FriendInteractDataType dataType, int addCnt) {
        if (!DAILY_INTERACT_STATISTIC_TYPE_SET.contains(dataType)) {
            return 0;
        }

        AllFriendInteractAttr allFriendInteractAttr = player.getUserAttr().getRelationMapInfo()
                .getAllFriendInteractAttr();
        FriendInteractDetailAttr interactDetailAttr = getOrCreateInteractDetail(
                allFriendInteractAttr.getDailyInteractStatistic(), friendUid, dataType.getNumber());
        interactDetailAttr.addInteractCount(addCnt);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} friend {} add daily interact statistic {}, add count {}, total count {}",
                    player.getUid(), friendUid, dataType, addCnt, interactDetailAttr.getInteractCount());
        }

        return interactDetailAttr.getInteractCount();
    }

    /**
     * 缓存好友历史交互数据
     */
    public void cacheFriendInteractHistory() {
        if (!friendHistoryInteractStatisticEnable()) {
            return;
        }

        if (!friendHistoryInteractCacheEnable()) {
            return;
        }

        // 离线后好友的操作不会同步过来
//        // 已有缓存，则不进行缓存
//        if (!friendInteractHistoryDataMap.isEmpty()) {
//            return;
//        }

        List<Long> friendUidList = getNeedCacheInteractHistoryFriendList();
        if (friendUidList.isEmpty()) {
            return;
        }

        try {
            Map<Long, FriendHistoryInteractData.Builder> interactHistoryMap = FriendHistoryInteractTableDao.batchLoadFriendInteractHistory(
                    player.getUid(), friendUidList);
            friendInteractHistoryDataMap.putAll(interactHistoryMap);

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("cache player {} friend interact history size {}", player.getUid(),
                        interactHistoryMap.size());
            }

        } catch (Exception e) {
            LOGGER.error("cache player {} friend interact history error ", player.getUid(), e);
        }
    }

    /**
     * 获取需要缓存交互历史数据的好友UID列表
     *
     * @return
     */
    private List<Long> getNeedCacheInteractHistoryFriendList() {
        List<Long> friendUidList = new ArrayList<>(dbRelationInfo.getModRelationInfo().size());

        int dayDiff = PropertyFileReader.getRealTimeIntItem("cache_friend_interact_history_day_diff", 3);
        int limit = getFriendHistoryInteractCacheLimit();
        int count = 0;
        for (DBRelation dbRelation : dbRelationInfo.getModRelationInfo().values()) {
            if (dbRelation.getHotData().getRecentInteractTs() <= 0) {
                continue;
            }

            if (DateUtils.getDayInterval(dbRelation.getHotData().getRecentInteractTs(), Framework.currentTimeMillis())
                    <= dayDiff) {
                friendUidList.add(dbRelation.getUid());

                if (++count >= limit) {
                    break;
                }
            }
        }

        return friendUidList;
    }

    /**
     * 更新好友历史交互数据统计缓存
     *
     * @param friendUid
     * @param interactHistory
     */
    public void updateFriendInteractHistoryCache(long friendUid, FriendHistoryInteractData.Builder interactHistory) {
        if (null == interactHistory) {
            return;
        }

        if (!friendHistoryInteractCacheEnable()) {
            return;
        }

        // 平台好友会统计数据，但是不缓存
        if (!isGameFriend(friendUid)) {
            return;
        }

        friendInteractHistoryDataMap.put(friendUid, interactHistory);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} update friend {} interact history cache, total size {}",
                    player.getUid(), friendUid, friendInteractHistoryDataMap.size());
        }
    }

    /**
     * 更新好友历史交互数据缓存的某一项数据
     *
     * @param friendUid
     * @param dataType
     * @param newValue
     */
    public void updateFriendInteractHistoryCacheField(long friendUid, FriendInteractDataType dataType, int newValue,
                                                      boolean syncToFriend) {
        if (!friendHistoryInteractCacheEnable()) {
            return;
        }

        // 平台好友会统计数据，但是不缓存
        if (!isGameFriend(friendUid)) {
            return;
        }

        FriendHistoryInteractData.Builder interactHistory = friendInteractHistoryDataMap.get(friendUid);
        if (null != interactHistory) {
            FriendHistoryInteractTableDao.updateInteractHistoryData(interactHistory, dataType, newValue);

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player {} update friend {} history interact data {}-{}", player.getUid(),
                        friendUid, dataType, newValue);
            }
        }

        if (syncToFriend && isFriendOnline(friendUid)) {
            try {
                SsGamesvr.RpcSyncFriendInteractHistoryReq.Builder syncReq = SsGamesvr.RpcSyncFriendInteractHistoryReq.newBuilder()
                        .setUid(friendUid)
                        .setFriendUid(player.getUid())
                        .setType(dataType)
                        .setNewValue(newValue);

                GameService.get().rpcSyncFriendInteractHistory(syncReq);
            } catch (Exception e) {
                LOGGER.error("player {} sync history interact data {}-{} to friend {} error ",
                        player.getUid(), dataType, newValue, friendUid, e);
            }
        }
    }

    public static boolean friendHistoryInteractStatisticEnable() {
        return PropertyFileReader.getRealTimeBooleanItem("friend_history_interact_statistic_enable", false);
    }

    public static boolean friendHistoryInteractCacheEnable() {
        return PropertyFileReader.getRealTimeBooleanItem("friend_history_interact_cache_enable", false);
    }

    public static int getFriendHistoryInteractCacheLimit() {
        return PropertyFileReader.getRealTimeIntItem("friend_history_interact_cache_limit", 100);
    }

    /**
     * 获取好友交互历史数据
     *
     * @param friendUid
     * @return
     */
    public void getFriendInteractHistoryStatistic(long friendUid,
                                                  CsRelation.GetFriendDailyInteractData_S2C_Msg.Builder ret) {
        if (!friendHistoryInteractStatisticEnable()) {
            return;
        }

        DBRelation modRelationInfo = dbRelationInfo.getModRelationInfo(friendUid);
        if (null == modRelationInfo) {
            return;
        }

        FriendHistoryInteractData.Builder interactHistory = null;
        try {
            interactHistory = friendInteractHistoryDataMap.get(friendUid);
            if (null == interactHistory) {
                interactHistory = FriendHistoryInteractTableDao.loadFriendInteractHistory(player.getUid(), friendUid);
                updateFriendInteractHistoryCache(friendUid, interactHistory);
            }
        } catch (Exception e) {
            LOGGER.error("get player {} friend {} interact history error ", player.getUid(), friendUid, e);
        }

        int friendDays = DateUtils.getDayInterval(modRelationInfo.getAddTime(), Framework.currentTimeMillis()) + 1;
        proto_FriendInteractDetailAttr.Builder friendDayDetail = proto_FriendInteractDetailAttr.newBuilder()
                .setType(FriendInteractDataType.FIDT_FriendDays_VALUE)
                .setInteractCount(friendDays);

        ret.addHistoryInteractData(friendDayDetail);
        if (null != interactHistory) {
            ret.addAllHistoryInteractData(interactHistory.getInteractDetailList());
        } else {
            for (FriendInteractDataType type : FriendHistoryInteractTableDao.getInteractHistoryDataTypeSet()) {
                ret.addHistoryInteractData(proto_FriendInteractDetailAttr.newBuilder()
                        .setType(type.getNumber())
                        .setInteractCount(0));
            }
        }
    }

    /**
     * 增加好友历史交互次数统计
     *
     * @param friendUid
     * @param dataType
     * @param addCnt
     */
    protected void increaseFriendInteractHistoryStatistic(long friendUid, FriendInteractDataType dataType, int addCnt) {
        if (!friendHistoryInteractStatisticEnable()) {
            return;
        }

        if (!FriendHistoryInteractTableDao.needSaveInteractHistory(dataType)) {
            return;
        }

        if (!player.getFriendManager().isFriend(friendUid)) {
            return;
        }

        CurrentExecutorUtil.runJobNoException("updateHistoryInteract",
                new IncreaseFriendInteractHistoryCallable(friendUid, dataType, addCnt));
    }

    private class IncreaseFriendInteractHistoryCallable implements Callable<Void> {

        private final long friendUid;
        private final FriendInteractDataType dataType;
        private final int addCnt;

        public IncreaseFriendInteractHistoryCallable(long friendUid, FriendInteractDataType dataType, int addCnt) {
            this.friendUid = friendUid;
            this.dataType = dataType;
            this.addCnt = addCnt;
        }

        @Override
        public Void call() throws Exception {
            try {
                int newDataValue = FriendHistoryInteractTableDao.increaseInteractCnt(
                        player.getUid(), friendUid, dataType, addCnt);

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("player {} increase friend {} interact history data {}-{} done {}",
                            player.getUid(), friendUid, dataType, addCnt, newDataValue);
                }

                updateFriendInteractHistoryCacheField(friendUid, dataType, newDataValue, true);

                TlogFlowMgr.sendFriendInteractHistoryFlow(player, friendUid, dataType, addCnt, newDataValue);
            } catch (Exception e) {
                LOGGER.error("increase player {} friend {} history interact {}-{} error ",
                        player.getUid(), friendUid, dataType, addCnt, e);
            }

            return null;
        }
    }

    /**
     * 获取最近交互时间
     *
     * @param friendUid
     * @return
     */
    public long getRecentInteractTimeMs(long friendUid) {
        DBRelation modRelationInfo = dbRelationInfo.getModRelationInfo(friendUid);
        if (null == modRelationInfo) {
            return 0L;
        }

        return modRelationInfo.getHotData().getRecentInteractTs();
    }

    public boolean isFriendOnline(long friendUid) {
        DBRelation modRelationInfo = dbRelationInfo.getModRelationInfo(friendUid);
        if (null == modRelationInfo) {
            LOGGER.error("player {} check friend {} online status error, not friend", player.getUid(), friendUid);
            return false;
        }

        return PlayerStateMgr.isOnline(modRelationInfo.getHotData().getPlayerStatus());
    }

    public static int getSendGiftMailTemplateId() {
        return getConfValueOrDefault(RelationConfEnum.RCI_SEND_GIFT_MAIL_TEMPLATE_ID, 0);
    }
}
