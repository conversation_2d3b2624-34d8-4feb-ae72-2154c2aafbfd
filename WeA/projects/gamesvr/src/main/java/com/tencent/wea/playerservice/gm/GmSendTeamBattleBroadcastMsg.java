package com.tencent.wea.playerservice.gm;

import static com.tencent.wea.protocol.common.ChatType.CT_TeamGroup;

import com.tencent.timiutil.time.*;
import com.tencent.wea.playerservice.player.*;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.rpc.service.*;
import java.util.*;
import org.apache.logging.log4j.*;

public class GmSendTeamBattleBroadcastMsg implements GmHandler {

    private static final Logger LOGGER = LogManager.getLogger(GmSendTeamBattleBroadcastMsg.class);

    /**
     * gm处理逻辑接口
     *
     * @param player 玩家
     * @param param gm参数列表
     * @return int
     */
    @Override
    public int handle(Player player, List<String> params) {
        ChatService chatService = ChatService.get();
        if (chatService == null) {
            LOGGER.error(" sendBattleBroadcastChatMsg failed");
            return 0;
        }
        ChatGroupKey chatGroupKey = ChatGroupKey.newBuilder()
                .setType(CT_TeamGroup)
                .setId(player.getPlayerRoomMgr().getTeamId()).build();
        LOGGER.debug("chatGroupKey:{}", chatGroupKey.toString());
        KVArray.Builder kvArray = KVArray.newBuilder();
        int paramIndex = 0;
        for (String param : params) {
            if (paramIndex == 0) {
                kvArray.addArray(
                        KVEntry.newBuilder().setKey("MsgIndex").setValue(param));
                paramIndex++;
            } else {
                kvArray.addArray(
                        KVEntry.newBuilder().setKey(String.format("Param%d", paramIndex++)).setValue(param));
            }
        }
        ChatMsgData msg = ChatMsgData.newBuilder().setMsgType(ChatMsgType.CMT_TeamBattleBroadcast)
                .setFromId(player.getUid()).setSafetyCheckPassFlag(1).setKvArray(kvArray).build();
        try {
            player.getPlayerChatManager().sendMsg(chatGroupKey, msg, DateUtils.currentTimeMillis(), true, true);
        } catch (Exception e) {
            LOGGER.error("sendBattleBroadcastChatMsg send failed ec:{}", e);
        }
        return 0;
    }
}
