package com.tencent.wea.playerservice.sns.invitee;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.MailTemplateConfData;
import com.tencent.wea.attr.PlayerSnsInvitationData;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.MailInteraction.TlogSendReason;
import com.tencent.wea.playerservice.activity.implement.TakeawayActivity;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.common.SnsInvitationData;
import com.tencent.wea.protocol.common.SnsInvitationParam;
import com.tencent.wea.tcaplus.TcaplusDb.SnsInvitationTableOrBuilder;
import com.tencent.wea.xlsRes.ResActivity.TakeawayConf;
import com.tencent.wea.xlsRes.ResMailTemplate;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class TakeawayInvitee extends BaseActivityInvitee<TakeawayActivity> {

    private static final Logger LOGGER = LogManager.getLogger(TakeawayInvitee.class);

    @Override
    protected ActivityType getActivityType() {
        return ActivityType.ATTakeaway;
    }

    @Override
    protected NKErrorCode check(Player player, int activityId, TakeawayActivity activity,
            SnsInvitationTableOrBuilder invitation) {
        return NKErrorCode.OK;
    }

    @Override
    protected void accept(Player player, int activityId,
            TakeawayActivity activity, SnsInvitationTableOrBuilder acceptedInvitation) {

        TakeawayConf conf = activity.getRelatedTakeawayConf();
        ResMailTemplate.MailTemplateConfData mailConf = Optional.ofNullable(conf)
                .map(c -> c.getInvitee().getSupportRewardMailId())
                .map(mail -> MailTemplateConfData.getInstance().getMailTemplate(mail)).orElse(null);

        if (mailConf == null) {
            LOGGER.info("reward mail not found though invitation accepted, uid:{} activity:{} inviter:{}",
                    player.getUid(), activityId, acceptedInvitation.getInviterId());
            return;
        }

        long ret = MailInteraction.sendTemplateMail(player.getUid(), mailConf.getId(),
                TlogSendReason.activityTakeawayInviteeReward);
        LOGGER.info("reward mail sent as invitation accepted, uid:{} activity:{} inviter:{} ret:{}", player.getUid(),
                activityId, acceptedInvitation.getInviterId(), ret);
    }

    @Override
    protected void gmDeleteAcceptRelatedData(Player player, int activityId, TakeawayActivity activity, int cfg,
            PlayerSnsInvitationData param) {
    }
}
