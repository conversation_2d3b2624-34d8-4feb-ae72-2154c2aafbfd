package com.tencent.wea.playerservice.permit;

import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.PermitRewardConfig;
import com.tencent.resourceloader.resclass.PermitTaskConfig;
import com.tencent.resourceloader.resclass.QuickRewardConfData;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.reward.QuickRewardInterface;
import com.tencent.wea.playerservice.reward.RewardCompensateInterface;
import com.tencent.wea.protocol.common.RewardItemInfo;
import com.tencent.wea.xlsRes.ResPermit;
import com.tencent.wea.xlsRes.ResReward;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.RewardCompensateType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/8
 */
public class PermitLevelAwardManager extends PlayerModule implements QuickRewardInterface {

    private static final Logger LOGGER = LogManager.getLogger(PermitLevelAwardManager.class);

    public PermitLevelAwardManager(Player player) {
        super(GameModuleId.GMI_PermitLevelAwardMgr, player);
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {
        player.getQuickRewardMgr().registerQuickReward(this);
    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {

    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {

    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {

    }

    @Override
    public void onMidNight() {

    }

    @Override
    public void onWeekStart() {

    }

    @Override
    public ResReward.QuickRewardType getQuickRewardType() {
        return ResReward.QuickRewardType.QRT_PERMIT_LEVEL;
    }

    @Override
    public Map<Integer, List<RewardItemInfo.Builder>> getQuickRewardItemList() {
        List<ResReward.QuickRewardConf> confList = QuickRewardConfData.getInstance().getConfListByType(getQuickRewardType());
        Map<Integer, List<RewardItemInfo.Builder>> confItemMap = new HashMap<>();
        if (confList.isEmpty()) {
            return confItemMap;
        }
        ResReward.QuickRewardConf quickRewardConf = confList.get(0);
        List<RewardItemInfo.Builder> levelItems = player.getPermitManager().getLevelItems();
        confItemMap.put(quickRewardConf.getId(), levelItems);

        return confItemMap;
    }

    @Override
    public ItemChangeDetails receiveQuickReward() {
        ItemChangeDetails itemChangeDetails = null;
        int maxCheckTimes = 100;
        while (maxCheckTimes > 0) {
            ItemChangeDetails changes = player.getPermitManager().handlePermitAllLevelRewardInternal();
            if (changes == null || changes.getChangedTotalItemsMap().size() == 0) {
                break;
            }
            if (itemChangeDetails == null) {
                itemChangeDetails = changes;
            } else {
                itemChangeDetails.merge(changes);
            }
            maxCheckTimes--;
        }
        return itemChangeDetails;
    }
}
