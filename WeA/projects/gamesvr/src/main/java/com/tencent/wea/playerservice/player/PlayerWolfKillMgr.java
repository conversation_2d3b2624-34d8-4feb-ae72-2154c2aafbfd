package com.tencent.wea.playerservice.player;

import com.tencent.activity.CommonActivityEnum;
import com.tencent.condition.event.player.common.PlayerWolfKillCumulativeRoleInfoPointsEvent;
import com.tencent.condition.event.player.common.WolfNewPlayerRoleInfoTotalTimesEvent;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.rank.utils.RankIdSeasonIdMapper;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.NR3E3MonthCardBuyLimit;
import com.tencent.resourceloader.resclass.NR3E3MonthCardFreeGift;
import com.tencent.resourceloader.resclass.NR3E3MonthCardLevel;
import com.tencent.resourceloader.resclass.NR3E3MonthCardPrivilege;
import com.tencent.resourceloader.resclass.NR3E3Treasure;
import com.tencent.resourceloader.resclass.NR3E3TreasureBlackList;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.resourceloader.resclass.WolfKillComeBack;
import com.tencent.resourceloader.resclass.WolfKillDecorationAni;
import com.tencent.resourceloader.resclass.WolfKillRoadToMaster;
import com.tencent.resourceloader.resclass.WolfKillRoleInfoRate;
import com.tencent.resourceloader.resclass.WolfKillVocation;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.Item;
import com.tencent.wea.attr.WolfKillRewardItem;
import com.tencent.wea.attr.WolfKillTreasureEquipInfo;
import com.tencent.wea.attr.WolfKillTreasureRentHighVersionItemInfo;
import com.tencent.wea.battlehistory.BattleHistoryUtil;
import com.tencent.wea.battleresult.GlobalBattleResult;
import com.tencent.wea.g6.irpc.proto.competition.Competition;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.playerservice.event.common.PlayerWolfKillRoleInfoPointsEvent;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.CsLetsgo;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.CsWolfkill;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.common.DeliverGoodsMetaData;
import com.tencent.wea.protocol.common.G6Common;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.RewardItemInfo;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.PlayerNoticeMsgType;
import com.tencent.wea.protocol.common.PlayerRankInfo;
import com.tencent.wea.protocol.common.WereWolfRepeatWinInfo;
import com.tencent.wea.protocol.common.WereWolfRoleInfoPointsInfo;
import com.tencent.wea.room.RoomUtil;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResMall;
import com.tencent.wea.xlsRes.ResMatch;
import com.tencent.wea.xlsRes.ResNR3E3MonthCardBuyLimit;
import com.tencent.wea.xlsRes.ResNR3E3MonthCardFreeGift;
import com.tencent.wea.xlsRes.ResNR3E3MonthCardLevel;
import com.tencent.wea.xlsRes.ResNR3E3MonthCardPrivilege;
import com.tencent.wea.xlsRes.ResNR3E3Treasure;
import com.tencent.wea.xlsRes.ResNR3E3Treasure.NR3E3TreasureUnlockItem;
import com.tencent.wea.xlsRes.ResNR3E3Vocation;
import com.tencent.wea.xlsRes.ResRanking;
import com.tencent.wea.xlsRes.ResWolfKillComeBack;
import com.tencent.wea.xlsRes.ResWolfKillDecoration;
import com.tencent.wea.xlsRes.ResWolfKillRoadToMaster;
import com.tencent.wea.xlsRes.ResWolfKillRoleInfoRate;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.RankRule;
import com.tencent.wea.xlsRes.keywords.WolfKillTreasureReason;
import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.tencent.wea.wolfKill.Util.getWolfKillRealTreasureLevel;
import static com.tencent.wea.wolfKill.Util.isSameSeason;
import static com.tencent.wea.wolfKill.Util.wolfKillCheck;
import static com.tencent.wea.wolfKill.Util.wolfKillCheckIsShouldBlock;
import static com.tencent.wea.wolfKill.Util.wolfKillCheckIsTeamMatchType;

public class PlayerWolfKillMgr extends PlayerModule {
    protected static final Logger LOGGER = LogManager.getLogger(PlayerWolfKillMgr.class);
    private final String MONTH_CARD_ID = "2_102042";

    Player player;

    TcaplusDb.WolfKillTable.Builder wolfKillBuilder;

    public PlayerWolfKillMgr(Player player) {
        super(GameModuleId.GMI_WolfKillMgr, player);
        this.player = player;
    }

    public void loadWolfKillInfo() {
        TcaplusDb.WolfKillTable.Builder req = TcaplusDb.WolfKillTable.newBuilder();
        req.setUid(player.getUid());
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(req).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.debug("loadWolfKillInfo error");
            return;
        }
        TcaplusDb.WolfKillTable.Builder builder;
        if (rsp.getResult().recordNotExist()) {
            LOGGER.debug("loadWolfKillInfo not exist");
            builder = TcaplusDb.WolfKillTable.newBuilder();
            builder.setUid(player.getUid());
            int defaultScore = MiscConf.getInstance().getMiscConf().getWolfKillDefaultScore();
            builder.setReputationScore(defaultScore);
            int defaultActionScore = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreDefault();
            builder.setActionScore(defaultActionScore);
            builder.setActionScoreInit(1);
            TcaplusUtil.newInsertReq(builder).send();
        } else {
            LOGGER.debug("loadWolfKillInfo exist");
            builder = (TcaplusDb.WolfKillTable.Builder) rsp.firstRecordData().msg.toBuilder();
        }
        if (builder.getActionScoreInit() != 1) {
            builder.setActionScoreInit(1);
            int defaultActionScore = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreDefault();
            builder.setActionScore(defaultActionScore);
            TcaplusUtil.newUpdateReq(builder).send();
        }
        wolfKillBuilder = builder;
        LOGGER.debug("loadWolfKillInfo wolfKillBuilder {}", wolfKillBuilder);
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.WolfKillInit);
        //对局数变动
        wolfKillRoleInfoPointsEvent();
    }

    public TcaplusDb.WolfKillTable getWolfKillInfo() {
        if (wolfKillBuilder == null) {
            return TcaplusDb.WolfKillTable.newBuilder().setUid(0).build();
        }
        return wolfKillBuilder.build();
    }

    private void calReputationScore(int score, int reason, long roomId, String matchID, String battleID,
                                    String battleType, String mapID, int campType, int campRoleType) {
        int curScore = wolfKillBuilder.getReputationScore();
        int totalScore = curScore + score;
        int defaultScore = MiscConf.getInstance().getMiscConf().getWolfKillDefaultScore();
        if (totalScore > defaultScore) {
            totalScore = defaultScore;
        }
        if (totalScore < 0) {
            totalScore = 0;
        }
        wolfKillBuilder.setReputationScore(totalScore);
        TcaplusDb.WolfKillScoreRecord.Builder recordBuilder = TcaplusDb.WolfKillScoreRecord.newBuilder();
        recordBuilder.setDt(Framework.currentTimeMillis());
        recordBuilder.setTotalScore(totalScore);
        recordBuilder.setDeltaScore(score);
        recordBuilder.setReason(reason);
        int maxRecord = MiscConf.getInstance().getMiscConf().getWolfKillMaxRecord();
        if (wolfKillBuilder.getScoreRecordBuilder().getRecordsCount() >= maxRecord) {
            wolfKillBuilder.getScoreRecordBuilder().removeRecords(0);
        }
        wolfKillBuilder.getScoreRecordBuilder().addRecords(recordBuilder);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.ReputationScoreChange);
        TlogFlowMgr.sendSuspectCreditFlow(player, matchID, battleID, battleType, mapID, campType, campRoleType,
                score, curScore, totalScore, reason);
    }

    public void addWolfKillReputationScore(int score, int reason, long roomID, String matchID, String battleID,
                                           String battleType, String mapID, int campType, int campRoleType) {
        int defaultScore = MiscConf.getInstance().getMiscConf().getWolfKillDefaultScore();
        int curScore = wolfKillBuilder.getReputationScore();
        if (curScore >= defaultScore) {
            return;
        }
        calReputationScore(score, reason, roomID, matchID, battleID, battleType, mapID, campType, campRoleType);
    }

    public void subWolfKillReputationScore(int score, int reason, long roomID, String matchID, String battleID,
                                           String battleType, String mapID, int campType, int campRoleType) {
        int curScore = wolfKillBuilder.getReputationScore();
        if (curScore <= 0) {
            return;
        }
        calReputationScore(score * -1, reason, roomID, matchID, battleID, battleType, mapID, campType, campRoleType);
        switch (reason) {
            case 101:
                MailInteraction.sendTemplateMail(player.getUid(), 140,
                        MailInteraction.TlogSendReason.wolfKillSubReputation,
                        score);
                break;
            case 102:
                MailInteraction.sendTemplateMail(player.getUid(), 141,
                        MailInteraction.TlogSendReason.wolfKillSubReputation,
                        score);
                break;
            case 103:
                MailInteraction.sendTemplateMail(player.getUid(), 142,
                        MailInteraction.TlogSendReason.wolfKillSubReputation,
                        score);
                break;
            case 104:
                MailInteraction.sendTemplateMail(player.getUid(), 144,
                        MailInteraction.TlogSendReason.wolfKillSubReputation,
                        score);
        }
    }

    public void clearReputationRecord() {
        long dt = 30L * 24 * 3600 * 1000;
        long curTs = DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis());
        TcaplusDb.WolfKillScoreRecordArray.Builder array = TcaplusDb.WolfKillScoreRecordArray.newBuilder();
        for (TcaplusDb.WolfKillScoreRecord record : wolfKillBuilder.getScoreRecord().getRecordsList()) {
            if ((curTs - DateUtils.getDayBeginTimeMs(record.getDt())) <= dt) {
                array.addRecords(record);
            }
        }
        wolfKillBuilder.setScoreRecord(array);
        dt = 7L * 24 * 3600 * 1000;
        TcaplusDb.WolfKillSingleReportArray.Builder singleReportArray = TcaplusDb.WolfKillSingleReportArray.newBuilder();
        for (TcaplusDb.WolfKillSingleReport singleReport : wolfKillBuilder.getSingleReportRecord().getSingleReportsList()) {
            if ((curTs - DateUtils.getDayBeginTimeMs(singleReport.getDt())) <= dt) {
                singleReportArray.addSingleReports(singleReport);
            }
        }
        wolfKillBuilder.setSingleReportRecord(singleReportArray);
        TcaplusDb.WolfKillReportArray.Builder reportArray = TcaplusDb.WolfKillReportArray.newBuilder();
        for (TcaplusDb.WolfKillReport report : wolfKillBuilder.getReportRecord().getReportsList()) {
            if ((curTs - DateUtils.getDayBeginTimeMs(report.getDt())) <= dt) {
                reportArray.addReports(report);
            }
        }
        wolfKillBuilder.setReportRecord(reportArray);
        TcaplusDb.WolfKillPassiveReportArray.Builder passiveReportArray = TcaplusDb.WolfKillPassiveReportArray.newBuilder();
        for (TcaplusDb.WolfKillPassiveReport report : wolfKillBuilder.getPassiveReport().getReportsList()) {
            if ((curTs - DateUtils.getDayBeginTimeMs(report.getDt())) <= dt) {
                passiveReportArray.addReports(report);
            }
        }
        wolfKillBuilder.setPassiveReport(passiveReportArray);
        dt = 3600 * 1000;
        curTs = DateUtils.currentTimeMillis();
        TcaplusDb.WolfKillViolationArray.Builder violationArray = TcaplusDb.WolfKillViolationArray.newBuilder();
        for (TcaplusDb.WolfKillViolation violation : wolfKillBuilder.getWolfKillViolation().getViolationsList()) {
            if ((curTs - violation.getTs()) <= dt) {
                violationArray.addViolations(violation);
            }
        }
        wolfKillBuilder.setWolfKillViolation(violationArray);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public void reputationScoreNotEnoughNtf(SsGamesvr.RpcReputationScoreNotEnoughNtfReq req) {
        var msg = "";
        for (String name : req.getNameListList()) {
            msg = msg + name + " ";
        }
        msg = msg + "信誉分过低，无法匹配";
        CsPlayer.PlayerNoticeMsgNtf.Builder ntf = CsPlayer.PlayerNoticeMsgNtf.newBuilder()
                .setType(PlayerNoticeMsgType.PNT_UNKNOWN)
                .setNotice(msg);
        ntf.addParams(msg);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, ntf);
    }

    public void onlineAddReport(ArrayList<Long> reporters) {
        TcaplusDb.WolfKillReportArray.Builder reportArray = wolfKillBuilder.getReportRecordBuilder();
        int beforeReportNum = reportArray.getReportsList().size();
        int beforeCreditScore = wolfKillBuilder.getReputationScore();
        reportArray.addReports(TcaplusDb.WolfKillReport.newBuilder().setDt(DateUtils.currentTimeMillis()));
        int subNumber = MiscConf.getInstance().getMiscConf().getWolfKillReportSub();
        if (reportArray.getReportsList().size() >= subNumber) {
            int subScore = MiscConf.getInstance().getMiscConf().getWolfKillReportSubScore();
            subWolfKillReputationScore(subScore, 103, 0, "", "", "", "", 0, 0);
            for (Long reporter : reporters) {
                MailInteraction.sendTemplateMail(reporter, 135, MailInteraction.TlogSendReason.wolfKillReport, player.getName());
            }
            int index = Math.min(reportArray.getReportsList().size() - subNumber + 1,
                    MiscConf.getInstance().getMiscConf().getWolfKillCommunicateActionScoreList().size()) - 1;
            subWolfKillActionScore(MiscConf.getInstance().getMiscConf().getWolfKillCommunicateActionScore(index), "", "", "", 103);
        }
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        TlogFlowMgr.sendSuspectReportedRecord(player, reportArray.getReportsList().size(), beforeReportNum,
                beforeCreditScore - wolfKillBuilder.getReputationScore(), wolfKillBuilder.getReputationScore(), beforeCreditScore);
    }

    public static void offlineAddReport(TcaplusDb.WolfKillTable.Builder builder, ArrayList<Long> reporters) {
        TcaplusDb.WolfKillReportArray.Builder reportArray = builder.getReportRecordBuilder();
        int beforeReportNum = reportArray.getReportsList().size();
        int beforeCreditScore = builder.getReputationScore();
        reportArray.addReports(TcaplusDb.WolfKillReport.newBuilder().setDt(DateUtils.currentTimeMillis()));
        int subNumber = MiscConf.getInstance().getMiscConf().getWolfKillReportSub();
        int subScore = MiscConf.getInstance().getMiscConf().getWolfKillReportSubScore();
        if (reportArray.getReportsList().size() >= subNumber) {
            for (Long reporter : reporters) {
                MailInteraction.sendTemplateMail(reporter, 135, MailInteraction.TlogSendReason.wolfKillReport, "");
            }

            int curScore = builder.getReputationScore();
            if (curScore <= 0) {
                return;
            }
            int score = subScore * -1;
            int totalScore = curScore + score;
            if (totalScore < 0) {
                totalScore = 0;
            }
            builder.setReputationScore(totalScore);

            TcaplusDb.WolfKillScoreRecord.Builder recordBuilder = TcaplusDb.WolfKillScoreRecord.newBuilder();
            recordBuilder.setDt(Framework.currentTimeMillis());
            recordBuilder.setTotalScore(totalScore);
            recordBuilder.setDeltaScore(score);
            recordBuilder.setReason(103);
            builder.getScoreRecordBuilder().addRecords(recordBuilder);
            int maxRecord = MiscConf.getInstance().getMiscConf().getWolfKillMaxRecord();
            if (builder.getScoreRecordBuilder().getRecordsCount() >= maxRecord) {
                builder.getScoreRecordBuilder().removeRecords(0);
            }

            int index = Math.min(reportArray.getReportsList().size() - subNumber + 1,
                    MiscConf.getInstance().getMiscConf().getWolfKillCommunicateActionScoreList().size()) - 1;
            int actionScore = builder.getActionScore() -
                    MiscConf.getInstance().getMiscConf().getWolfKillCommunicateActionScore(index);
            int max = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreMax();
            int min = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreMin();
            if (actionScore > max) {
                actionScore = max;
            }
            if (actionScore < min) {
                actionScore = min;
            }
            builder.setActionScore(actionScore);
        }
        TlogFlowMgr.sendOffLineSuspectReportedRecord(builder.getUid(), reportArray.getReportsList().size(),
                beforeReportNum, beforeCreditScore - builder.getReputationScore(), builder.getReputationScore(), beforeCreditScore);
    }

    public void dealWithCommunicateReport(SsGamesvr.RpcWolfKillReportCommunicateReq.Builder req) {
        TlogFlowMgr.sendSuspectReportFlow(player, String.valueOf(req.getMatchId()), String.valueOf(req.getBattleId()),
                String.valueOf(req.getBattleType()), String.valueOf(req.getBeReporter()), "1", "");
    }

    public void dealWithOnlineCommunicateBeReport(SsGamesvr.RpcWolfKillBeReportCommunicateReq.Builder req) {
        Map<Long, Map<Long, TcaplusDb.WolfKillSingleReport>> data = new HashMap<>();
        TcaplusDb.WolfKillSingleReportArray.Builder singleReportArray = wolfKillBuilder.getSingleReportRecordBuilder();
        for (TcaplusDb.WolfKillSingleReport singleReport : singleReportArray.getSingleReportsList()) {
            if (!data.containsKey(singleReport.getBattleId())) {
                data.put(singleReport.getBattleId(), new HashMap<>());
            }
            data.get(singleReport.getBattleId()).put(singleReport.getUid(), singleReport);
        }
        long battleId = req.getBattleId();
        long reporter = req.getReporter();
        if (!data.containsKey(battleId)) {
            TcaplusDb.WolfKillSingleReportArray.Builder a1 = TcaplusDb.WolfKillSingleReportArray.newBuilder();
            TcaplusDb.WolfKillSingleReportArray.Builder a2 = TcaplusDb.WolfKillSingleReportArray.newBuilder();
            for (Map<Long, TcaplusDb.WolfKillSingleReport> temp : data.values()) {
                if (temp.size() == 1) {
                    for (TcaplusDb.WolfKillSingleReport singleReport : temp.values()) {
                        if (singleReport.getDt() + 10 * 60 * 1000 <= DateUtils.currentTimeMillis()) {
                            a1.addSingleReports(singleReport);
                        } else {
                            a2.addSingleReports(singleReport);
                        }
                    }
                } else {
                    for (TcaplusDb.WolfKillSingleReport singleReport : temp.values()) {
                        a2.addSingleReports(singleReport);
                    }
                }
            }
            int singleReportBatch = MiscConf.getInstance().getMiscConf().getWolfKillSingleReportBatch();
            if (a1.getSingleReportsList().size() >= singleReportBatch) {
                while (a1.getSingleReportsList().size() >= singleReportBatch) {
                    long reporterId = 0L;
                    for (int i = 0; i < singleReportBatch; i++) {
                        reporterId = a1.getSingleReports(0).getUid();
                        a1.removeSingleReports(0);
                    }
                    ArrayList<Long> reporters = new ArrayList<>();
                    reporters.add(reporterId);
                    onlineAddReport(reporters);
                }
                while (!a1.getSingleReportsList().isEmpty()) {
                    a2.addSingleReports(a1.getSingleReportsList().get(0));
                    a1.removeSingleReports(0);
                }
                singleReportArray = a2;
            }
        } else {
            if (data.get(battleId).size() == 1) {
                if (data.get(battleId).containsKey(reporter)) {
                    return;
                }
                ArrayList<Long> reporters = new ArrayList<>();
                reporters.add(reporter);
                for (TcaplusDb.WolfKillSingleReport report : data.get(battleId).values()) {
                    reporters.add(report.getUid());
                }
                onlineAddReport(reporters);
            } else if (data.get(battleId).size() > 1) {
                int subNumber = MiscConf.getInstance().getMiscConf().getWolfKillReportSub();
                if (wolfKillBuilder.getReportRecord().getReportsList().size() >= subNumber) {
                    ArrayList<Long> dts = new ArrayList<>();
                    for (TcaplusDb.WolfKillSingleReport report : data.get(battleId).values()) {
                        dts.add(report.getDt());
                    }
                    dts.sort(Comparator.naturalOrder());
                    if (dts.get(1) >= wolfKillBuilder.getReportRecord().getReports(subNumber - 1).getDt()) {
                        MailInteraction.sendTemplateMail(reporter, 135, MailInteraction.TlogSendReason.wolfKillReport, player.getName());
                    }
                }
            }
        }
        singleReportArray.addSingleReports(TcaplusDb.WolfKillSingleReport.newBuilder().setDt(DateUtils.currentTimeMillis()).setBattleId(battleId).setUid(reporter));
        wolfKillBuilder.setSingleReportRecord(singleReportArray);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        LOGGER.debug("dealWithOnlineCommunicateBeReport data :{}", wolfKillBuilder.build());
    }

    public static void dealWithOfflineCommunicateBeReport(SsGamesvr.RpcWolfKillBeReportCommunicateReq.Builder req) {
        long beReporter = req.getUid();
        TcaplusDb.WolfKillTable.Builder db = TcaplusDb.WolfKillTable.newBuilder();
        db.setUid(beReporter);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(db).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            return;
        }
        if (rsp.getResult().recordNotExist()) {
            return;
        }
        Map<Long, Map<Long, TcaplusDb.WolfKillSingleReport>> data = new HashMap<>();
        TcaplusDb.WolfKillTable.Builder builder = (TcaplusDb.WolfKillTable.Builder) rsp.firstRecordData().msg.toBuilder();
        TcaplusDb.WolfKillSingleReportArray.Builder singleReportArray = builder.getSingleReportRecordBuilder();
        for (TcaplusDb.WolfKillSingleReport singleReport : singleReportArray.getSingleReportsList()) {
            if (!data.containsKey(singleReport.getBattleId())) {
                data.put(singleReport.getBattleId(), new HashMap<>());
            }
            data.get(singleReport.getBattleId()).put(singleReport.getUid(), singleReport);
        }
        long battleId = req.getBattleId();
        long reporter = req.getReporter();
        if (!data.containsKey(battleId)) {
            TcaplusDb.WolfKillSingleReportArray.Builder a1 = TcaplusDb.WolfKillSingleReportArray.newBuilder();
            TcaplusDb.WolfKillSingleReportArray.Builder a2 = TcaplusDb.WolfKillSingleReportArray.newBuilder();
            int reportEndTime = MiscConf.getInstance().getMiscConf().getWolfKillReportEndTime() * 60 * 1000;
            for (Map<Long, TcaplusDb.WolfKillSingleReport> temp : data.values()) {
                if (temp.size() == 1) {
                    for (TcaplusDb.WolfKillSingleReport singleReport : temp.values()) {
                        if (singleReport.getDt() + reportEndTime <= DateUtils.currentTimeMillis()) {
                            a1.addSingleReports(singleReport);
                        } else {
                            a2.addSingleReports(singleReport);
                        }
                    }
                } else {
                    for (TcaplusDb.WolfKillSingleReport singleReport : temp.values()) {
                        a2.addSingleReports(singleReport);
                    }
                }
            }
            int singleReportBatch = MiscConf.getInstance().getMiscConf().getWolfKillSingleReportBatch();
            if (a1.getSingleReportsList().size() >= singleReportBatch) {
                while (a1.getSingleReportsList().size() >= singleReportBatch) {
                    long reporterId = 0L;
                    for (int i = 0; i < singleReportBatch; i++) {
                        reporterId = a1.getSingleReports(0).getUid();
                        a1.removeSingleReports(0);
                    }
                    ArrayList<Long> reporters = new ArrayList<>();
                    reporters.add(reporterId);
                    offlineAddReport(builder, reporters);
                }
                while (!a1.getSingleReportsList().isEmpty()) {
                    a2.addSingleReports(a1.getSingleReportsList().get(0));
                    a1.removeSingleReports(0);
                }
                singleReportArray = a2;
            }
        } else {
            if (data.get(battleId).size() == 1) {
                if (data.get(battleId).containsKey(reporter)) {
                    return;
                }
                ArrayList<Long> reporters = new ArrayList<>();
                reporters.add(reporter);
                for (TcaplusDb.WolfKillSingleReport report : data.get(battleId).values()) {
                    reporters.add(report.getUid());
                }
                offlineAddReport(builder, reporters);
            } else if (data.get(battleId).size() > 1) {
                int subNumber = MiscConf.getInstance().getMiscConf().getWolfKillReportSub();
                if (builder.getReportRecord().getReportsList().size() >= subNumber) {
                    ArrayList<Long> dts = new ArrayList<>();
                    for (TcaplusDb.WolfKillSingleReport report : data.get(battleId).values()) {
                        dts.add(report.getDt());
                    }
                    dts.sort(Comparator.naturalOrder());
                    if (dts.get(1) >= builder.getReportRecord().getReports(subNumber - 1).getDt()) {
                        MailInteraction.sendTemplateMail(reporter, 135, MailInteraction.TlogSendReason.wolfKillReport, "");
                    }
                }
            }
        }
        singleReportArray.addSingleReports(TcaplusDb.WolfKillSingleReport.newBuilder().setDt(DateUtils.currentTimeMillis())
                .setBattleId(battleId).setUid(reporter));
        builder.setSingleReportRecord(singleReportArray);
        TcaplusUtil.newUpdateReq(builder).send();
        LOGGER.debug("dealWithOfflineCommunicateBeReport data :{}", builder.build());
    }

    public void dealWithPassiveReport(SsGamesvr.RpcWolfKillReportPassiveReq.Builder req) {
        TlogFlowMgr.sendSuspectReportFlow(player, String.valueOf(req.getMatchId()), String.valueOf(req.getBattleId()),
                String.valueOf(req.getBattleType()), String.valueOf(req.getBeReporter()), "2", "");
    }

    public void dealWithOnlinePassiveBeReport(SsGamesvr.RpcWolfKillBeReportPassiveReq.Builder req) {
        LOGGER.debug("dealWithOnlinePassiveBeReport uid is {}, battleId is {}, isEnd is {}", req.getUid(),
                req.getBattleId(), req.getIsEnd());

        long battleId = req.getBattleId();
        TcaplusDb.WolfKillPassiveReportArray.Builder passive = wolfKillBuilder.getPassiveReportBuilder();
        int index = -1;
        for (int i = 0; i < passive.getReportsList().size(); i++) {
            if (passive.getReports(i).getBattleId() == battleId) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            boolean hasReport = false;
            TcaplusDb.WolfKillPassiveReport.Builder report = passive.getReportsBuilder(index);
            for (int i = 0; i < report.getPlayerListCount(); i++) {
                if (report.getPlayerList(i) == req.getReporter()) {
                    hasReport = true;
                    break;
                }
            }
            if (hasReport) {
                return;
            }
            report.addPlayerList(req.getReporter());
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        }
        boolean isEnd = req.getIsEnd();
        if (isEnd) {
            if (index == -1) {
                return;
            }
            TcaplusDb.WolfKillPassiveReport.Builder report = passive.getReportsBuilder(index);
            int passiveTime = MiscConf.getInstance().getMiscConf().getWolfKillPassiveTime();
            if (report.getPassiveTime() < passiveTime) {
                return;
            }
            if (report.getIsHasSub()) {
                MailInteraction.sendTemplateMail(req.getReporter(), 135, MailInteraction.TlogSendReason.wolfKillReport, player.getName());
                return;
            }
            report.setIsHasSub(true);
            int subScore = MiscConf.getInstance().getMiscConf().getWolfKillPassiveScore();
            subWolfKillReputationScore(subScore, 104, 0, "", "", "", "", 0, 0);
            MailInteraction.sendTemplateMail(req.getReporter(), 135, MailInteraction.TlogSendReason.wolfKillReport, player.getName());
            index = Math.min(passive.getReportsList().size(), MiscConf.getInstance().getMiscConf().getWolfKillPassiveActionScoreList().size()) - 1;
            subWolfKillActionScore(MiscConf.getInstance().getMiscConf().getWolfKillPassiveActionScore(index), "", "", "", 104);
        } else {
            if (index == -1) {
                TcaplusDb.WolfKillPassiveReport.Builder report = TcaplusDb.WolfKillPassiveReport.newBuilder();
                report.setBattleId(battleId);
                report.setDt(DateUtils.currentTimeMillis());
                report.addPlayerList(req.getReporter());
                passive.addReports(report);
            }
        }
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        LOGGER.debug("dealWithOnlinePassiveBeReport builder is {}", wolfKillBuilder);
    }

    public static void dealWithOfflinePassiveBeReport(SsGamesvr.RpcWolfKillBeReportPassiveReq.Builder req) {
        long beReporter = req.getUid();
        TcaplusDb.WolfKillTable.Builder db = TcaplusDb.WolfKillTable.newBuilder();
        db.setUid(beReporter);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(db).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            return;
        }
        if (rsp.getResult().recordNotExist()) {
            return;
        }
        TcaplusDb.WolfKillTable.Builder builder = (TcaplusDb.WolfKillTable.Builder) rsp.firstRecordData().msg.toBuilder();
        long battleId = req.getBattleId();
        TcaplusDb.WolfKillPassiveReportArray.Builder passive = builder.getPassiveReportBuilder();
        int index = -1;
        for (int i = 0; i < passive.getReportsList().size(); i++) {
            if (passive.getReports(i).getBattleId() == battleId) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            boolean hasReport = false;
            TcaplusDb.WolfKillPassiveReport.Builder report = passive.getReportsBuilder(index);
            for (int i = 0; i < report.getPlayerListCount(); i++) {
                if (report.getPlayerList(i) == req.getReporter()) {
                    hasReport = true;
                    break;
                }
            }
            if (hasReport) {
                return;
            }
            report.addPlayerList(req.getReporter());
            TcaplusUtil.newUpdateReq(builder).send();
        }
        boolean isEnd = req.getIsEnd();
        if (isEnd) {
            if (index == -1) {
                return;
            }
            TcaplusDb.WolfKillPassiveReport.Builder report = passive.getReportsBuilder(index);
            int passiveTime = MiscConf.getInstance().getMiscConf().getWolfKillPassiveTime();
            if (report.getPassiveTime() < passiveTime) {
                return;
            }
            if (report.getIsHasSub()) {
                MailInteraction.sendTemplateMail(req.getReporter(), 135, MailInteraction.TlogSendReason.wolfKillReport, "");
                return;
            }
            int curScore = builder.getReputationScore();
            if (curScore <= 0) {
                return;
            }
            int subScore = MiscConf.getInstance().getMiscConf().getWolfKillPassiveScore();
            int score = subScore * -1;
            int totalScore = curScore + score;
            if (totalScore < 0) {
                totalScore = 0;
            }
            builder.setReputationScore(totalScore);
            TcaplusDb.WolfKillScoreRecord.Builder recordBuilder = TcaplusDb.WolfKillScoreRecord.newBuilder();
            recordBuilder.setDt(Framework.currentTimeMillis());
            recordBuilder.setTotalScore(totalScore);
            recordBuilder.setDeltaScore(score);
            recordBuilder.setReason(104);
            builder.getScoreRecordBuilder().addRecords(recordBuilder);
            int maxRecord = MiscConf.getInstance().getMiscConf().getWolfKillMaxRecord();
            if (builder.getScoreRecordBuilder().getRecordsCount() >= maxRecord) {
                builder.getScoreRecordBuilder().removeRecords(0);
            }
            MailInteraction.sendTemplateMail(beReporter, 144, MailInteraction.TlogSendReason.wolfKillReport, subScore);
            index = Math.min(passive.getReportsList().size(), MiscConf.getInstance().getMiscConf().getWolfKillPassiveActionScoreList().size()) - 1;
            subScore = MiscConf.getInstance().getMiscConf().getWolfKillPassiveActionScore(index);
            curScore = builder.getActionScore();
            totalScore = curScore - subScore;
            int max = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreMax();
            int min = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreMin();
            if (totalScore > max) {
                totalScore = max;
            }
            if (totalScore < min) {
                totalScore = min;
            }
            builder.setActionScore(totalScore);
        } else {
            if (index == -1) {
                TcaplusDb.WolfKillPassiveReport.Builder report = TcaplusDb.WolfKillPassiveReport.newBuilder();
                report.setBattleId(battleId);
                report.setDt(DateUtils.currentTimeMillis());
                report.addPlayerList(req.getReporter());
                passive.addReports(report);
            }
        }
        TcaplusUtil.newUpdateReq(builder).send();
    }

    public void updatePassiveTime(long battleId, int time) {
        LOGGER.debug("updatePassiveTime, uid is {}, battleId is {}, time is {}", player.getUid(), battleId, time);
        if (time <= 0) {
            return;
        }
        TcaplusDb.WolfKillPassiveReportArray.Builder passive = wolfKillBuilder.getPassiveReportBuilder();
        int index = -1;
        for (int i = 0; i < passive.getReportsList().size(); i++) {
            if (passive.getReports(i).getBattleId() == battleId) {
                index = i;
                break;
            }
        }
        if (index == -1) {
            TcaplusDb.WolfKillPassiveReport.Builder report = TcaplusDb.WolfKillPassiveReport.newBuilder();
            report.setBattleId(battleId);
            report.setPassiveTime(time);
            report.setDt(DateUtils.currentTimeMillis());
            passive.addReports(report);
        } else {
            TcaplusDb.WolfKillPassiveReport.Builder report = passive.getReportsBuilder(index);
            report.setPassiveTime(time).setDt(DateUtils.currentTimeMillis());
            int passiveTime = MiscConf.getInstance().getMiscConf().getWolfKillPassiveTime();
            if (report.getPassiveTime() >= passiveTime) {
                report.setIsHasSub(true);
                int subScore = MiscConf.getInstance().getMiscConf().getWolfKillPassiveScore();
                subWolfKillReputationScore(subScore, 104, 0, "", "", "", "", 0, 0);
                for (int i = 0; i < report.getPlayerListCount(); i++) {
                    MailInteraction.sendTemplateMail(report.getPlayerList(i), 135, MailInteraction.TlogSendReason.wolfKillReport, player.getName());
                }
                index = Math.min(passive.getReportsList().size(), MiscConf.getInstance().getMiscConf().getWolfKillPassiveActionScoreList().size()) - 1;
                subWolfKillActionScore(MiscConf.getInstance().getMiscConf().getWolfKillPassiveActionScore(index), "", "", "", 104);
            }
        }
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        LOGGER.debug("updatePassiveTime, builder is {}", wolfKillBuilder);
    }

    public void verbalViolationReputationScorePunishProcess(int type, long battleId, int battleType) {
        TcaplusDb.WolfKillViolationArray.Builder violation = wolfKillBuilder.getWolfKillViolationBuilder();
        for (TcaplusDb. WolfKillViolation v : violation.getViolationsList()) {
            if (v.getBattleId() == battleId) {
                return;
            }
        }
        TcaplusDb. WolfKillViolation.Builder v = TcaplusDb. WolfKillViolation.newBuilder();
        v.setBattleId(battleId);
        v.setTs(DateUtils.currentTimeMillis());
        violation.addViolations(v);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        switch (type) {
            case 1: {
                int subScore = MiscConf.getInstance().getMiscConf().getWolfKillTextScore();
                subWolfKillReputationScore(subScore, 105, 0, "", Long.toString(battleId),
                        Integer.toString(battleType), "", 0, 0);
                subWolfKillActionScore(MiscConf.getInstance().getMiscConf().getWolfKillTextActionScore(), "", "", "", 105);
                break;
            }
            case 2: {
                int subScore = MiscConf.getInstance().getMiscConf().getWolfKillVoiceScore();
                subWolfKillReputationScore(subScore, 106, 0, "", Long.toString(battleId),
                        Integer.toString(battleType), "", 0, 0);
                subWolfKillActionScore(MiscConf.getInstance().getMiscConf().getWolfKillTextActionScore(), "", "", "", 106);
                break;
            }
        }
    }

    public void verbalViolationReputationScoreUnPunishProcess(int score) {
        addWolfKillReputationScore(score, 203, 0, "", "",
                "", "", 0, 0);
    }

    private void calActionScore(int score, String battleID, String battleType, String mapID, int reason) {
        int max = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreMax();
        int min = MiscConf.getInstance().getMiscConf().getWolfKillActionScoreMin();
        int curScore = wolfKillBuilder.getActionScore();
        int totalScore = curScore + score;
        if (totalScore > max) {
            totalScore = max;
        }
        if (totalScore < min) {
            totalScore = min;
        }
        wolfKillBuilder.setActionScore(totalScore);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        LOGGER.debug("calActionScore, uid is {}, score is {}", player.getUid(), totalScore);

        int addOrReduce = 0;
        if (score <= 0) {
            addOrReduce = 1;
        }

        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.WolfKillActionScoreChange);
        TlogFlowMgr.sendSuspectBehaviorScoreFlow(player, battleID, battleType, mapID, score, addOrReduce,
                curScore, totalScore, reason);
    }

    public void addWolfKillActionScore(int score, String battleID, String battleType, String mapID, int reason) {
        calActionScore(score, battleID, battleType, mapID, reason);
    }

    public void subWolfKillActionScore(int score, String battleID, String battleType, String mapID, int reason) {
        calActionScore(score * -1, battleID, battleType, mapID, reason);
    }

    public void resultNetLog(String matchID, String battleID, String battleType, String mapID,
                             int campType, int campRoleType) {
        TlogFlowMgr.sendSuspectBehaviorScoreFlow(player, battleID, battleType, mapID, 0, 0,
                wolfKillBuilder.getActionScore(), wolfKillBuilder.getActionScore(), 1000);
        TlogFlowMgr.sendSuspectCreditFlow(player, matchID, battleID, battleType, mapID, campType, campRoleType,
               0, wolfKillBuilder.getReputationScore(), wolfKillBuilder.getReputationScore(), 1000);
    }

    public CsWolfkill.WolfkillToMasterReward_S2C_Msg.Builder getToMasterReward() {
        CsWolfkill.WolfkillToMasterReward_S2C_Msg.Builder repMsg = CsWolfkill.WolfkillToMasterReward_S2C_Msg.newBuilder();

        // 领取在db里
        TcaplusDb.WolfKillTable wolfKillTable = getWolfKillInfo();
        for (TcaplusDb.WolfKillRoadToMasterReward rewardItem : wolfKillTable.getRoadToMaster().getRewardList()) {
            CsWolfkill.WolfkillToMasterRewardItem.Builder item = CsWolfkill.WolfkillToMasterRewardItem.newBuilder();
            item.setToMasterId(rewardItem.getRoadToMasterId());
            item.setRecv(rewardItem.getRecv());
            repMsg.addRewardList(item);
        }

        repMsg.setScore(0);
        repMsg.setRank(0);
        try {
            repMsg.setScore(getRoleInfoTotalPoints());

            int rankId = RankingConfData.getInstance().getByRuleFirstOrDefault(RankRule.RR_PlayMode_Werewolf_Total, 0);
            if (rankId == 0) {
                LOGGER.error("getToMasterReward rankingConf is null");
                return repMsg;
            }
            PlayerRankInfo playerRankInfo = player.getRankManager()
                    .getSelf(rankId, RankIdSeasonIdMapper.ofNow(rankId).getId());
            if (playerRankInfo == null) {
//                LOGGER.error("getToMasterReward playerRankInfo is null");
                return repMsg;
            }
            repMsg.setRank(playerRankInfo.getRankNo());
            LOGGER.debug("wolfkill rankId：{}， rank :{}", rankId, playerRankInfo.getRankNo());
        } catch (Exception e) {
            LOGGER.error("getToMasterReward unexpected error", e);
        }

        return repMsg;
    }

    // 领取大师之路奖励
    public int recvToMasterReward(int toMasterId) {
        //先判断配置
        ResWolfKillRoadToMaster.WolfKillRoadToMasterItem roadToMasterItem = WolfKillRoadToMaster.get(toMasterId);
        if (roadToMasterItem == null) {
            return CsWolfkill.WolfkillToMasterRewardRecvResult.RECV_NO_CONFIG_VALUE;
        }

        LOGGER.debug("recvToMasterReward,roadToMasterItem id:{}, num:{}", roadToMasterItem.getRewardItemId(), roadToMasterItem.getRewardNum());

        int score = getRoleInfoTotalPoints();
        if (score < roadToMasterItem.getToMasterScore()) {
            return CsWolfkill.WolfkillToMasterRewardRecvResult.RECV_LOCK_VALUE;
        }

        TcaplusDb.WolfKillTable wolfKillTable = getWolfKillInfo();
        List<TcaplusDb.WolfKillRoadToMasterReward> rewardList = wolfKillTable.getRoadToMaster().getRewardList();
        for (TcaplusDb.WolfKillRoadToMasterReward rewardItem : rewardList) {
            if (rewardItem.getRoadToMasterId() == toMasterId) {
                return CsWolfkill.WolfkillToMasterRewardRecvResult.RECV_RECVED_VALUE;
            }
        }

        // 领取成功
        List<ItemInfo> listItem = new ArrayList<>();
        ItemInfo.Builder item = ItemInfo.newBuilder();
        item.setItemId(roadToMasterItem.getRewardItemId());
        item.setItemNum(roadToMasterItem.getRewardNum());
        listItem.add(item.build());
        player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_Wolfkill_RoadToMaster_VALUE, roadToMasterItem.getToMasterId());

        // 写回到领取表
        TcaplusDb.WolfKillRoadToMasterReward.Builder newReward = TcaplusDb.WolfKillRoadToMasterReward.newBuilder();
        newReward.setRecv(1);
        newReward.setRoadToMasterId(roadToMasterItem.getToMasterId());
        wolfKillBuilder.getRoadToMasterBuilder().addReward(newReward);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        return CsWolfkill.WolfkillToMasterRewardRecvResult.RECV_SUCCUSS_VALUE;
    }

    // 更新专精分排行榜
    private void updateRoleInfoRank(int roleType, TcaplusDb.WolfKillRoleInfoArray.Builder wolfKillRoleInfo, int score) {
        ResRanking.RankingConf rankingConfRole = RankingConfData.getInstance().getByDescId(RankRule.RR_PlayMode_Werewolf_Role, roleType);
        if (rankingConfRole != null) {
            player.getRankManager().refresh(rankingConfRole.getRankId(), score, 1);
        }


        ResNR3E3Vocation.NR3E3VocationData vocationData = WolfKillVocation.get(roleType);
        int totalScore = 0;
        int factionScore = 0;
        // 计算阵营的分数
        for (int i = 0; i < wolfKillRoleInfo.getRoleInfosCount(); i++) {
            totalScore += wolfKillRoleInfo.getRoleInfos(i).getPoints();
            ResNR3E3Vocation.NR3E3VocationData vocationDataItem = WolfKillVocation.get(wolfKillRoleInfo.getRoleInfos(i).getRoleType());
            if (vocationDataItem != null && vocationData != null && vocationDataItem.getFaction() == vocationData.getFaction()) {
                factionScore += wolfKillRoleInfo.getRoleInfos(i).getPoints();
            }
        }

        if (vocationData != null) {
            ResRanking.RankingConf rankingConfFaction = RankingConfData.getInstance()
                    .getByDescId(RankRule.RR_PlayMode_Werewolf_Faction, vocationData.getFaction());
            if (rankingConfFaction != null) {
                player.getRankManager().refresh(rankingConfFaction.getRankId(), factionScore, 1);
            }
        }

        int totalRankId = RankingConfData.getInstance().getByRuleFirstOrDefault(RankRule.RR_PlayMode_Werewolf_Total, 0);
        if (totalRankId != 0) {
            player.getRankManager().refresh(totalRankId, totalScore, 1);
        }
    }

    // 对局结束后的最终结算
    public void finalBattleSettlement(long interactId, PlayerInteraction.PiiDsSettlementParams dsSettlementParams) {
        int matchType = dsSettlementParams.getDetailData().getMatchType();
        long roomId = dsSettlementParams.getRoomId();
        long battleId = dsSettlementParams.getDetailData().getBattleId();

        GlobalBattleResult.Builder globalBattleResultBuilder = new GlobalBattleResult.Builder(
                dsSettlementParams.getBattleResult(),
                dsSettlementParams.getDetailData());
        globalBattleResultBuilder.
                setWarmRoundSettlementInfo(dsSettlementParams.getWarmRoundSettlement())
                .setRoomMemberList(dsSettlementParams.getRoomMemberList())
                .setSideMemberList(dsSettlementParams.getSideMemberList())
                .setChampionTeamInfo(dsSettlementParams.getChampionTeamInfo())
                .setBattleMemberList(dsSettlementParams.getBattleMemberList())
                .setDetailed(player.getPlayerBattleMgr().getBattleDetailedScore())
                .setCompetitionSettlementInfo(dsSettlementParams.getCompetitionSettlement())
                .setTegGameId(dsSettlementParams.getTegGameId());
        final GlobalBattleResult globalBattleResult = globalBattleResultBuilder.build();

        MemberBaseInfo.Builder battlePlayerMemberInfo = MemberBaseInfo.newBuilder();
        for (MemberBaseInfo memberInfo : dsSettlementParams.getBattleMemberList()) {
            if (memberInfo.getUid() == player.getUid()) {
                battlePlayerMemberInfo.mergeFrom(memberInfo);
                LOGGER.debug("uid:{} battleId:{} memberInfo:{} battlePlayerMemberInfo:{}", player.getUid(), battleId, memberInfo, battlePlayerMemberInfo);
                break;
            }
        }
        // 专精分最终结算
        roleInfoSettlement(globalBattleResult, battlePlayerMemberInfo);
    }

    // 专精分结算
    public WereWolfRoleInfoPointsInfo roleInfoSettlement(GlobalBattleResult globalBattleResult,
                                   MemberBaseInfo.Builder battlePlayerMemberInfo) {
        int matchType = globalBattleResult.getMatchType();
        long battleId = globalBattleResult.getBattleId();
        int playerNum = 0;
        ResMatch.MatchType conf = MatchTypeData.getInstance().get(matchType);
        if (null != conf) {
            // mark-glueli 狼人杀的 忽略处理
            playerNum = conf.getBattlePlayerNum();
        }
        if (playerNum != 8 && playerNum != 10 && playerNum != 12) {
            return null;
        }
        int roleType = globalBattleResult.getCampRole();
        int rank = globalBattleResult.getFinalRank();
        boolean isWin = globalBattleResult.getBattleResult() == G6Common.BattleResultCode.BATTLE_RESULT_CODE_WIN;
        boolean isMvp = globalBattleResult.isWolfMvp();

        LOGGER.info("WolfKill roleInfoSettlement player:{} battleId:{} matchType:{}  globalBattleResult:{}",
                player.getUid(), battleId, matchType, globalBattleResult.toString());
        
        return updateRoleInfoPointsV2(roleType, rank, isWin, isMvp, playerNum,
                    String.valueOf(battlePlayerMemberInfo.getMatchID()), String.valueOf(battleId),
                    String.valueOf(matchType), globalBattleResult.isGiveUp());
    }

    public void hyperCoreBattleSettlement(GlobalBattleResult globalBattleResult){
        LOGGER.debug("{} hyperCoreBattleSettlement, globalBattleResult:{}", player.getUid(), globalBattleResult);
        player.getUserAttr().getWolfKillInfo().addHyperCoreScore(1);
    }

    public void clearWolfKillHyperCoreScore() {
        LOGGER.debug("{} hyperCoreBattleSettlement, clearWolfKillHyperCoreScore", player.getUid());
        player.getUserAttr().getWolfKillInfo().setHyperCoreScore(-1);
    }

    // 狼人结算
    public void battleSettlement(GlobalBattleResult globalBattleResult,
                                      CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder,
                                      MemberBaseInfo.Builder battlePlayerMemberInfo) {
        int matchType = globalBattleResult.getMatchType();
        long battleId = globalBattleResult.getBattleId();
        long battleEndTime = globalBattleResult.getBattleEndTime();
        // 仅狼人对局生效
        if (!wolfKillCheck(matchType)) {
            return;
        }
        // 仅狼人匹配生效
        if (wolfKillCheckIsShouldBlock(matchType)) {
            matchBattleSettlement(globalBattleResult, ntfBuilder, battlePlayerMemberInfo);
        }

        // 连胜信息
        int campType = globalBattleResult.getCampType();
        boolean isWin = globalBattleResult.getBattleResult() == G6Common.BattleResultCode.BATTLE_RESULT_CODE_WIN;
        WereWolfRepeatWinInfo info = updateRepeatWinInfo(isWin, campType);
        ntfBuilder.setWereWolfRepeatWinInfo(info);
        LOGGER.debug("battleSettlement wolfKillCheck, isFinish:{}, uid:{}", globalBattleResult.isBattleFinished(), player.getUid());

        // 回归完成对局次数
        if (globalBattleResult.isBattleFinished()) {
            if (!(battleEndTime != 0 && battleEndTime < DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis()))) {
                addComeBackFinishCount();
            }
        }
        // 狼人对决上次选项记录
        int lastBrawlLastSelected = globalBattleResult.getWolfBrawlLastSelected();
        if (lastBrawlLastSelected > 0) {
            LOGGER.debug("{} setBrawlLastSelected: {}", player.getUid(), lastBrawlLastSelected);
            player.getUserAttr().getWolfKillInfo().setBrawlLastSelected(lastBrawlLastSelected);
        }
        // 狼人结算流水上报
        resultNetLog(String.valueOf(battlePlayerMemberInfo.getMatchID()), String.valueOf(battleId), String.valueOf(matchType),
                    String.valueOf(battlePlayerMemberInfo.getMapId()), campType, globalBattleResult.getCampRole());

    }
    // 狼人匹配结算
    public void matchBattleSettlement(GlobalBattleResult globalBattleResult,
                                   CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder,
                                   MemberBaseInfo.Builder battlePlayerMemberInfo) {
        long battleId = globalBattleResult.getBattleId();
        // giveup或对局未结束，此时阵容可能会赢，需要另外结算。
        boolean isShouldAdd = globalBattleResult.getWolfAddRoleInfoPoints() &&
                globalBattleResult.isBattleFinished() &&
                !globalBattleResult.isGiveUp();
        LOGGER.debug("wolfKillCheckIsShouldBlock isShouldAdd: {}, giveup:{}, finishBattle:{}", isShouldAdd,
                globalBattleResult.isGiveUp(),
                globalBattleResult.isBattleFinished());
        if (isShouldAdd) {
            // 专精分结算
            WereWolfRoleInfoPointsInfo wereWolfRoleInfoPointsInfo = roleInfoSettlement(globalBattleResult, battlePlayerMemberInfo);
            ntfBuilder.setWereWolfRoleInfoPointsInfo(wereWolfRoleInfoPointsInfo);
        }

        // 狼人杀的赛季奖励统一发放
        if (player.getUserAttr().getWolfKillSeasonRewardListSize()>0){
            LOGGER.debug("wolfKillCheckIsShouldBlock isShouldAdd: {}, itemSize: {}", isShouldAdd, player.getUserAttr().getWolfKillSeasonRewardListSize());
            List<ItemInfo> listItem = new ArrayList<>();
            for (WolfKillRewardItem wolfKillRewardItem:player.getUserAttr().getWolfKillSeasonRewardList().values()){
                ItemInfo.Builder item = ItemInfo.newBuilder();
                item.setItemId(wolfKillRewardItem.getItemId());
                item.setItemNum(wolfKillRewardItem.getNum());
                listItem.add(item.build());
            }
            player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_WolfKill_Season_VALUE, 0);
            player.getUserAttr().clearWolfKillSeasonRewardList();
        }
        // 消极比赛扣分
        updatePassiveTime(battleId, globalBattleResult.getWolfKillPassiveTime());
        // 狼人超凡分
        hyperCoreBattleSettlement(globalBattleResult);
    }

    //修改为按策略加专精
    public WereWolfRoleInfoPointsInfo updateRoleInfoPointsV2(int roleType, int rank, boolean isWin, boolean isMvp,
                                                             int mode, String matchId, String battleId, String battleType, boolean bGiveUp) {
        WereWolfRoleInfoPointsInfo.Builder pointsInfoBuilder = WereWolfRoleInfoPointsInfo.newBuilder();
        pointsInfoBuilder.setIsWin(isWin ? 1 : 0);
        TcaplusDb.WolfKillRoleInfoArray.Builder roleInfoBuilder = wolfKillBuilder.getRoleInfoBuilder();
        int wolfNewPlayer = wolfKillBuilder.getWolfNewPlayer();
        int index = -1;
        for (int i = 0; i < roleInfoBuilder.getRoleInfosCount(); i++) {
            if (roleInfoBuilder.getRoleInfos(i).getRoleType() == roleType) {
                index = i;
                break;
            }
        }
        int curPoints = 0;
        if (index >= 0) {
            curPoints = roleInfoBuilder.getRoleInfosBuilder(index).getPoints();
        }

        LOGGER.debug("updateRoleInfoPointsV2, roleType:{}, isWin:{}, isMvp:{}, mode:{}, matchId:{}, battleId:{}, battleType:{}, bGiveUp:{}", roleType, isWin, isMvp,
                mode, matchId, battleId, battleType, bGiveUp);

        // 没配置的职业
        ResNR3E3Vocation.NR3E3VocationData vocationData = WolfKillVocation.get(roleType);
        if (vocationData == null) {
            LOGGER.error("updateRoleInfoPointsV2 error, vocationMapData is null,roleType:{}, battleId:{}", roleType, battleId);
            pointsInfoBuilder.setRoleType(roleType);
            pointsInfoBuilder.setCurPoints(curPoints);
            pointsInfoBuilder.setMaxPoints(0);
            pointsInfoBuilder.setAddPoints(0);
            TcaplusDb.WolfKillRoleInfo.Builder roleInfo;
            if (index >= 0) {
                roleInfo = roleInfoBuilder.getRoleInfosBuilder(index);
            } else {
                roleInfo = TcaplusDb.WolfKillRoleInfo.newBuilder();
                roleInfo.setRoleType(roleType);
            }
            roleInfo.setTotalTimes(roleInfo.getTotalTimes() + 1);
            if (isWin) {
                roleInfo.setWinTimes(roleInfo.getWinTimes() + 1);
            }
            if (isMvp) {
                roleInfo.setMvpTimes(roleInfo.getMvpTimes() + 1);
            }
            if (index < 0) {
                roleInfoBuilder.addRoleInfos(roleInfo);
            }
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
            return pointsInfoBuilder.build();
        }
        // 没配置的策略
        List<ResWolfKillRoleInfoRate.WolfKillRoleInfoRateItem> roleInfoList =
                WolfKillRoleInfoRate.getInstance().getListByTypeId(vocationData.getRoleInfoPointsAddTypeId());
        if (roleInfoList == null) {
            LOGGER.info("updateRoleInfoPointsV2 cannot add rolePoints, roleInfoList is null, roleType:{},typeId:{},battleId:{}",
                    roleType, vocationData.getRoleInfoPointsAddTypeId(), battleId
            );
            pointsInfoBuilder.setRoleType(roleType);
            pointsInfoBuilder.setCurPoints(curPoints);
            pointsInfoBuilder.setMaxPoints(0);
            pointsInfoBuilder.setAddPoints(0);
            TcaplusDb.WolfKillRoleInfo.Builder roleInfo;
            if (index >= 0) {
                roleInfo = roleInfoBuilder.getRoleInfosBuilder(index);
            } else {
                roleInfo = TcaplusDb.WolfKillRoleInfo.newBuilder();
                roleInfo.setRoleType(roleType);
            }
            roleInfo.setTotalTimes(roleInfo.getTotalTimes() + 1);
            if (isWin) {
                roleInfo.setWinTimes(roleInfo.getWinTimes() + 1);
            }
            if (isMvp) {
                roleInfo.setMvpTimes(roleInfo.getMvpTimes() + 1);
            }
            if (index < 0) {
                roleInfoBuilder.addRoleInfos(roleInfo);
            }
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
            return pointsInfoBuilder.build();
        }
        // 没解锁的职业，不加专精点。在彩蛋局中，可以使用未解锁的职业
        if (!WolfKillVocation.getInstance().getUnlockList().contains(roleType)){
//            if (!player.getBagManager().isItemsEnough(
//                    vocationData.getIdentityItemUnlock(), 1, ItemChangeReason.ICR_WereWolfSetSideIdentity)) {
            if (player.getBagManager().getItemNumByItemIdIgnoreTemp(
                    vocationData.getIdentityItemUnlock())==0) {
                LOGGER.info("updateRoleInfoPointsV2 cannot add rolePoints, roleType lock, roleType:{},typeId:{},battleId:{}",
                        roleType, vocationData.getRoleInfoPointsAddTypeId(), battleId
                );
                TcaplusDb.WolfKillRoleInfo.Builder roleInfo;
                if (index >= 0) {
                    roleInfo = roleInfoBuilder.getRoleInfosBuilder(index);
                } else {
                    roleInfo = TcaplusDb.WolfKillRoleInfo.newBuilder();
                    roleInfo.setRoleType(roleType);
                }
                roleInfo.setTotalTimes(roleInfo.getTotalTimes() + 1);
                if (isWin) {
                    roleInfo.setWinTimes(roleInfo.getWinTimes() + 1);
                }
                if (isMvp) {
                    roleInfo.setMvpTimes(roleInfo.getMvpTimes() + 1);
                }
                if (index < 0) {
                    roleInfoBuilder.addRoleInfos(roleInfo);
                }
                TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
                pointsInfoBuilder.setRoleType(roleType);
                pointsInfoBuilder.setCurPoints(curPoints);
                pointsInfoBuilder.setMaxPoints(0);
                pointsInfoBuilder.setAddPoints(0);
                return pointsInfoBuilder.build();
            }
        }

        int rankPoints = 0;
        int maxPoints = 0;
        for (ResWolfKillRoleInfoRate.WolfKillRoleInfoRateItem rateItem : roleInfoList) {
            if (curPoints >= rateItem.getMin() && curPoints <= rateItem.getMax()) {
                if (isMvp) {
                    if (wolfKillCheckIsTeamMatchType(battleType)) {
                        rankPoints = rateItem.getMvpScoreTeam() + rateItem.getWinScoreTeam();
                    } else if (mode == 8) {
                        rankPoints = rateItem.getMvpScore() + rateItem.getWinScore();
                    } else if (mode == 10 || mode == 12) {
                        rankPoints = rateItem.getMvpScore10() + rateItem.getWinScore10();
                    }
                } else if (isWin) {
                    if (wolfKillCheckIsTeamMatchType(battleType)) {
                        rankPoints = rateItem.getWinScoreTeam();
                    } else if (mode == 8) {
                        rankPoints = rateItem.getWinScore();
                    } else if (mode == 10 || mode == 12) {
                        rankPoints = rateItem.getWinScore10();
                    }
                } else {
                    if (wolfKillCheckIsTeamMatchType(battleType)) {
                        rankPoints = rateItem.getLoseScoreTeam();
                    } else if (mode == 8) {
                        rankPoints = rateItem.getLoseScore();
                    } else if (mode == 10 || mode == 12) {
                        rankPoints = rateItem.getLoseScore10();
                    }
                }
            }
            if (rateItem.getMax() > maxPoints) {
                maxPoints = rateItem.getMax();
            }
        }

        // 放弃改成0
        if (bGiveUp) {
            rankPoints = 0;
        }

        int addPoints = rankPoints;
        TcaplusDb.WolfKillRoleInfo.Builder roleInfo;
        if (index >= 0) {
            roleInfo = roleInfoBuilder.getRoleInfosBuilder(index);
        } else {
            roleInfo = TcaplusDb.WolfKillRoleInfo.newBuilder();
            roleInfo.setRoleType(roleType);
        }
        int beforePoints = roleInfo.getPoints();
        int beforeTotalPoints = getRoleInfoTotalPoints();
        if (roleInfo.getPoints() + addPoints > maxPoints) {
            // 确保专精点数不会减少
            if (roleInfo.getPoints() > maxPoints) {
                maxPoints = roleInfo.getPoints();
            }
            roleInfo.setPoints(maxPoints);
        } else {
            roleInfo.setPoints(roleInfo.getPoints() + addPoints);
        }
        roleInfo.setTotalTimes(roleInfo.getTotalTimes() + 1);
        if (isWin) {
            roleInfo.setWinTimes(roleInfo.getWinTimes() + 1);
        }
        if (isMvp) {
            roleInfo.setMvpTimes(roleInfo.getMvpTimes() + 1);
        }
        if (index < 0) {
            roleInfoBuilder.addRoleInfos(roleInfo);
        }
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        TlogFlowMgr.sendSuspectPerformFlow(player, matchId, battleId, battleType, roleType, addPoints,
                beforePoints, roleInfo.getPoints(), beforeTotalPoints, getRoleInfoTotalPoints());

        pointsInfoBuilder.setRoleType(roleType);
        pointsInfoBuilder.setCurPoints(curPoints);
        pointsInfoBuilder.setMaxPoints(maxPoints);
        pointsInfoBuilder.setAddPoints(addPoints);

        // 更新排行榜
        updateRoleInfoRank(roleType, roleInfoBuilder, roleInfo.getPoints());
        // 消息通知
        new PlayerWolfKillRoleInfoPointsEvent(player, roleInfo.getPoints() - beforePoints).dispatch();
        player.getPlayerEventManager().dispatch(new PlayerWolfKillCumulativeRoleInfoPointsEvent(player.getConditionMgr())
                .setPoints(roleInfo.getPoints() - beforePoints));
        //对局数变动
        wolfKillRoleInfoPointsEvent();
        LOGGER.debug("player {} {} WolfNewPlayer:{},roleInfoTotalTimes:{}", player.getUid(), player.getName(),
                wolfKillBuilder.getWolfNewPlayer(), getRoleInfoTotalTimes());
        return pointsInfoBuilder.build();
    }

    public void wolfKillRoleInfoPointsEvent() {
        int roleInfoTotalTimes = getRoleInfoTotalTimes();
        if (wolfKillBuilder.getWolfNewPlayer() == CommonActivityEnum.WolfPlayerType.NO_WOLF_NEW_PLAYER && roleInfoTotalTimes == 0) {
            wolfKillBuilder.setWolfNewPlayer(CommonActivityEnum.WolfPlayerType.WOLF_NEW_PLAYER);
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
            LOGGER.debug("player {} {} is WolfNewPlayer", player.getUid(), player.getName());
        }
        if (wolfKillBuilder.getWolfNewPlayer() == CommonActivityEnum.WolfPlayerType.WOLF_NEW_PLAYER) {
            player.getPlayerEventManager().dispatch(new WolfNewPlayerRoleInfoTotalTimesEvent(
                    player.getConditionMgr()).setWolfNewPlayer(wolfKillBuilder.getWolfNewPlayer())
                    .setWolfNewPlayerTotalTimes(roleInfoTotalTimes));//狼人对局数更新
        }
    }

    public int getWoldNewPlayer() {
        if (wolfKillBuilder == null) {
            return 0;
        }
        return wolfKillBuilder.getWolfNewPlayer();
    }

    public void gmWoldNewPlayer() {
        if (wolfKillBuilder != null && wolfKillBuilder.getWolfNewPlayer() != 1) {
            wolfKillBuilder.setWolfNewPlayer(1);
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        }
    }

    public int getRoleInfoTotalPoints() {
        int totalPoints = 0;
        if (wolfKillBuilder != null) {
            for (TcaplusDb.WolfKillRoleInfo info : wolfKillBuilder.getRoleInfo().getRoleInfosList()) {
                totalPoints += info.getPoints();
            }
        }
        return totalPoints;
    }

    public int getRoleInfoTotalTimes() {
        int totalTimes = 0;
        if (wolfKillBuilder == null || wolfKillBuilder.getRoleInfo() == null) {
            return 0;
        }
        for (TcaplusDb.WolfKillRoleInfo info : wolfKillBuilder.getRoleInfo().getRoleInfosList()) {
            totalTimes += info.getTotalTimes();
        }
        return totalTimes;
    }

    public void gmAddRoleInfoPoints(int roleType, int points) {
        TcaplusDb.WolfKillRoleInfoArray.Builder roleInfoBuilder = wolfKillBuilder.getRoleInfoBuilder();
        // roleType=0, 则随机设置roleType，使得总和达到points
        if (roleType==0){
            roleInfoBuilder.clear();
            int sumPoints = 0;
            for (ResNR3E3Vocation.NR3E3VocationData vocationData:WolfKillVocation.getInstance().dataArray){
                List<ResWolfKillRoleInfoRate.WolfKillRoleInfoRateItem> roleInfoList =
                        WolfKillRoleInfoRate.getInstance().getListByTypeId(vocationData.getRoleInfoPointsAddTypeId());
                if (roleInfoList == null) {
                    LOGGER.error("updateRoleInfoPointsV2 error, roleInfoList is null");
                    continue;
                }
                int maxPoints = 0;
                for (ResWolfKillRoleInfoRate.WolfKillRoleInfoRateItem rateItem : roleInfoList) {
                    if (rateItem.getMax() > maxPoints) {
                        maxPoints = rateItem.getMax();
                    }
                }
                roleType = vocationData.getId();
                if (points-sumPoints>0){
                    int addPoints = Math.min(points - sumPoints, maxPoints);
                    TcaplusDb.WolfKillRoleInfo.Builder roleInfo;
                    roleInfo = TcaplusDb.WolfKillRoleInfo.newBuilder();
                    roleInfo.setRoleType(roleType);
                    roleInfo.setPoints(addPoints);
                    roleInfoBuilder.addRoleInfos(roleInfo);
                    sumPoints += addPoints;

                    // 更新排行榜
                    updateRoleInfoRank(roleType, roleInfoBuilder, roleInfo.getPoints());
                    // 消息通知
                    new PlayerWolfKillRoleInfoPointsEvent(player, roleInfo.getPoints()).dispatch();
                    player.getPlayerEventManager().dispatch(new PlayerWolfKillCumulativeRoleInfoPointsEvent(player.getConditionMgr())
                            .setPoints(roleInfo.getPoints()));
                } else{
                    break;
                }
            }
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
            return;
        }

        int index = -1;
        for (int i = 0; i < roleInfoBuilder.getRoleInfosCount(); i++) {
            if (roleInfoBuilder.getRoleInfos(i).getRoleType() == roleType) {
                index = i;
                break;
            }
        }
        int maxPoints = 0;
        ResNR3E3Vocation.NR3E3VocationData vocationData = WolfKillVocation.get(roleType);
        if (vocationData == null) {
            LOGGER.error("updateRoleInfoPointsV2 error, vocationMapData is null");
            return;
        }
        List<ResWolfKillRoleInfoRate.WolfKillRoleInfoRateItem> roleInfoList =
                WolfKillRoleInfoRate.getInstance().getListByTypeId(vocationData.getRoleInfoPointsAddTypeId());
        if (roleInfoList == null) {
            LOGGER.error("updateRoleInfoPointsV2 error, roleInfoList is null");
            return;
        }
        for (ResWolfKillRoleInfoRate.WolfKillRoleInfoRateItem rateItem : roleInfoList) {
            if (rateItem.getMax() > maxPoints) {
                maxPoints = rateItem.getMax();
            }
        }

        TcaplusDb.WolfKillRoleInfo.Builder roleInfo;
        if (index >= 0) {
            roleInfo = roleInfoBuilder.getRoleInfosBuilder(index);
        } else {
            roleInfo = TcaplusDb.WolfKillRoleInfo.newBuilder();
            roleInfo.setRoleType(roleType);
        }
        if (roleInfo.getPoints() + points > maxPoints) {
            roleInfo.setPoints(maxPoints);
        } else {
            roleInfo.setPoints(roleInfo.getPoints() + points);
        }
        if (index < 0) {
            roleInfoBuilder.addRoleInfos(roleInfo);
        }
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        // 更新排行榜
        updateRoleInfoRank(roleType, roleInfoBuilder, roleInfo.getPoints());
        // 消息通知
        new PlayerWolfKillRoleInfoPointsEvent(player, roleInfo.getPoints()).dispatch();
        player.getPlayerEventManager().dispatch(new PlayerWolfKillCumulativeRoleInfoPointsEvent(player.getConditionMgr())
                .setPoints(roleInfo.getPoints()));
    }

    public void setFeedbackCount(int count) {
        wolfKillBuilder.setFeedbackCount(count);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public void setSeasonRecvCount(int count) {
        wolfKillBuilder.setSeasonRecvCount(count);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public WereWolfRepeatWinInfo updateRepeatWinInfo(boolean isWin, int campType) {
        WereWolfRepeatWinInfo.Builder info = WereWolfRepeatWinInfo.newBuilder();
        if (isWin) {
            int totalWin = wolfKillBuilder.getRepeatWin() + 1;
            wolfKillBuilder.setRepeatWin(totalWin);

            int campWin = 0;
            int index = -1;
            TcaplusDb.WolfKillCampRepeatWinArray.Builder campRepeatWinBuilder = wolfKillBuilder.getCampRepeatWinBuilder();
            for (int i = 0; i < campRepeatWinBuilder.getCampRepeatWinsCount(); i++) {
                if (campRepeatWinBuilder.getCampRepeatWinsBuilder(i).getCampType() == campType) {
                    index = i;
                    campWin = campRepeatWinBuilder.getCampRepeatWinsBuilder(i).getWinTime() + 1;
                    campRepeatWinBuilder.getCampRepeatWinsBuilder(i).setWinTime(campWin);
                    break;
                }
            }

            if (index < 0 && campType != 0) {
                campWin = 1;
                campRepeatWinBuilder.addCampRepeatWins(TcaplusDb.WolfKillCampRepeatWin.newBuilder()
                        .setCampType(campType).setWinTime(campWin).build());
            }

            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

            info.setTotalRepeatWin(totalWin);
            info.setCampRepeatWin(campWin);
        } else {
            wolfKillBuilder.setRepeatWin(0);
            TcaplusDb.WolfKillCampRepeatWinArray.Builder campRepeatWinBuilder = wolfKillBuilder.getCampRepeatWinBuilder();
            for (int i = 0; i < campRepeatWinBuilder.getCampRepeatWinsCount(); i++) {
                if (campRepeatWinBuilder.getCampRepeatWinsBuilder(i).getCampType() == campType) {
                    campRepeatWinBuilder.getCampRepeatWinsBuilder(i).setWinTime(0);
                    break;
                }
            }

            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

            info.setTotalRepeatWin(0);
            info.setCampRepeatWin(0);
        }
        LOGGER.debug("updateRepeatWinInfo uid is {}, isWin is {}, campType is {}, totalWin is {}, campWin is {}",
                player.getUid(), isWin, campType, info.getTotalRepeatWin(), info.getCampRepeatWin());
        return info.build();
    }

    public void openComeBack() {
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        comeBack.clear();
        long curTs = DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis());
        comeBack.setOpenTs(curTs);
        long duration = MiscConf.getInstance().getMiscConf().getWolfKillComeBackDays() * DateUtils.ONE_DAY_MILLIS;
        comeBack.setEndTs(curTs + duration);
        comeBack.setUnlockCount(1);
        for (ResWolfKillComeBack.WolfKillComeBackItem conf : WolfKillComeBack.getInstance().getArrayList()) {
            List<Integer> exist = new ArrayList<>();
            List<Integer> notExist = new ArrayList<>();
            for (int itemId : conf.getPoolList()) {
                if (player.getBagManager().getItemNumByItemId(itemId) > 0) {
                    exist.add(itemId);
                } else {
                    notExist.add(itemId);
                }
            }
            LOGGER.debug("openComeBack notExist size is {}, exist is {}, conf count is {}", notExist.size(), exist.size(), conf.getCount());

            TcaplusDb.WolfKillComeBackReward.Builder reward = TcaplusDb.WolfKillComeBackReward.newBuilder();
            reward.setId(conf.getId());
            if (notExist.size() > conf.getCount()) {
                for (int i = 0; i < conf.getCount(); i++) {
                    int randNum = (int) (Math.random() * notExist.size());
                    LOGGER.debug("openComeBack notExist size is {}, randNum is {}", notExist.size(), randNum);
                    reward.addItemList(notExist.get(randNum));
                    notExist.remove(randNum);
                }
            } else {
                for (int itemId : notExist) {
                    reward.addItemList(itemId);
                }
                for (int i = 0; i < conf.getCount() - notExist.size(); i++) {
                    int randNum = (int) (Math.random() * exist.size());
                    LOGGER.debug("openComeBack exist size is {}, randNum is {}", exist.size(), randNum);
                    reward.addItemList(exist.get(randNum));
                    exist.remove(randNum);
                }
            }
            comeBack.addRewardList(reward);
        }
        LOGGER.debug("openComeBack comeBack is {}, uid is {}", comeBack, player.getUid());
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        int score = MiscConf.getInstance().getMiscConf().getWolfKillReLoginActionScore();
        addWolfKillActionScore(score, "", "", "", 204);

        TlogFlowMgr.sendSuspectRefluxFlow(player);
    }

    public void checkComeBackSystemOpen() {
        if (comeBackSysIsOpen()) {
            LOGGER.debug("CheckComeBackSystemOpen is opening, uid is {}", player.getUid());
            return;
        }

        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        LOGGER.debug("CheckComeBackSystemOpen rewardList count is {}, uid is {}", comeBack.getRewardListCount(), player.getUid());
        if (comeBack.getRewardListCount() > 0) {
            if (comeBack.getRewardList(comeBack.getRewardListCount()-1).getIsHasGet()) {
                LOGGER.debug("CheckComeBackSystemOpen has get role, uid is {}", player.getUid());
                return;
            }
        }

        if (!player.getUserAttr().getUnLockGameModeSet().contains(151)) {
            LOGGER.debug("CheckComeBackSystemOpen not contains 151, uid is {}", player.getUid());
            return;
        }

        if (wolfKillBuilder.getLatestTs() <= 0) {
            long latestTs = 0L;
            BattleHistoryUtil.Result result = BattleHistoryUtil.queryAllSnapshotRecord(0, player.getUid());
            for (TcaplusDb.BattleHistory record : result.records) {
                if (wolfKillCheck(record.getBattleDetailData().getMatchType())) {
                    latestTs = DateUtils.getDayBeginTimeMs(record.getBattleDetailData().getBattleStartTime());
                    break;
                }
            }
            if (latestTs <= 0) {
                wolfKillBuilder.setLatestTs(1);
            } else {
                wolfKillBuilder.setLatestTs(latestTs);
            }
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        }

        long curTs = DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis());
        long duration = MiscConf.getInstance().getMiscConf().getWolfKillComeBackTrigerDays() * DateUtils.ONE_DAY_MILLIS;
        LOGGER.debug("CheckComeBackSystemOpen latestTs is {}, curTs is {}, duration is {}, uid is {}", wolfKillBuilder.getLatestTs(), curTs, duration, player.getUid());
        if (wolfKillBuilder.getLatestTs() == 1 || curTs - DateUtils.getDayBeginTimeMs(wolfKillBuilder.getLatestTs()) <= duration) {
            return;
        }
        LOGGER.debug("CheckComeBackSystemOpen endTs is {}, curTs is {}, duration is {}, uid is {}", comeBack.getEndTs(), curTs, duration, player.getUid());
        if (curTs - comeBack.getEndTs() < duration) {
            return;
        }

        LOGGER.debug("CheckComeBackSystemOpen open, uid is {}", player.getUid());
        openComeBack();
    }

    public boolean conditionWolfReturnPlayer(){
        if (wolfKillBuilder == null) {
            LOGGER.debug("WolfKillInfo is null loadWolfKillInfo, uid is {}", player.getUid());
            loadWolfKillInfo();
            LOGGER.debug("WolfKillInfo is null loadWolfKillInfo end, uid is {}", player.getUid());
            if (wolfKillBuilder == null) {
                LOGGER.debug("WolfKillInfo is null loadWolfKillInfo end after, uid is {}", player.getUid());
                return false;
            }
        }
        if(wolfKillBuilder.getWolfKillComeBack() == null){
            LOGGER.debug("wolfKillBuilder.getWolfKillComeBack is null loadWolfKillInfo, uid is {}", player.getUid());
            return false;
        }
        checkComeBackSystemOpen();
        return comeBackSysIsOpen();
    }

    private boolean comeBackSysIsOpen() {
        TcaplusDb.WolfKillComeBack comeBack = wolfKillBuilder.getWolfKillComeBack();
        if (comeBack.getOpenTs() <= 0) {
            LOGGER.debug("ComeBackSysIsOpen openTs less than 0, uid is {}", player.getUid());
            return false;
        }
        if (DateUtils.currentTimeMillis() >= comeBack.getEndTs()) {
            LOGGER.debug("ComeBackSysIsOpen timeout, uid is {}", player.getUid());
            return false;
        }
        boolean isGetAll = true;
        for (TcaplusDb.WolfKillComeBackReward reward : comeBack.getRewardListList()) {
            if (!reward.getIsHasGet()) {
                isGetAll = false;
                break;
            }
        }
        if (isGetAll) {
            LOGGER.debug("ComeBackSysIsOpen get all reward, uid is {}", player.getUid());
            return false;
        }
        LOGGER.debug("ComeBackSysIsOpen open, uid is {}", player.getUid());
        return true;
    }

    public void updateLatestTs(long latestTs) {
        wolfKillBuilder.setLatestTs(latestTs);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public void addComeBackUnlockCount() {
        if (!comeBackSysIsOpen()) {
            return;
        }
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        int unlockCount = comeBack.getUnlockCount();
        LOGGER.debug("AddComeBackUnlockCount unlockCount is {} uid is {}", unlockCount, player.getUid());

        int max = 0;
        for (ResWolfKillComeBack.WolfKillComeBackItem item : WolfKillComeBack.getInstance().getArrayList()) {
            if (item.getDay() > max) {
                max = item.getDay();
            }
        }
        if (unlockCount >= max) {
            return;
        }
        if (unlockCount > comeBack.getFinishCount()) {
            return;
        }

        comeBack.setUnlockCount(unlockCount + 1);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public void addComeBackFinishCount() {
        if (!comeBackSysIsOpen()) {
            return;
        }
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        int unlockCount = comeBack.getUnlockCount();
        int finishCount = comeBack.getFinishCount();
        LOGGER.debug("AddComeBackFinishCount unlockCount is {}, finishCount is {} uid is {}", unlockCount, finishCount, player.getUid());
        if (finishCount >= unlockCount) {
            return;
        }
        comeBack.setFinishCount(finishCount + 1);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public void addComeBackActionScore() {
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        if (comeBack.getOpenTs() <= 0) {
            return;
        }
        int day = MiscConf.getInstance().getMiscConf().getWolfKillComeBackDays();
        if (DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis()) > comeBack.getOpenTs() + day * DateUtils.ONE_DAY_MILLIS) {
            return;
        }
        int score = MiscConf.getInstance().getMiscConf().getWolfKillReLoginActionScore();
        addWolfKillActionScore(score, "", "", "", 204);
    }

    public CsWolfkill.WolfKillGetComeBackInfo_S2C_Msg.Builder getComeBackInfo() {
        checkComeBackSystemOpen();
        CsWolfkill.WolfKillGetComeBackInfo_S2C_Msg.Builder resp = CsWolfkill.WolfKillGetComeBackInfo_S2C_Msg.newBuilder();
        if (!comeBackSysIsOpen()) {
            resp.setIsOpen(false);
            return resp;
        }
        resp.setIsOpen(true);
        TcaplusDb.WolfKillComeBack comeBack = wolfKillBuilder.getWolfKillComeBack();
        CsWolfkill.WolfKillComeBackInfo.Builder info = CsWolfkill.WolfKillComeBackInfo.newBuilder();
        info.setOpenTs(comeBack.getOpenTs());
        info.setEndTs(comeBack.getEndTs() - 1000);
        info.setUnlockCount(comeBack.getUnlockCount());
        info.setFinishCount(comeBack.getFinishCount());

        for (TcaplusDb.WolfKillComeBackReward reward : comeBack.getRewardListList()) {
            CsWolfkill.WolfKillComeBackReward.Builder item = CsWolfkill.WolfKillComeBackReward.newBuilder();
            item.setId(reward.getId());
            for (int id : reward.getItemListList()) {
                CsWolfkill.WolfKillComeBackRewardItem.Builder temp = CsWolfkill.WolfKillComeBackRewardItem.newBuilder();
                temp.setItemId(id);
                temp.setItemAmount(1);
                item.addSelectList(temp);
            }
            ResWolfKillComeBack.WolfKillComeBackItem conf = WolfKillComeBack.getInstance().get(reward.getId());
            if (conf != null) {
                CsWolfkill.WolfKillComeBackRewardItem.Builder temp = CsWolfkill.WolfKillComeBackRewardItem.newBuilder();
                temp.setItemId(conf.getItemId());
                temp.setItemAmount(conf.getItemAmount());
                item.addSelectList(temp);
            }
            item.setIsHasGet(reward.getIsHasGet());
            info.addRewardList(item);
        }

        LOGGER.debug("getComeBackInfo info is {}", info);

        resp.setInfo(info);
        return resp;
    }

    public void getComeBackReward(int id, int index) {
        LOGGER.debug("getComeBackReward, id is {}, index is {}, uid is {}", id, index, player.getUid());
        if (!comeBackSysIsOpen()) {
            NKErrorCode.InvalidParams.throwError("ComeBack System not open");
            return;
        }
        ResWolfKillComeBack.WolfKillComeBackItem conf = WolfKillComeBack.getInstance().get(id);
        if (conf == null) {
            LOGGER.error("getComeBackReward conf not found, id is {}, uid is {}", id, player.getUid());
            NKErrorCode.ResNotFound.throwError("conf not found");
            return;
        }
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        if (comeBack.getFinishCount() < conf.getDay()) {
            LOGGER.error("getComeBackReward not finish, finishCount is {}, conf is {}, uid is {}", comeBack.getFinishCount(), conf.getDay(), player.getUid());
            NKErrorCode.InvalidParams.throwError("not finish");
            return;
        }
        TcaplusDb.WolfKillComeBackReward.Builder rewardItem = null;
        for (TcaplusDb.WolfKillComeBackReward.Builder item : comeBack.getRewardListBuilderList()) {
            if (item.getId() == id) {
                rewardItem = item;
                break;
            }
        }
        if (rewardItem == null) {
            LOGGER.error("getComeBackReward, db has no data, uid is {}", player.getUid());
            NKErrorCode.InvalidParams.throwError("db has no data");
            return;
        }
        if (rewardItem.getIsHasGet()) {
            LOGGER.error("getComeBackReward, has get reward, uid is {}", player.getUid());
            NKErrorCode.InvalidParams.throwError("has get reward");
            return;
        }
        if (index < 0 || index > rewardItem.getItemListCount()) {
            LOGGER.error("getComeBackReward index error, index is {}, uid is {}", index, player.getUid());
            NKErrorCode.InvalidParams.throwError("index error");
            return;
        }
        rewardItem.setIsHasGet(true);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        List<ItemInfo> listItem = new ArrayList<>();
        if (index < rewardItem.getItemListCount()) {
            ItemInfo.Builder item = ItemInfo.newBuilder();
            item.setItemId(rewardItem.getItemList(index));
            item.setItemNum(1);
            if (conf.getLimit() > 0) {
                LOGGER.debug("getComeBackReward ExpireTimeMs is {}, uid is {}", conf.getLimit() * DateUtils.ONE_DAY_MILLIS, player.getUid());
                item.setExpireTimeMs(conf.getLimit() * DateUtils.ONE_DAY_MILLIS + DateUtils.currentTimeMillis());
            }
            listItem.add(item.build());
        } else {
            ItemInfo.Builder item = ItemInfo.newBuilder();
            item.setItemId(conf.getItemId());
            item.setItemNum(conf.getItemAmount());
            listItem.add(item.build());
        }
        player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_WolfKill_ComeBackReward_VALUE, conf.getDay());
    }

    public void resetComeBack() {
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        comeBack.clear();
        wolfKillBuilder.setLatestTs(0);
        LOGGER.debug("resetComeBack comeBack is {}, uid is {}", comeBack, player.getUid());
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    public boolean isHasGetComeBackRoleReward() {
        if (wolfKillBuilder == null) {
            return false;
        }
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        return comeBack.getRewardListCount() > 0 && comeBack.getRewardList(comeBack.getRewardListCount() - 1).getIsHasGet();
    }

    public long getComeBackEndTs() {
        if (wolfKillBuilder == null) {
            return 0;
        }
        if (!comeBackSysIsOpen()) {
            return 0;
        }
        TcaplusDb.WolfKillComeBack.Builder comeBack = wolfKillBuilder.getWolfKillComeBackBuilder();
        return comeBack.getEndTs();
    }

    public List<Integer> checkWolfKillTreasureOpen(){
        // 第一个等级的最低版本号
        List<Integer> listRet = new ArrayList<>();
        if(NR3E3Treasure.getInstance().dataArray.isEmpty()){
            return listRet;
        }
        ResNR3E3Treasure.NR3E3TreasureItem treasureItemConf = NR3E3Treasure.getInstance().dataArray.get(0);
        if (treasureItemConf.hasMinVersion() && VersionUtil.versionLT(player.getClientVersion64(), treasureItemConf.getMinVersion())) {
            LOGGER.debug("checkWolfKillTreasureOpen version too low ");
            return listRet;
        }

        // 判断是否拥有特殊道具
        HashSet<ItemType> listWolfKillAni = new HashSet<>() {{
            add(ItemType.ItemType_NR3E_ReportAnim);
            add(ItemType.ItemType_NR3E_AttackAnim);
            add(ItemType.ItemType_NR3E_MVPAnim);
        }};

        List<Integer> excludeSelf = new ArrayList<>();
        excludeSelf.addAll(MiscConf.getInstance().getMiscConf().getDressDefaultItemsList()) ;
        Map<ItemType, List<Integer>> mapItems = player.getItemManager().getItemsByItemTypeWithTypeExcludeItemsAndExcludeExpired(listWolfKillAni, excludeSelf);

//        int count = 0;
        for (Map.Entry<ItemType, List<Integer>>  aniItems : mapItems.entrySet()) {
            ItemType itemType = aniItems.getKey();
            for (Integer itemId : aniItems.getValue()) {
                ResWolfKillDecoration.WolfKillResDecorationAniItem aniItem = WolfKillDecorationAni.get(itemId);
                if (aniItem==null){
                    LOGGER.error("WolfKill itemId:{}", itemId);
                    continue;
                }
                if (aniItem.getIsRandom()==1){
                    continue;
                }
                listRet.add(itemId);
//                count++;
            }
        }

        return listRet;
    }

    public void initWolfKillTreasure(){
        if (!player.getUserAttr().getWolfKillInfo().getInitTreasureFlag()){
            // 得到所有的动画物品数量，排除掉限时、排除掉默认、排除掉随机
            List<Integer> listAni = checkWolfKillTreasureOpen();
            int count = listAni.size();
            if (count>0){
                int addNum = MiscConf.getInstance().getMiscConf().getWolfKillTreasureAddNum();
                LOGGER.debug("WolfKill initWolfKillTreasure, count:{}, addNum:{}, total:{}", count, addNum, count*addNum);
                if (count*addNum>0){
                    List<Long> curLevenAndNum = getWolfKillRealTreasureLevel(count *addNum, player.getClientVersion64());
                    int levelNew = curLevenAndNum.get(0).intValue();
                    long numNew = curLevenAndNum.get(1);
                    long numAllNew = curLevenAndNum.get(2);
                    if (numAllNew==0){
                        return;
                    }

                    int itemId = MiscConf.getInstance().getMiscConf().getWolfKillTreasureItemId();
                    if (itemId==0){
                        return;
                    }
                    List<ItemInfo> listItem = new ArrayList<>();
                    ItemInfo.Builder item = ItemInfo.newBuilder();
                    item.setItemId(itemId);
                    item.setItemNum((long) count *addNum);
                    listItem.add(item.build());

                    player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_WolfKill_Treasure_VALUE, 0);
                    player.getUserAttr().getWolfKillInfo().setInitTreasureFlag(true);

                    player.getUserAttr().getWolfKillInfo().setLv(levelNew);
                    player.getUserAttr().getWolfKillInfo().setTreasureNum(numNew);
                    player.getUserAttr().getWolfKillInfo().setTreasureNumAll(numAllNew);
                    player.getUserAttr().getWolfKillInfo().setTreasureNumAdd(numAllNew);

                    // 初始化计算等级，珍宝等
                    for (int i=0;i<count;i++){
                        List<Long> curLevenAndNumSingle = getWolfKillRealTreasureLevel((i+1) *addNum, player.getClientVersion64());
                        int levelNewSingle = curLevenAndNumSingle.get(0).intValue();
//                        long numNewSingle = curLevenAndNumSingle.get(1);
//                        long numAllNewSingle = curLevenAndNumSingle.get(2);
                        String subReason = listAni.get(i)+":1";
                        TlogFlowMgr.sendSuspectTreasureFlow( player, WolfKillTreasureReason.WolfKill_Treasure_Reason_Ani_VALUE,
                                subReason, addNum,  (i+1) *addNum, (i) *addNum, levelNewSingle);
                    }

                }
            }
        }
    }

    private void addrRentHighVersionItemList(){
        List<ItemInfo> listItem = new ArrayList<>();
        List<Integer> curVersionCanGetItemIds = new ArrayList<>();

        if (!player.getUserAttr().getWolfKillInfo().getRentHighVersionItemList().isEmpty()){
            for (WolfKillTreasureRentHighVersionItemInfo itemInfo:player.getUserAttr().getWolfKillInfo().getRentHighVersionItemList().values()){
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
                if (itemConf == null) {
                    continue;
                }
                if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
                    continue;
                }
                //
                LOGGER.debug("WolfKill addrRentHighVersionItemList, itemId:{}, fromUid:{}, expiredTime:{}, rentType:{}, relation:{}",
                        itemInfo.getItemId(), itemInfo.getSharePlayerUid(), itemInfo.getExpiredTime(), itemInfo.getRentType(), itemInfo.getRelation());
                ItemInfo.Builder item = ItemInfo.newBuilder();
                item.setItemId(itemInfo.getItemId());
                item.setItemNum(itemInfo.getItemNum());
                item.setExpireType(ItemExpireType.IET_ABSOLUTE_VALUE);
                item.setExpireTimeMs(itemInfo.getExpiredTime());
                item.setLessorUid(itemInfo.getSharePlayerUid());
                listItem.add(item.build());

                String sendTlogItem = ""+itemInfo.getItemId();
                // 这里改成一个一个发送tlog
                TlogFlowMgr.sendSuspectTreasureShareFlow(player,2, itemInfo.getRentType(),  itemInfo.getSharePlayerUid(),
                        itemInfo.getRelation(), sendTlogItem, 1);

                //删掉该物品
                curVersionCanGetItemIds.add(itemInfo.getItemId());
            }
        }

        if(!listItem.isEmpty()){
            player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_WolfKill_Treasure_share_VALUE, 0);
            player.getUserAttr().getWolfKillInfo().getRentHighVersionItemList().removeAll(curVersionCanGetItemIds);
        }

    }

    // 由于涉及到珍宝等级开启时间，需要刷新等级和剩余珍宝
    public boolean refreshWolfKillTreasureLevel(){
        int treasureItemId = MiscConf.getInstance().getMiscConf().getWolfKillTreasureItemId();

        if (treasureItemId==0){
            return false;
        }
        long ownedNum = player.getBagManager().getMoneyNum(treasureItemId);
        List<Long> curLevenAndNum = getWolfKillRealTreasureLevel(ownedNum, player.getClientVersion64());
        int levelNew = curLevenAndNum.get(0).intValue();
        long numNew = curLevenAndNum.get(1);
        long numAllNew = curLevenAndNum.get(2);

        // 当前等级
        int levelCur = player.getUserAttr().getWolfKillInfo().getLv();
        long numCur = player.getUserAttr().getWolfKillInfo().getTreasureNum();
        long numAllCur = player.getUserAttr().getWolfKillInfo().getTreasureNumAll();
        if (levelNew!=levelCur || numAllNew>numAllCur){
            List<ItemInfo> listItem = new ArrayList<>();
            LOGGER.info("WolfKill refreshWolfKillTreasureLevel, levelNew:{}, numNew:{}, levelCur:{}, numCur:{},numAllCur：{}",
                    levelNew, numNew, levelCur, numCur, numAllCur);
            // 升级珍宝
            player.getUserAttr().getWolfKillInfo().setLv(levelNew);
            player.getUserAttr().getWolfKillInfo().setTreasureNum(numNew);
            // 只能增加到上限
            player.getUserAttr().getWolfKillInfo().setTreasureNumAll(numAllNew);
            player.getUserAttr().getWolfKillInfo().setTreasureNumAdd(player.getUserAttr().getWolfKillInfo().getTreasureNumAll()-
                    player.getUserAttr().getWolfKillInfo().getTreasureNumAllLast());

            // 解锁珍宝
            for (int i=levelCur+1; i<=levelNew; i++ ) {
                ResNR3E3Treasure.NR3E3TreasureItem treasureItem = NR3E3Treasure.getInstance().get(i);
                for (NR3E3TreasureUnlockItem treasureUnlock : treasureItem.getTreasureList()) {
                    WolfKillTreasureEquipInfo wolfKillTreasureEquipInfo = new WolfKillTreasureEquipInfo();
                    wolfKillTreasureEquipInfo.setId(treasureUnlock.getId());
                    wolfKillTreasureEquipInfo.setInUse(true);
                    wolfKillTreasureEquipInfo.setIsNew(true);
                    player.getUserAttr().getWolfKillInfo()
                            .putTreasureEquipInfo(treasureUnlock.getId(), wolfKillTreasureEquipInfo);
                    if (treasureUnlock.getCanUse() == 1 || treasureUnlock.getCanDayUse() == 1){

                    }else{
                        if (treasureUnlock.getItemNumsCount() != treasureUnlock.getItemsCount()){
                            LOGGER.error("wolfkill refreshWolfKillTreasureLevel, config is error");
                            return true;
                        }
                        for (int iUnlock = 0; iUnlock < treasureUnlock.getItemsCount(); iUnlock++){
                            long itemNum = treasureUnlock.getItemNums(iUnlock);
                            ItemInfo.Builder item = ItemInfo.newBuilder();
                            item.setItemId(treasureUnlock.getItems(iUnlock));
                            item.setItemNum(itemNum);
                            listItem.add(item.build());
                        }
                    }
                    LOGGER.debug("wolfkill refreshWolfKillTreasureLevel,level:{} unlock {}",i, treasureUnlock.getId()
                    );
                }
                TlogFlowMgr.sendSuspectTreasureFlow( player, WolfKillTreasureReason.WolfKill_Treasure_Reason_VersionOrTimeLimit_VALUE,
                        "", 0,
                        (int)ownedNum,
                        (int)ownedNum, i);
            }
            if (!listItem.isEmpty()){
                // 增加物品
                player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_WolfKill_Treasure_VALUE, 0);
            }
            return  true;
        }
        return false;
    }


    // 添加身份卡使用记录，保留近30天
    public void addIdentityItemRecord(int identityID) {
        long curMs = DateUtils.currentTimeMillis();
        long dt = 30L * 24 * 3600 * 1000; // 30天
        int maxCount = 100;
        TcaplusDb.WolfKillIdentityItemRecordArray.Builder records = wolfKillBuilder.getIdentityItemRecordBuilder();
        TcaplusDb.WolfKillIdentityItemRecord.Builder record = TcaplusDb.WolfKillIdentityItemRecord.newBuilder();
        record.setIdentityID(identityID);
        record.setTs(curMs);
        // 添加最近使用记录
        records.addRecords(0, record); // 插入到最前
        // 设置最近使用时间
        records.putLastUseMs(identityID, curMs);

        // 清理过期记录
        List<TcaplusDb.WolfKillIdentityItemRecord> newRecords = new ArrayList<>();
        for (int i=0; i<records.getRecordsList().size(); i++) {
            TcaplusDb.WolfKillIdentityItemRecord originRecord = records.getRecordsList().get(i);
            // 保留前100个
            if (i>=maxCount) {
                break;
            }
            // 保留30天
            if (curMs - originRecord.getTs() > dt) {
                break;
            }
            newRecords.add(originRecord);
        }
        records.clearRecords();
        records.addAllRecords(newRecords);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
    }

    // 获取近30天所有身份卡使用次数
    public Map<Integer, Integer> getIdentityItemUseTimes() {
        long curMs = DateUtils.currentTimeMillis();
        long dt = 30L * 24 * 3600 * 1000; // 30天
        TcaplusDb.WolfKillIdentityItemRecordArray records = wolfKillBuilder.getIdentityItemRecord();
        Map<Integer, Integer> useTimes = new HashMap<>();
        for (TcaplusDb.WolfKillIdentityItemRecord record : records.getRecordsList()) {
            if (curMs - record.getTs() > dt) {
                break;
            }
            int identityID = record.getIdentityID();
            useTimes.computeIfAbsent(identityID, k -> 0);
            useTimes.put(identityID, useTimes.get(identityID) + 1);
        }
        return useTimes;
    }

    public Map<Integer, Long> getIdentityItemLastUseMs() {
        return wolfKillBuilder.getIdentityItemRecord().getLastUseMsMap();
    }

    public boolean monthCardIsUnlock() {
        return true;
    }

    public boolean isCanAddMonthCard() {
        TcaplusDb.WolfKillMonthCard monthCard = wolfKillBuilder.getMonthCard();
        long curTs = DateUtils.currentTimeMillis();
        long endTs = monthCard.getEndTs();
        LOGGER.debug("isCanAddMonthCard - curTs is {}, uid is {}", curTs, player.getUid());
        LOGGER.debug("isCanAddMonthCard - endTs is {}, uid is {}", endTs, player.getUid());
        if (endTs > curTs && (endTs - curTs) / DateUtils.ONE_DAY_MILLIS
                >= MiscConf.getInstance().getMiscConf().getWolfKillMonthCardMaxDay()) {
            LOGGER.debug("isCanAddMonthCard - {} over max day {}, uid is {}", ((endTs - curTs) / DateUtils.ONE_DAY_MILLIS),
                    MiscConf.getInstance().getMiscConf().getWolfKillMonthCardMaxDay(), player.getUid());
            return false;
        }
        return true;
    }

    public void addMonthCard(int itemId, int addDay, String billNo) {
        LOGGER.debug("openMonthCard - addDay is {}, uid is {}", addDay, player.getUid());
        TcaplusDb.WolfKillMonthCard.Builder monthCard = wolfKillBuilder.getMonthCardBuilder();
        int totalDay = monthCard.getTotalDay();
        LOGGER.debug("openMonthCard - totalDay is {}, uid is {}", totalDay, player.getUid());
        monthCard.setTotalDay(totalDay + addDay);
        long endTs = monthCard.getEndTs();
        LOGGER.debug("openMonthCard - endTs is {}, uid is {}", endTs, player.getUid());
        long curTs = DateUtils.currentTimeMillis();
        LOGGER.debug("openMonthCard - curTs is {}, uid is {}", curTs, player.getUid());
        if (endTs <= 0) {
            monthCard.setFreeGiftNum(1);
        }
        if (curTs >= endTs && monthCard.getFreeGiftNum() > 1) {
            monthCard.setFreeGiftNum(1);
        }
        int beforeRemainDays = 0;
        int beforeLv = getMonthCardLevel();
        if (endTs < curTs) {
            endTs = curTs + addDay * DateUtils.ONE_DAY_MILLIS;
        } else {
            beforeRemainDays = DateUtils.getDayInterval(curTs, endTs);
            endTs = endTs + addDay * DateUtils.ONE_DAY_MILLIS;
        }
        int afterRemainDays = DateUtils.getDayInterval(curTs, endTs);
        int afterLv = getMonthCardLevel();
        LOGGER.debug("openMonthCard - after endTs is {}, uid is {}", endTs, player.getUid());
        monthCard.setEndTs(endTs);
        LOGGER.debug("openMonthCard - wolfKillBuilder is {}, uid is {}", wolfKillBuilder, player.getUid());
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        // 推送月卡信息
        CsWolfkill.WolfKillMonthCardInfo.Builder info = CsWolfkill.WolfKillMonthCardInfo.newBuilder();
        info.setTotalDay(monthCard.getTotalDay());
        info.setEndTs(monthCard.getEndTs());
        info.setFreeGiftNum(monthCard.getFreeGiftNum());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_WOLFKILLMONTHCARDNTF, CsWolfkill.WolfKillMonthCardNtf.newBuilder().setInfo(info));

        // 添加月卡物品
        List<ItemInfo> listItem = new ArrayList<>();
        if (addDay == MiscConf.getInstance().getMiscConf().getWolfKillMonthCardDay()) {
            ItemInfo.Builder experienceGift = ItemInfo.newBuilder();
            int experienceGiftId = MiscConf.getInstance().getMiscConf().getWolfKillMonthCardExperienceGift();
            experienceGift.setItemId(experienceGiftId);
            experienceGift.setItemNum(1);
            listItem.add(experienceGift.build());

            ItemInfo.Builder exclusiveGift = ItemInfo.newBuilder();
            int exclusiveGiftId = MiscConf.getInstance().getMiscConf().getWolfKillMonthCardExclusiveGift();
            exclusiveGift.setItemId(exclusiveGiftId);
            exclusiveGift.setItemNum(1);
            listItem.add(exclusiveGift.build());
        }

        ResNR3E3MonthCardPrivilege.ResNR3E3MonthCardPrivilegeItem conf = NR3E3MonthCardPrivilege.getInstance().get(3);
        for (int i = 0;i < conf.getItemIdsCount();i++) {
            ItemInfo.Builder meeting = ItemInfo.newBuilder();
            meeting.setItemId(conf.getItemIds(i));
            meeting.setItemNum(conf.getItemNums(i));
            meeting.setExpireTimeMs(curTs + addDay * DateUtils.ONE_DAY_MILLIS);
            listItem.add(meeting.build());
        }
        player.getPlayerCompetitionMgr().addItem(listItem, ItemChangeReason.ICR_WolfKill_MonthCard_Reward_VALUE, 0);

        // 更新月卡属性
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.WolfKillMonthCardChange);

        int buyType = 0;
        if (!billNo.isEmpty()) {
            buyType = 1;
        }

        // 上报开通流水
        TlogFlowMgr.sendSuspectMonthCardPurchaseFlow(player, Integer.toString(itemId), 1, "", afterRemainDays,
                beforeRemainDays, buyType, beforeLv, afterLv, billNo);
    }

    public void dealMidasMonthCard(Collection<NKPair<String, Integer>> productList, String billNo, ItemChangeReason reason, long costDiamonds) {
        LOGGER.debug("dealMidasMonthCard - productList is {}, costDiamonds is {}", productList, costDiamonds);
        int addDay = MiscConf.getInstance().getMiscConf().getWolfKillMonthCardDay();
        LOGGER.debug("dealMidasMonthCard - addDay is {}", addDay);
        for (NKPair<String, Integer> product : productList) {
            String productId = product.getKey();
            if (!productId.equals(MONTH_CARD_ID)) {
                LOGGER.error("dealMidasMonthCard - not a month card({})", productId);
                continue;
            }
            int productNum = product.getValue();
            for (int i = 0; i < productNum; i++) {
                addMonthCard(0, addDay, billNo);
            }
        }
    }

    public int getMonthCardLevel() {
        if (wolfKillBuilder == null) {
            return 0;
        }
        TcaplusDb.WolfKillMonthCard monthCard = wolfKillBuilder.getMonthCard();
        long endTs = monthCard.getEndTs();
        long curTs = DateUtils.currentTimeMillis();
        if (endTs <= curTs) {
            return 0;
        }
        int level = 0;
        int totalDay = monthCard.getTotalDay();
        for (ResNR3E3MonthCardLevel.ResNR3E3MonthCardLevelItem conf : NR3E3MonthCardLevel.getInstance().dataArray) {
            if (conf.getDay() > totalDay) {
                break;
            }
            level = conf.getLevel();
        }
        return level;
    }

    public long getMonthCardEndTs() {
        if (wolfKillBuilder == null) {
            return 0;
        }
        return wolfKillBuilder.getMonthCard().getEndTs();
    }

    public void setMonthCardEndTs(long sec) {
        TcaplusDb.WolfKillMonthCard.Builder monthCard = wolfKillBuilder.getMonthCardBuilder();
        monthCard.setEndTs(DateUtils.currentTimeMillis() + sec * 1000);
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        CsWolfkill.WolfKillMonthCardInfo.Builder info = CsWolfkill.WolfKillMonthCardInfo.newBuilder();
        info.setTotalDay(monthCard.getTotalDay());
        info.setEndTs(monthCard.getEndTs());
        info.setFreeGiftNum(monthCard.getFreeGiftNum());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_WOLFKILLMONTHCARDNTF, CsWolfkill.WolfKillMonthCardNtf.newBuilder().setInfo(info));

        ResNR3E3MonthCardPrivilege.ResNR3E3MonthCardPrivilegeItem conf = NR3E3MonthCardPrivilege.getInstance().get(3);
        for (int i = 0;i < conf.getItemIdsCount();i++) {
            HashMap<Long, Item> playerItems = player.getItemManager().getItemsByItemId(conf.getItemIds(i));
            if (playerItems.isEmpty()){
                continue;
            }
            for (Item item : playerItems.values()) {
                if (item.getExpireMs() > monthCard.getEndTs() && item.getNumber() > 0) {
                   item.setExpireMs(monthCard.getEndTs());
                }
            }
        }

        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.WolfKillMonthCardChange);
    }

    public CsWolfkill.GetWolfKillMonthCardInfo_S2C_Msg.Builder getMonthCardInfo() {
        TcaplusDb.WolfKillMonthCard monthCard = wolfKillBuilder.getMonthCard();
        CsWolfkill.WolfKillMonthCardInfo.Builder info = CsWolfkill.WolfKillMonthCardInfo.newBuilder();
        info.setTotalDay(monthCard.getTotalDay());
        info.setEndTs(monthCard.getEndTs());
        info.setFreeGiftNum(monthCard.getFreeGiftNum());
        CsWolfkill.GetWolfKillMonthCardInfo_S2C_Msg.Builder rsp = CsWolfkill.GetWolfKillMonthCardInfo_S2C_Msg.newBuilder();
        rsp.setInfo(info);
        return rsp;
    }

    public CsWolfkill.OpenWolfKillMonth_S2C_Msg.Builder openMonthCard() throws NKCheckedException {
        if (!monthCardIsUnlock()) {
            NKErrorCode.Nr3e3MonthCardNotUnlock.throwError("month card is not unlock");
            return null;
        }
        if (!isCanAddMonthCard()) {
            NKErrorCode.Nr3e3MonthCardTimeMax.throwError("month card can not add");
            return null;
        }
        HashMap<String, Integer> productMap = new HashMap<>();
        productMap.put(MONTH_CARD_ID, 1);
        player.getPlayerMoneyMgr().midasDiamondPay(productMap, null, ItemChangeReason.ICR_WolfKill_MonthCard_Cost);
        return CsWolfkill.OpenWolfKillMonth_S2C_Msg.newBuilder();
    }

    public CsWolfkill.GetWolfKillMonthFreeGift_S2C_Msg.Builder getMonthFreeGiftReward() {
        TcaplusDb.WolfKillMonthCard.Builder monthCard = wolfKillBuilder.getMonthCardBuilder();
        if (DateUtils.currentTimeMillis() >= monthCard.getEndTs()) {
            NKErrorCode.Nr3e3MonthCardNotOpen.throwError("not open");
            return null;
        }
        if (monthCard.getFreeGiftNum() <= 0) {
            NKErrorCode.Nr3e3MonthCardHasGetReward.throwError("has no reward");
            return null;
        }
        LOGGER.debug("getMonthFreeGiftInfo - free giftNum is {}, uid is {}", monthCard.getFreeGiftNum(), player.getUid());
        List<ItemInfo> rewardList = new ArrayList<>();
        for (int i = 0; i < monthCard.getFreeGiftNum(); i++) {
            int totalRate = 0;
            List<ResNR3E3MonthCardFreeGift.ResNR3E3MonthCardFreeGiftItem> itemList = new ArrayList<>();
            for (ResNR3E3MonthCardFreeGift.ResNR3E3MonthCardFreeGiftItem item : NR3E3MonthCardFreeGift.getInstance().dataArray) {
                totalRate += item.getRate();
                itemList.add(item);
            }
            int randomWeight = RandomUtils.nextInt(0, totalRate);
            LOGGER.error("getMonthFreeGiftInfo - randomWeight is {}, uid is {}", randomWeight, player.getUid());
            int currentWeight = 0;
            for (ResNR3E3MonthCardFreeGift.ResNR3E3MonthCardFreeGiftItem item : itemList) {
                currentWeight += item.getRate();
                if (randomWeight <= currentWeight) {
                    ItemInfo.Builder reward = ItemInfo.newBuilder();
                    reward.setItemId(item.getItemId());
                    reward.setItemNum(item.getItemNum());
                    rewardList.add(reward.build());
                    break;
                }
            }
        }
        if (rewardList.isEmpty()) {
            LOGGER.error("getMonthFreeGiftInfo - rewardList is empty, uid is {}", player.getUid());
            return null;
        }
        LOGGER.debug("getMonthFreeGiftInfo - rewardList is {}, uid is {}", rewardList, player.getUid());

        monthCard.setFreeGiftNum(0);
        monthCard.setGetFreeGiftTs(DateUtils.currentTimeMillis());
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        player.getPlayerCompetitionMgr().addItem(rewardList, ItemChangeReason.ICR_WolfKill_MonthCard_Free_Gift_VALUE, 0);
        CsWolfkill.GetWolfKillMonthFreeGift_S2C_Msg.Builder rsp = CsWolfkill.GetWolfKillMonthFreeGift_S2C_Msg.newBuilder();
        rsp.setFreeGiftNum(0);
        for (ItemInfo itemInfo : rewardList) {
            rsp.addRewardList(RewardItemInfo.newBuilder().setItemId(itemInfo.getItemId()).setRewardItemNum(itemInfo.getItemNum()).build());
        }
        return rsp;
    }

    public CsWolfkill.GetWolfKillShieldVocation_S2C_Msg.Builder getShieldVocation() {
        LOGGER.debug("getShieldVocation - ShieldVocationBuilder 1 is {}", wolfKillBuilder.getShieldVocation().toString());
        boolean isNeedSave = false;
        TcaplusDb.WolfKillShieldVocationArray.Builder array = wolfKillBuilder.getShieldVocationBuilder();
        for (var data : array.getDataBuilderList()) {
            for (int i = 0; i < data.getVocationListCount(); i++) {
                int roleType = data.getVocationList(i);
                if (roleType <= 0) {
                    continue;
                }
                ResNR3E3Vocation.NR3E3VocationData vocationData = WolfKillVocation.get(roleType);
                if (vocationData != null) {
                    int itemId = vocationData.getIdentityItemUnlock();
                    if (itemId <= 0 || vocationData.getPreparationsDefaultUnLock() == 1) {
                        continue;
                    }
                    LOGGER.debug("getShieldVocation - itemId is {}", itemId);
                    HashMap<Long, Item> playerItems = player.getItemManager().getItemsByItemId(itemId);
                    if (playerItems.isEmpty()){
                        LOGGER.debug("getShieldVocation - item is empty 1, itemId is {}", itemId);
                        data.setVocationList(i, -1);
                        isNeedSave = true;
                        continue;
                    }
                    boolean isEmpty = true;
                    for (Item item : playerItems.values()) {
                        if (!player.getItemManager().isExpired(item) && item.getNumber() > 0) {
                            isEmpty = false;
                            break;
                        }
                    }
                    if (isEmpty) {
                        LOGGER.debug("getShieldVocation - item is empty 2, itemId is {}", itemId);
                        data.setVocationList(i, -1);
                        isNeedSave = true;
                    }
                }
            }
        }
        LOGGER.debug("getShieldVocation - ShieldVocationBuilder 2 is {}", wolfKillBuilder.getShieldVocation().toString());
        if (isNeedSave) {
            TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        }
        CsWolfkill.GetWolfKillShieldVocation_S2C_Msg.Builder rsp = CsWolfkill.GetWolfKillShieldVocation_S2C_Msg.newBuilder();
        for (var data : array.getDataList()) {
            CsWolfkill.WolfKillShieldVocation.Builder info = CsWolfkill.WolfKillShieldVocation.newBuilder();
            info.setCampType(data.getCampType());
            info.addAllVocationList(data.getVocationListList());
            rsp.addData(info);
        }
        rsp.setLevel(getMonthCardLevel());
        return rsp;
    }

    public CsWolfkill.WolfKillShieldVocation_S2C_Msg.Builder setShieldVocation(CsWolfkill.WolfKillShieldVocation_C2S_Msg req) {
        LOGGER.debug("setShieldVocation req is {}", req);

        TcaplusDb.WolfKillShieldVocationArray.Builder array = wolfKillBuilder.getShieldVocationBuilder();
        ResNR3E3Treasure.NR3E3TreasureBlackListItem conf = NR3E3TreasureBlackList.getInstance().get(getMonthCardLevel());
        if (conf == null) {
            NKErrorCode.InvalidParams.throwError("conf not exist");
            return null;
        }

        StringBuilder str = new StringBuilder();
        CsWolfkill.WolfKillShieldVocation_S2C_Msg.Builder rsp = CsWolfkill.WolfKillShieldVocation_S2C_Msg.newBuilder();
        for (CsWolfkill.WolfKillShieldVocation data : req.getDataList()) {
            int count = 0;
            for (int role : data.getVocationListList()) {
                if (role == -1) {
                    continue;
                }
                ResNR3E3Vocation.NR3E3VocationData vocationData = WolfKillVocation.get(role);
                if (vocationData == null) {
                    NKErrorCode.InvalidParams.throwError("role not exist");
                    return null;
                }
                if (vocationData.getFaction() != data.getCampType()) {
                    NKErrorCode.InvalidParams.throwError("camp not match");
                    return null;
                }
                count++;
                str.append(role).append(";");
                int itemId = vocationData.getIdentityItemUnlock();
                if (itemId <= 0 || vocationData.getPreparationsDefaultUnLock() == 1) {
                    continue;
                }
                HashMap<Long, Item> playerItems = player.getItemManager().getItemsByItemId(itemId);
                if (playerItems.isEmpty()){
                    NKErrorCode.InvalidParams.throwError("role not unlocked");
                    return null;
                }
                boolean isEmpty = true;
                for (Item item : playerItems.values()) {
                    if (!player.getItemManager().isExpired(item) && item.getNumber() > 0) {
                        isEmpty = false;
                        break;
                    }
                }
                if (isEmpty) {
                    NKErrorCode.InvalidParams.throwError("role not unlocked");
                    return null;
                }
            }

            int limitNum = 0;
            switch (data.getCampType()) {
                case 1: {
                    limitNum = conf.getHunter();
                    break;
                }
                case 2: {
                    limitNum = conf.getPrey();
                    break;
                }
                case 3: {
                    limitNum = conf.getNeutral();
                    break;
                }
                default: {
                    NKErrorCode.InvalidParams.throwError("camp invalid");
                    return null;
                }
            }
            if (count > limitNum) {
                NKErrorCode.InvalidParams.throwError("count more than limitNum");
                return null;
            }

            int index = -1;
            for (int i = 0; i < array.getDataCount(); i++) {
                if (data.getCampType() == array.getData(i).getCampType()) {
                    index = i;
                    break;
                }
            }
            if (index < 0) {
                TcaplusDb.WolfKillShieldVocation.Builder info = TcaplusDb.WolfKillShieldVocation.newBuilder();
                info.setCampType(data.getCampType());
                info.addAllVocationList(data.getVocationListList());
                array.addData(info);
            } else {
                TcaplusDb.WolfKillShieldVocation.Builder info = array.getDataBuilder(index);
                info.clearVocationList();
                info.addAllVocationList(data.getVocationListList());
            }
            rsp.addData(data);
        }
        LOGGER.debug("setShieldVocation - ShieldVocationBuilder is {}", wolfKillBuilder.getShieldVocation().toString());
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.WolfKillShieldVocationChange);
        TlogFlowMgr.sendSuspectHideIdentityFlow(player, str.toString());
        return rsp;
    }

    public int getMonthCardAddLimit(ResMall.MallCommodity commodityConf) {
        for (int itemId : commodityConf.getItemIdsList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                continue;
            }
            if (itemId == 200101 || itemId == 200102) {
                int level = getMonthCardLevel();
                LOGGER.debug("getMonthCardAddLimit - level is {}, uid is {}", level, player.getUid());
                if (level <= 0) {
                    return 0;
                }
                ResNR3E3MonthCardBuyLimit.ResNR3E3MonthCardBuyLimitItem limitConf = NR3E3MonthCardBuyLimit.getInstance().get(level);
                if (limitConf == null) {
                    return 0;
                }
                int add = 0;
                switch (itemId) {
                    case 200101: {
                        add = limitConf.getRole();
                        break;
                    }
                    case 200102: {
                        add = limitConf.getCamp();
                        break;
                    }
                }
                LOGGER.debug("getMonthCardAddLimit - item is {}, add is {}, uid is {}", itemId, add, player.getUid());
                return add;
            }
        }
        return 0;
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {

    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        if (wolfKillBuilder == null) {
            loadWolfKillInfo();
        }
        initWolfKillTreasure();
        refreshWolfKillTreasureLevel();
        addrRentHighVersionItemList();
    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {

    }

    @Override
    public void onMidNight() {
        if (wolfKillBuilder == null) {
            loadWolfKillInfo();
        }

        clearReputationRecord();

        int loginScore = MiscConf.getInstance().getMiscConf().getWolfKillLoginScore();
        addWolfKillReputationScore(loginScore, 202, player.getPlayerRoomMgr().getRoomId(), "",
                "", "", "", 0, 0);

        wolfKillBuilder.setFeedbackCount(0);
        wolfKillBuilder.setSeasonRecvCount(0);
        if (wolfKillBuilder.getMonthCard().getTotalDay() > 0) {
            long curTs = DateUtils.currentTimeMillis();
            long getTs = wolfKillBuilder.getMonthCard().getGetFreeGiftTs();
            LOGGER.debug("wolfKill onMidNight - uid is {}, curTs is {}, getTs is {}", player.getUid(),
                    DateUtils.currentTimeMillis(), wolfKillBuilder.getMonthCard().getGetFreeGiftTs());
            if (curTs > getTs) {
                int addNum = DateUtils.getDayInterval(curTs, getTs);
                int freeGiftNum = wolfKillBuilder.getMonthCard().getFreeGiftNum();
                LOGGER.debug("wolfKill onMidNight - uid is {}, addNum is {}, freeGiftNum is {}",
                        player.getUid(), addNum, freeGiftNum);
                freeGiftNum += addNum;
                if (freeGiftNum > 2) {
                    freeGiftNum = 2;
                }
                wolfKillBuilder.getMonthCardBuilder().setFreeGiftNum(freeGiftNum);
            }
        }
        TcaplusUtil.newUpdateReq(wolfKillBuilder).send();

        addComeBackUnlockCount();
        addComeBackActionScore();

        // 如果有，则清空当日领取
        int treasureId = NR3E3Treasure.getInstance().getGoldFinger();
        // 要判断是否拥有这个功能
        if (player.getUserAttr().getWolfKillInfo().getTreasureEquipInfo(treasureId)!=null){
            WolfKillTreasureEquipInfo wolfKillTreasureEquipInfo = new WolfKillTreasureEquipInfo();
            wolfKillTreasureEquipInfo.setId(treasureId);
            wolfKillTreasureEquipInfo.setRewarded(false);
            player.getUserAttr().getWolfKillInfo().putTreasureEquipInfo(treasureId, wolfKillTreasureEquipInfo);
        }
        // 每月清理
        if (!DateUtils.isSameMonth(player.getUserAttr().getWolfKillInfo().getRentClearTime(), Framework.currentTimeMillis())){
            LOGGER.debug("lastClearTime:{}, nowTime:{}", player.getUserAttr().getWolfKillInfo().getRentClearTime(),  Framework.currentTimeMillis());
            player.getUserAttr().getWolfKillInfo().clearRentVocation();
            player.getUserAttr().getWolfKillInfo().clearRentAni();
            player.getUserAttr().getWolfKillInfo().setRentClearTime(Framework.currentTimeMillis());
        }
        //按赛季清理
        Competition.NR3ERealTimeConfigRes nr3eConfig =  WujiConfigMgr.getNR3ERealTimeConfig();
        if (nr3eConfig != null) {
            try {
                List<Long> seasonStart = new ArrayList<>();
                List<Long> seasonEnd = new ArrayList<>();
                for (Competition.WolfKillSeasonTimeLimited seasonTimeLimited : nr3eConfig.getSeasonTimeLimitedList()){
                    seasonStart.add(seasonTimeLimited.getStartTime()*1000);
                    seasonEnd.add(seasonTimeLimited.getEndTime()*1000);
                }
                if (!isSameSeason(player.getUserAttr().getWolfKillInfo().getHyperCoreClearTime(), Framework.currentTimeMillis(),seasonStart,seasonEnd)){
                    player.getUserAttr().getWolfKillInfo().setHyperCoreClearTime(Framework.currentTimeMillis());
                    player.getUserAttr().getWolfKillInfo().setHyperCoreScore(0);
                }

            } catch (Exception e) {
                LOGGER.error("setHyperCoreClearTime error:{}", e.getMessage());
            }
        }
    }

    @Override
    public void onWeekStart() {
        int treasureId = NR3E3Treasure.getInstance().getInteractEmojId();
        if (player.getUserAttr().getWolfKillInfo().getTreasureEquipInfo(treasureId)!=null){
            WolfKillTreasureEquipInfo wolfKillTreasureEquipInfo = new WolfKillTreasureEquipInfo();
            wolfKillTreasureEquipInfo.setId(treasureId);
            wolfKillTreasureEquipInfo.setRewarded(false);
            player.getUserAttr().getWolfKillInfo().putTreasureEquipInfo(treasureId, wolfKillTreasureEquipInfo);
        }
    }
}
