package com.tencent.wea.playerservice.cshandler.handler.activity2;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.playerservice.activity.implement.TreasureHuntActivity;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity2;
import com.tencent.wea.protocol.CsHead;
import java.util.Objects;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ActivityTreasureHuntSellMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ActivityTreasureHuntSellMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsActivity2.ActivityTreasureHuntSell_C2S_Msg reqMsg = (CsActivity2.ActivityTreasureHuntSell_C2S_Msg) request;
        var runningActivity = player.getActivityManager().getRunningActivity(reqMsg.getActivityId(), TreasureHuntActivity.class);
        if (Objects.isNull(runningActivity)) {
            LOGGER.error("activityNotFound uid:{} activity:{}", player.getUid(), reqMsg.getActivityId());
            NKErrorCode.ActivityNotRunning.throwError("activityNotFound:{}", reqMsg.getActivityId());
        }
        return runningActivity.sellMsgHandler();
    }
}
