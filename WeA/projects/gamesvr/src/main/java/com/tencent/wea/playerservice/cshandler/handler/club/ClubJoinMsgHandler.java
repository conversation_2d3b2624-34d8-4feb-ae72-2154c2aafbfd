package com.tencent.wea.playerservice.cshandler.handler.club;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsClub;
import com.tencent.wea.protocol.CsHead;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ClubJoinMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ClubJoinMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsClub.ClubJoin_C2S_Msg reqMsg = (CsClub.ClubJoin_C2S_Msg)request;
        CsClub.ClubJoin_S2C_Msg.Builder rspMsg = player.getClubMgr().joinClub(reqMsg);
        if (rspMsg.getResult() != 0) {
            throw new NKRuntimeException(NKErrorCode.forNumberOrUnknown(rspMsg.getResult()));
        }
        return rspMsg;
    }
}