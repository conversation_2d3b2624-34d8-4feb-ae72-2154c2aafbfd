package com.tencent.wea.scripts

import com.github.javaparser.utils.StringEscapeUtils
import com.google.protobuf.ByteString
import com.google.protobuf.InvalidProtocolBufferException
import com.google.protobuf.TextFormat
import com.tencent.nk.commonframework.Framework
import com.tencent.nk.groovy.GroovyScript
import com.tencent.tcaplus.dao.OpenIdToUidDao
import com.tencent.timiCoroutine.CurrentExecutorUtil
import com.tencent.timiCoroutine.TimiCoroExecutorService
import com.tencent.wea.attr.DsUserDBInfoUnion
import com.tencent.wea.g6.irpc.proto.ds_dsdbsvr.DsDsdbsvr
import com.tencent.wea.playerservice.playerref.PlayerRef
import com.tencent.wea.playerservice.playerref.PlayerRefMgr
import com.tencent.wea.protocol.AttrDSItem
import com.tencent.wea.protocol.AttrDSOwnerBackpackInfo
import com.tencent.wea.protocol.AttrDsUserDBInfo
import com.tencent.wea.protocol.AttrTycoonBuildingItem
import com.tencent.wea.protocol.AttrTycoonUserDBInfo
import com.tencent.wea.protocol.SsDsdbsvr
import com.tencent.wea.protocol.common.LocalServiceType
import com.tencent.wea.rpc.service.DsdbService

import java.nio.charset.StandardCharsets

/**
 * 操作玩家DB中存储的属性
 *
 * <AUTHOR>
 * @create 2023/11/14 18:19
 */
class OperateDsUserDBInfoGroovy implements GroovyScript {

    static int MATCH_TYPE = 39
    static long WORLD_NUM = 10000000000000000L;

    @Override
    String runScript(String[] args) {
        String str = "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";
        //是否是base64，lua日志中陷阱摆放为baseStr；玩家db属性打印为非base64
        boolean isBase64 = true;
        //ba64Str需要存入的dbId，和关卡id
        int base64DBId = 6543;
        int base64LevelId = 80701;
        boolean base64IsTrapDB = false;
        //是否需要修改DB
        boolean isSaveDB = true;
        long uid = 1890140737488375338L;
        try {
            ByteString byteStringAfter;
            if (isBase64) {
                byte[] byteArray = Base64.getDecoder().decode(str);
                byteStringAfter = ByteString.copyFrom(byteArray);
            } else {
                byteStringAfter = TextFormat.unescapeBytes(str);
            }

            // 使用字节数组创建Protocol Buffers对象
            DsDsdbsvr.OMDUserDBInfo info = DsDsdbsvr.OMDUserDBInfo.parseFrom(byteStringAfter);
            if (isSaveDB) {
                CurrentExecutorUtil.runJobSequentialByKey(uid, LocalServiceType.LOCAL_GAMESVR_PLAYER_SERVICE, () -> {
                    try {
                        if (isBase64 && base64IsTrapDB) {
                            long saveId = uid - (uid % WORLD_NUM) + base64DBId;
                            saveReq(saveId, byteStringAfter, base64LevelId);
                        } else {
                            saveReq(uid, byteStringAfter, 0);
                        }
                    } catch (Exception e) {
                        LOGGER.info("OperateDsUserDBInfoGroovy run job exception:", e)
                    }
                }, "OperateDsUserDBInfoGroovy run job", false);
            }
            return info.toString()
        } catch (InvalidProtocolBufferException e) {
            return "Error parsing Protocol Buffers message: " + e.getMessage();
        } catch (Exception e) {
            return "exception: " + e.getMessage();
        }
    }

    void getReq(long uid, int slotIdx) {
        SsDsdbsvr.RpcDsUserDBInfoGetReq.Builder reqBuilder = SsDsdbsvr.RpcDsUserDBInfoGetReq.newBuilder();
        reqBuilder.setMatchType(MATCH_TYPE).setUid(uid).setSlot(slotIdx);
        DsdbService.get().rpcDsUserDBInfoGet(reqBuilder)
    }

    void saveReq(long uid, ByteString omdDBInfo, int slotIdx) {
        SsDsdbsvr.RpcDsUserDBInfoSaveReq.Builder reqBuilder = SsDsdbsvr.RpcDsUserDBInfoSaveReq.newBuilder().setUid(uid);
        reqBuilder.addSlotArray(slotIdx);
        reqBuilder.getDsUserDBInfoBuilder().setMatchType(MATCH_TYPE)
                .getDsUserDBInfoUnionBuilder().setOmdUserDBInfo(omdDBInfo);
        DsdbService.get().rpcDsUserDBInfoSave(reqBuilder)
    }

}


