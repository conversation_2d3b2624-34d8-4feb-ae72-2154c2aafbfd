package com.tencent.wea.playerservice.cshandler.handler.battle;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.rpc.RpcResult;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.attr.ChatGroupKey;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.numericattr.PlayerNumericAttrMgr;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.login.LoginStatMgr;
import com.tencent.wea.playerservice.login.LoginStepEnum;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.AttrChatGroupKey.proto_ChatGroupKey;
import com.tencent.wea.protocol.CsBattle;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.SsBattlesvr;
import com.tencent.wea.protocol.common.G6Common;
import com.tencent.wea.rpc.service.BattleService;
import com.tencent.wea.starp.tlog.StarPTlogFlowMgr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.tencent.wea.starp.StarPConfs;

/**
 * <AUTHOR>
 * 逻辑已经整合到了登录协议 PlayerAfterLoginNtf 中，这里逻辑保留
 */
public class BattleGetOngoingDsAddrMsgHandler extends AbstractGsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(BattleGetOngoingDsAddrMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        int enterDsContextId = LoginStatMgr.startStep(player.getOpenId(), LoginStepEnum.LS_ENTER_DS, "battle", 0);
        int goingDsContextId = LoginStatMgr.startStep(player.getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, enterDsContextId);
        CsBattle.BattleGetOngoingDsAddr_C2S_Msg reqMsg = (CsBattle.BattleGetOngoingDsAddr_C2S_Msg) request;
        long starPId = reqMsg.getStarPId();
        LOGGER.info("player battle get ongoing ds address, openId:{}, uid:{}, starPId:{} reLogin:{}", player.getOpenId(), player.getUid(), starPId, player.isReLogin());
        CsBattle.BattleGetOngoingDsAddr_S2C_Msg.Builder ret = CsBattle.BattleGetOngoingDsAddr_S2C_Msg.newBuilder();
        // 触发保护逻辑，避免弱网重连玩家room未online
        player.getPlayerRoomMgr().sendRpcRoomPlayerOnlineIfNeed();
        long currBattleId = player.getUserAttr().getBattleInfo().getBattleid();
        long giveUpBattleId = player.getNumericAttrMgr().get(PlayerNumericAttrMgr.Key.GIVE_UP_BATTLE_ID);

        if (currBattleId == 0 || currBattleId == giveUpBattleId) {
            LOGGER.debug("BattleGetOngoingDsAddrMsgHandler currBattleId == 0 || currBattleId == " +
                            "giveUpBattleId, uid:{}, currBattleId {}, giveUpBattleId: {}, roomInfo: {}", player.getUid(),
                    currBattleId, giveUpBattleId, player.getUserAttr().getRoomInfo().toString());
            LoginStatMgr.endStep(player.getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, "noDs", goingDsContextId);
            return player.getPlayerStarPMgr().getLastBattleInfo(starPId);
        }
        // coc玩法重登时强制退出和结算
        if (currBattleId > 0 && player.getUserAttr().getBattleInfo().getMatchType() == 1201) {
             player.getPlayerBattleMgr().giveUpCurrentBattle(G6Common.QuitBattleCode.QUIT_BATTLE_CODE_LEAVE_VALUE);
             LOGGER.info("BattleGetOngoingDsAddrMsgHandler coc forve give up.uid:{}, battleId:{}", player.getUid(),
                     currBattleId);
             return ret;
        }

        // 走到下面的一般就是pvp或者pve了，目前直接将abtest标签置为0
        ret.setTabType(0);

        BattleService battleService = BattleService.get();
        if (battleService == null) {
            LoginStatMgr.endStep(player.getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, "no battle service", goingDsContextId);
            return ret;
        }
        SsBattlesvr.RpcBattlePlayerOnlineReq.Builder ssReqMsg = SsBattlesvr.RpcBattlePlayerOnlineReq
                .newBuilder();
        ssReqMsg.setBallteId(player.getUserAttr().getBattleInfo().getBattleid());
        ssReqMsg.setUid(player.getUid());
        ssReqMsg.setVersionGroupId(player.getBattleVersionGroupId(player.getUserAttr().getBattleInfo().getMatchType()));
        try {
            RpcResult<SsBattlesvr.RpcBattlePlayerOnlineRes.Builder> rpcResult = battleService
                    .rpcBattlePlayerOnline(ssReqMsg);
            int result = rpcResult.getData().getResult();
            ret.setErrorCode(result);
            if (result == NKErrorCode.OK.getValue()) {
                if(rpcResult.getData().hasBattleInfo() && rpcResult.getData().getBattleInfo().hasDsAddr()) {
                    ret.setDsAddr(rpcResult.getData().getBattleInfo().getDsAddr());
                } else {
                    ret.setDsAddr(player.getUserAttr().getBattleInfo().getDsAddr());
                }
                if(rpcResult.getData().hasBattleInfo()) {
                    ret.setSceneId(rpcResult.getData().getBattleInfo().getSceneId());
                } else {
                    ret.setSceneId(player.getUserAttr().getBattleInfo().getSceneId());
                }
                ret.setBattleId(player.getUserAttr().getBattleInfo().getBattleid());
                ret.setDesModInfo(player.getUserAttr().getBattleInfo().getDesModInfo());
                ret.setDsAuthToken(player.getUserAttr().getBattleInfo().getDsAuthToken());
                ret.setMatchType(player.getUserAttr().getBattleInfo().getMatchType());
                // 返回局内的聊天key
                ChatGroupKey globalChatGroupKey = player.getUserAttr().getBattleInfo().getGlobalChatGroupKey();
                ret.setChatGroupKey(proto_ChatGroupKey.newBuilder().setChatType(globalChatGroupKey.getChatType())
                        .setId(globalChatGroupKey.getId())
                        .setSubID(globalChatGroupKey.getSubID()).build());
                ChatGroupKey sideChatGroup = player.getUserAttr().getBattleInfo().getSideChatGroupKey();
                ret.setSideChatGroupKey(proto_ChatGroupKey.newBuilder().setChatType(sideChatGroup.getChatType())
                        .setId(sideChatGroup.getId())
                        .setSubID(sideChatGroup.getSubID()).build());
                //ret.setPakType(rpcResult.getData().getBattleInfo().getPakType()); 废弃
                ret.addAllPakGroupIdList(rpcResult.getData().getBattleInfo().getPakGroupIdListList());

                // 返回赛事信息
                ret.setCompetitionBasicInfo(rpcResult.getData().getBattleInfo().getCompetitionBasicInfo());

                String[] matchTypeStr = new String[]{String.valueOf(player.getUserAttr().getBattleInfo().getMatchType())};
                Monitor.getInstance().add.fail(MonitorId.attr_tyc_battle_ongoing_query_count, 1, matchTypeStr);
                // ds状态置为对局中
                player.getPlayerStateMgr().enterBattleScene();
            } else if (result == NKErrorCode.BattleScenePlayerChanging.getValue()) {
                String[] matchTypeStr = new String[]{String.valueOf(player.getUserAttr().getBattleInfo().getMatchType())};
                Monitor.getInstance().add.fail(MonitorId.attr_tyc_battle_ongoing_query_count, 1, matchTypeStr);
                // 把結果給客戶端
                if(rpcResult.getData().hasBattleInfo()) {
                    ret.setSceneId(rpcResult.getData().getBattleInfo().getSceneId());
                }
                // ds状态置为对局中
                player.getPlayerStateMgr().enterBattleScene();
            } else if (result == NKErrorCode.BattleModAlreadyEnd.getValue()) {
                // 在中途加入对局的导播，导播在结束的时候会没有结算信息，因此会有此报错信息，这里修复一下
                if (player.getUserAttr().getBattleInfo().getRobotType()==-1){
                    LOGGER.error("GetOngoingDsAddr observer not exist, uid:{}, battleId:{}, ret:{}", player.getUid(), currBattleId, result);
                    result = NKErrorCode.OK.getValue();
                }
            } else {
                if (result != NKErrorCode.BattleDsInCreating.getValue()) {
                    player.getPlayerBattleMgr().clearBattleInfo(player.getUserAttr().getBattleInfo().getBattleid(), "BattleGetOngoingDsAddrMsgHandler " +
                            "rpcBattlePlayerOnline return" + result);
                    LOGGER.error("GetOngoingDsAddr not exist, uid:{}, battleId:{}, ret:{}", player.getUid(), currBattleId, result);
                    //if (starPId == 0 && !player.isReLogin() && player.getPlayerRoomMgr().isStarPTeam()) {
                    //    player.getPlayerRoomMgr().exitAllRoomQuietly("starp onGoing");
                    player.getPlayerRoomMgr().sendRpcRoomPlayerOnline();
                } else {
                    LOGGER.error("GetOngoingDsAddr warn, uid:{}, battleId:{} in creating ds, ret:{}", player.getUid(),
                            currBattleId, result);
                }
            }
            ret.setErrorCode(result);
            //SP复用元梦匹配逻辑玩法的断线重连数据监控
            if (StarPConfs.isStarPGame(player.getUserAttr().getBattleInfo().getMatchType())){
                StarPTlogFlowMgr.sendStarPPlayerGetOngoingFlow(starPId,player,ret,result);
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("GetOngoingDsAddr rpcBattlePlayerOnline throw exception err:{}, uid:{}", e, player.getUid());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Call BattleGetOngoingDsAddrMsgHandler, uid:{}, retDsAddr:{}",
                    player.getUid(), ret.getDsAddr());
        }

        /*long roomId = player.getUserAttr().getRoomInfo().getRoomid();
        if (0 != roomId && ret.hasDsAddr()) {
            RoomService roomService = RoomService.get();
            if (roomService == null) {
                return ret;
            }
            SsRoomsvr.RpcRoomPlayerSetBattleReq.Builder req = SsRoomsvr.RpcRoomPlayerSetBattleReq
                    .newBuilder().setRoomId(roomId).setUid(player.getUid());
            try {
                roomService.rpcRoomPlayerSetBattle(req);
            } catch (NKTimeoutException | RpcException e) {
                LOGGER.error("GetOngoingDsAddr rpcRoomPlayerSetBattle throw exception :{}, roomid: {}, uid: {}",
                        e, roomId, player.getUid());
            }
        }*/

        /*
        // 容错 battleId丢失的情况
        if ((!ret.hasDsAddr() || ret.getDsAddr().isEmpty())
                && player.getUserAttr().getRoomInfo().getStatus() == RoomStatus.RS_BATTLE_START) {
            LOGGER.error("ReLogin met invalid battle status uid:{}, roomId:{}, battleId:{}",
                    player.getUid(), roomId, player.getUserAttr().getBattleInfo().getBattleid());

            player.getPlayerRoomMgr().clearRoomInfo("get ongoing battle met invalid status, just clear");
            if (roomId != 0) {
                CsRoom.RoomMemberModifyNtf.Builder ntfBuilder = CsRoom.RoomMemberModifyNtf.newBuilder()
                        .setRoomID(roomId)
                        .setModify(ModifyType.MT_DEL)
                        .addMemberList(MemberBaseInfo.newBuilder().setUid(player.getUid()));
                player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMEMBERMODIFYNTF, ntfBuilder);
                LOGGER.error("ReLogin met invalid battle status uid:{}, send MT_DEL", player.getUid());
            }
            WechatLog.debugPanicWithNoticer(Programmer.wenhuazheng,
                    "ReLogin met invalid battle status uid:{}, roomId:{}, battleId:{}",
                    player.getUid(), roomId, player.getUserAttr().getBattleInfo().getBattleid());
        }
        */
        LoginStatMgr.endStep(player.getOpenId(), LoginStepEnum.LS_BATTLE_GET_ON_GOING_DS_C2S, goingDsContextId);
        return ret;
    }
}