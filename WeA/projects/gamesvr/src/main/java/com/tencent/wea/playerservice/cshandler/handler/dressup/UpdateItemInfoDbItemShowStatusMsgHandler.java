package com.tencent.wea.playerservice.cshandler.handler.dressup;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.attr.DressUpDetailInfo;
import com.tencent.wea.attr.Item;
import com.tencent.wea.attr.ItemDetailInfo;
import com.tencent.wea.attr.PlayerPublicEquipments;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsDressup;
import com.tencent.wea.protocol.CsHead;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class UpdateItemInfoDbItemShowStatusMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(UpdateItemInfoDbItemShowStatusMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsDressup.UpdateItemInfoDbItemShowStatus_C2S_Msg reqMsg = (CsDressup.UpdateItemInfoDbItemShowStatus_C2S_Msg) request;
        CsDressup.UpdateItemInfoDbItemShowStatus_S2C_Msg.Builder rsp = CsDressup.UpdateItemInfoDbItemShowStatus_S2C_Msg.newBuilder();
        long itemUUID = reqMsg.getItemUUID();
        int showStatus = reqMsg.getShowStatus();
        player.getItemEquipManager().changeDressUpItemShowStatus(itemUUID, showStatus);
        player.getPlayerBattleMgr().updateBattleDsPlayerPublicInfo();
        rsp.setShowStatus(showStatus).setItemUUID(itemUUID);
        return rsp;
    }
}
