//generated by tool
package com.tencent.wea.playerservice.idiphandler;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.idip.IdipUtil;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.rank.console.BackendConsole;
import com.tencent.wea.playerservice.idiphandler.common.IdipMsgPlayerHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.common.BanInfo;
import com.tencent.wea.protocol.common.BanStatus;
import com.tencent.wea.protocol.common.BanType;
import com.tencent.wea.protocol.idip.*;
import com.tencent.wea.tcaplus.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class DoSecurityPunishRemoveReqHandler implements IdipMsgPlayerHandler {
    public static final Logger LOGGER = LogManager.getLogger(DoSecurityPunishRemoveReqHandler.class);

    @Override
    public IdipResponse.Builder handle(Player player, IdipRequest.Builder request) {
        DoSecurityPunishRemoveReq.Builder req =  request.getDoSecurityPunishRemoveReqBuilder();
        DoSecurityPunishRemoveRsp.Builder res = DoSecurityPunishRemoveRsp.newBuilder();

        LOGGER.debug("DoSecurityPunishRemove, openid:{}, uid:{}, punish_remove_type:{}",
                req.getOpenId(), req.getUid(), req.getPunishRemoveType());

        if (player != null) {
            // 从db中获取用户封禁信息, 更新缓存
            boolean getDbSuccessFlag = false;
            try {
                Optional<TcaplusDb.IdipBanInfo> idipBanInfo = IdipUtil.getIdipBanInfo(player.getOpenId(), player.getUid());
                if (idipBanInfo.isPresent()) {
                    player.setIdipBanInfo(idipBanInfo.get().getBanInfo());
                    getDbSuccessFlag = true;
                }
            } catch (Exception ex) {
                LOGGER.error("db operate catch exception, ", ex);
            }

            // 安全处罚解除处理
            SecurityPunishRemoveProcess(player, req, getDbSuccessFlag);

            // 给用户推送ntf消息
            sendPlayerBanNtf(player, req);
        } else {
            if (req.getPunishRemoveType() == BanType.BT_Rank_VALUE) {
                NKErrorCode errorCode = BackendConsole.getInstance().unbanAll(req.getUid());
                if (NKErrorCode.OK != errorCode)
                {
                    LOGGER.error("unban player {} rank offline failed: {}", req.getUid(), errorCode);
                }
                LOGGER.debug("unban player {} rank offline, error: {}", req.getUid(), errorCode);
            }
        }

        res.setResult(0);
        res.setRetMsg("success");

        IdipResponse.Builder idipResponse = IdipResponse.newBuilder().setDoSecurityPunishRemoveRsp(res);
        return idipResponse;
    }

    /**
     * 安全处罚移除处理
     * @param player
     * @param req
     * @param getDbSuccessFlag
     */
    private void SecurityPunishRemoveProcess(Player player, DoSecurityPunishRemoveReq.Builder req, boolean getDbSuccessFlag) {
        if (player == null || req == null) {
            return;
        }

        BanType banType = BanType.forNumber(req.getPunishRemoveType());
        if (banType == null) {
            return;
        }

        if (banType == BanType.BT_Rank) {
            NKErrorCode errorCode = player.getRankManager().unbanAll();
            if (NKErrorCode.OK != errorCode)
            {
                LOGGER.error("unban player {} rank failed: {}", player.getUid(), errorCode);
            }
            LOGGER.debug("unban player {} rank, error: {}", player.getUid(), errorCode);
            return;
        }

        if (getDbSuccessFlag || player.getBanStatus(banType) == null)
        {
            return;
        }

        BanInfo.Builder builder = player.getIdipBanInfo().toBuilder();
        builder.removeBanStatusMap(banType.getNumber());
        player.setIdipBanInfo(builder.build());
    }

    /**
     * 给用户推送解除封禁消息
     * @param player
     * @param req
     */
    private void sendPlayerBanNtf(Player player, DoSecurityPunishRemoveReq.Builder req) {
        if (player == null || req == null) {
            return;
        }

        switch (req.getPunishRemoveType()) {
            case BanType.BT_AiImage_VALUE: {
                player.sendBanInfoNtf(BanType.BT_AiImage, 0, NKErrorCode.AiImageIsBanned,
                        req.getPunishRemoveReason(), false);
                break;
            }
            case BanType.BT_Voice_VALUE: {
                player.sendBanInfoNtf(BanType.BT_Voice, 0, NKErrorCode.VoiceFunctionIsBanned,
                        req.getPunishRemoveReason(), false);
                break;
            }
            case BanType.BT_Search_VALUE: {
                player.sendBanInfoNtf(BanType.BT_Search, 0, NKErrorCode.SearchIsBanned,
                        req.getPunishRemoveReason(), false);
                break;
            }
            case BanType.BT_AiChangeColor_VALUE: {
                player.sendBanInfoNtf(BanType.BT_AiChangeColor, 0, NKErrorCode.AiChangeColorIsBanned,
                        req.getPunishRemoveReason(), false);
                break;
            }
            case BanType.BT_FarmSignature_VALUE: {
                player.sendBanInfoNtf(BanType.BT_FarmSignature, 0, NKErrorCode.FarmSignatureIsBanned,
                        req.getPunishRemoveReason(), false);
                break;
            }
            case BanType.BT_CreateOrModifyElement_VALUE: {
                player.sendBanInfoNtf(BanType.BT_CreateOrModifyElement, 0, NKErrorCode.CreateOrModifyElementIsBanned,
                        req.getPunishRemoveReason(), false);
                break;
            }
            case BanType.BT_CreateOrModifyUgcAchievement_VALUE: {
                player.sendBanInfoNtf(BanType.BT_CreateOrModifyUgcAchievement, 0,
                        NKErrorCode.CreateOrModifyUgcAchievementIsBanned, req.getPunishRemoveReason(), false);
                break;
            }
        }
    }
}
