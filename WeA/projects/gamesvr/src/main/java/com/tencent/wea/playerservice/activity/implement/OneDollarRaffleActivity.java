package com.tencent.wea.playerservice.activity.implement;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.IEnumedException;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.ActivityCheckInManualData;
import com.tencent.resourceloader.resclass.RechargeScratchOffTicketData;
import com.tencent.resourceloader.resclass.WeekendGiftCheckInConfig;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.*;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResRecharge;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

public class OneDollarRaffleActivity extends BaseActivity {

    private static final Logger LOGGER = LogManager.getLogger(OneDollarRaffleActivity.class);
    private static final int MAX_AWARD_COUNT = 3;

    public OneDollarRaffleActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
    }

    @Override
    public void onExpire() {

    }

    @Override
    public void stop() {

    }

    @Override
    public void onStatusChange() {

    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATOneDollarRaffle;
    }

    @Override
    public void afterLoginFinish() {
        resetWeekendGift();

    }

    private void resetWeekendGift() {
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        if (getResetWeek() > 0) {
            long weekBeginTimeMs = DateUtils.getFirstDayOfWeek(oneDollarRaffleData.getLastAwardTimeMs()).getTime();
            long resetTimeMs = weekBeginTimeMs + (getResetWeek() ) * DateUtils.ONE_DAY_MILLIS;
            if ((oneDollarRaffleData.getLastAwardTimeMs() < resetTimeMs && DateUtils.currentTimeMillis() > resetTimeMs)
                    || DateUtils.currentTimeMillis() - resetTimeMs > DateUtils.ONE_WEEK_MILLIS) {
                sendMailRewardAndResetData();
                resetActivityData();
                checkActivityRedPoint();
                LOGGER.info("reset data, uid:{} activityId:{}", player.getUid(), activityId);
            }
        }else {
            LOGGER.error("getResetWeek error, uid:{} activityId:{} {}", player.getUid(), activityId, getResetWeek());
        }
    }

    private void resetActivityData(){
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        oneDollarRaffleData.setIsUpgrade(false).setAwardCount(0);
        oneDollarRaffleData.getAwardList().clear();
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
    }

    /**
     * 每日逻辑 各个活动根据需求自己清理相关数据
     *
     * @return void
     */
    @Override
    public void onMidNight() {
        resetWeekendGift();
        int awardCount = getActivityUnit().getDetailData().getOneDollarRaffleActivity().getAwardCount();
        if (getIsUpgrade() && awardCount<MAX_AWARD_COUNT){
            checkActivityRedPoint();
        }
    }

    // 获取每周几重置
    private int getResetWeek() {
        return getActivityMainConfig().getActivityParam(1);
    }


    // 开通周末礼包
    public NKErrorCode UpgradeWeekendGift() {

        int activityParamConfigId = getActivityMainConfig().getActivityParam(0);
        if (getIsUpgrade()) {
            LOGGER.warn("OneDollarRaffleActivity is unlocked player:{} is higher", player.getUid());
            return NKErrorCode.ActivityOneDollarRaffleIsUpgraded;
        }
        //充值配置表   刮刮乐
        ResRecharge.RechargeScratchOffTicketCfg rechargeCfg = RechargeScratchOffTicketData.getInstance()
                .get(activityParamConfigId);
        if (rechargeCfg == null) {
            LOGGER.warn("farmDaily player:{} invalid recharge cfg:{}",
                    player.getUid(), activityParamConfigId);
            return NKErrorCode.ActivityCheckInManualInvalidRechargeCfg;
        }
        try {
            UpgradeWeekendGiftMidasMetaData.Builder midas = UpgradeWeekendGiftMidasMetaData.newBuilder().setActivityId(activityId)
                    .setRechargeId(rechargeCfg.getId()).setCommodityId(rechargeCfg.getCommodityId());
            player.getPlayerMoneyMgr()
                    .midasDirectBuy(rechargeCfg.getMidasId(), DeliverGoodsMetaData.newBuilder().setUpgradeWeekendGiftMidasMetaData(midas),
                            ItemChangeReason.ICR_UpgradeWeekendGift);
        } catch (Exception e) {
            LOGGER.error("oneDollar raffle activity failed to buy midas upgrade gift product, uid:{} id:{} commodity:{} err:{}",
                    player.getUid(), activityId, rechargeCfg.getCommodityId(), e);
            NKErrorCode errorCode =
                    (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                            : NKErrorCode.UnknownError;
            NKErrorCode.MidasBuyGoodsFailed.throwError("oneDollar raffle activity failed to buy midas product, reason:{}", errorCode);
        }
        return NKErrorCode.OK;
    }

    // 确认开通周末礼包
    public NKErrorCode confirmUpgradeWeekendGift() {
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData()
                .getOneDollarRaffleActivity();
        oneDollarRaffleData.setIsUpgrade(true);
        checkActivityRedPoint();
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
        LOGGER.info("confirmUpgradeWeekendGift {}", player.getUid());
        return NKErrorCode.OK;
    }


    // 是否升级为高级
    public boolean getIsUpgrade() {
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        return oneDollarRaffleData.getIsUpgrade();
    }

    // 发送剩余奖励重置数据
    private void sendMailRewardAndResetData() {
        if (!getIsUpgrade()) {
            return;
        }
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        MapAttrObj<Integer, UpgradeAwardInfo> awardList = oneDollarRaffleData.getAwardList();

        List<ResCommon.Item> rewardItemList = new ArrayList<>();

        List<ResActivity.WeekendGiftCheckInConfig> weekendGiftCheckInConfigs = WeekendGiftCheckInConfig
                .getInstance()
                .getArrayList()
                .stream()
                .filter(v -> v.getActivityId() == this.activityId)
                .collect(Collectors.toList());
        for (int index = 5; index < weekendGiftCheckInConfigs.size()+5; index++) {
            if (awardList.get(index).getIsGetRewards()  ) {
                //已经领过
            }else {
                getCheckInManualRewardByIndex(index, rewardItemList);
            }
        }
        if (rewardItemList.isEmpty()) {
            LOGGER.warn("get Weekend Gift CheckIn Config Reward get all failed:{}, {}",
                    player.getUid(), activityId);
            return;
        }

        int mailConfId = getActivityMainConfig().getActivityParam(2);
        MailAttachmentList.Builder mailReward = MailAttachmentList.newBuilder();
        for (ResCommon.Item item : rewardItemList) {
            mailReward.addList(MailAttachment.newBuilder().setItemIfo(
                    ItemInfo.newBuilder().setItemId(item.getItemId()).setItemNum(item.getItemNum()).build()));
        }
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                long mailId = MailInteraction.sendTemplateMail(player.getUid(), mailConfId, mailReward,
                        MailInteraction.TlogSendReason.OneDollarRaffle);
                if (mailId == 0) {
                    LOGGER.warn("oneDollarRaffleActivity  send mail failed:{}, {}",
                            player.getUid(), activityId);
                }
                LOGGER.debug("oneDollarRaffleActivity  send mail success:{}, {}, {}",
                        player.getUid(), activityId, mailId);
                return null;
            }, "sendMailReward", true);
        } catch (NKCheckedException e) {
            LOGGER.error("player {} oneDollarRaffleActivity runJob catch {}", player.getUid(), e);
        }
    }

    // 领取礼包奖励
    public NKErrorCode getCheckInManualReward(CsActivity.GetWeekendGiftReward_C2S_Msg reqMsg) {
        int checkInIndex = reqMsg.getCheckInIndex();
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        MapAttrObj<Integer, UpgradeAwardInfo> awardList = oneDollarRaffleData.getAwardList();
        UpgradeAwardInfo upgradeAwardInfo = awardList.get(checkInIndex);
        if (upgradeAwardInfo.getIsGetRewards()) {
            return NKErrorCode.ActivityOneDollarRaffleIsAwarded;
        }
        List<ResCommon.Item> rewardItemList = new ArrayList<>();
        getCheckInManualRewardByIndex(checkInIndex, rewardItemList);

        // 给与奖励
        ChangedItems changeItem = new ChangedItems(rewardItemList,
                ItemChangeReason.ICR_GetUpgradeWeekendGift.getNumber(), "");
        changeItem.setChangeReservedParams(5,getIsUpgrade() ? 1 : 0);
        changeItem.setActivityId(activityId);
        NKPair<NKErrorCode, ItemChangeDetails> addRes = player.getBagManager().AddItems2(
                changeItem, true, checkInIndex);
        NKErrorCode errorCode = addRes.getKey();
        if (!errorCode.isOk()) {
            LOGGER.warn("get weekend gift Reward add item failed:{},{},{}",
                    player.getUid(), changeItem.toString(), errorCode);
            return errorCode;
        }
        upgradeAwardInfo.setId(checkInIndex).setIsGetRewards(true);
        oneDollarRaffleData.putAwardList(checkInIndex,upgradeAwardInfo);
        oneDollarRaffleData.setAwardCount(oneDollarRaffleData.getAwardCount() + 1);
        oneDollarRaffleData.setLastAwardTimeMs(Framework.currentTimeMillis());
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
        return NKErrorCode.OK;
    }

    // 领取指定某天的礼包奖励
    private void getCheckInManualRewardByIndex(int checkInIndex, List<ResCommon.Item> rewardItemList) {

        List<ResActivity.WeekendGiftCheckInConfig> weekendGiftCheckInConfigs = WeekendGiftCheckInConfig
                .getInstance()
                .getArrayList()
                .stream()
                .filter(v -> v.getActivityId() == this.activityId)
                .filter(v -> v.getId() == checkInIndex).collect(Collectors.toList());
        ResActivity.WeekendGiftCheckInConfig weekendGiftCheckInConfig = weekendGiftCheckInConfigs.get(0);
        if (weekendGiftCheckInConfig != null) {
            UpgradeAwardInfo checkInInfo = getManualInfoByCheckInIndex(checkInIndex);
            if (getIsUpgrade() ) {
                checkInInfo = new UpgradeAwardInfo();
                checkInInfo.setId(checkInIndex).setIsGetRewards(true);
                addCheckInInfoByCheckInIndex(checkInIndex, checkInInfo);
                for (ResCommon.Item itemInfo : weekendGiftCheckInConfig.getWeekendRewardInfoList()) {
                    rewardItemList.add(ResCommon.Item.newBuilder()
                            .setItemId(itemInfo.getItemId())
                            .setItemNum(itemInfo.getItemNum())
                            .build());
                }
            }
        } else {
            LOGGER.warn("get weekendGiftCheckInConfig reward By Index invalid config : player:{}, checkInIndex:{}, activityId:{}",
                    player.getUid(), checkInIndex, getActivityMainConfig().getId());
        }
        LOGGER.warn("weekendGiftCheckInConfig invalid config : player:{}, checkInIndex:{}, activityId:{}",
                player.getUid(), checkInIndex, getActivityMainConfig().getId());

    }

    // 获取指定id的周末礼包信息
    private UpgradeAwardInfo getManualInfoByCheckInIndex(int checkInIndex) {
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        return  oneDollarRaffleData.getAwardList(checkInIndex);
    }

    // 增加指定id的周末礼包信息
    private void addCheckInInfoByCheckInIndex(int checkInIndex, UpgradeAwardInfo checkInInfo) {
        OneDollarRaffleData oneDollarRaffleData = getActivityUnit().getDetailData().getOneDollarRaffleActivity();
        oneDollarRaffleData.putAwardList(checkInIndex, checkInInfo);
    }

    @Override
    public void checkActivityRedPoint() {
        boolean isShowRedPoint = activityUnit.getRedDotShow();
        if(isShowRedPoint){
            updateRedDotInfo();
            return;
        }
        delRedDot();
    }




}
