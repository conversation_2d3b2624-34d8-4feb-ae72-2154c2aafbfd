package com.tencent.wea.playerservice.interaction;

import com.google.protobuf.TextFormat;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class AddWarmRound extends AbstractInteraction{
    private static final Logger LOGGER = LogManager.getLogger(AddWarmRound.class);

    @Override
    public boolean exec(Player player,
                        PlayerInteraction.PlayerInteractionData body,
                        long caller,
                        InteractionMgr.INTERACTION_SOURCE source) {
        try {
            player.getWarmRoundManager().updateWarmRoundByInteraction(body.getAddWarmRoundParams());
        } catch (Exception e) {
            LOGGER.error("player {} handle updateWarmRoundByInteraction catch {}",
                    player.getUid(), e.toString());
            return false;
        }


        if (LOGGER.isDebugEnabled()) {
            LOGGER.error("player {} updateWarmRoundByInteraction succ param {}",
                    player.getUid(), TextFormat.shortDebugString(body.getAddWarmRoundParams()));
        }
        return true;
    }
}
