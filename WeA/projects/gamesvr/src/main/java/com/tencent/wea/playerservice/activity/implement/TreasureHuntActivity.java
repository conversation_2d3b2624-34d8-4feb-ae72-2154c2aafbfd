package com.tencent.wea.playerservice.activity.implement;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.random.util.RandomByWeight;
import com.tencent.resourceloader.resclass.ActivityLotteryDrawMiscData;
import com.tencent.resourceloader.resclass.WolfTeamChestGiftConfData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.AttrTreasureHunt;
import com.tencent.wea.attr.TreasureHuntHistoryData;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.event.common.PlayerGetItemEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity2;
import com.tencent.wea.xlsRes.ResWolfTeamChest.WolfTeamChestGiftConf;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 秘境寻宝活动
 */
public class TreasureHuntActivity extends TaskAndShopActivity implements EventConsumer {

    private static final Logger LOGGER = LogManager.getLogger(TreasureHuntActivity.class);

    public TreasureHuntActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
        if (Objects.nonNull(player)) {
            player.getEventSwitch().register(this);
        }
    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATTreasureHunt;
    }

    @Override
    public void init(boolean isNew) {
        super.init(isNew);
        if (isNew) {
            clearItems();
        }
        checkActivityRedPoint();
    }

    @Override
    public void onExpire() {
        clearItems();
    }

    @Override
    public void stop() {
        if (Objects.nonNull(player)) {
            player.getEventSwitch().unregister(this);
        }
    }

    @Override
    public void onStatusChange() {

    }

    @Override
    public void afterLoginFinish() {

    }

    @Override
    public void checkActivityRedPoint() {
        super.checkActivityRedPoint();
    }

    @Override
    public boolean checkFeaturedRedPoints() {
        return hasRedDot();
    }

    @Override
    public boolean isDestroyed() {
        return false;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerGetItem)
    private void onEvent(PlayerGetItemEvent event) {
        checkActivityRedPoint();
    }

    /////////////////////////////////////////////////////////////////////////////////////////////// util

    private AttrTreasureHunt getActivityAttr() {
        return getActivityUnit().getDetailData().getTreasureHunt();
    }

    private int getHoeItemId() {
        return getActivityMainConfig().getActivityParam(1);
    }

    private List<Integer> getAllTreasureItemIds() {
        List<WolfTeamChestGiftConf> confList = WolfTeamChestGiftConfData.getInstance().getConfList(activityId);
        return confList.stream().flatMap(e -> e.getItemIdList().stream()).distinct().collect(Collectors.toList());
    }

    private WolfTeamChestGiftConf excavateImpl(List<WolfTeamChestGiftConf> confList, int lotteryDrawNum) {
        long uid = player.getUid();
        List<WolfTeamChestGiftConf> guaranteeConf = new ArrayList<>();  // 达到保底的奖励
        RandomByWeight<WolfTeamChestGiftConf> normalConf = new RandomByWeight<>(false);
        for (WolfTeamChestGiftConf conf : confList) {
            if (conf.getGuaranteeTimes() > 0) {
                // 触发保底
                if (lotteryDrawNum % conf.getGuaranteeTimes() == 0) {
                    if (conf.getGuaranteeTimesCount() > 0) {
                        // 保底次数上限
                        if (lotteryDrawNum / conf.getGuaranteeTimes() <= conf.getGuaranteeTimesCount()) {
                            guaranteeConf.add(conf);
                        }
                    } else {
                        guaranteeConf.add(conf);
                    }
                }
            }
            // 加入权重随机队列
            if (conf.getWeight() > 0) {
                normalConf.add(conf, conf.getWeight());
            }
        }

        WolfTeamChestGiftConf res;
        // 没触发保底
        if (guaranteeConf.isEmpty()) {
            res = normalConf.rand();
        } else {
            res = guaranteeConf.stream()
                    .max(Comparator.comparingInt(WolfTeamChestGiftConf::getGuaranteeTimes))
                    .get();
        }

        LOGGER.debug("excavate result. uid:{} activityId:{} guaranteeConfSize:{} guaranteeConfSize:{} resId:{}", uid, activityId, normalConf.size(), guaranteeConf.size(), res.getId());
        return res;
    }

    /** 是否有红点 */
    private boolean hasRedDot() {
        int hoeItemId = getHoeItemId();
        // 有锄头
        if (player.getBagManager().getItemNumByItemId(hoeItemId) > 0L) {
            return true;
        }
        // 有宝藏
        return getAllTreasureItemIds().stream().anyMatch(e -> player.getBagManager().getItemNumByItemId(e) > 0L);
    }

    public boolean isCritTime() {
        return ActivityLotteryDrawMiscData.getInstance().critTimeRange.isInRange();
    }

    public void clearItems() {
        ChangedItems items = new ChangedItems(ItemChangeReason.ICR_TreasureHuntActivityActivityExpire.getNumber(), "");

        int hoeItemId = getHoeItemId();
        long itemNumByItemId = player.getBagManager().getItemNumByItemId(hoeItemId);
        if (itemNumByItemId > 0) {
            items.mergeItemInfo(hoeItemId, itemNumByItemId);
        }

        for (Integer treasureItemId : getAllTreasureItemIds()) {
            long itemNum = player.getBagManager().getItemNumByItemId(treasureItemId);
            if (itemNum > 0) {
                items.mergeItemInfo(treasureItemId, itemNum);
            }
        }

        items.getTlogData().setChangeSubReason(activityId);
        player.getBagManager().minItemsAsPossible(items);
    }

    /////////////////////////////////////////////////////////////////////////////////////////////// csHandler

    /** 挖掘(消耗锄头,获得宝藏) */
    @SuppressWarnings("ConstantConditions")
    public CsActivity2.ActivityTreasureHuntExcavate_S2C_Msg.Builder excavateMsgHandler(CsActivity2.ActivityTreasureHuntExcavate_C2S_Msg reqMsg) {
        long uid = player.getUid();
        // 做的时候策划没有确定需要单个挖掘还是一键挖掘, 2种我都做了, 后面策划确定只要单个挖掘, 所以这里屏蔽掉. 后续如果要增加一键挖掘, 这里再放开
        if (reqMsg.getIsOneKey()) {
            LOGGER.error("can not oneKey excavate. uid:{} activityId:{}", uid, activityId);
            NKErrorCode.InvalidParams.throwError("can not oneKey excavate");
        }

        int hoeItemId = getHoeItemId();
        var activityAttr = getActivityAttr();
        int haveItemNum = (int) player.getBagManager().getItemNumByItemId(hoeItemId);
        int costItemNum = reqMsg.getIsOneKey() ? haveItemNum : 1;
        int lotteryDrawNum = activityAttr.getLotteryDrawNum();
        long currentTimeSec = DateUtils.currentTimeNanos();
        boolean isCrit = isCritTime();

        List<WolfTeamChestGiftConf> confList = WolfTeamChestGiftConfData.getInstance().getConfList(activityId);
        if (confList.isEmpty()) {
            LOGGER.error("confList is empty. uid:{} activityId:{}", uid, activityId);
            NKErrorCode.ConfigNotExists.throwError("confList is empty");
        }

        List<WolfTeamChestGiftConf> realTimeConfList = new ArrayList<>();
        for (WolfTeamChestGiftConf conf : confList) {
            if (isCrit && conf.getIsCrit() || (!isCrit && !conf.getIsCrit())) {
                realTimeConfList.add(conf);
            }
        }
        if (realTimeConfList.isEmpty()) {
            LOGGER.error("realTimeConfList is empty. uid:{} activityId:{}", uid, activityId);
            NKErrorCode.ConfigNotExists.throwError("realTimeConfList is empty");
        }

        ChangedItems costItems = new ChangedItems(hoeItemId, costItemNum, ItemChangeReason.ICR_TreasureHuntActivityExcavateCost.getNumber(), "");
        if (0 == costItemNum || !player.getBagManager().isItemsEnough(costItems)) {
            LOGGER.error("item not enough. uid:{} activityId:{} costItemNum:{} reqMsg:{}", uid, activityId, costItemNum, reqMsg);
            NKErrorCode.ItemNumNotEnough.throwError("item not enough");
        }

        WolfTeamChestGiftConf excavateConf = null;
        ChangedItems obtainItems = new ChangedItems(ItemChangeReason.ICR_TreasureHuntActivityExcavateObtain.getNumber(), "");
        for (long i = 0; i < costItemNum; i++) {
            WolfTeamChestGiftConf conf = excavateImpl(realTimeConfList, lotteryDrawNum += 1);
            excavateConf = conf;
            obtainItems.mergeItemInfo(conf.getItemIdList(), conf.getItemNumList());
        }
        activityAttr.setLotteryDrawNum(lotteryDrawNum);
        /*activityAttr.addExcavateNum(1);

        // 先改属性再操作道具
        NKErrorCode nkErrorCode = player.getBagManager().MinItems(costItems, activityId);
        if (!nkErrorCode.isOk()) {
            nkErrorCode.throwError("excavate min item error. uid:{} activityId:{} reqMsg:{}", uid, activityId, reqMsg);
        }

        NKPair<NKErrorCode, ItemChangeDetails> addRes = player.getBagManager().AddItems2(obtainItems);
        if (!addRes.key.isOk()) {
            LOGGER.error("excavate add item error. uid:{} activityId:{} reqMsg:{} res:{}", uid, activityId, reqMsg, addRes);
        }

        activityAttr.putTreasureHuntHistory(activityAttr.getExcavateNum(), new TreasureHuntHistoryData()
                .setExcavateNum(activityAttr.getExcavateNum())
                .setExcavateEpochSecs(currentTimeSec)
                // 我: 那就是我们这个活动, 不会配多个道具是吧, 如果配了的话, 我取第1个道具ok吧
                // 策划: ok
                .setItemId(excavateConf.getItemId(0))
                .setItemCnt(excavateConf.getItemNum(0)));
        int excavateHistoryMaxNum = ActivityLotteryDrawMiscData.getInstance().getExcavateHistoryMaxNum();
        int historyNum = activityAttr.getTreasureHuntHistory().size();
        int needDeleteNum = historyNum - excavateHistoryMaxNum;
        if (needDeleteNum > 0) {
            List<TreasureHuntHistoryData> historyByEpoch = new ArrayList<>(activityAttr.getTreasureHuntHistory().values())
                    .stream()
                    .sorted(Comparator.comparingLong(TreasureHuntHistoryData::getExcavateEpochSecs))
                    .collect(Collectors.toList());
            for (int i = 0; i < needDeleteNum; i++) {
                activityAttr.removeTreasureHuntHistory(historyByEpoch.get(i).getExcavateNum());
            }
        }*/

        checkActivityRedPoint();
        // player.sendNtfMsg(MSG_TYPE_ACTIVITYTREASUREHUNTATTRUPDATENTF, CsActivity2.ActivityTreasureHuntAttrUpdateNtf.newBuilder());
        return CsActivity2.ActivityTreasureHuntExcavate_S2C_Msg.newBuilder();
    }

    /** 售卖 */
    public CsActivity2.ActivityTreasureHuntSell_S2C_Msg.Builder sellMsgHandler() {
        long uid = player.getUid();

        List<WolfTeamChestGiftConf> confList = WolfTeamChestGiftConfData.getInstance().getConfList(activityId);
        // List<Integer> itemIds = confList.stream().flatMap((e -> e.getItemIdList().stream())).distinct().collect(Collectors.toList());
        if (confList.isEmpty()) {
            LOGGER.error("confList is empty. uid:{} activityId:{}", uid, activityId);
            NKErrorCode.ConfigNotExists.throwError("confList is empty");
        }

        ChangedItems costItems = new ChangedItems(ItemChangeReason.ICR_TreasureHuntActivitySellCost.getNumber(), "");
        ChangedItems obtainItems = new ChangedItems(ItemChangeReason.ICR_TreasureHuntActivitySellObtain.getNumber(), "");
        List<Integer> soldItemIds = new ArrayList<>();
        for (WolfTeamChestGiftConf conf : confList) {
            for (int i = 0, size = conf.getItemIdList().size(); i < size; ++i) {
                // 兼容配置错误
                if (conf.getSellObtainItemIdsList().isEmpty() || conf.getSellObtainItemNumsList().isEmpty()) {
                    continue;
                }
                int itemId = conf.getItemId(i);
                if (soldItemIds.contains(itemId)) {
                    continue;
                }
                long haveItemNum = player.getBagManager().getItemNumByItemId(itemId);
                soldItemIds.add(itemId);
                if (haveItemNum > 0) {
                    costItems.mergeItemInfo(itemId, haveItemNum);
                    obtainItems.mergeItemInfo(conf.getSellObtainItemIds(i), conf.getSellObtainItemNums(i) * haveItemNum);
                }
            }
        }

        NKErrorCode nkErrorCode = player.getBagManager().MinItems(costItems, activityId);
        if (!nkErrorCode.isOk()) {
            nkErrorCode.throwError("sell min item error. uid:{} activityId:{}", uid, activityId);
        }

        NKPair<NKErrorCode, ItemChangeDetails> addRes = player.getBagManager().AddItems2(obtainItems);
        if (!addRes.key.isOk()) {
            LOGGER.error("excavate add item error. uid:{} activityId:{} res:{}", uid, activityId, addRes);
        }

        checkActivityRedPoint();
        return CsActivity2.ActivityTreasureHuntSell_S2C_Msg.newBuilder();
    }

    /** GM */
    @Override
    public NKErrorCode doGeneralGmCmd(List<String> args) {
        LOGGER.error("TreasureHuntActivity.gmHandle playerId:{} GM:{}", player.getUid(), args);
        int subCmdId = Integer.parseInt(args.get(0));
        switch (subCmdId) {
            case 0: {
                getActivityAttr().clear();
                LOGGER.error("doGmCmdAnswer:{}", getActivityAttr());
            }
            break;

            case 1: {
                LOGGER.error("doGmCmdAnswer:{}", getActivityAttr());
            }
            break;

            case 2: {
                CsActivity2.ActivityTreasureHuntExcavate_S2C_Msg.Builder ret = excavateMsgHandler(CsActivity2.ActivityTreasureHuntExcavate_C2S_Msg.newBuilder()
                        .setIsOneKey(Boolean.parseBoolean(args.get(1))).build());
                LOGGER.error("doGmCmdAnswer:{}", ret);
            }
            break;

            case 3: {
                CsActivity2.ActivityTreasureHuntSell_S2C_Msg.Builder ret = sellMsgHandler();
                LOGGER.error("doGmCmdAnswer:{}", ret);
            }
            break;

            case 4: {
                ActivityLotteryDrawMiscData.getInstance().gmExcavateHistoryMaxNum = Integer.parseInt(args.get(1));
                LOGGER.error("doGmCmdAnswer:{}", ActivityLotteryDrawMiscData.getInstance().gmExcavateHistoryMaxNum);
            }
            break;

            default:
                LOGGER.error("gmHandle playerId:{} subCmdIdNotFound:{}", player.getUid(), subCmdId);
                break;
        }

        return NKErrorCode.OK;
    }
}
