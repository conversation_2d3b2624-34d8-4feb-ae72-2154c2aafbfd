package com.tencent.wea.playerservice.match.warmroundupdater;

import com.tencent.wea.playerservice.match.PlayerMatchWarmRoundManager;
import com.tencent.wea.playerservice.match.warmroundupdater.impl.*;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.keywords.WarmRoundScoreType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;

/**
 * 温暖局分数更新器工厂
 */
public class WarmRoundScoreCalFactory {
    private static final Logger LOGGER = LogManager.getLogger(WarmRoundScoreCalFactory.class);

    /**
     * 获取温暖分对应的结算器
     * @param warmRoundScoreType 温暖分类型
     * @param player 玩家
     * @param warmRoundManager 玩家的温暖分管理器
     * @return 结算器 可能为null
     */
    @Nullable
    public static WarmRoundScoreEndBattleUpdater getUpdater(WarmRoundScoreType warmRoundScoreType,
                                                            Player player,
                                                            PlayerMatchWarmRoundManager warmRoundManager) {
        switch (warmRoundScoreType) {
            case WRST_QuickPlay:
            case WRST_Qualifying: {
                return new MainGameOneWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_QualifyingTwo:
            case WRST_QuickPlayTwo: {
                return new MainGameTwoWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_QualifyingFour:
            case WRST_QuickPlayFour: {
                return new MainGameFourWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_BrGameQualifying:
            case WRST_BrGame: {
                return new FpsBrOneWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_BrGameTwoQualifying:
            case WRST_BrGameTwo: {
                return new FpsBrTwoWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_BrGameFourQualifying:
            case WRST_BrGameFour: {
                return new FpsBrFourWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_FPS_GunGameQualifying:
            case WRST_FPS_GunGame: {
                return new FpsGunGameWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_FPS_GunGameTeamQualifying:
            case WRST_FPS_GunGameTeam: {
                return new FpsGunGameTeamWarmRoundScoreUpdater(player, warmRoundManager);
            }

            // 冲锋竞技 和 赏金玩法 计算一样 复用
            case WRST_FPS_GunGameTDMTeam:
            case WRST_GunGameKC: {
                return new FpsGunGameTDMTeamWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_StarChase:
            case WRST_StarChaseQualifying:
            case WRST_ThreeStarChase:
            case WRST_ThreeStarChaseQualifying:{
                // 小星快跑 ds控制分数
                return new DirectWarmRoundScoreUpdater(player, warmRoundManager);
            }
            case WRST_DfGame: {
                // 撤离玩法 ds控制分数
                return new DirectWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_TYC_TDGameTeam: {
                return new TYCTDWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_TYC_TDSGameTeam: {
                return new TYCTDSWarmRoundScoreUpdater(player, warmRoundManager);
            }

            // moba根据排名
            case WRST_Arena_Hz:
            case WRST_Arena_HzQualifying:
            case WRST_HOK:
            case WRST_HOKQualifying:
            case WRST_Arena:
            case WRST_ArenaQualifying: {
                return new SelfRankWarmRoundScoreUpdater(player, warmRoundManager);
            }

            case WRST_Lighting: {
                return new LightningWarmRoundScoreUpdater(player, warmRoundManager);
            }

            // 峡谷吃鸡根据排名
            case WRST_Arena_Bs:
            case WRST_Arena_BsQualifying: {
                return new SelfRankWarmRoundScoreUpdater(player, warmRoundManager);
            }

            // ug c炸船 沙漠灰爆破用排名
            case WRST_PUGC_BombTheShip:
            case WRST_FPS_DesertGreyDemolition: {
                return new SelfRankWarmRoundScoreUpdater(player, warmRoundManager);
            }

            default: {
                LOGGER.warn("WarmRoundScoreType {} has no updater",
                        warmRoundScoreType);
                return null;
            }
        }
    }
}
