package com.tencent.wea.playerservice.player;

import static com.tencent.wea.wolfKill.Util.wolfKillCheck;
import static com.tencent.wea.xlsRes.ResMatch.MatchTypeSettleProc.MTSC_Rank;
import static com.tencent.wea.xlsRes.keywords.LevelCompletionType.LCT_Completed;

import com.tencent.match.matchConfigProxyMgr.ConfigProxyMgr;
import com.tencent.match.specialrule.WereWolfSpecialUtils;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.guid.BaseGenerator;
import com.tencent.nk.util.guid.CustomRoomIdGenerator;
import com.tencent.nk.util.guid.GuidType;
import com.tencent.nk.util.guid.RoomIdGenerator;
import com.tencent.nk.util.guid.StarPTeamIdGenerator;
import com.tencent.opentracing.tps.TpsSpan;
import com.tencent.opentracing.tps.TpsTracer;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.CustomRoomData;
import com.tencent.resourceloader.resclass.FeatureKeySwitchConf;
import com.tencent.resourceloader.resclass.LevelInfoData;
import com.tencent.resourceloader.resclass.MatchConstsData;
import com.tencent.resourceloader.resclass.MatchDegreeTypeGroupData;
import com.tencent.resourceloader.resclass.MatchTypeConflictOutlookData;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.resourceloader.resclass.MatchTypeOutlookReplaceData;
import com.tencent.resourceloader.resclass.MatchUnlockConditionData;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.QDTSeasonCfgData;
import com.tencent.resourceloader.resclass.QRCodeCalculationRuleData;
import com.tencent.resourceloader.resclass.ServerTextConfData;
import com.tencent.resourceloader.resclass.StarPModeConf;
import com.tencent.resourceloader.resclass.VersionCompBattleConf;
import com.tencent.resourceloader.resclass.WolfKillPreparationsWidgetConf;
import com.tencent.resourceloader.resclass.WolfKillVocation;
import com.tencent.rpc.RpcResult;
import com.tencent.seq.SeqUtil;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tconnd.Session;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiCoroutine.NoAsyncCheck;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.timiutil.tool.FunctionUtil;
import com.tencent.tss.TssEnum.PICTYPE;
import com.tencent.ugc.CommonUtil;
import com.tencent.ugc.UgcFuncUtil;
import com.tencent.ugcsafe.UicEnum.SUB_SCENE;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.AttrCustomRoomInfo;
import com.tencent.wea.attr.AttrRoomInfo;
import com.tencent.wea.attr.CommonConditionGroup;
import com.tencent.wea.attr.ConditionGroupInfo;
import com.tencent.wea.attr.DBRelation;
import com.tencent.wea.attr.DressItemInfo;
import com.tencent.wea.attr.RoguelikePassLevelInfo;
import com.tencent.wea.attr.RoomExtraInfo;
import com.tencent.wea.framework.GSConfig;
import com.tencent.wea.interaction.player.SendNoticeInteraction;
import com.tencent.wea.midjoin.HokConfs;
import com.tencent.wea.midjoin.TycConfs;
import com.tencent.wea.playerservice.condition.group.MatchTypeUnlockConditionGroup;
import com.tencent.wea.playerservice.condition.group.MatchUnlockConditionGroup;
import com.tencent.wea.playerservice.condition.group.PreparationWidgetUnlockConditionGroup;
import com.tencent.wea.playerservice.event.common.PlayerQRCodePointEvent;
import com.tencent.wea.playerservice.event.common.PlayerUnlockMatchType;
import com.tencent.wea.playerservice.event.consumer.PlayerChangeProfileConsumer;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.midjoin.BattleSvrFriend;
import com.tencent.wea.playerservice.player.PlayerStateMgr.PlayerStateAction;
import com.tencent.wea.playerservice.publicroom.PublicRoom;
import com.tencent.wea.playerservice.publicroom.PublicRoomMgr;
import com.tencent.wea.playerservice.room.CoMatchSubComponent;
import com.tencent.wea.playerservice.room.PublicRoomListComponent;
import com.tencent.wea.playerservice.room.RecruitComponent;
import com.tencent.wea.playerservice.room.StarPRecruitComponent;
import com.tencent.wea.playerservice.room.ai.AIInvitationComponent;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr.UgcMapEnjoyBeginData;
import com.tencent.wea.playerservice.uic.BattleChatReportInfo;
import com.tencent.wea.playerservice.uic.PlayerTssUtil;
import com.tencent.wea.protocol.AttrBattleInfo.proto_BattleInfo;
import com.tencent.wea.protocol.AttrPlayerPublicBattleInfo;
import com.tencent.wea.protocol.CsBattle.ExitBattleNtf;
import com.tencent.wea.protocol.CsCommon.StateType;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.CsRoom;
import com.tencent.wea.protocol.CsRoom.RoomBroadcastInfoNtf;
import com.tencent.wea.protocol.CsRoom.RoomMapDownloadReminderNtf;
import com.tencent.wea.protocol.CsRoom.RoomMapDownloadStatNtf;
import com.tencent.wea.protocol.CsRoom.RoomMapStateSyncNtf;
import com.tencent.wea.protocol.CsRoom.RoomMiniGameInvitationNtf;
import com.tencent.wea.protocol.CsRoom.RoomPlayerReadyReminderNtf;
import com.tencent.wea.protocol.CsRoom.RoomSPBroadcastInfoNtf;
import com.tencent.wea.protocol.CsRoom.RoomWantToJoinNtf;
import com.tencent.wea.protocol.CsRoom.TeamRecruitQueryByPlayGroup_C2S_Msg;
import com.tencent.wea.protocol.CsUgc;
import com.tencent.wea.protocol.CsUgc.RecListType;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsCommon.SeqInfo;
import com.tencent.wea.protocol.SsCommon.SeqType;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.SsGamesvr.PlayerRoomQueryType;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomAskToJoinRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomConfirmEnterProgressNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomMemberModifyNtfReq;
import com.tencent.wea.protocol.SsLbssvr;
import com.tencent.wea.protocol.SsRoomsvr;
import com.tencent.wea.protocol.SsRoomsvr.RpcCreateRoomRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcExitRoomReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcExitRoomRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcJoinRoomRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcRoomInfoReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcRoomInfoRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcRoomInvitationRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcRoomPlayerOfflineRes.Builder;
import com.tencent.wea.protocol.SsRoomsvr.RpcRoomPlayerOnlineRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcUgcRoomQuickJoinRes;
import com.tencent.wea.protocol.SsSeqsvr;
import com.tencent.wea.protocol.SsStarproomsvr;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.common.AIInvitationData;
import com.tencent.wea.protocol.common.CoMatchAgreeFlag;
import com.tencent.wea.protocol.common.CompetitionData;
import com.tencent.wea.protocol.common.EnmStarPTeamUpdateType;
import com.tencent.wea.protocol.common.ExitLastSceneReason;
import com.tencent.wea.protocol.common.FaceToFaceRoomType;
import com.tencent.wea.protocol.common.GameModeType;
import com.tencent.wea.protocol.common.InvitationSourceType;
import com.tencent.wea.protocol.common.JoinMidwayFriendSvrInfo;
import com.tencent.wea.protocol.common.KVEntry;
import com.tencent.wea.protocol.common.LongArray;
import com.tencent.wea.protocol.common.MatchCancelReason;
import com.tencent.wea.protocol.common.MatchContentInfo;
import com.tencent.wea.protocol.common.MatchDynamicConfigData;
import com.tencent.wea.protocol.common.MatchIsolateType;
import com.tencent.wea.protocol.common.MatchRuleClientInfo;
import com.tencent.wea.protocol.common.MatchRuleInfo;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.MiniGameNoticeInfo;
import com.tencent.wea.protocol.common.ModifyType;
import com.tencent.wea.protocol.common.OpSource;
import com.tencent.wea.protocol.common.PlatMiniGameNoticeInfo;
import com.tencent.wea.protocol.common.PlayerClientInfo;
import com.tencent.wea.protocol.common.PlayerNoticeMsgType;
import com.tencent.wea.protocol.common.PlayerPublicInfo;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.RobotSourceType;
import com.tencent.wea.protocol.common.RoomBriefInfo;
import com.tencent.wea.protocol.common.RoomBroadcastInfo;
import com.tencent.wea.protocol.common.RoomBroadcastInfoType;
import com.tencent.wea.protocol.common.RoomCreateOptions;
import com.tencent.wea.protocol.common.RoomDisplayInfo;
import com.tencent.wea.protocol.common.RoomExitType;
import com.tencent.wea.protocol.common.RoomGeoInfo;
import com.tencent.wea.protocol.common.RoomInfo;
import com.tencent.wea.protocol.common.RoomJoinType;
import com.tencent.wea.protocol.common.RoomLevelSetting;
import com.tencent.wea.protocol.common.RoomMiniGameInfo;
import com.tencent.wea.protocol.common.RoomPlayerClientInfo;
import com.tencent.wea.protocol.common.RoomPublicType;
import com.tencent.wea.protocol.common.RoomRecommendListSource;
import com.tencent.wea.protocol.common.RoomRecruitArray;
import com.tencent.wea.protocol.common.RoomRoundInfo;
import com.tencent.wea.protocol.common.RoomSetting;
import com.tencent.wea.protocol.common.RoomShareType;
import com.tencent.wea.protocol.common.RoomSpecialSetting;
import com.tencent.wea.protocol.common.RoomSpecialSettingType;
import com.tencent.wea.protocol.common.RoomType;
import com.tencent.wea.protocol.common.RoomUgcInfo;
import com.tencent.wea.protocol.common.StarPRoomPveInfo;
import com.tencent.wea.protocol.common.StarPTeamUserInfo;
import com.tencent.wea.protocol.common.StreamStateInfo;
import com.tencent.wea.protocol.common.TeamInvitationType;
import com.tencent.wea.protocol.common.TeamJoinRoomExtraInfo;
import com.tencent.wea.protocol.common.TeamJoinRoomExtraInfoType;
import com.tencent.wea.protocol.common.TeamQuickJoinRoomInfo;
import com.tencent.wea.protocol.common.UgcMatchMapBriefFromRemote_Server_BirthInfo;
import com.tencent.wea.protocol.common.UgcRoomArray;
import com.tencent.wea.protocol.common.UgcRoomInfo;
import com.tencent.wea.protocol.common.UniversalRoom;
import com.tencent.wea.protocol.common.UniversalRoomArray;
import com.tencent.wea.protocol.common.WereWolfSideIdentityInfo;
import com.tencent.wea.protocol.common.XiaoWoType;
import com.tencent.wea.redis.PublicCustomRoomInfoDao;
import com.tencent.wea.redis.PublicPlayerBattleDao;
import com.tencent.wea.room.RoomUtil;
import com.tencent.wea.room.RoomUtil.UpdateMemberBaseInfoSource;
import com.tencent.wea.rpc.RpcServiceMgr;
import com.tencent.wea.rpc.service.GameService;
import com.tencent.wea.rpc.service.LbsService;
import com.tencent.wea.rpc.service.RoomService;
import com.tencent.wea.rpc.service.SeqService;
import com.tencent.wea.rpc.service.StarproomService;
import com.tencent.wea.rpc.service.UgcService;
import com.tencent.wea.starp.StarPConfs;
import com.tencent.wea.starp.tlog.StarPTlogFlowMgr;
import com.tencent.wea.starp.tlog.flow.TlogMacros.SP_PVP_MATCH_OP_TYPE;
import com.tencent.wea.starp.tlog.flow.TlogMacros.StarPTeamOpType;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tlog.flow.TlogMacros;
import com.tencent.wea.tlog.flow.TlogMacros.ENUMSecEditFlowScenceID;
import com.tencent.wea.tlog.flow.TlogMacros.TEAM_OP_TYPE;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResLevelInfo.T_LevelInfoData;
import com.tencent.wea.xlsRes.ResMatch;
import com.tencent.wea.xlsRes.ResMatch.CustomRoomRobotSetting;
import com.tencent.wea.xlsRes.ResMatch.CustomRoomRule;
import com.tencent.wea.xlsRes.ResMatch.MatchType;
import com.tencent.wea.xlsRes.ResMatch.MatchUnlockConditionConfig;
import com.tencent.wea.xlsRes.ResMatch.TeamRecruitModeType;
import com.tencent.wea.xlsRes.ResNR3E3Vocation;
import com.tencent.wea.xlsRes.ResPreparations;
import com.tencent.wea.xlsRes.ResQRCode.QRCodeCalculationRule;
import com.tencent.wea.xlsRes.ResQualifying.MatchDegreeTypeGroupConfig;
import com.tencent.wea.xlsRes.ResQualifying.MatchDegreeTypeGroupInfo;
import com.tencent.wea.xlsRes.ResQualifying.SeasonCfg;
import com.tencent.wea.xlsRes.keywords.CommonLimitType;
import com.tencent.wea.xlsRes.keywords.ContionGroupType;
import com.tencent.wea.xlsRes.keywords.FeatureOpenType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.MatchConstEnum;
import com.tencent.wea.xlsRes.keywords.PlayerStateType;
import com.tencent.wea.xlsRes.keywords.QualifyingDegreeInfo;
import com.tencent.wea.xlsRes.keywords.RoomRecruitVersion;
import com.tencent.wea.xlsRes.keywords.RoomStateType;
import com.tencent.wea.xlsRes.keywords.RoomStatus;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.PriorityQueue;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 房间管理
 *
 * <AUTHOR>
 * @date 2021/09/18
 */
public class PlayerRoomMgr extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(PlayerRoomMgr.class);

    private static final int LOCK_TIME_MS = 15 * 1000;
    private final boolean localAllowInviteeLock;
    private long lockedByRoomId = 0L;
    private long lockedByRoomExpire = 0L;
    private long tenSecondStamptCache = 0L;
    // 以下两个变量用于锁定相互邀请, 同时同意, ugly but has no alternatives
    private long wantToJoinRoomId = 0L;
    private long wantToQuitRoomId = 0L;
    private long inviteeWantToJoinRoomId = 0L;
    private long inviteeWantToQuitRoomId = 0L;
    private long inviteeLockExpire = 0L;


    private int roomMemberCount = 0;
    private MemberBaseInfo roomLeader;
    private RoomType roomType;

    private List<MemberBaseInfo> memberList = new ArrayList<>();

    /**
     * 记录玩家当前的队伍信息
     */
    private UniversalRoom.Builder currentTeamInfoBuidler = UniversalRoom.newBuilder();

    /**
     * 记录玩家当前自定义房间信息
     */
    private UniversalRoom.Builder currentRoomInfoBuidler = UniversalRoom.newBuilder();

    private CommonConditionGroup matchTypeConditionAttr;
    private CommonConditionGroup matchUnlockTypeConditionAttr;
    // 备战按钮解锁
    private CommonConditionGroup preparationWidgetConditionAttr;
    // 上次查询的招募列表
    private RoomRecruitArray lastRecruitQueryResultList = RoomRecruitArray.newBuilder().build();
    // 上次查询的自定义房间列表
    private UniversalRoomArray lastCustomRoomResultList = UniversalRoomArray.newBuilder().build();
    // 上次查询的ugc自定义房间列表
    private UniversalRoomArray lastUgcCustomRoomResultList = UniversalRoomArray.newBuilder().build();

    private Queue<Long> lastLeaveRoomIds = new LinkedList<>();

    // 上次带队创建房间的时间
    private long lastCreateRoomWithPrejoinTimestampMs = 0L;

    private Map<Long, AskToJoinRequestInfo> askToJoinHistory = new HashMap<>();

    // 是否需要重新online
    private AtomicBoolean needTeamOnline = new AtomicBoolean(false);
    private AtomicBoolean needRoomOnline = new AtomicBoolean(false);

    // 面对面建房的逻辑
    private Face2FaceRoomInfo currentFace2FaceRoomInfo = null;
    // 面对面建房的逻辑

    // 玩家信息同步逻辑
    private MemberBaseInfoUpdateHistory memberBaseInfoUpdateHistory = new MemberBaseInfoUpdateHistory(
            DateUtils.currentTimeMillis());
    // 玩家信息同步逻辑
    // 当前组队的啾灵角色信息
    private StarPTeamUserInfo currStarPInfo = StarPTeamUserInfo.getDefaultInstance();


    // 组件
    private RecruitComponent recruitComponent;
    private PublicRoomListComponent publicRoomListComponent;
    private CoMatchSubComponent coMatchSubComponent;
    private AIInvitationComponent aiInvitationComponent;
    //记录已经注册条件的玩法
    private List<Integer> conditionMatchRecord = new ArrayList<>();
    private StarPRecruitComponent starPRecruitComponent;

    public PlayerRoomMgr(Player player) {
        super(GameModuleId.GMI_RoomMgr, player);
        recruitComponent = new RecruitComponent(player);
        aiInvitationComponent = new AIInvitationComponent(player);

        localAllowInviteeLock = PropertyFileReader.getRealTimeBooleanItem("allowInviteeLock", true);
        matchTypeConditionAttr = player.getUserAttr().getCommonConditionAttr(ContionGroupType.CGT_MatchType);
        if (matchTypeConditionAttr == null) {
            matchTypeConditionAttr = new CommonConditionGroup().setContionGroupType(ContionGroupType.CGT_MatchType);
            player.getUserAttr().getCommonConditionAttr().put(ContionGroupType.CGT_MatchType, matchTypeConditionAttr);
        }

        matchUnlockTypeConditionAttr = player.getUserAttr()
                .getCommonConditionAttr(ContionGroupType.CGT_MatchUnlockType);
        if (matchUnlockTypeConditionAttr == null) {
            matchUnlockTypeConditionAttr = new CommonConditionGroup().setContionGroupType(
                    ContionGroupType.CGT_MatchUnlockType);
            player.getUserAttr().getCommonConditionAttr()
                    .put(ContionGroupType.CGT_MatchUnlockType, matchUnlockTypeConditionAttr);
        }

        preparationWidgetConditionAttr = player.getUserAttr()
                .getCommonConditionAttr(ContionGroupType.CGT_PreparationWidget);
        if (preparationWidgetConditionAttr == null) {
            preparationWidgetConditionAttr = new CommonConditionGroup().setContionGroupType(
                    ContionGroupType.CGT_PreparationWidget);
            player.getUserAttr().getCommonConditionAttr()
                    .put(ContionGroupType.CGT_PreparationWidget, preparationWidgetConditionAttr);
        }

        matchUnlockTypeConditionAttr = player.getUserAttr().getCommonConditionAttr(ContionGroupType.CGT_MatchUnlockType);
        if (matchUnlockTypeConditionAttr == null) {
            matchUnlockTypeConditionAttr =  new CommonConditionGroup().setContionGroupType(ContionGroupType.CGT_MatchUnlockType);
            player.getUserAttr().getCommonConditionAttr().put(ContionGroupType.CGT_MatchUnlockType,matchUnlockTypeConditionAttr);
        }
        player.getEventSwitch().register(new PlayerChangeProfileConsumer(player));
    }

    public RecruitComponent getRecruitComponent() {
        if (recruitComponent == null) {
            recruitComponent = new RecruitComponent(player);
        }
        return recruitComponent;
    }

    public PublicRoomListComponent getPublicRoomListComponent() {
        if (publicRoomListComponent == null) {
            publicRoomListComponent = new PublicRoomListComponent(player);
        }
        return publicRoomListComponent;
    }

    public CoMatchSubComponent getCoMatchSubComponent() {
        if (coMatchSubComponent == null) {
            coMatchSubComponent = new CoMatchSubComponent(player);
        }
        return coMatchSubComponent;
    }

    public AIInvitationComponent getAiInvitationComponent() {
        if (aiInvitationComponent == null) {
            aiInvitationComponent = new AIInvitationComponent(player);
        }
        return aiInvitationComponent;
    }

    public StarPRecruitComponent getStarPRecruitComponent() {
        if (starPRecruitComponent == null) {
            starPRecruitComponent = new StarPRecruitComponent(player);
        }
        return starPRecruitComponent;
    }

    public static int getState2RoomStatus(int state) {
        int result = RoomStatus.RS_NORMAL_VALUE;
        switch (state) {
            case StateType.ST_Room_Wait_VALUE:
            case StateType.ST_Room_Ready_VALUE:
            case StateType.ST_Player_Online_VALUE:
            case StateType.ST_Player_Offline_VALUE:
            case StateType.ST_Player_Ready_VALUE:
                result = RoomStatus.RS_TEAM_VALUE;
                break;
            case StateType.ST_Room_Confirm_VALUE:
                result = RoomStatus.RS_Matching_VALUE;
                break;
            case StateType.ST_Room_Battle_VALUE:
                result = RoomStatus.RS_BATTLE_START_VALUE;
                break;
        }
        return result;
    }

    public static MatchDynamicConfigData genMatchDynamicConfigData(ResMatch.MatchType matchType,
            TcaplusDb.MatchRoomInfo roomInfo) {
        if (matchType != null && ConfigProxyMgr.getInstance().isUseDynamicCfg(matchType)) {
            // 这里添加玩法的规则代码
            if (matchType.getGameModeType() == GameModeType.GMT_UgcMatch_VALUE
                    && roomInfo.getMatchRule().getUgcId() != 0) {  // ugc的匹配规则
                // 给他生成动态的配置
                if (roomInfo.hasUgcInfo() && roomInfo.getUgcInfo().hasUgcBriefInfo() && roomInfo.getUgcInfo()
                        .getUgcBriefInfo().hasBirthInfo()) {
                    UgcMatchMapBriefFromRemote_Server_BirthInfo birthInfo = roomInfo.getUgcInfo().getUgcBriefInfo()
                            .getBirthInfo();
                    return UgcFuncUtil.genUgcMatchDynamicConfigData(birthInfo.getPointNumber(),
                            birthInfo.getCampInfoList());
                }
            }
        }
        return MatchDynamicConfigData.newBuilder().build();
    }

    /**
     * 获取玩家组队是否已满信息
     * TODO 替换到从mgr内存中拿数据
     *
     * @param msgData 数据库记录
     * @return key roomid value 是否已满
     */
    public static NKPair<Long, Boolean> getRoomIsFull(TcaplusManager.TcaplusRecordData msgData, long uid) {
        NKPair<Long, Boolean> retPair = new NKPair<>(0L, false);
        TcaplusDb.MatchRoomInfo roomInfo = (TcaplusDb.MatchRoomInfo) msgData.msg;
        for (int i = 0; i < roomInfo.getMemList().getMemberInfoCount(); ++i) {
            MemberBaseInfo baseInfo = roomInfo.getMemList().getMemberInfoList().get(i);
            if (baseInfo.getUid() == uid) {
                retPair.key = roomInfo.getRoomId();
                break;
            }
        }

        if (retPair.getKey() > 0) {
            ResMatch.MatchType gameCfg = MatchTypeData.getInstance().get(roomInfo.getMatchRule().getMatchTypeId());
            if (null == gameCfg) {
                LOGGER.error("ResMatch.MatchModCf getModCfg failed gameCfg null {} {} {}",
                        roomInfo.getRoomId(), uid, roomInfo.getMatchRule().getMatchTypeId());
            } else {
                int maxTeamMember = ConfigProxyMgr.getInstance().getMaxTeamMember(genMatchDynamicConfigData(gameCfg, roomInfo), gameCfg);
                if (roomInfo.getMemList().getMemberInfoCount() >= maxTeamMember) {
                    retPair.value = true;
                } else {
                    LOGGER.debug("room is not full:{} {} {}", roomInfo.getRoomId(),
                            roomInfo.getMemList().getMemberInfoCount(), maxTeamMember);
                }
            }
        } else {
            LOGGER.debug("roomid == 0 {} {} {}", roomInfo.getRoomId(), roomInfo.toString(), uid);
        }
        return retPair;
    }

    private static NKErrorCode SafeErrorCodeFromInt(int errorCode) {
        NKErrorCode ret = NKErrorCode.forNumber(errorCode);
        return ret == null ? NKErrorCode.UnknownError : ret;
    }

    public static boolean isMatchTypeUrgentClose(int matchTypeId) {
        return FeatureKeySwitchConf.getInstance()
                .getByTypeidAnd1Keys(FeatureOpenType.FOT_K1_MatchType_VALUE, matchTypeId)
                != null;
    }

    public static boolean isCustomRoomUrgentClose(int customRoomPlayModeId) {
        return FeatureKeySwitchConf.getInstance()
                .getByTypeidAnd1Keys(FeatureOpenType.FOT_K1_CustomRoom_VALUE, customRoomPlayModeId)
                != null;
    }

    /**
     * 玩法是否屏蔽
     * @param player
     * @param matchTypeId
     * @return
     */
    public static boolean checkMatchIsBan(Player player, int matchTypeId) {
        if (player.getSession() == null || player.getSession().getClientInfo() == null) {
            //异常情况,避免玩法被全都屏蔽,返回false
            LOGGER.error("checkMatchIsBan error session is null uid:{} ,matchTypeId:{}", player.getUid(), matchTypeId);
            return false;
        }
        Session.ClientInfo clientInfo = player.getSession().getClientInfo();
        return MatchTypeData.getInstance().isPlayIdBlocked(matchTypeId, player.getLoginPlat(), clientInfo.getDevicePlatform(), clientInfo.getCloudGamePlat());

    }

    public static boolean checkMatchBanConfig(ResMatch.MatchType matchType) {
        return matchType.getIsBan() || matchType.getCloudIsBan();

    }

    public void clearLastCreateRoomWithPrejoinTimestampMs() {
        if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid() == player.getUid()) {
            LOGGER.debug("player clear last pre create time, playerUid:{} teamId:{}", player.getUid(), getTeamId());
            this.lastCreateRoomWithPrejoinTimestampMs = 0L;
        }
    }

    public UniversalRoom.Builder getTargetRoomInfoBuilder(long roomId) {
        if (roomId != getTeamId() && roomId != getRoomId()) {
            LOGGER.error(
                    "player logic get current room info with mismatch roomId, playerUid:{} roomId:{} roomInfo:{} {}",
                    player.getUid(), roomId, player.getUserAttr().getRoomInfo().getCopyCsBuilder(),
                    FunctionUtil.getStackTrace());
            return UniversalRoom.newBuilder();
        }
        if (roomId == getTeamId()) {
            // 使用队伍的数据
            return getCurrentTeamInfoBuidler();
        }
        return getCurrentRoomInfoBuidler();
    }

    public MemberBaseInfo.Builder getRoomLeader(long roomId) {
        UniversalRoom.Builder currentRoomInfoBuilder = getTargetRoomInfoBuilder(roomId);
        // 如果自己是队长 返回自己的基础信息
        if (currentRoomInfoBuilder.getRoomBriefInfoBuilder().getLeaderUid() == player.getUid()) {
            return player.getMemberBaseInfoBuilder();
        }
        // 其他人仅包含uid
        return MemberBaseInfo.newBuilder().setUid(getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder().getLeaderUid());
    }

    public void setRoomLeader(MemberBaseInfo roomLeader) {
        this.roomLeader = roomLeader;
    }

    public int getRoomMemberCount(long roomId) {
        return getTargetRoomInfoBuilder(roomId).getRoomBriefInfo().getCurMemberNumber();
    }

    public int getRoomTypeVal(long roomId) {
        if (roomId <= 0) {
            LOGGER.info(
                    "player logic get current room info with mismatch roomId, playerUid:{} roomId:{} roomInfo:{}",
                    player.getUid(), roomId, player.getUserAttr().getRoomInfo());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.error(
                        "player logic get current room info with mismatch roomId, playerUid:{} roomId:{} roomInfo:{} {}",
                        player.getUid(), roomId, player.getUserAttr().getRoomInfo(), FunctionUtil.getStackTrace());
            }
            return -1;
        }
        return getTargetRoomInfoBuilder(roomId).getRoomBriefInfo().getRoomType().getNumber();
    }

    public boolean isPlayerInTeam() {
        if (getTeamId() > 0 && getRoomMemberCount(getTeamId()) > 1) {
            return true;
        }
        if (getRoomId() > 0) {
            return true;
        }
        return false;
    }

    public boolean isPlayerLeader() {
        // 队伍人数大于一人时 才算队长
        if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
            return getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid() == player.getUid();
        }
        if (getRoomId() > 0) {
            return getCurrentRoomInfoBuidler().getRoomBriefInfo().getLeaderUid() == player.getUid();
        }
        return false;
    }

    /**
     * 获取玩家当前的房间信息
     *
     * @return
     */
    public UniversalRoom getCurrentRoomInfo() {
        return this.currentRoomInfoBuidler.build();
    }

    @Deprecated
    public boolean isInRoom() {
        return false;
    }

    private void registerAllPreparationWidgetUnlockCondition() {
        LOGGER.debug("PreparationWidgetUnlockConditionGroup registerAllPreparationWidgetUnlockCondition, len:{}",WolfKillPreparationsWidgetConf.getInstance().dataArray.size());
        for (ResPreparations.PreparationsWidgetConf preparationsWidgetConf : WolfKillPreparationsWidgetConf.getInstance().dataArray) {
            if (player.getUserAttr().getUnLockPreparationWidgetSet().contains(preparationsWidgetConf.getButtonname().getNumber())) {
                continue;
            }
            // 注意：unlockKey不能重复，要求MatchUnlockType的枚举不能超过100，否则重复引起问题
            int unlockKey = 100000000 + preparationsWidgetConf.getButtonname().getNumber();
            ConditionGroupInfo conditionGroupAttr = preparationWidgetConditionAttr.getAttrConditionGroup(preparationsWidgetConf.getButtonname().getNumber());
            if (conditionGroupAttr == null) {
                conditionGroupAttr = new ConditionGroupInfo().setId(unlockKey);
                preparationWidgetConditionAttr.getAttrConditionGroup().put(preparationsWidgetConf.getButtonname().getNumber(), conditionGroupAttr);
            }
            PreparationWidgetUnlockConditionGroup conditionGroup = new PreparationWidgetUnlockConditionGroup(player,
                    preparationsWidgetConf.getConditionGroup(), preparationsWidgetConf.getButtonname().getNumber(), conditionGroupAttr.getConditionGroup());
        }
    }

    private void checkMatchTypeUnLock() {
        for (ResMatch.MatchType matchType : MatchTypeData.getInstance().dataArray) {
            if (matchType.getIsOpenGrayScale() && !TycConfs.checkPlayerIsOpenTycoon(matchType, player.getOpenId())) {
                if (player.getUserAttr().getUnLockGameModeSet().contains(matchType.getId())) {
                    player.getUserAttr().getUnLockGameModeSet().remove(matchType.getId());
                    LOGGER.error("Success to remove matchType as not open, uid:{}, matchTypes:{}",
                            player.getUid(), matchType.getId());
                }
                continue;
            }

            if (checkMatchBanConfig(matchType)) {
                continue;//不在这里处理 在afterLogin里单独处理
            }

            if (player.getUserAttr().getUnLockGameModeSet().contains(matchType.getId())) {
                continue;
            }
            //如果在历史记录中解锁过了
            if (player.getUserAttr().getUnLockGameModeHistorySet().contains(matchType.getId())) {
                HashSet<Integer> unLockMatchTypeIds = new HashSet<>();
                unLockMatchTypeIds.add(matchType.getId());
                player.getPlayerRoomMgr().unLockMatchType(unLockMatchTypeIds);
                continue;
            }
            conditionRegisterUnlock(matchType);
        }
    }

    /**
     * 对ban的玩法再次检测是否能开启,对顶号流程做处理,loginAfter调用
     */
    public void checkBanMatchTypeUnLock() {
        for (ResMatch.MatchType matchType : MatchTypeData.getInstance().dataArray) {
            if (checkMatchBanConfig(matchType)) {
                if (checkMatchIsBan(player, matchType.getId())) {
                    continue;
                }
                if (player.getUserAttr().getUnLockGameModeSet().contains(matchType.getId())) {
                    continue;
                }
                //如果在历史记录中解锁过了
                if (player.getUserAttr().getUnLockGameModeHistorySet().contains(matchType.getId())) {
                    HashSet<Integer> unLockMatchTypeIds = new HashSet<>();
                    unLockMatchTypeIds.add(matchType.getId());
                    player.getPlayerRoomMgr().unLockMatchType(unLockMatchTypeIds);
                    continue;
                }
                //已经注册过了,不再进行注册
                conditionRegisterUnlock(matchType);
            }
        }
    }

    /**
     * 屏蔽相关的玩法
     */
    public void banMatchType() {
        for (ResMatch.MatchType matchType : MatchTypeData.getInstance().dataArray) {
            if (checkMatchIsBan(player, matchType.getId())) {
                if (player.getUserAttr().getUnLockGameModeSet().contains(matchType.getId())) {
                    if (!player.getUserAttr().getUnLockGameModeHistorySet().contains(matchType.getId())) {
                        player.getUserAttr().getUnLockGameModeHistorySet().add(matchType.getId());
                    }
                    player.getUserAttr().getUnLockGameModeSet().remove(matchType.getId());
                    clearUnLockConditionAttr(matchType.getId());
                    LOGGER.info("matchType login remove, uid:{}, matchTypes:{}",
                            player.getUid(), matchType.getId());

                    TlogFlowMgr.sendMatchTypeLockFlow(player, matchType.getId(), 0, 1);
                }
            }
        }
    }


    private void conditionRegisterUnlock(MatchType matchType) {
        //已经注册过了,不再进行注册
        if (conditionMatchRecord.contains(matchType.getId())) {
            return;
        }

        ConditionGroupInfo conditionGroupAttr = matchTypeConditionAttr.getAttrConditionGroup(matchType.getId());
        if (conditionGroupAttr == null) {
            conditionGroupAttr = new ConditionGroupInfo().setId(matchType.getId());
            matchTypeConditionAttr.getAttrConditionGroup().put(matchType.getId(), conditionGroupAttr);
        }
        MatchTypeUnlockConditionGroup conditionGroup = new MatchTypeUnlockConditionGroup(player,
                matchType.getConditionGroup(), matchType.getId(), conditionGroupAttr.getConditionGroup());
        conditionMatchRecord.add(matchType.getId());
    }

    public void unLockMatchType(HashSet<Integer> unLockMatchTypeIds) {
        player.getUserAttr().getUnLockGameModeSet().addAll(unLockMatchTypeIds);
        player.getUserAttr().getUnLockGameModeHistorySet().addAll(unLockMatchTypeIds);
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.GameModeUnlock);

        CsPlayer.UnLockMatchTypeNtf.Builder msg = CsPlayer.UnLockMatchTypeNtf.newBuilder();
        msg.addAllMatchTypeId(unLockMatchTypeIds);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_UNLOCKMATCHTYPENTF, msg);
        clearUnLockConditionAttr();
        new PlayerUnlockMatchType(player).setMatchTypeId(unLockMatchTypeIds).dispatch();
        // 解锁玩法内的模块
        for (int matchType : unLockMatchTypeIds) {
            registerMatchUnlockTypeCondition(matchType);
        }
        for (int matchType : unLockMatchTypeIds) {
            TlogFlowMgr.sendMatchTypeLockFlow(player, matchType, 1, 0);
        }
    }

    public void unLockPreparationWidget(HashSet<Integer> unLockButtonIds) {
        player.getUserAttr().getUnLockPreparationWidgetSet().addAll(unLockButtonIds);
    }

    private void registerAllMatchUnlockTypeCondition() {
        for (int matchType : player.getUserAttr().getUnLockGameModeSetList()) {
            registerMatchUnlockTypeCondition(matchType);
        }
    }

    private void registerMatchUnlockTypeCondition(int matchType) {
        MatchUnlockConditionConfig matchUnlockCondition = MatchUnlockConditionData.getInstance().get(matchType);
        if (matchUnlockCondition == null) {
            return;
        }
        for (ResMatch.MatchTypeUnlockConditionConfig matchTypeUnlockConditionConfig : matchUnlockCondition.getUnlockConfigList()) {
            if (player.getUserAttr().getMatchUnlock(matchType) != null
                    && player.getUserAttr().getMatchUnlock(matchType).getUnlockType().contains(matchTypeUnlockConditionConfig.getUnlockType().getNumber())) {
                continue;
            }
            int unlockKey = matchTypeUnlockConditionConfig.getUnlockType().getNumber() * 1000000 + matchType;
            ConditionGroupInfo conditionGroupAttr = matchUnlockTypeConditionAttr.getAttrConditionGroup(unlockKey);
            if (conditionGroupAttr == null) {
                conditionGroupAttr = new ConditionGroupInfo().setId(unlockKey);
                matchUnlockTypeConditionAttr.getAttrConditionGroup().put(unlockKey, conditionGroupAttr);
            }
            MatchUnlockConditionGroup conditionGroup = new MatchUnlockConditionGroup(player,
                    matchTypeUnlockConditionConfig.getConditionGroup(), matchType,
                    matchTypeUnlockConditionConfig.getUnlockType().getNumber(), conditionGroupAttr.getConditionGroup());
        }
        LOGGER.debug("registerMatchUnlockTypeCondition debug, uid:{} matchType:{}",player.getUid(),matchType);
    }

    public Collection<Integer> getMatchTypeUnlockType(int matchTypeId) {
        if (player.getUserAttr().getMatchUnlock(matchTypeId) == null) {
            return new ArrayList<>();
        }
        return player.getUserAttr().getMatchUnlock(matchTypeId).getUnlockTypeList();
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {
    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {
        registerAllMatchUnlockTypeCondition();
        checkMatchTypeUnLock();

        // 备战按钮解锁
        registerAllPreparationWidgetUnlockCondition();
    }

    @Override
    public void prepareLogin() throws NKCheckedException {
    }

    @Override
    public void onLogin() throws NKCheckedException {
        TpsSpan lastSpan = TpsTracer.currentSpan();
        CurrentExecutorUtil.runJob(() -> {
            TpsTracer.makeSpanCurrent(lastSpan);
            procLogin();
            return null;
        }, "PlayerRoomMgrOnLogin", true);
        // 设置合家欢每日获取上限
        player.getLimitManager().registerLimitInfo(CommonLimitType.CLT_QRCodeDailyAcquireLimit, player.getUid(),
                getQRCodePointDailyAcquiredLimit());
        player.getLimitManager().checkLimitInfo(CommonLimitType.CLT_QRCodeDailyAcquireLimit);
    }

    public void tickPerSecond() {
        // 10 秒一次的room心跳
        if (Framework.currentTimeMillis() - tenSecondStamptCache
                > PropertyFileReader.getRealTimeLongItem("room_heartbeat_step_sec", 10) * 1000) {
            tenSecondStamptCache = Framework.currentTimeMillis();
            roomHeartBeat(getRoomId());
            roomHeartBeat(getTeamId());
        }
        // 更新玩家信息到room
        roomMemberBaseInfoUpdate();
    }

    public void roomHeartBeat(long roomId) {
        if (0 == roomId) {
            return;
        }
        try {
            CurrentExecutorUtil.runJob(()-> {
                SsRoomsvr.RpcRoomStateCheckReq.Builder reqBuilder = SsRoomsvr.RpcRoomStateCheckReq.newBuilder();
                reqBuilder.setRoomId(roomId);
                reqBuilder.setPlayerUid(player.getUid());
                reqBuilder.setIsSystem(true);
                RpcResult<SsRoomsvr.RpcRoomStateCheckRes.Builder> rpcResult = RoomService.get()
                        .rpcRoomStateCheck(reqBuilder);
                if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                    LOGGER.error("player-{} room state check fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                            roomId);
                }
                return null;
            }, "roomHeartBeat", true);

        } catch (Exception e) {
            LOGGER.error(e);
        }
    }

    private void roomMemberBaseInfoUpdate() {
        if (this.memberBaseInfoUpdateHistory.startUpdate(player.getUid())) {
            // 执行更新
            updateMemberBaseInfoDirectly2Room(this.memberBaseInfoUpdateHistory);
            this.memberBaseInfoUpdateHistory.markUpdated();
        }
    }

    @Override
    public void onLeave() {
        leaveGame(LeaveType.LEAVE);
    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        //兼容老玩家数据
        player.getUserAttr().getUnLockGameModeHistorySet().addAll(player.getUserAttr().getUnLockGameModeSetList());
        banMatchType();
        checkBanMatchTypeUnLock();
        checkResetHokRankUnLock();
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                // 需要http 在job中查询
                //updatePlayerRoomAbTestGroupId();
                clearUnLockConditionAttr();
                return null;
            }, "PlayerRoomMgrAfterLogin", true);
        } catch (NKCheckedException e) {
            LOGGER.error("player {} runJob catch {}", player.getUid(), e);
        }
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {
        boolean playerResetAutoStartSwitchOnLogin = PropertyFileReader.getRealTimeBooleanItem(
                "player_reset_auto_start_switch_on_login", true);
        if (playerResetAutoStartSwitchOnLogin) {
            player.getUserAttr().getPlayerPublicGameSettings().setCustomRoomAutoStartWhenFull(true);
        }
    }

    public void sendRpcRoomPlayerOnline() {
        asyncOnlineRoom(getTeamId());
        asyncOnlineRoom(getRoomId());
    }

    public void checkResetHokRankUnLock() {
        if (!isHOKResetRankUnlockOpen()){
            return;
        }
        if (!player.getHokMgr().hokPlayerCountIsZero()){
            return;
        }
        int rankModeMatchType = HokConfs.getRankModeMatchType();
        if (player.getUserAttr().getUnLockGameModeSet().contains(rankModeMatchType)) {
            ResMatch.MatchType matchTypeConf = MatchTypeData.getInstance().get(rankModeMatchType);
            if (matchTypeConf == null){
                LOGGER.error("can not find match type conf {}", rankModeMatchType);
                return;
            }

            player.getUserAttr().getUnLockGameModeSet().remove(rankModeMatchType);
            clearUnLockConditionAttr(rankModeMatchType);

            LOGGER.info("checkResetHokRankUnLock matchType login reset, uid:{}, matchType:{}",
                    player.getUid(), rankModeMatchType);

            conditionMatchRecord.removeIf(element -> element != null && element == matchTypeConf.getId());
            conditionRegisterUnlock(matchTypeConf);
            Monitor.getInstance().add.succ(MonitorId.attr_hok_reset_unlock_condition, 1);
        }else {
            if (LOGGER.isDebugEnabled()){
                LOGGER.debug("checkResetHokRankUnLock user {} has not unlock moba 5v5 now", player.getUid());
            }
        }
    }

    private boolean isHOKResetRankUnlockOpen(){
        int selectPct = PropertyFileReader.getRealTimeIntItem("hok_reset_rankMode_unlock_pct", 0);
        if (player.getUid() % 100 < selectPct) {
            return true;
        }
        return false;
    }

    /**
     * 如果需要的话进行一次room online
     */
    public void sendRpcRoomPlayerOnlineIfNeed() {
        if (this.needTeamOnline.get()) {
            LOGGER.info("player need to online team by get on going request, player:{} teamId:{}", player.getUid(),
                    getTeamId());
            asyncOnlineRoom(getTeamId());
        }
        if (this.needRoomOnline.get()) {
            LOGGER.info("player need to online room by get on going request, player:{} teamId:{}", player.getUid(),
                    getRoomId());
            asyncOnlineRoom(getRoomId());
        }
    }

    /**
     * 同步上线逻辑
     */
    public void syncRoomOnline() {
        onlineRoom(getTeamId());
        onlineRoom(getRoomId());
    }

    private void asyncOnlineRoom(long roomId) {
        if (roomId == 0) {
            return;
        }
        LOGGER.error("player online room, playerUid:{} roomId:{}", player.getUid(), roomId);
        try {
            CurrentExecutorUtil.runJob(() -> {
                onlineRoom(roomId);
                return null;
            }, "asyncOnlineRoom", true);
        } catch (NKCheckedException e) {
            LOGGER.error("async online room fail, err:{}", e.getEnumErrCode());
        }
    }

    /**
     * 玩家同步上线状态到指定房间
     *
     * @param existRoomId
     */
    private void onlineRoom(long existRoomId) {
        if (existRoomId <= 0) {
            if (getTeamId() == existRoomId) {
                this.needTeamOnline.compareAndSet(true, false);
            } else if (getRoomId() == existRoomId) {
                this.needTeamOnline.compareAndSet(true, false);
            }
            return;
        }
        try {
            SsRoomsvr.RpcRoomPlayerOnlineReq.Builder ssReqMsg = SsRoomsvr.RpcRoomPlayerOnlineReq.newBuilder();
            ssReqMsg.setRoomId(existRoomId);
            ssReqMsg.setUid(player.getUid());
            ssReqMsg.setClientVersion(player.getCompVersion());
            ssReqMsg.addAllPlayBrief(player.dumpPlayInfos());
            RpcResult<RpcRoomPlayerOnlineRes.Builder> rpcResult = RoomService.get().rpcRoomPlayerOnline(ssReqMsg);
            if (rpcResult.getRet() != 0 || rpcResult.getData().getResult() != 0) {
                LOGGER.error("player room online fail, player:{} room:{} res:{}", player.getUid(),
                        existRoomId, rpcResult.getData().getResult());
                if (player.getPlayerBattleMgr().isInBattle() && (null != rpcResult.getData() &&
                        (rpcResult.getData().getResult() == NKErrorCode.RoomMemberClientVersionDiffersCantJoin.getValue() ||
                        rpcResult.getData().getResult() == NKErrorCode.BattlePlayerOnlineInInvalidVersion.getValue()))) {
                    ExitBattleNtf.Builder ntfBuilder = ExitBattleNtf.newBuilder();
                    ntfBuilder.setErrorCode(NKErrorCode.BattlePlayerOnlineInInvalidVersion.getValue());
                    player.sendNtfMsg(MsgTypes.MSG_TYPE_EXITBATTLENTF, ntfBuilder);
                }
                ntfClientClearRoomInfo(existRoomId, "room online fail");
                if (rpcResult.getData().getResult() != NKErrorCode.RoomNotExist.getValue() &&
                        rpcResult.getData().getResult() != NKErrorCode.RoomMemberNotExist.getValue()) {
                    exitRoomQuietly(existRoomId);
                }
            } else {
                LOGGER.info("player room online succ, uid:{} roomId:{} battleId:{}",
                        player.getUid(), existRoomId, rpcResult.getData().getBattleInfo().getBattleid());
                updateBattleData(rpcResult.getData().getBattleInfo(), rpcResult.getData().getMatchDynamicConfigData());
            }
            if (getTeamId() == existRoomId) {
                this.needTeamOnline.compareAndSet(true, false);
            } else if (getRoomId() == existRoomId) {
                this.needTeamOnline.compareAndSet(true, false);
            }
            return;
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("player room online fail uid:{} room:{} e:{}", player.getUid(), existRoomId, e);
            exitRoomQuietly(existRoomId);
        }
    }

    protected void procLogin() {
        TpsSpan curSpan = player.startPlayerSpan("playerRoomMgr.procLogin");

        //清除ds私服id
        if ((!ServerEngine.getInstance().isDev() || TycConfs.checkMatchUseLocalDS()) && !ServerEngine.getInstance().isDsStartToken()) {
            player.getPlayerRoomMgr().setDsDevEnvId(0, false, "procLogin");
        }
        // 正常重登退出个别啾灵模式队伍
        if (!player.isReLogin() && needLeaveStarPTeam()) {
            exitStarPTeam(getTeamId(), RoomExitType.RET_SP_LoginExit);
            fixCurrMatchType();
        } else {
            sendRpcRoomPlayerOnline();
        }
        player.sendPlayerAfterLoginNtf(Player.ROOM_MGR_PROCESS_MASK, "PlayerRoomMgr", curSpan);
    }

    public void playerOfflineFromBattle() {
        player.offlineBattle();
    }

    public void updateBattleData(proto_BattleInfo battleInfo,
            MatchDynamicConfigData matchDynamicConfigData) {
        if (battleInfo != null && battleInfo.getBattleid() > 0L) {
            player.getPlayerMatchMgr().updateMatchData(player.getUserAttr().getRoomInfo().getStatus(), 
                    battleInfo, matchDynamicConfigData);
        }
    }

    enum LeaveType {
        LOGOUT,
        LEAVE;
    }

    @Override
    public void onLogout() {
        leaveGame(LeaveType.LOGOUT);
    }

    public void offline(long roomID) {
        RoomService roomser = RoomService.get();
        if (null != roomser) {
            try {
                SsRoomsvr.RpcRoomPlayerOfflineReq.Builder ssReqMsg = SsRoomsvr.RpcRoomPlayerOfflineReq.newBuilder();
                ssReqMsg.setRoomId(roomID);
                ssReqMsg.setUid(player.getUid());
                RpcResult<Builder> rpcResult = roomser.rpcRoomPlayerOffline(ssReqMsg);
                if (rpcResult.getData().getResult() != 0) {
                    LOGGER.error("Room offline failed is player:{} room:{} res:{}", player.getUid(),
                            roomID, rpcResult.getData().getResult());
                }
            } catch (NKTimeoutException | RpcException e) {
                LOGGER.error(e);
            }

        } else {
            LOGGER.info("RoomService offline roomservice is null");
            return;
        }
    }

    /**
     * 从游戏中暂离，需要从队伍和房间中更新为暂离状态
     */
    public void leaveGame(LeaveType leaveType) {
        if(GSConfig.resetRoomMemberClientInfoOnOffline()) {
            player.getUserAttr().getRoomInfo().getRoomMemberClientInfo().setVoiceState(0);
            player.getUserAttr().getRoomInfo().getRoomMemberClientInfo().setVoiceRoomTag(0);
            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.VoiceStatusChange);
        }

        long currentTeamId = getTeamId();
        if (currentTeamId > 0) {
            //if (leaveType == LeaveType.LOGOUT && needLeaveStarPTeam()) {
            //    exitStarPTeam(currentTeamId);
            //}
            offline(currentTeamId);
            this.needTeamOnline.compareAndSet(false, true);
        }
        long currentRoomId = getRoomId();
        if (currentRoomId > 0) {
            offline(currentRoomId);
            this.needRoomOnline.compareAndSet(false, true);
        }
    }

    @Override
    public void onMidNight() {

    }

    @Override
    public void onWeekStart() {
        player.getLimitManager().checkLimitInfo(CommonLimitType.CLT_QRCodeDailyAcquireLimit);
    }

    /**
     * 检查房间状态--目的是修复客户端的数据
     * 如果属性系统中房间信息为空，则通知客户端房间退出
     * 如果客户端房间id与属性系统不匹配，则强制刷新一次 -- 一般是退出房间执行新房间创建/加入的操作的ntf都丢失的情况
     * 如果是状态不匹配，可能是切后台或者弱网丢失了状态切换的ntf，补发一次即可
     *
     * @param roomId         客户端持有的房间id
     * @param curRoomState   客户端持有的房间状态
     * @param curPlayerState 客户端持有的玩家状态
     */
    public CsRoom.RoomStateCheck_S2C_Msg.Builder checkRoomState(long roomId, RoomStateType curRoomState,
                                                                PlayerStateType curPlayerState) {
        CsRoom.RoomStateCheck_S2C_Msg.Builder ret = CsRoom.RoomStateCheck_S2C_Msg.newBuilder();
        long roomIdAtAttr = getCurrentRoomId(roomId);
        if (roomIdAtAttr == 0L) {
            LOGGER.info("player-{} room if from attr is empty, ntf client to delete from room", player.getUid());
            //clearRoomInfo("room state check has no room id", true);
            ntfClientClearRoomInfo(roomId, "room state check has no room id");
            return ret;
        }
        // 默认按照客户端持有的数据返回，此处先按照传入值赋值
        ret.setRoomId(roomId);
        ret.setState(curRoomState);
        ret.setCurPlayerState(curPlayerState);
        // 后两种均需要请求roomsvr触发当前房间的ntf补发，只是id不同时跳过判断直接补发
        SsRoomsvr.RpcRoomStateCheckReq.Builder reqBuilder = SsRoomsvr.RpcRoomStateCheckReq.newBuilder()
                .setRoomId(roomIdAtAttr);
        reqBuilder.setPlayerUid(player.getUid());
        reqBuilder.setState(curRoomState);
        reqBuilder.setCurPlayerState(curPlayerState);
        if (roomIdAtAttr != roomId) {
            reqBuilder.setForceRefresh(true);
        }
        try {
            RpcResult<SsRoomsvr.RpcRoomStateCheckRes.Builder> rpcResult = RoomService.get()
                    .rpcRoomStateCheck(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room state check fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        roomIdAtAttr);
                return ret;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(roomIdAtAttr, "room member not exist");
                    return ret;
                }
                LOGGER.error("player-{} room state check fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), roomIdAtAttr);
                return ret;
            }
            // 最终按照ss协议回包覆盖数据
            ret.setRoomId(rpcResult.getData().getRoomId());
            ret.setState(rpcResult.getData().getState());
            ret.setCurPlayerState(rpcResult.getData().getCurPlayerState());
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
        }
        return ret;
    }


    public NKErrorCode checkRoomConfig(MatchRuleInfo.Builder matchRuleInfo) {
        ResMatch.MatchType matchGameCfg = MatchTypeData.getInstance().get(matchRuleInfo.getMatchTypeId());
        if (null == matchGameCfg) {
            LOGGER.error(" config error MatchGameCfg is not valid {}", matchRuleInfo.getMatchTypeId());
            return NKErrorCode.RoomConfigurationNotExist;
        }

        boolean isRunningGameCfg = MatchTypeData.getInstance().isRunningGameCfg(matchRuleInfo.getMatchTypeId());
        if (!isRunningGameCfg) {
            LOGGER.error("gamecfg not running,{}", matchRuleInfo.getMatchTypeId());
            return NKErrorCode.RoomMatchNotOpen;
        } else {
            LOGGER.debug("player {}-{} check config running, mode id {}", player.getOpenId(),
                    player.getUid(), matchRuleInfo.getMatchTypeId());
        }

        // 排位赛检查玩家是否被封禁
        if (matchGameCfg.getSettleProc() == MTSC_Rank) {
            player.checkIsBanLadderAndNotify();
        }
        // 检查玩法是否被封禁
        player.checkIsBanGameModeAndNotify(matchRuleInfo.getMatchTypeId());
        if (matchGameCfg.getGameModeType() == GameModeType.GMT_UgcMatch_VALUE) {
            if (matchRuleInfo.getUgcId() <= 0) {
                NKErrorCode.UgcMatchInValid.throwError("ugcId:0 matchTypeId:{} invalid", matchRuleInfo.getMatchTypeId());
            }
        } else if(matchGameCfg.getGameModeType() == GameModeType.GMT_UgcCoPlay_VALUE) {
            if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
                NKErrorCode.RoomUgcCoPlayerNotSupportMultiPlayer.throwError("ugc co play match with team partner");
            }
        } else if(matchGameCfg.getGameModeType() == GameModeType.GMT_CompetitionMatch_VALUE) {
            if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
                NKErrorCode.CompetitionPointsRoomNotSupportMultiPlayer.throwError("points competition match with team partner");
            }
        } else if (matchGameCfg.getGameModeType() == 0) {
            LOGGER.debug("match type has no game mode type config, set to GMT_Normal, matchTypeId:{}", matchRuleInfo.getMatchTypeId());
            matchRuleInfo.setGameModeType(GameModeType.GMT_Normal);
        } else {
            matchRuleInfo.setGameModeType(GameModeType.forNumber(matchGameCfg.getGameModeType()));
        }

        /*排查客户端代码除肉鸽玩法均不使用此字段，肉鸽玩法也使用额外自定义字段处理此逻辑，故注释 --20240207
        for (RuleDimInfo dimInfo : matchRuleInfo.getDimensionListList()) {
            ResMatch.MatchDimension dimcfg = MatchDimensionData.getInstance().get(dimInfo.getId());
            if (null == dimcfg) {
                LOGGER.error(" get match req dimension isn‘t valid id:{} value:{} detail:{}", dimInfo.getId(),
                        dimInfo.getValue(),
                        matchRuleInfo);
                return NKErrorCode.RoomConfigurationNotExist;
            }
        }*/

        return NKErrorCode.OK;
    }

    public void handleCancelMatch(long roomID) {
        SsRoomsvr.RpcMatchCancelReq.Builder req = SsRoomsvr.RpcMatchCancelReq.newBuilder();
        req.setUid(player.getUid());
        req.setRoomId(roomID);
        LOGGER.debug("handleCancelMatch, roomId:{}, uid:{}", roomID, player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcMatchCancelRes.Builder> res = RoomService.get().rpcMatchCancel(req);
            LOGGER.debug("handleCancelMatch result, ResCode:{}", roomID, res.getData().getResCode());
            if (NKErrorCode.OK.getValue() != res.getData().getResCode()) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, roomID, StarPTeamOpType.SPIOpType_CANCEL_MATCH,
                        res.getData().getResCode());
                if (res.getData().getResCode() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(getTeamId(), "change mode but room not exist");
                    return ;
                }
                LOGGER.error("handleCancelMatch rpc ret err, roomId:{}, e:{}", roomID, res.getData().getResCode());
                NKErrorCode.throwErrorOf(NKErrorCode.forNumber(res.getData().getResCode()));
            }
        } catch (RpcException e) {
            LOGGER.error("handleCancelMatch rpc err, roomId:{}, e:", roomID, e);
            NKErrorCode.throwErrorOf(NKErrorCode.RoomRpcError);
        } catch (NKTimeoutException e) {
            LOGGER.error("handleCancelMatch rpc timeout, roomId:{}, e:", roomID, e);
            NKErrorCode.throwErrorOf(NKErrorCode.Timeout);
        }
    }

    /**
     * cs请求 在匹配中修改队伍的信息(阵营)
     * @param currRoomId 队伍id
     * @param reqMsg 请求体
     * @return 错误码 0 表示成功
     */
    public int handleModifyMatchingTeamInfo(long currRoomId, CsRoom.RoomModifyMatchingTeamInfo_C2S_Msg reqMsg) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} room {} fields {}",
                    player.getUid(), currRoomId, NKStringFormater.formatShortPbCollections(reqMsg.getFieldsList()));
        }

        if (reqMsg.getFieldsCount() == 0) {
            LOGGER.error("player {} room {} fields is empty",
                    player.getUid(), currRoomId);
            return NKErrorCode.RoomModifyMatchingTeamInfoReqInvalid.getValue();
        }

        SsRoomsvr.RpcRoomModifyMatchingTeamInfoReq.Builder reqBuilder = SsRoomsvr.RpcRoomModifyMatchingTeamInfoReq.newBuilder()
                .setRoomId(currRoomId)
                .setRequesterId(player.getUid())
                .addAllFields(reqMsg.getFieldsList());

        int ret = NKErrorCode.UnknownError.getValue();
        try {
            RpcResult<SsRoomsvr.RpcRoomModifyMatchingTeamInfoRes.Builder> rpcResult = RoomService.get()
                    .rpcRoomModifyMatchingTeamInfo(reqBuilder);
            if (!rpcResult.isOK()) {
                ret = rpcResult.getRet();
            } else {
                ret = NKErrorCode.OK.getValue();
            }
            LOGGER.info("player {} room {} ret:{}",
                    player.getUid(), currRoomId, ret);
        } catch (NKCheckedException | NKRuntimeException e) {
            ret = e.getEnumErrCode().getValue();
            LOGGER.error("player {} room {} rpcRoomModifyMatchingTeamInfo catch",
                    player.getUid(), currRoomId, e);
        } catch (Exception e) {
            ret = NKErrorCode.UnknownError.getValue();
            LOGGER.error("player {} room {} rpcRoomModifyMatchingTeamInfo catch unknown ",
                    player.getUid(), currRoomId, e);
        }

        return ret;
    }

    public NKErrorCode handleCancelPreStart(long roomId) {
        SsRoomsvr.RpcRoomCancelPreStartReq.Builder req = SsRoomsvr.RpcRoomCancelPreStartReq.newBuilder();
        req.setUid(player.getUid());
        req.setRoomId(roomId);
        LOGGER.debug("handleCancelPreStart, roomId:{}, uid:{}", roomId, player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcRoomCancelPreStartRes.Builder> res = RoomService.get().rpcRoomCancelPreStart(req);
            LOGGER.debug("handleCancelPreStart result, ret:{}", roomId, res.getRet());
            if (NKErrorCode.OK.getValue() != res.getRet()) {
                LOGGER.error("handleCancelPreStart rpc ret err, roomId:{}, e:{}", roomId, res.getRet());
            }
            return NKErrorCode.forNumber(res.getRet());
        } catch (RpcException e) {
            LOGGER.error("handleCancelPreStart rpc err, roomId:{}, e:", roomId, e);
            return NKErrorCode.RoomRpcError;
        } catch (NKTimeoutException e) {
            LOGGER.error("handleCancelPreStart rpc timeout, roomId:{}, e:", roomId, e);
            return NKErrorCode.Timeout;
        }
    }
    //检查狼人杀道具是否满足
    private boolean checkWereWolfConsumeItemEnough(MatchConstEnum matchConstEnum, int identityID) {
        ResMatch.MatchConstsInfo matchConstsInfo = MatchConstsData.getInstance().get(matchConstEnum);
        if (null != matchConstsInfo) {
            //检查该身份是否解锁，就是检查一个对应物品
            if (identityID>0){
                ResNR3E3Vocation.NR3E3VocationData vocationData = WolfKillVocation.get(identityID);
                if (vocationData.getPreparationsDefaultUnLock()==0) {
                    // 判断物品
                    if (!player.getBagManager().isItemsEnough(
                            vocationData.getIdentityItemUnlock(), 1, ItemChangeReason.ICR_WereWolfSetSideIdentity)) {
                        LOGGER.error("checkWereWolfConsumeItemEnough vocation unlock false {} {} {}",
                                player.getUid(), matchConstEnum, matchConstsInfo.getValue());
                        return false;
                    } else {
                        LOGGER.info("checkWereWolfConsumeItemEnough vocation unlock true {} {} {}",
                                player.getUid(), matchConstEnum, matchConstsInfo.getValue());
                    }
                }
            }

            if (!player.getBagManager().isItemsEnough(
                    matchConstsInfo.getValue(), 1, ItemChangeReason.ICR_WereWolfSetSideIdentity)) {
                LOGGER.error("checkWereWolfConsumeItemEnough isItemsEnough false {} {} {}",
                        player.getUid(), matchConstEnum, matchConstsInfo.getValue());
                return false;
            } else {
                LOGGER.info("checkWereWolfConsumeItemEnough isItemsEnough true {} {} {}",
                        player.getUid(), matchConstEnum, matchConstsInfo.getValue());
            }
        } else {
            LOGGER.error("checkWereWolfConsumeItemEnough matchConstsInfo == null {} {}",
                    player.getUid(), matchConstEnum);
        }

        return true;
    }

    //检查狼人杀道具是否满足
    private boolean checkWereWolfConsumeItem(int matchTypeID, WereWolfSideIdentityInfo wereWolfSideIdentityInfo) {
        if (!WereWolfSpecialUtils.isSpecialRuleRoom(matchTypeID)) {
            LOGGER.debug("checkWereWolfConsumeItem isSpecialRuleRoom false {} {}",
                    player.getUid(), matchTypeID);
            return true;
        }
        // 多人模式不允许，109玩法也不允许
        int curRoomCnt = getRoomMemberCount(getTeamId());
        LOGGER.debug("checkWereWolfConsumeItem isSpecialRuleRoom false {}, room size:{}",
                player.getUid(), curRoomCnt );
        if (curRoomCnt>1 || matchTypeID==109){
            if (wereWolfSideIdentityInfo.getIdentityID()>0 || wereWolfSideIdentityInfo.getSideID()>0){
                LOGGER.error("checkWereWolfConsumeItem checkSetIdentity param {} {}",
                        player.getUid(), wereWolfSideIdentityInfo);
                return false;
            }
        }
        boolean consumeIdentity = false;    //检测了身份就不用检测阵营
        if (WereWolfSpecialUtils.checkSetIdentity(wereWolfSideIdentityInfo)) {
            if (checkWereWolfConsumeItemEnough(MatchConstEnum.MCE_WerewolfRandIdentityItemID, wereWolfSideIdentityInfo.getIdentityID())) {
                consumeIdentity = true;
                LOGGER.info("checkWereWolfConsumeItem checkSetIdentity ok {} {}",
                        player.getUid(), wereWolfSideIdentityInfo);
            } else {
                LOGGER.error("checkWereWolfConsumeItem checkSetIdentity fail {} {}",
                        player.getUid(), wereWolfSideIdentityInfo);
                return false;
            }
        } else {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("checkWereWolfConsumeItem not set identity {} {} {}", player.getUid(),
                        matchTypeID, wereWolfSideIdentityInfo);
            }
        }

        if (!consumeIdentity) {
            if (WereWolfSpecialUtils.checkSetSide(wereWolfSideIdentityInfo)) {
                if (checkWereWolfConsumeItemEnough(MatchConstEnum.MCE_WerewolfRandSideItemID, 0 )) {
                    LOGGER.info("checkWereWolfConsumeItem checkSetSide succ {} {}",
                            player.getUid(), wereWolfSideIdentityInfo);
                } else {
                    LOGGER.error("checkWereWolfConsumeItem checkSetSide fail {} {}",
                            player.getUid(), wereWolfSideIdentityInfo);
                    return false;
                }
            } else {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("checkWereWolfConsumeItem not set side {} {} {}", player.getUid(),
                            matchTypeID, wereWolfSideIdentityInfo);
                }
            }
        }
        LOGGER.info("checkWereWolfConsumeItem succ {} {} {}",
                player.getUid(), consumeIdentity, wereWolfSideIdentityInfo);
        return true;
    }

    private BaseGenerator getRoomIdGenerator(int matchTypeId) {
        if (RpcServiceMgr.isStarPServiceClose())
            return RoomIdGenerator.getInstance();

        if (StarPConfs.isStarPGame(matchTypeId)) {
            return StarPTeamIdGenerator.getInstance();
        }
        return RoomIdGenerator.getInstance();
    }

    /**
     * 根据模式玩法创建房间
     * 但会根据解锁情况及玩法开放状态进行切换
     * **不是一定** 按照传入的玩法创建房间
     *
     * @param ruleInfo
     * @return
     */
    public UniversalRoom createRoomWithMatchRuleInfo(MatchRuleInfo ruleInfo) {
        TxStopWatch stopWatch = NKStopWatch.SW_CreateRoom.getStopWatch();
        if (Framework.currentTimeMillis() < player.getRoomModifyTime() + PropertyFileReader.getRealTimeIntItem("roomModifyInterval", 1000)) {
            NKErrorCode.RoomModifyIsBusy.throwError("room modify is busy");
        }
        // 首先判断目标模式玩法是否ok
        MatchRuleInfo.Builder targetRuleInfo = MatchRuleInfo.newBuilder();
        if (ruleInfo == null || ruleInfo.getMatchTypeId() == 0) {
            // 使用玩家已解锁列表中的默认玩法
            MatchType defaultMatchTypeConfigPersonal = MatchTypeData.getInstance().getDefaultMatchTypeConfigPersonal(
                    player.getUserAttr().getUnLockGameModeSet().getValues());
            if (defaultMatchTypeConfigPersonal == null) {
                LOGGER.error("cannot find default match config for player, player:{}", player.getUid());
                NKErrorCode.ResNotFound.throwError("cannot find default match");
            }
            targetRuleInfo.setMatchTypeId(defaultMatchTypeConfigPersonal.getId());
            targetRuleInfo.setGameModeType(GameModeType.forNumber(defaultMatchTypeConfigPersonal.getGameModeType()));
        } else {
            // 判断传入的模式玩法是否有效
            checkRoomConfig(ruleInfo.toBuilder()).throwErrorIfNotOk("checkRoomConfig error");
            targetRuleInfo = ruleInfo.toBuilder();
        }
        if (ruleInfo != null) {
            targetRuleInfo.setRecid(ruleInfo.getRecid());
            targetRuleInfo.setExpTag(ruleInfo.getExpTag());
        }

        if (!checkWereWolfConsumeItem(targetRuleInfo.getMatchTypeId(), targetRuleInfo.getWereWolfSideIdentityInfo())) {
            LOGGER.error("createRoomWithMatchRuleInfo checkWereWolfConsumeItem fail player:{} {} {}",
                    player.getUid(), targetRuleInfo.getMatchTypeId(), targetRuleInfo.getWereWolfSideIdentityInfo());
            NKErrorCode.RoomWereWolfConsumeItemNotEnough.throwError("createRoomWithMatchRuleInfo checkWereWolfConsumeItem fail");
        }

        try {
            // 这里判断房间是否已经存在 存在退出之前房间，
            // 测试阶段房间删除未完善 暂时不开启 RoomCreate_S2C_Msg 加个返回值
            stopWatch.mark("createRoom.PlayerHandleCreateRoom.start_" + player.getUid());
            // 退出当前所有形式的组队
            exitAllRoom("create team");
            stopWatch.mark("createRoom.PlayerHandleCreateRoom.allocRoomID_" + player.getUid());
            // RoomBaseInfo retRoom = new RoomBaseInfo(roomId);
            MemberBaseInfo.Builder memberBaseInfo;
            if (StarPConfs.isStarPGame(targetRuleInfo.getMatchTypeId())) {
                memberBaseInfo = getSPCreatorMemberBaseInfoBuilder();
            } else {
                memberBaseInfo = getCreatorMemberBaseInfoBuilder();
            }

            long roomId = getRoomIdGenerator(targetRuleInfo.getMatchTypeId()).allocGuid();
            this.fillAndCheckStarP(memberBaseInfo,targetRuleInfo);
            memberBaseInfo.setRoomID(roomId);
            memberBaseInfo.setRuleInfo(targetRuleInfo);
            SsRoomsvr.RpcCreateRoomReq.Builder rpcRoomCreate = SsRoomsvr.RpcCreateRoomReq.newBuilder();
            rpcRoomCreate.setRoomid(roomId).setUid(player.getUid());

            rpcRoomCreate.setRuleInfo(targetRuleInfo);
            rpcRoomCreate.setMemberBaseInfo(memberBaseInfo);
            RpcResult<SsRoomsvr.RpcCreateRoomRes.Builder> rpcCreateRoomResult = RoomService.get()
                    .rpcCreateRoom(rpcRoomCreate);
            if (NKErrorCode.OK.getValue() != rpcCreateRoomResult.getData().getResult()) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, roomId, StarPTeamOpType.SPTOpType_CREATE,
                        rpcCreateRoomResult.getData().getResult());
                LOGGER.error("RoomService rpcCreateRoom ret err uid:{}, Error:{}",
                        player.getUid(), rpcCreateRoomResult.getData().getResult());
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
                NKErrorCode.forNumber(rpcCreateRoomResult.getData().getResult()).throwError("create room fail");
            }
            //player.setCurrentRoomId(roomId);
            player.setCurrentMatchType(targetRuleInfo.getMatchTypeId());
            player.setRoomModifyTime(Framework.currentTimeMillis());
            this.roomLeader = player.getMemberBaseInfo();
            // 发送创建房间安全流水
            // 此处还没有MT_init数据来初始化mgr维护的队伍信息，直接用当前玩家的数据上报
            TlogFlowMgr.sendSecRoomFlow(player, 1, roomId, 1, RoomType.DefaultRoom_VALUE,
                    "", "", player.getOpenId(), player.getUid());

            // 发送房间创建运营流水
            TlogFlowMgr.sendTeamFlow(player, roomId, TEAM_OP_TYPE.TEAM_CREATE_REQ, 0,
                    String.format("%s,%d;", player.getUid(),
                            player.getFriendManager().getRelationTypeVal(player.getUid())),
                    rpcCreateRoomResult.getData().getRoomInfoBuilder(), 0);
            LOGGER.info("rpcCreateRoom uid:{},roomId:{}", player.getUid(), roomId);
            stopWatch.mark("createRoom.PlayerHandleCreateRoom.end_" + player.getUid());
            stopWatch.dump(0);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_create_room, 1);
            return rpcCreateRoomResult.getData().getRoomInfo();
        } catch (NKTimeoutException | RpcException e) {
            stopWatch.dump(0);
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
            LOGGER.error("rpcCreateRoom uid:{}, err:", player.getUid(), e);
            NKErrorCode.RoomServiceIsBusy.throwError("create room fail");
        }
        return null;
    }

    public UniversalRoom createTeamWithAi(MemberBaseInfo ai, MatchRuleInfo ruleInfo) {
        try {
            // 目标teamId
            long roomId = RoomIdGenerator.getInstance().allocGuid();
            // 当前玩家数据
            MemberBaseInfo.Builder memberBaseInfo = getJoinerMemberBaseInfoBuilder();
            memberBaseInfo.setRoomID(roomId);
            memberBaseInfo.setRuleInfo(ruleInfo);
            // 填充邀请相关的剩余次数信息 TODO 按照玩法通用化
            if (HokConfs.isHokRankGame(ruleInfo.getMatchTypeId())) {
                AIInvitationData.Builder builder = AIInvitationData.newBuilder();
                builder.setUpdateTimestampMs(DateUtils.currentTimeMillis());
                builder.setAvailableMatchCnt(player.getHokMgr().getAiInviteLeftTimes());
                memberBaseInfo.setAiInvitationData(builder);
            }
            // 请求体
            SsRoomsvr.RpcCreateRoomReq.Builder rpcRoomCreate = SsRoomsvr.RpcCreateRoomReq.newBuilder();
            rpcRoomCreate.setRoomid(roomId);
            rpcRoomCreate.setUid(ai.getUid());
            rpcRoomCreate.setRuleInfo(ruleInfo);
            rpcRoomCreate.setMemberBaseInfo(ai); // ai作为创建者
            // 创建参数
            RoomCreateOptions.Builder createOpts = RoomCreateOptions.newBuilder();
            createOpts.setCreateWithMultiMember(true);
            rpcRoomCreate.setCreateOpts(createOpts);
            // 多玩家，直接加自己
            rpcRoomCreate.getMultiPlayerBuilder().addMemberInfo(memberBaseInfo);

            RpcResult<RpcCreateRoomRes.Builder> rpcCreateRoomResult = RoomService.get().rpcCreateRoom(rpcRoomCreate);
            if (NKErrorCode.OK.getValue() != rpcCreateRoomResult.getRet()) {
                // TODO 日志
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
                return null;
            }
            if (NKErrorCode.OK.getValue() != rpcCreateRoomResult.getData().getResult()) {
                // TODO 日志
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
                return null;
            }
            // TODO 流水 监控 区分一下来源
            LOGGER.info("player create team with ai, uid:{} roomId:{} playId:{}", player.getUid(), roomId,
                    ruleInfo.getMatchTypeId());
            TlogFlowMgr.sendTeamFlow(player, roomId, TEAM_OP_TYPE.TEAM_CREATE_REQ,
                    RoomJoinType.RJT_AcceptAIInvitation_VALUE, String.format("%s,%d;", player.getUid(),
                            player.getFriendManager().getRelationTypeVal(player.getUid())),
                    rpcCreateRoomResult.getData().getRoomInfoBuilder(), 0);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_create_room, 1);
            return rpcCreateRoomResult.getData().getRoomInfo();
        } catch (NKTimeoutException | RpcException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
            LOGGER.error("rpcCreateRoom uid:{}, err:{}", player.getUid(), e.getEnumErrCode());
        }
        return null;
    }

    /**
     * @param ruleInfo
     * @return
     */
    public UniversalRoom createCustomRoomWithMatchRuleInfo(MatchRuleInfo ruleInfo, String title, String passStr,
            int maxMemberLimit, RoomSetting.Builder settingBuilder) {
        if (!player.getPlayerCreditScoreMgr().checkCreateRoomScenePlayerCreditScoreIsOk()) {
            NKErrorCode.RoomCannotCreateCauseCreditScoreLow.throwError("credit score check fail");
        }
        try {
            // 这里判断房间是否已经存在
            if (getRoomId() > 0) {
                NKErrorCode.RoomCreateFailWhenInRoom.throwError("already in room");
            }
            // 检查参数
            MatchType matchType = MatchTypeData.getInstance().get(ruleInfo.getMatchTypeId());
            if (matchType == null) {
                NKErrorCode.InvalidParams.throwError("play not exist");
            }
            // mark-glueli 这里是自定义房间调用的 可以忽略掉 只处理到队伍的 2024-11-27
            maxMemberLimit = Math.min(maxMemberLimit, matchType.getBattlePlayerNum());
            settingBuilder.getRobotSettingBuilder().setRobotLevel(getValidRobotLevel(ruleInfo.getMatchTypeId(),
                    settingBuilder.getRobotSetting().getRobotLevel()));
            List<RoomLevelSetting> levelSettings = checkRoomLevelSettings(matchType.getModeID(), settingBuilder.getMapSetting()
                    .getLevelSettingsList());
            settingBuilder.getMapSettingBuilder().clearLevelSettings().addAllLevelSettings(levelSettings);

            boolean needParnterConfirm = false;
            if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
                if (getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid() != player.getUid()) {
                    exitAllRoomQuietly("create room with team parnter role");
                } else {
                    needParnterConfirm = true;
                    if (maxMemberLimit < getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber()) {
                        NKErrorCode.RoomCreateFailTeamMemberBeyondPlayMax.throwError("create room fail");
                    }
                }
            }
            long roomId = CustomRoomIdGenerator.getInstance().allocGuid();
            MemberBaseInfo.Builder memberBaseInfo = getCreatorMemberBaseInfoBuilder();
            memberBaseInfo.setRoomID(roomId);
            memberBaseInfo.setRuleInfo(ruleInfo);
            if (needParnterConfirm) {
                if (DateUtils.currentTimeMillis() - lastCreateRoomWithPrejoinTimestampMs < 15 * 1000L) {
                    NKErrorCode.RoomCannotCreateBeforePartnerConfirmLastCreate.throwError("cannot create because partner not confirm");
                }
                this.lastCreateRoomWithPrejoinTimestampMs = DateUtils.currentTimeMillis();
                memberBaseInfo.setRoomID(getTeamId());
            }

            SsRoomsvr.RpcCreateRoomReq.Builder rpcRoomCreate = SsRoomsvr.RpcCreateRoomReq.newBuilder();
            rpcRoomCreate.setRoomid(roomId).setUid(player.getUid());
            rpcRoomCreate.setRuleInfo(ruleInfo);
            rpcRoomCreate.setMemberBaseInfo(memberBaseInfo);
            rpcRoomCreate.setRoomType(RoomType.CustomRoom);
            rpcRoomCreate.setPwdStr(passStr);
            rpcRoomCreate.setDisplayInfo(RoomDisplayInfo.newBuilder().setTitle(title).build());
            //rpcRoomCreate.setRobotLevel(settingBuilder.getRobotSetting().getRobotLevel());
            //rpcRoomCreate.addAllLevelSetting(levelSettings);
            RoomCreateOptions.Builder createOpts = RoomCreateOptions.newBuilder().setPreCreate(needParnterConfirm);
            createOpts.setRoomSetting(settingBuilder);
            rpcRoomCreate.setCreateOpts(createOpts);

            RpcResult<SsRoomsvr.RpcCreateRoomRes.Builder> rpcCreateRoomResult = RoomService.get()
                    .rpcCreateRoom(rpcRoomCreate);
            rpcCreateRoomResult.throwError();
            if (NKErrorCode.OK.getValue() != rpcCreateRoomResult.getData().getResult()) {
                LOGGER.error("RoomService rpcCreateRoom ret err uid:{}, Error:{}",
                        player.getUid(), rpcCreateRoomResult.getData().getResult());
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
                NKErrorCode.forNumber(rpcCreateRoomResult.getData().getResult()).throwError("create room fail");
            }
            //player.setCurrentRoomId(roomId);
            //player.getUserAttr().getRoomInfo().setCurrentMatchType(targetRuleInfo.getMatchTypeId());
            LOGGER.info("createRoom uid:{},roomId:{}", player.getUid(), roomId);
            if (needParnterConfirm) {
                LOGGER.debug("team leader notice partner to confirm join room, playerUid:{} teamId:{} targetRoom:{}",
                        player.getUid(), getTeamId(), rpcCreateRoomResult.getData().getRoomInfo().getRoomBriefInfo());
                NKErrorCode preJoinNoticeRet = preJoinNotice(getTeamId(),
                        rpcCreateRoomResult.getData().getRoomInfo().getRoomBriefInfo(), RoomJoinType.RJT_Default,
                        TeamJoinRoomExtraInfo.getDefaultInstance());
                if (preJoinNoticeRet == NKErrorCode.RoomOnlyHimself) {
                    // 发起后队伍只剩自己了，直接online激活房间即可
                    exitRoomQuietly(getTeamId());
                    asyncOnlineRoom(roomId);
                } else {
                    preJoinNoticeRet
                            .throwErrorIfNotOk("pre join notice fail");
                }
                // 前置预创建的房间 房主是离线状态，整个房间处于不对外服务的状态
                // 过程中有任何操作失败后续都会通过超时回收，
                // 房主会在真正全部成员加入成功后通过MT_Init的ntf才将房间id写入属性系统
            } else {
                PublicRoomMgr.getInstance().addRoomAfterCreate(roomId, RoomPublicType.RPT_CustomLobby);
            }
            // 上报房间title流水
            TlogFlowMgr.sendSecEditFlow(player, title, ENUMSecEditFlowScenceID.ESEFS_CreateRoom, roomId);
            // 上报房间安全流水
            TlogFlowMgr.sendSecRoomFlow(player, 1, roomId, 1, RoomType.CustomRoom_VALUE,
                    title, "", player.getOpenId(), player.getUid());
            // 发送房间创建运营流水
            TlogFlowMgr.sendTeamFlow(player, roomId, TEAM_OP_TYPE.TEAM_CREATE_REQ, 0,
                    String.format("%s,%d;", player.getUid(),
                            player.getFriendManager().getRelationTypeVal(player.getUid())),
                    rpcCreateRoomResult.getData().getRoomInfoBuilder(), 0);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_create_room, 1);
            return rpcCreateRoomResult.getData().getRoomInfo();
        } catch (NKTimeoutException | RpcException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
            LOGGER.error("rpcCreateRoom uid:{}, err:", player.getUid(), e);
            NKErrorCode.RoomServiceIsBusy.throwError("create room fail");
        }
        return null;
    }

    private int getValidRobotLevel(int playId, int robotLevel) {
        CustomRoomRule customRoomRule = CustomRoomData.getInstance().get(playId);
        if (customRoomRule == null) {
            NKErrorCode.InvalidParams.throwError("play not exist");
        }
        int finalRobotLevel = customRoomRule.getRobotSettingsCount() > 0 ?
                customRoomRule.getRobotSettings(0).getRobotLevel() : 1;
        // 确保机器人难度值是对的
        for (CustomRoomRobotSetting customRoomRobotSetting : customRoomRule.getRobotSettingsList()) {
            if (customRoomRobotSetting.getRobotLevel() == robotLevel) {
                finalRobotLevel = robotLevel;
                break;
            }
        }
        return finalRobotLevel;
    }

    private List<RoomLevelSetting> checkRoomLevelSettings(int modeId, List<RoomLevelSetting> levelSettings) {
        List<RoomLevelSetting> ret = new ArrayList<>();
        for (RoomLevelSetting levelSetting : levelSettings) {
            RoomLevelSetting.Builder levelSettingBuilder = RoomLevelSetting.newBuilder().setIndex(levelSetting.getIndex());
            for (int levelId : levelSetting.getLevelIdsList()) {
                T_LevelInfoData tLevelInfoData = LevelInfoData.getInstance().get(levelId);
                if (tLevelInfoData == null) {
                    LOGGER.error("level id from client is missing at config, levelId:{}", levelId);
                    continue;
                }
                // 主玩法的未完成关卡跳过
                if (modeId == 1 && tLevelInfoData.getIsCompleted() != LCT_Completed) {
                    continue;
                }
                // 其他玩法关卡默认存在就都能玩
                if (tLevelInfoData != null) {
                    levelSettingBuilder.addLevelIds(levelId);
                }
            }
            if (levelSettingBuilder.getLevelIdsCount() == 0) {
                NKErrorCode.InvalidParams.throwError("miss valid level id");
            }
            ret.add(levelSettingBuilder.build());
        }
        return ret;
    }

    // 请求加入的逻辑参照iwiki https://iwiki.woa.com/p/4010628159

    public NKPair<NKErrorCode, Long> handleInvitation(long clientRoomId, MatchRuleInfo ruleInfo, List<Long> uidList,
            InvitationSourceType sourceType, int roleTypeInt) {
        long currRoomId = getCurrentRoomId(clientRoomId);
        if (currRoomId == 0) {
            UniversalRoom roomWithMatchRuleInfo = createRoomWithMatchRuleInfo(ruleInfo);
            currRoomId = roomWithMatchRuleInfo.getRoomBriefInfo().getRoomID();
            LOGGER.info("RoomInviteMsgHandler currRoomId == 0, created new room:{}", currRoomId);
        }
        SsRoomsvr.RpcRoomInvitationReq.Builder ssReqMsg = SsRoomsvr.RpcRoomInvitationReq.newBuilder();
        ssReqMsg.addAllBeInvitedUid(uidList);
        ssReqMsg.setRoomId(currRoomId);
        ssReqMsg.setUid(player.getUid());
        ssReqMsg.setRuleInfo(ruleInfo);
        ssReqMsg.setUgcTestPlayerLimit(player.getPlayerUgcManager().ugcTestPlayerInvitePlayerCountLimit(uidList));
        ssReqMsg.setIst(sourceType);
        ssReqMsg.setTargetRoleType(roleTypeInt);
        try {
            RpcResult<RpcRoomInvitationRes.Builder> rpcResult = RoomService.get().rpcRoomInvitation(ssReqMsg);
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, clientRoomId, StarPTeamOpType.SPIOpType_INVITE,
                        rpcResult.getData().getResult(), !uidList.isEmpty() ? String.valueOf(uidList.get(0)) : "");
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(currRoomId, "invite other but room not exist");
                    return new NKPair<NKErrorCode, Long>(NKErrorCode.RoomServiceIsBusy, 0L);
                }
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_invite_player_room, 1);
                return new NKPair<NKErrorCode, Long>(NKErrorCode.forNumber(rpcResult.getData().getResult()), 0L);
            }
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_invite_player_room, 1);
            return new NKPair<NKErrorCode, Long>(NKErrorCode.OK, rpcResult.getData().getFailureTime());
        } catch (NKTimeoutException e) {
            LOGGER.error("handleInvitation timeout room:{}, inviter:{}", currRoomId, player.getUid());
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_invite_player_room, 1);
            return new NKPair<NKErrorCode, Long>(NKErrorCode.Timeout, 0L);
        } catch (RpcException e) {
            LOGGER.error("handleInvitation rpc exception room:{}, inviter:{}, e:", currRoomId, player.getUid(), e);
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_invite_player_room, 1);
            return new NKPair<NKErrorCode, Long>(NKErrorCode.RoomRpcError, 0L);
        } finally {
            TlogFlowMgr.sendTeamInviteFlow(player, currRoomId, sourceType.getNumber(), uidList.get(0), "",
                    getTargetRoomInfoBuilder(currRoomId));
        }
    }

    /**
     * 请求加入其他玩家的room
     *
     * @param targetPlayerUid 目标玩家uid
     * @param invitationSourceType
     * @return
     */
    public NKErrorCode askToJoin(long targetPlayerUid, int invitationSourceType) {
        // 判断点J1
        // 当前玩家标记为作弊禁止组队
        if (player.getMatchIsolateID() == MatchIsolateType.MIT_CHEAT_VALUE) {
            LOGGER.info("player cannot ask to join when match isolated, player:{}", player.getUid());
            return NKErrorCode.RoomCannotInviteCauseIsolated;
        }
        // 判断玩家是否在小游戏内
        player.getPlayerStateMgr().checkConfirm().throwErrorIfNotOk("player at mini game console");

        SsGamesvr.RpcRoomAskToJoinReq.Builder ssReqMsg = SsGamesvr.RpcRoomAskToJoinReq.newBuilder();
        ssReqMsg.setUid(targetPlayerUid);
        ssReqMsg.setProposalInfo(getJoinerMemberBaseInfoBuilder());
        ssReqMsg.setInvitationSourceType(invitationSourceType);
        try {
            RpcResult<RpcRoomAskToJoinRes.Builder> rpcResult = GameService.get().rpcRoomAskToJoin(ssReqMsg);
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKCheckedException e) {
            LOGGER.error("askToJoin fail, playerUid:{}, targetPlayerUid:{}", player.getUid(), targetPlayerUid);
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    /**
     * 被请求方处理请求加入的请求
     *
     * @param proposalInfo 请求加入的玩家信息
     * @param invitationSourceType 请求加入的来源枚举值
     * @return
     */
    public SsGamesvr.RpcRoomAskToJoinRes.Builder handleAskToJoin(MemberBaseInfo proposalInfo, int invitationSourceType) {
        RpcRoomAskToJoinRes.Builder resBuilder = RpcRoomAskToJoinRes.newBuilder().setResult(NKErrorCode.OK.getValue());
        if (player.forbiddenStrangerRoomInvitation() && !player.getFriendManager().isFriend(proposalInfo.getUid())) {
            resBuilder.setResult(NKErrorCode.RoomCannotInviteCausePlayerForbiddenStrangerInvite.getValue());
            return resBuilder;
        }
        // 判断点J2
        UniversalRoom.Builder currentAvailableRoomInfo = getCurrentAvailableRoomInfo();
        if (currentAvailableRoomInfo == null || currentAvailableRoomInfo.getRoomBriefInfo().getRoomID() <= 0L) {
            LOGGER.info("player ask to join but current player has no room, player:{} askPlayer:{}", player.getUid(),
                    proposalInfo.getUid());
            return resBuilder;
        }
        long currRoomId = currentAvailableRoomInfo.getRoomBriefInfo().getRoomID();
        // 判断高低版本号
        var matchTypeId = currentAvailableRoomInfo.getRoomBriefInfo().getRuleInfo().getMatchTypeId();
        var proposalPlayVersion = Player.getPlayVersion(matchTypeId, proposalInfo);
        var myPlayVersion = player.getPlayVersion(matchTypeId);
        if (proposalPlayVersion != myPlayVersion) {
            LOGGER.debug("player ask to join fail because version mismatch, proposalUid:{} versionFromProposal:{} targetUid:{} targetVersion:{}",
                    proposalInfo.getUid(), proposalPlayVersion, player.getUid(), myPlayVersion);
            if (proposalPlayVersion < myPlayVersion) {
                resBuilder.setResult(NKErrorCode.RoomClientVersionLowerThanLeader.getValue());
                return resBuilder;
            } else {
                resBuilder.setResult(NKErrorCode.RoomClientVersionHigherThanLeader.getValue());
                return resBuilder;
            }
        }
        // 当前玩家标记为作弊禁止组队
        if (player.getMatchIsolateID() == MatchIsolateType.MIT_CHEAT_VALUE) {
            LOGGER.info("player cannot be join when match isolated, player:{}", player.getUid());
            resBuilder.setResult(NKErrorCode.RoomCannotBeInvitedCauseIsolated.getValue());
            return resBuilder;
        }

        // 判断是否拉黑了请求者
        if (player.getBlackManager().isBlack(proposalInfo.getUid())) {
            LOGGER.info("ask to join block cause current player blacklist proposal, curPlayer:{}, proposal:{}",
                    player.getUid(), proposalInfo.getUid());
            return resBuilder;
        }

        SsRoomsvr.RpcRoomInvitationReq.Builder ssReqMsg = SsRoomsvr.RpcRoomInvitationReq.newBuilder();
        ssReqMsg.setRoomId(currRoomId);
        ssReqMsg.addBeInvitedUid(proposalInfo.getUid());
        ssReqMsg.setUid(player.getUid());
        List<Long> uidList = new ArrayList<>();
        uidList.add(proposalInfo.getUid());
        ssReqMsg.setUgcTestPlayerLimit(player.getPlayerUgcManager().ugcTestPlayerInvitePlayerCountLimit(uidList));
        // 兼容枚举不存在的情况
        InvitationSourceType ist = InvitationSourceType.forNumber(invitationSourceType);
        if (ist == null) {
            ist = InvitationSourceType.IST_Other;
        }
        ssReqMsg.setIst(ist);
        ssReqMsg.setType(TeamInvitationType.TIT_AskForJoin_VALUE);

        try {
            RpcResult<RpcRoomInvitationRes.Builder> rpcResult = RoomService.get().rpcRoomInvitation(ssReqMsg);
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(currRoomId, "other ask to join but room not exist");
                } else if (rpcResult.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(currRoomId, "other ask to join but not in current room");
                } else {
                    resBuilder.setResult(rpcResult.getData().getResult());
                    return resBuilder;
                }
            }
        } catch (NKCheckedException e) {
            LOGGER.error("handleInvitation timeout room:{}, inviter:{}", currRoomId, player.getUid());
            resBuilder.setResult(NKErrorCode.RoomServiceIsBusy.getValue());
            return resBuilder;
        }
        // 记录当前的uniqueId和实际记录请求的roomId，用于后续的同意时的校验
        long currentTimeMillis = DateUtils.currentTimeMillis();
        askToJoinHistory.put(currentTimeMillis, new AskToJoinRequestInfo(currRoomId, proposalInfo.getUid()));
        // 发消息给房主
        RoomWantToJoinNtf.Builder ntfBuilder = RoomWantToJoinNtf.newBuilder();
        ntfBuilder.setProposalInfo(proposalInfo);
        ntfBuilder.setIst(invitationSourceType);
        ntfBuilder.setUniqueId(currentTimeMillis);
        ntfBuilder.setRoomBriefInfo(currentAvailableRoomInfo.getRoomBriefInfo());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMWANTTOJOINNTF, ntfBuilder);

        return resBuilder;
    }

    /**
     * 处理请求加入的同意逻辑
     *
     * @param targetPlayerUid 请求加入的玩家uid
     * @param uniqueId 当前请求的唯一id
     * @return
     */
    public NKErrorCode handleApprovalOfAskToJoin(long targetPlayerUid, long uniqueId) {
        shrinkAskToJoinHistoryMap();
        // 判断点J4
        // 已经失效了
        AskToJoinRequestInfo askToJoinRequestInfo = askToJoinHistory.get(uniqueId);
        if (askToJoinRequestInfo == null || askToJoinRequestInfo.roomId <= 0L ) {
            return NKErrorCode.RoomAskToJoinExpired;
        }
        long targetRoomId = askToJoinRequestInfo.roomId;
        // 记录的信息不一致，请求已失效
        UniversalRoom.Builder currentAvailableRoomInfo = getCurrentAvailableRoomInfo();
        if (currentAvailableRoomInfo == null) {
            return NKErrorCode.RoomAskToJoinExpired;
        }
        if (currentAvailableRoomInfo.getRoomBriefInfo().getRoomID() != targetRoomId ||
                targetPlayerUid != askToJoinRequestInfo.proposal) {
            return NKErrorCode.RoomAskToJoinExpired;
        }
        SsGamesvr.RpcNoticePlayerDoJoinRoomReq.Builder ssReqMsg = SsGamesvr.RpcNoticePlayerDoJoinRoomReq.newBuilder();
        ssReqMsg.setUid(targetPlayerUid);
        ssReqMsg.setRoomId(targetRoomId);
        ssReqMsg.setRoomJoinType(RoomJoinType.RJT_JoinApplyApproved_VALUE);
        try {
            RpcResult<SsGamesvr.RpcNoticePlayerDoJoinRoomRes.Builder> rpcResult = GameService.get()
                    .rpcNoticePlayerDoJoinRoom(ssReqMsg);
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKCheckedException e) {
            LOGGER.error("handleApprovalOfAskToJoin fail, playerUid:{}, targetPlayerUid:{}", player.getUid(), targetPlayerUid);
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    /**
     * 处理请求加入的拒绝逻辑
     */
    public void handleRejectionOfAskToJoin(long targetUid, long uniqueId) {
        shrinkAskToJoinHistoryMap();
        askToJoinHistory.remove(uniqueId);
        // 发通知给对方玩家
        SendNoticeInteraction.sendNotice(targetUid
                , PlayerNoticeMsgType.PNT_ERR_CODE_NTF
                , Arrays.asList((long)NKErrorCode.RoomAskToJoinRejected.getValue())
                , 0L);
    }

    /**
     * 处理过期的请求加入缓存数据
     */
    private void shrinkAskToJoinHistoryMap() {
        Map<Long, AskToJoinRequestInfo> tempMap = new HashMap<>(askToJoinHistory);
        // 获取HashMap的迭代器
        for (Entry<Long, AskToJoinRequestInfo> entry : tempMap.entrySet()) {
            // 判断key的有效性，即超过有效期需要处理掉，有效期60s
            if (DateUtils.currentTimeMillis() - entry.getKey() > 60 * 1000L) {
                askToJoinHistory.remove(entry.getKey());
            }
        }
    }

    /**
     * 执行应答后的真实加入行为
     *
     * @param roomId 目标roomId
     * @param roomJoinType 加入方式
     * @return
     */
    public NKErrorCode doRoomJoinForAskToJoin(long roomId, int roomJoinType) {
        // 判断点J5
        // 处于样板间
        if (player.getUserAttr().getXiaoWoInfo().getVisitXiaoWoInfo().getCurrentXiaoWoType() == XiaoWoType.XT_SampleRoom_VALUE &&
                BaseGenerator.getUidType(roomId) == GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
            // 在样板间内的玩家不能被邀请去房间组队
            return NKErrorCode.RoomCannotJoinCauseInSampleRoom;
        }
        // 处于农场
        if ((player.getPlayerFarmMgr().getCurrentFarmId() != 0 || player.getPlayerHouseMgr().getCurrentHouseId() != 0) &&
                BaseGenerator.getUidType(roomId) == GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
            // 在农场内的玩家不能被邀请去房间组队
            return NKErrorCode.RoomCannotJoinCauseInFarm;
        }
        RoomJoinType rjt = RoomJoinType.forNumber(roomJoinType);
        if (rjt == null) {
            rjt = RoomJoinType.RJT_JoinApplyApproved;
        }
        try {
            return roomJoin(roomId, "", rjt);
        } catch (NKRuntimeException e) {
            if (e.getEnumErrCode() == NKErrorCode.RoomCannotJoinBecauseSeatNotEnough) {
                return NKErrorCode.RoomProposalCannotJoinCauseSeatNotEnough;
            }
            LOGGER.info("do room join for ask to join fail, player:{} ret:{}", player.getUid(), e.getEnumErrCode());
            return e.getEnumErrCode();
        }
    }

    /**
     * 获取当前可加入的roomId
     *
     * @return
     */
    private UniversalRoom.Builder getCurrentAvailableRoomInfo() {
        // 封装一下，不是所有的房间或者队伍都需要外显或者可以加入
        // TODO 梳理现在所有的房间类型，外显信息需要，也需要判断是否允许求加入
        if (getRoomId() > 0) {
            // TODO 不同的房间类型的特殊判断
            return getCurrentRoomInfoBuidler();
        }
        if (getTeamId() > 0) {
            return getCurrentTeamInfoBuidler();
        }
        return null;
    }

    private boolean acceptInvitationAndJumpToBattle(int ist) {
        return ist == InvitationSourceType.IST_UGCFriendInDs_VALUE
                || ist == InvitationSourceType.IST_UGCClubInDs_VALUE
                || ist == InvitationSourceType.IST_UGCNearInDS_VALUE
                || ist == InvitationSourceType.IST_UGCRecentPlayInDs_VALUE
                || ist == InvitationSourceType.IST_UGCPlayerSearchInDs_VALUE;
    }

    public NKErrorCode GMJoinRoom(long roomId, RoomJoinType joinType, long starpId) {
        exitAllRoom("GMJoin");

        MemberBaseInfo.Builder memberBaseInfoBuilder;
        if (BaseGenerator.getUidType(roomId) == GuidType.GUID_TYPE_STARP_TEAM_ID) {
            memberBaseInfoBuilder = player.getSPMemberBaseInfoBuilder();
        } else {
            memberBaseInfoBuilder = player.getMemberBaseInfoBuilder();
        }
        this.checkBeforeJoinStarP(starpId, roomId, memberBaseInfoBuilder, 0);

        try {
            SsRoomsvr.RpcJoinRoomReq.Builder rpcJoinRoomReq = SsRoomsvr.RpcJoinRoomReq.newBuilder();
            rpcJoinRoomReq.setUid(player.getUid());
            rpcJoinRoomReq.setRoomId(roomId);
            rpcJoinRoomReq.setMemberBaseInfo(memberBaseInfoBuilder);
            rpcJoinRoomReq.setJoinType(joinType);
            RpcResult<RpcJoinRoomRes.Builder> rpcResult = RoomService.get().rpcJoinRoom(rpcJoinRoomReq);
            if (!rpcResult.isOK()) {
                LOGGER.error("join room fail");
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    getRoomNotExistErrorCode(roomId).throwError("room not exist");
                }

                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException |RpcException e) {
            LOGGER.error("room join fail, err:{}", e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    /**
     * 接受邀请
     *
     * @param roomId 房间id
     * @param invitorUid 邀请者
     * @param inRoomRoleTypeInt 加入的房间角色类型
     * @param sendTimestampMs
     * @return 错误码
     */
    public NKErrorCode acceptInvitation(long roomId, long invitorUid, int inRoomRoleTypeInt, int ist,
            long sendTimestampMs) {
        LOGGER.debug("acceptInvitation begin, roomId={}, invitorUid={}, inRoomRoleTypeInt={}", roomId, invitorUid,
                inRoomRoleTypeInt);
        if (Framework.currentTimeMillis() < player.getRoomModifyTime() + PropertyFileReader.getRealTimeIntItem(
                "roomModifyInterval", 1000)) {
            NKErrorCode.RoomModifyIsBusy.throwError("room modify is busy");
        }
        if (ist == InvitationSourceType.IST_PLAY_AI_VALUE) {
            // AI邀请的特殊逻辑
            LOGGER.debug("player accept invitation from ai, player:{}", player.getUid());
            // TODO 前置判断，是否可以接受邀请
            exitAllRoom("accept invitation from ai");
            return aiInvitationComponent.acceptAITeamInvitation(invitorUid, sendTimestampMs);
        }
        // 加入指定房间
        SsRoomsvr.RpcJoinRoomReq.Builder rpcJoinRoomReq = SsRoomsvr.RpcJoinRoomReq.newBuilder();
        rpcJoinRoomReq.setUid(player.getUid());
        rpcJoinRoomReq.setRoomId(roomId);
        rpcJoinRoomReq.setInviterId(invitorUid);
        rpcJoinRoomReq.setJoinType(RoomJoinType.RJT_AcceptInvitation);
        rpcJoinRoomReq.setJoinRoleType(inRoomRoleTypeInt);
        if (acceptInvitationAndJumpToBattle(ist)) {
            rpcJoinRoomReq.setJoinType(RoomJoinType.RJT_JoinApplyApprovedJumpToBattle);
        }
        MemberBaseInfo.Builder memberBaseInfoBuilder = player.getMemberBaseInfoBuilder();
        // 退出当前的组队
        exitAllRoom("accept invitation");
        rpcJoinRoomReq.setMemberBaseInfo(memberBaseInfoBuilder);
        try {
            RpcResult<RpcJoinRoomRes.Builder> rpcResult = RoomService.get().rpcJoinRoom(rpcJoinRoomReq);
            if (!rpcResult.isOK()) {
                LOGGER.debug("join target room fail : {}", rpcResult.getRet());
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_join_room, 1);
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
                NKErrorCode.forNumber(rpcResult.getRet()).throwError("room join fail");
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    return NKErrorCode.RoomInviteExpired;
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomPlayIdBlockedByClientInfo.getValue()) {
                    return NKErrorCode.RoomJoinFailWhenPlayBlockedByClientInfo;
                }
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_join_room, 1);
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
                NKErrorCode.forNumber(rpcResult.getData().getResult()).throwError("room join fail");
            }
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_join_room, 1);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
            player.setRoomModifyTime(Framework.currentTimeMillis());
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("join room catch exception : {}", e.getMessage());
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    /**
     * 接受邀请
     *
     * @param roomId         房间id
     * @param invitorUid     邀请者
     * @param inRoomRoleTypeInt 加入的房间角色类型
     * @return 错误码
     */
    public NKErrorCode acceptInvitation(long roomId, long invitorUid, int inRoomRoleTypeInt, int ist, long starPWorldId, long starPRoleId) {
        LOGGER.debug("acceptInvitation begin, roomId={}, invitorUid={}, inRoomRoleTypeInt={}, starPWorldId={}", roomId, invitorUid, inRoomRoleTypeInt, starPWorldId);
        if (Framework.currentTimeMillis() < player.getRoomModifyTime() + PropertyFileReader.getRealTimeIntItem(
                "roomModifyInterval", 1000)) {
            NKErrorCode.RoomModifyIsBusy.throwError("room modify is busy");
        }
        // 加入指定房间
        SsRoomsvr.RpcJoinRoomReq.Builder rpcJoinRoomReq = SsRoomsvr.RpcJoinRoomReq.newBuilder();
        rpcJoinRoomReq.setUid(player.getUid());
        rpcJoinRoomReq.setRoomId(roomId);
        rpcJoinRoomReq.setInviterId(invitorUid);
        rpcJoinRoomReq.setJoinType(RoomJoinType.RJT_AcceptInvitation);
        rpcJoinRoomReq.setJoinRoleType(inRoomRoleTypeInt);
        if (acceptInvitationAndJumpToBattle(ist)) {
            rpcJoinRoomReq.setJoinType(RoomJoinType.RJT_JoinApplyApprovedJumpToBattle);
        }
        MemberBaseInfo.Builder memberBaseInfoBuilder = player.getSPMemberBaseInfoBuilder();
        this.checkBeforeJoinStarP(starPWorldId, roomId, memberBaseInfoBuilder, starPRoleId);
        // 退出当前的组队
        exitAllRoom("accept invitation");
        rpcJoinRoomReq.setMemberBaseInfo(memberBaseInfoBuilder);
        try {
            RpcResult<RpcJoinRoomRes.Builder> rpcResult = RoomService.get().rpcJoinRoom(rpcJoinRoomReq);
            if (!rpcResult.isOK()) {
                LOGGER.debug("join target room fail : {}", rpcResult.getRet());
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_join_room, 1);
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
                NKErrorCode.forNumber(rpcResult.getRet()).throwError("room join fail");
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    return NKErrorCode.RoomInviteExpired;
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomPlayIdBlockedByClientInfo.getValue()) {
                    return NKErrorCode.RoomJoinFailWhenPlayBlockedByClientInfo;
                }
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_join_room, 1);
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
                NKErrorCode.forNumber(rpcResult.getData().getResult()).throwError("room join fail");
            }
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_join_room, 1);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
            player.setRoomModifyTime(Framework.currentTimeMillis());
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("join room catch exception : {}", e.getMessage());
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    /**
     * 拒绝邀请
     *
     * @param roomId 房间id
     * @param invitorUid 邀请者uid
     * @return
     */
    public NKErrorCode rejectInvitation(long roomId, long invitorUid, int ist, long sendTimestampMs) {
        if (ist == InvitationSourceType.IST_PLAY_AI_VALUE) {
            // AI邀请的特殊逻辑
            LOGGER.debug("player accept invitation from ai, player:{}", player.getUid());
            return aiInvitationComponent.rejectAITeamInvitation(invitorUid, sendTimestampMs);
        }
        // TODO 是否需要判断邀请有效，即透传roomId给邀请者做确认
        SendNoticeInteraction.sendNotice(invitorUid, PlayerNoticeMsgType.PNT_DENY_INVITATION,
                player.getUid());
        return NKErrorCode.OK;
    }

    public NKErrorCode handleKickRoomMember(long roomId, long kickedPlayerUid) {
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }

        LOGGER.info("player kick other player, player:{}, roomId:{}, targetPlayerUid:{}", player
                .getUid(), currentRoomId, kickedPlayerUid);
        SsRoomsvr.RpcKickPlayerReq.Builder rpcKickPlayer = SsRoomsvr.RpcKickPlayerReq.newBuilder();
        rpcKickPlayer.setKickUid(kickedPlayerUid);
        rpcKickPlayer.setUid(player.getUid());
        rpcKickPlayer.setRoomId(currentRoomId);
        try {
            RpcResult<SsRoomsvr.RpcKickPlayerRes.Builder> rpcResult = RoomService.get().rpcKickPlayer(rpcKickPlayer);
            LOGGER.info("handleKickRoomMember ret:{}, uid:{}, kicked:{}",
                    rpcResult.getData().getResult(), player.getUid(), kickedPlayerUid);
            if (rpcResult.getData().getResult() != 0) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, currentRoomId, StarPTeamOpType.SPIOpType_KICK,
                                        rpcResult.getData().getResult());
                if (rpcResult.getData().getResult() == NKErrorCode.RoomTargetMemberNotExist.getValue()) {
                    return NKErrorCode.OK;
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()
                        || rpcResult.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    LOGGER.info("player kick other in room but room or self not exist, playerUid:{} roomId:{}",
                            player.getUid(), currentRoomId);
                    ntfClientClearRoomInfo(currentRoomId, "room not exist when kick");
                    return NKErrorCode.OK;
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomStateIsWaitingForMatchConfirm.getValue()) {
                    return NKErrorCode.RoomCannotKickWhenWaitConfirm;
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomStateIsMatching.getValue()) {
                    return NKErrorCode.RoomCannotKickWhenWaitConfirm;
                }
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_kick_player_room, 1);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
            // 流水
            StringBuilder memberStr = new StringBuilder();
            for (Long uid : rpcResult.getData().getRoomInfo().getRoomTFlowInfo().getRoomMemberUidArray()
                    .getArrayLongList()) {
                memberStr.append(uid).append(",").append(player.getFriendManager().getRelationTypeVal(uid)).append(";");
            }
            TlogFlowMgr.sendTeamKickFlow(player, currentRoomId, memberStr.toString(),
                    rpcResult.getData().getRoomInfoBuilder(), kickedPlayerUid);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_kick_player_room, 1);
            return NKErrorCode.OK;
        } catch (NKTimeoutException | RpcException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_kick_player_room, 1);
            LOGGER.error("handleKickRoomMember caught err, roomId:{} playerUid:{} kicked:{} e:",
                    currentRoomId, player.getUid(), kickedPlayerUid, e.getMessage());
            return NKErrorCode.RoomRpcError;
        }
    }

    /**
     * 离开组队
     *
     * @return {@link NKErrorCode}
     */
    public NKErrorCode leaveRoom(long clientRoomId, RoomExitType type) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        LOGGER.info("player leave room, player:{}, roomId:{}", player.getUid(), currentRoomId);
        GuidType uidType = BaseGenerator.getUidType(currentRoomId);
        // 退出房间
        if (uidType == GuidType.GUID_TYPE_CUSTOM_ROOM_ID){
            // 自己为队伍队长时，整队退出
            if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid() == player.getUid()) {
                NKErrorCode ret = exitRoom(currentRoomId, RoomExitType.RET_TeamExit);
                // 队伍退出，且仅自己退成功，需要同时退出队伍
                if (ret == NKErrorCode.RoomTeamLeaveButQuitHimself) {
                    exitRoomQuietly(getTeamId());
                    return NKErrorCode.OK;
                }
                return ret;
            } else {
                exitRoom(clientRoomId, RoomExitType.RET_SelfExit).throwErrorIfNotOk("exit room fail");
            }
            return NKErrorCode.OK;
        }
        if (GuidType.GUID_TYPE_STARP_TEAM_ID == uidType) {
            type = RoomExitType.RET_SP_SelfExit;
        }
        NKErrorCode result = exitRoom(currentRoomId, type);
        StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, currentRoomId, StarPTeamOpType.SPIOpType_LEAVE,
                result.getValue());
        return result;
    }

    public void handleModifyGameMode(MatchRuleInfo.Builder ruleInfo) {
        // 是否啾灵模式切换!
        boolean starP = StarPConfs.isStarPGame(ruleInfo.getMatchTypeId());
        if (!starP && !player.getUserAttr().getUnLockGameModeSet().contains(ruleInfo.getMatchTypeId())) {
            NKErrorCode.RoomMatchRuleConfigInvalid.throwError("checkMatchType Unlock error");
        }
        if (checkRoomConfig(ruleInfo) != NKErrorCode.OK) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_modify_gamemode_room, 1);
            NKErrorCode.RoomInviteRuleConfigInvalid.throwError("checkRoomConfig error");
        }

        int beforeMatchType = player.getUserAttr().getRoomInfo().getCurrentMatchType();
        player.setCurrentMatchType(ruleInfo.getMatchTypeId());
        if (beforeMatchType != ruleInfo.getMatchTypeId()) {
            NKPair<Boolean, Collection<Integer>> outLookReplace = MatchTypeOutlookReplaceData.getInstance()
                    .checkPlayerOutlookReplace(ruleInfo.getMatchTypeId(),
                            player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList(),
                            player.getUserAttr().getPlayerPublicEquipments().getBackupDressUpInfosList());
            // StarP玩法装扮替换
//            if (StarPConfs.isStarPGame(ruleInfo.getMatchTypeId())) {
//                Collection<Integer> moeDressUpList = outLookReplace.getKey() ? outLookReplace.getValue() :
//                        player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList();
//                TcaplusDb.PlayerPublic playerPublic = PlayerPublicDao.getTcaplusPlayerPublic(player.getUid(),
//                        PlayerPublicAttrKey.StarPlayerInfo);
//                if (playerPublic != null && playerPublic.hasStarPlayerInfo()) {
//                    TlogStarPDressUpFlow starPDressUpFlow = TlogStarPDressUpFlow.defaultInstance();
//                    outLookReplace = StarPConfs.checkPlayerOutlookReplace(ruleInfo.getMatchTypeId(),
//                            moeDressUpList, player.getUid(), playerPublic.getStarPlayerInfo(), starPDressUpFlow);
//                    starPDressUpFlow.logToTlogd();
//                }
//            }
            if (outLookReplace.getKey()) {
                CsPlayer.PlayerNoticeMsgNtf.Builder noticeMsg = CsPlayer.PlayerNoticeMsgNtf.newBuilder();
                noticeMsg.setType(PlayerNoticeMsgType.PNT_OUTLOOK_GROUP_CONFLICT_IN_MATCH_TYPE);
                noticeMsg.setNotice(ServerTextConfData.getInstance().getNKErrorText(NKErrorCode.OutLookConflictInMatchType.getValue()));
                player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, noticeMsg);
            }
        }

        if (!checkWereWolfConsumeItem(ruleInfo.getMatchTypeId(), ruleInfo.getWereWolfSideIdentityInfo())) {
            LOGGER.error("handleModifyGameMode checkWereWolfConsumeItem fail player:{} {} {}", player.getUid(),
                    ruleInfo.getMatchTypeId(), ruleInfo.getWereWolfSideIdentityInfo());
            NKErrorCode.RoomWereWolfConsumeItemNotEnough.throwError("handleModifyGameMode checkWereWolfConsumeItem fail");
        }

        this.checkModifyModeWithStarP(ruleInfo);
        if (getTeamId() != 0) {
            SsRoomsvr.RpcRoomModifyGameModeReq.Builder roomReq = SsRoomsvr.RpcRoomModifyGameModeReq.newBuilder();
            roomReq.setRoomid(getTeamId());
            roomReq.setUid(player.getUid());
            roomReq.setRuleInfo(ruleInfo);
            RoomService roomSrv = RoomService.get();
            if (null != roomSrv) {
                try {
                    RpcResult<SsRoomsvr.RpcRoomModifyGameModeRes.Builder> rpcResult = roomSrv
                            .rpcRoomModifyGameMode(roomReq);
                    if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                        String extra = ruleInfo.getMatchTypeId() + "," + ruleInfo.getDifficultyId();
                        StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, getTeamId(),
                                StarPTeamOpType.SPIOpType_MODIFY_MODE, rpcResult.getData().getResult(), extra);
                        if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                            ntfClientClearRoomInfo(getTeamId(), "change mode but room not exist");
                            return;
                        }
                        Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_modify_gamemode_room, 1);
                        NKErrorCode.throwErrorOf(NKErrorCode.forNumber(rpcResult.getData().getResult()));
                    }
                    Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_modify_gamemode_room, 1);
                } catch (NKTimeoutException | RpcException e) {
                    Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_modify_gamemode_room, 1);
                    LOGGER.error("rpcRoomModifyGameMode error :", e);
                }
            }
        }
        player.setCurrentMatchType(ruleInfo.getMatchTypeId());
    }

    public NKErrorCode handleStartMatch(long roomId, MatchRuleInfo matchRuleInfo, MatchContentInfo matchContentInfo) {
        RoomService roomSvr = RoomService.get();
        if (null == roomSvr) {
            LOGGER.error("handleStartMatch RoomService is null, room:{}", roomId);
            return NKErrorCode.RoomServiceError;
        }

        SsRoomsvr.RpcStartMatchReq.Builder rpcStartMatch = SsRoomsvr.RpcStartMatchReq.newBuilder();
        rpcStartMatch.setRoomId(roomId);
        rpcStartMatch.setUid(player.getUid());
        rpcStartMatch.setRuleInfo(matchRuleInfo);
        rpcStartMatch.setMatchContentInfo(matchContentInfo);
        try {
            RpcResult<SsRoomsvr.RpcStartMatchRes.Builder> rpcResult = roomSvr.rpcStartMatch(rpcStartMatch);
            if (rpcResult.getData().getResult() != 0
                    && rpcResult.getData().getResult() == NKErrorCode.RoomMemberReputationScoreNotPass.getValue()) {
                // 信誉分有对应的直达toast所以不走错误码提示
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_start_match_room, 1);
                LOGGER.error("handleStartMatch rpcStartMatch ret err, room:{}, err:{}",
                        roomId, rpcResult.getData().getResult());
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
            // 玩家在匹配中
            //Player.updatePrivateRoomStatus(player, RoomStatus.RS_Matching_VALUE);
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_start_match_room, 1);
            return NKErrorCode.OK;
        } catch (NKTimeoutException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_start_match_room, 1);
            LOGGER.error("handleStartMatch rpcStartMatch timeout err, room:{}", roomId);
            return NKErrorCode.Timeout;
        } catch (RpcException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_start_match_room, 1);
            LOGGER.error("handleStartMatch rpcStartMatch timeout err, room:{}, e:", roomId, e);
            return NKErrorCode.RoomRpcError;
        }
    }

    public MemberBaseInfo.Builder getCreatorMemberBaseInfoBuilder() {
        MemberBaseInfo.Builder wholeMemberBaseInfoBulder = player.getMemberBaseInfoBuilder();
        // 每次创建行为都清空qq群应用的任务id，再次创建时不能复用此次的taskid
        player.getUserAttr().getQqApplicationInfo().getQqTeamInfo().clear();
        // 获取当前的直播数据
        List<StreamStateInfo> streamStateInfoListList = wholeMemberBaseInfoBulder.getStreamStateInfoListList();
        wholeMemberBaseInfoBulder.clearStreamStateInfoList();
        // 过滤掉livelink相关的设置
        for (StreamStateInfo streamStateInfo : streamStateInfoListList) {
            if (player.getLiveLinkMgr().isOnLiveLinkStreamPlay(streamStateInfo.getStreamPlatTypeVal())) {
                LOGGER.debug("ignore streamStateInfo with play, stateInfo:{}", streamStateInfo);
                continue;
            }
            wholeMemberBaseInfoBulder.addStreamStateInfoList(streamStateInfo);
        }
        // TODO 清理不需要的字段
        return wholeMemberBaseInfoBulder;
    }

    public MemberBaseInfo.Builder getSPCreatorMemberBaseInfoBuilder() {
        MemberBaseInfo.Builder wholeMemberBaseInfoBulder = player.getSPMemberBaseInfoBuilder();
        // 每次创建行为都清空qq群应用的任务id，再次创建时不能复用此次的taskid
        player.getUserAttr().getQqApplicationInfo().getQqTeamInfo().clear();
        // 获取当前的直播数据
        List<StreamStateInfo> streamStateInfoListList = wholeMemberBaseInfoBulder.getStreamStateInfoListList();
        wholeMemberBaseInfoBulder.clearStreamStateInfoList();
        // 过滤掉livelink相关的设置
        for (StreamStateInfo streamStateInfo : streamStateInfoListList) {
            if (player.getLiveLinkMgr().isOnLiveLinkStreamPlay(streamStateInfo.getStreamPlatTypeVal())) {
                LOGGER.debug("ignore streamStateInfo with play, stateInfo:{}", streamStateInfo);
                continue;
            }
            wholeMemberBaseInfoBulder.addStreamStateInfoList(streamStateInfo);
        }
        return wholeMemberBaseInfoBulder;
    }

    public MemberBaseInfo.Builder getJoinerMemberBaseInfoBuilder() {
        // 每次加入行为都清空qq群应用的任务id
        player.getUserAttr().getQqApplicationInfo().getQqTeamInfo().clear();
        if (isStarPTeam()) {
            return player.getSPMemberBaseInfoBuilder();
        }
        
        MemberBaseInfo.Builder wholeMemberBaseInfoBulder = player.getMemberBaseInfoBuilder();
        // 获取当前的直播数据
        List<StreamStateInfo> streamStateInfoListList = wholeMemberBaseInfoBulder.getStreamStateInfoListList();
        wholeMemberBaseInfoBulder.clearStreamStateInfoList();
        // 过滤掉livelink相关的设置
        for (StreamStateInfo streamStateInfo : streamStateInfoListList) {
            if (player.getLiveLinkMgr().isOnLiveLinkStreamPlay(streamStateInfo.getStreamPlatTypeVal())) {
                LOGGER.debug("ignore streamStateInfo with play, stateInfo:{}", streamStateInfo);
                continue;
            }
            wholeMemberBaseInfoBulder.addStreamStateInfoList(streamStateInfo);
        }
        return wholeMemberBaseInfoBulder;
    }

    public SsGamesvr.RpcRoomMidJoinFriendBattleSvrRes.Builder roomMidJoinFriendBattleSvr(SsGamesvr.RpcRoomMidJoinFriendBattleSvrReq.Builder req) {
        SsGamesvr.RpcRoomMidJoinFriendBattleSvrRes.Builder res = SsGamesvr.RpcRoomMidJoinFriendBattleSvrRes.newBuilder()
                .setResult(0);
        ResMatch.MatchType matchType = MatchTypeData.getInstance().get(req.getMatchTypeId());
        if (matchType == null) {
            return res;
        }

        HashMap<Long, DBRelation> onlineFriends = player.getFriendManager().getAllOnlineFriend();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.info("BattleJoinTeam log, room: {} matchTypeId: {} onlineFriends: {} ",
                    req.getRoomId(), req.getMatchTypeId(), onlineFriends);
        }

        if (!onlineFriends.isEmpty()) {
            // 移除不在战斗的好友
            //onlineFriends.entrySet().removeIf(entry -> entry.getValue().getHotData().getPlayerStatus() != PlayerStateType.PST_Battle);
            onlineFriends.entrySet().removeIf(entry -> {
                PlayerStateType stateType = entry.getValue().getHotData().getPlayerStatus();
                return (stateType != PlayerStateType.PST_Battle) && (stateType != PlayerStateType.PST_Online);
            });

            // 根据好友亲密度排名
            Queue<DBRelation> sortQueue = new PriorityQueue<>(Comparator.comparingLong(o -> -1 * o.getHotData().getIntimacy()));
            sortQueue.addAll(onlineFriends.values());

            int maxNum = MiscConf.getInstance().getMiscConf().getJoinMidwayConf().getFriendsTopN();
            List<Long> battleFriends = new ArrayList<>();
            for (int i = 0; i < maxNum; i++) {
                DBRelation it = sortQueue.poll();
                if (it != null) {
                    battleFriends.add(it.getUid());
                } else {
                    break;
                }
            }

            Map<Integer, BattleSvrFriend> battleSvrFriends = new HashMap<>();
            if (!battleFriends.isEmpty()) {
                // 从redis拉取数据
                Map<Long, AttrPlayerPublicBattleInfo.proto_PlayerPublicBattleInfo.Builder> redisPlayerPublicDataMap = new HashMap<>(battleFriends.size());
                List<Long> missList = PublicPlayerBattleDao.batchGetBattle(battleFriends, redisPlayerPublicDataMap);

                if (!redisPlayerPublicDataMap.isEmpty()) {
                    for (Map.Entry<Long, AttrPlayerPublicBattleInfo.proto_PlayerPublicBattleInfo.Builder> entry : redisPlayerPublicDataMap.entrySet()) {
                        AttrPlayerPublicBattleInfo.proto_PlayerPublicBattleInfo.Builder publicBattleInfo = entry.getValue();

                        // 根据类型筛选
                        if (isEqualGame(matchType, publicBattleInfo.getMatchTypeId())) {
                            int svrId = publicBattleInfo.getBattleSvrId();
                            BattleSvrFriend battleSvrFriend = battleSvrFriends.computeIfAbsent(svrId, o -> new BattleSvrFriend(svrId));
                            battleSvrFriend.addFriend(publicBattleInfo.getBattleId());
                        }
                    }
                }
            }

            //根据好友密度排序
            Queue<BattleSvrFriend> svrFriends = new PriorityQueue<>(Comparator.comparingInt(BattleSvrFriend::size));
            svrFriends.addAll(battleSvrFriends.values());
            int maxSvrNum = MiscConf.getInstance().getMiscConf().getJoinMidwayConf().getBattleSvrNum();
            for (int i = 0; i < maxSvrNum; i++) {
                BattleSvrFriend battleSvrFriend = svrFriends.poll();
                if (battleSvrFriend != null) {
                    JoinMidwayFriendSvrInfo svrInfo = JoinMidwayFriendSvrInfo.newBuilder()
                            .setBattleSvrId(battleSvrFriend.getSvrId())
                            .addAllUidList(battleSvrFriend.getFiends())
                            .build();
                    res.addJoinMidwayFriendSvrInfo(svrInfo);
                } else {
                    break;
                }
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.info("BattleJoinTeam log, room: {} matchTypeId: {} onlineFriends: {} sortQueue: {} res: {} ",
                        req.getRoomId(), req.getMatchTypeId(), onlineFriends, sortQueue, res);
            }
        }

        return res;
    }

    /**
     * 根据启动的游戏类型判断
     *
     * @param originMatch
     * @param friendMatchId
     * @return
     */
    private boolean isEqualGame(ResMatch.MatchType originMatch, int friendMatchId) {
        ResMatch.MatchType matchType = MatchTypeData.getInstance().get(friendMatchId);
        if (matchType == null) {
            return false;
        }

        return matchType.getGameTypeId() == originMatch.getGameTypeId();
    }

    public MemberBaseInfo.Builder getMemberBaseInfoBuilder() {
        return player.getMemberBaseInfoBuilder();
    }

    public NKErrorCode roomInfoChange(long clientRoomId, String title, MatchRuleInfo ruleInfo, int memberLimit, String pwd,
            RoomSetting.Builder settingBuilder) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }

        MatchType matchType = MatchTypeData.getInstance().get(ruleInfo.getMatchTypeId());
        if (matchType == null) {
            NKErrorCode.InvalidParams.throwError("play not exist");
        }
        // mark-glueli 这里是自定义房间调用的 可以忽略掉 只处理到队伍的 2024-11-27
        memberLimit = Math.min(memberLimit, matchType.getBattlePlayerNum());
        if(matchType.getGameModeType() != GameModeType.GMT_Ugc_VALUE && matchType.getGameModeType() != GameModeType.GMT_UgcTest_VALUE) { //有些模式下不需要计算机器人信息
            settingBuilder.getRobotSettingBuilder().setRobotLevel(getValidRobotLevel(ruleInfo.getMatchTypeId(),
            settingBuilder.getRobotSetting().getRobotLevel()));
            List<RoomLevelSetting> levelSettings = checkRoomLevelSettings(matchType.getModeID(), settingBuilder.getMapSetting()
                    .getLevelSettingsList());
            settingBuilder.getMapSettingBuilder().clearLevelSettings().addAllLevelSettings(levelSettings);
        }
        
        // 记录玩家的历史选择--设置房间是否自动满员开局
        player.getUserAttr().getPlayerPublicGameSettings().setCustomRoomAutoStartWhenFull(settingBuilder.getAutoStartWhenFull());
        
        try {
            SsRoomsvr.RpcRoomChangeInfoReq.Builder req = SsRoomsvr.RpcRoomChangeInfoReq.newBuilder();
            req.setRoomId(currentRoomId);
            req.setOpSource(OpSource.OS_Player);
            req.setUid(player.getUid());
            req.setTitle(title);
            req.setRuleInfo(ruleInfo);
            req.setMemberLimit(memberLimit);
            req.setPwd(pwd);
            //req.setRobotLevel(settingBuilder.getRobotSetting().getRobotLevel());
            //req.addAllLevelSetting(levelSettings);
            req.setRoomSetting(settingBuilder);
            req.setChangeAllUgcRoomInfo(true);
            RpcResult<SsRoomsvr.RpcRoomChangeInfoRes.Builder> rpcResult = RoomService.get().rpcRoomChangeInfo(req);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()
                        || rpcResult.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(getTeamId(), "info change but room not exist");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public void roomModifyNotify(SsGamesvr.RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
//        LOGGER.debug("user attr, playerUid:{}, roomInfo:{}", player.getUid(),
//                player.getUserAttr().getRoomInfo().getCopyCsBuilder());
        int beforeMatchType = player.getUserAttr().getRoomInfo().getCurrentMatchType();
        // 刷新mgr中维护的当前房间数据
        try (NoAsyncCheck check = NoAsyncCheck.newInstance().begin()) {
            refreshCurrentRoomInfo(roomModifyNtfReq);

            // 拼接返回给客户端的数据
            CsRoom.RoomMemberModifyNtf.Builder ntfBuilder = CsRoom.RoomMemberModifyNtf.newBuilder();
            ntfBuilder.addAllMemberList(roomModifyNtfReq.getMemberListList());
            ntfBuilder.addAllObserverMemberList(roomModifyNtfReq.getObserverMemberListList());
            ntfBuilder.setRoomID(roomModifyNtfReq.getRoomID());
            ntfBuilder.setRoomType(roomModifyNtfReq.getRoomType());
            ntfBuilder.setModify(transferModifyType(roomModifyNtfReq.getModify())); // 转义一次透传给客户端的取值
            ntfBuilder.setLeaderID(roomModifyNtfReq.getLeaderID());
            ntfBuilder.setRoomState(roomModifyNtfReq.getRoomState());
            ntfBuilder.setStateStartTime(roomModifyNtfReq.getStateStartTime());
            ntfBuilder.setTeamType(roomModifyNtfReq.getTeamType());
            ntfBuilder.setMaxMemberNum(roomModifyNtfReq.getMaxMemberNum());
            ntfBuilder.setMaxObserverNum(roomModifyNtfReq.getMaxObserverNum());
            ntfBuilder.setState(roomModifyNtfReq.getState());
            ntfBuilder.setRoomBriefInfo(roomModifyNtfReq.getRoomBriefInfo());
            ntfBuilder.setAutoSwitchMatchType(roomModifyNtfReq.getAutoSwitchMatchType());
            ntfBuilder.setRoomSetting(roomModifyNtfReq.getRoomSetting());
            ntfBuilder.setCompElimRoomInfo(roomModifyNtfReq.getCompElimRoomInfo());
            ntfBuilder.setRoomGeoInfo(roomModifyNtfReq.getRoomGeoInfo());
            for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                    roomModifyNtfReq.getObserverMemberListList())) {
                for (MemberBaseInfo member : memberList) {
                    if (member.getUid() == player.getUid()) {
                        ntfBuilder.setCurPlayerState(member.getState());
                        ntfBuilder.setMatchID(member.getMatchID());
                        break;
                    }
                }
            }
            ntfBuilder.setBattleCount(roomModifyNtfReq.getBattleCount());

            ntfBuilder.setRuleInfo(roomModifyNtfReq.getRuleInfo());
            ntfBuilder.setChatGroupKey(roomModifyNtfReq.getChatGroupKey());
            // ugc数据
            ntfBuilder.setUgcBriefInfo(roomModifyNtfReq.getUgcBriefInfo());
            // 房间通用数据
            ntfBuilder.setPwd(roomModifyNtfReq.getPwd());
            ntfBuilder.setRoomNo(String.valueOf(roomModifyNtfReq.getRoomBriefInfo().getRoomNo()));

            ntfBuilder.addAllMapInfosForRoomMapVote(roomModifyNtfReq.getMapInfosForRoomMapVoteList());
            ntfBuilder.addAllVoteInfos(roomModifyNtfReq.getVoteInfosList());
            ntfBuilder.setFirstUserVoteTime(roomModifyNtfReq.getFirstUserVoteTime());
            ntfBuilder.setRecommendMapAlgoInfo(roomModifyNtfReq.getRecommendMapAlgoInfo());
            ntfBuilder.setUgcMatchRoomExtra(roomModifyNtfReq.getUgcMatchRoomExtra());
            player.getUserAttr().getPlayerPublicLiveStatus().setRoomId(roomModifyNtfReq.getRoomID());
            if (roomModifyNtfReq.getMemberListCount() >= roomModifyNtfReq.getMaxMemberNum()) {
                player.getUserAttr().getPlayerPublicLiveStatus().setRoomIsFull(true);
            }
            ntfBuilder.setModifyExtraInfo(roomModifyNtfReq.getModifyExtraInfo());
            ntfBuilder.addAllUgcBriefInfos(roomModifyNtfReq.getUgcBriefInfosList());
            //Player.updatePrivateRoomStatusOnMemberModify(player, roomModifyNtfReq);

            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMEMBERMODIFYNTF, ntfBuilder);
        }
        Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_update_room, 1);
        // 房间类型自动切换提示 20240602 客户端所有场景都会提示切换，服务器不下发
        /*if (roomModifyNtfReq.getAutoSwitchMatchType()) {
            CsPlayer.PlayerNoticeMsgNtf.Builder noticeMsg = CsPlayer.PlayerNoticeMsgNtf.newBuilder();
            noticeMsg.setType(PlayerNoticeMsgType.PNT_ROOM_MATCH_TYPE_SWITCH);
            MatchType matchConfig = MatchTypeData.getInstance().get(roomModifyNtfReq.getRuleInfo().getMatchTypeId());
            noticeMsg.setNotice(
                    String.format("玩法自动切换为：%s-%s", matchConfig.getDesc(), matchConfig.getButtonDesc()));
            noticeMsg.addParams(matchConfig.getTeamTag());
            noticeMsg.addParams(matchConfig.getDesc());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, noticeMsg);
        }*/
        // 检测时装搭配
        if (beforeMatchType != roomModifyNtfReq.getRuleInfo().getMatchTypeId()
                || roomModifyNtfReq.getModify() == ModifyType.MT_Init) {
            if (MatchTypeConflictOutlookData.getInstance()
                    .checkPlayerOutlookConflict(roomModifyNtfReq.getRuleInfo().getMatchTypeId(),
                            player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList())) {
                CsPlayer.PlayerNoticeMsgNtf.Builder noticeMsg = CsPlayer.PlayerNoticeMsgNtf.newBuilder();
                noticeMsg.setType(PlayerNoticeMsgType.PNT_OUTLOOK_GROUP_CONFLICT_IN_MATCH_TYPE);
                noticeMsg.setNotice(ServerTextConfData.getInstance().getNKErrorText(NKErrorCode.OutLookConflictInAutoChangeBackupFitting.getValue()));
                player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, noticeMsg);
            }
            NKPair<Boolean, Collection<Integer>> outLookReplace = MatchTypeOutlookReplaceData.getInstance()
                    .checkPlayerOutlookReplace(roomModifyNtfReq.getRuleInfo().getMatchTypeId(),
                            player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList(),
                            player.getUserAttr().getPlayerPublicEquipments().getBackupDressUpInfosList());
            // StarP玩法装扮替换
//            if (StarPConfs.isStarPGame(roomModifyNtfReq.getRuleInfo().getMatchTypeId())) {
//                Collection<Integer> moeDressUpList = outLookReplace.getKey() ? outLookReplace.getValue() :
//                        player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList();
//                TcaplusDb.PlayerPublic playerPublic = PlayerPublicDao.getTcaplusPlayerPublic(player.getUid(),
//                        PlayerPublicAttrKey.StarPlayerInfo);
//                if (playerPublic != null && playerPublic.hasStarPlayerInfo()) {
//                    TlogStarPDressUpFlow starPDressUpFlow = TlogStarPDressUpFlow.defaultInstance();
//                    outLookReplace = StarPConfs.checkPlayerOutlookReplace(
//                            roomModifyNtfReq.getRuleInfo().getMatchTypeId(),
//                            moeDressUpList, player.getUid(), playerPublic.getStarPlayerInfo(), starPDressUpFlow);
//                    starPDressUpFlow.logToTlogd();
//                }
//            }
            if (outLookReplace.getKey()) {
                CsPlayer.PlayerNoticeMsgNtf.Builder noticeMsg = CsPlayer.PlayerNoticeMsgNtf.newBuilder();
                noticeMsg.setType(PlayerNoticeMsgType.PNT_OUTLOOK_GROUP_CONFLICT_IN_MATCH_TYPE);
                noticeMsg.setNotice(ServerTextConfData.getInstance().getNKErrorText(NKErrorCode.OutLookConflictInMatchType.getValue()));
                player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, noticeMsg);
            }
        }
        // 遍历找到ntf中的玩家当前的状态，检查是否需要同步一下场景状态
        if (!isDel(roomModifyNtfReq.getModify()) && roomModifyNtfReq.getModify() != ModifyType.MT_ADD
                && roomModifyNtfReq.getModify() != ModifyType.MT_Disband) {
            for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                    roomModifyNtfReq.getObserverMemberListList())) {
                for (MemberBaseInfo memberBaseInfo : memberList) {
                    if (memberBaseInfo.getUid() == player.getUid()) {
                        if (!player.getPlayerStateMgr().sceneStatusCheck(memberBaseInfo.getStatusDetails())) {
                            LOGGER.debug("status details hold by room need to update, player:{} teamId:{}",
                                    player.getUid(), roomModifyNtfReq.getRoomID());
                            updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.StateCheckByNtf);
                            break;
                        }
                    }
                }
            }
        }
    }
    
    public void rpcUgcRoomPlayerRecommendMapNtf(SsGamesvr.RpcUgcRoomPlayerRecommendMapNtfReq recommendMapNtf) {
        // 拼接返回给客户端的数据
        CsUgc.UgcRoomPlayerRecommendMapNtf.Builder ntfBuilder = CsUgc.UgcRoomPlayerRecommendMapNtf.newBuilder();
        ntfBuilder.setRecommendPlayerInfo(recommendMapNtf.getRecommendPlayerInfo());
        ntfBuilder.setUgcMapInfo(recommendMapNtf.getUgcMapInfo());
        // 获取好友亲密度
        int intimacy = (int) player.getFriendManager().getFriendIntimacy(recommendMapNtf.getRecommendPlayerInfo().getUid());
        ntfBuilder.setIntimacy(intimacy);

        player.sendNtfMsg(MsgTypes.MSG_TYPE_UGCROOMPLAYERRECOMMENDMAPNTF, ntfBuilder);
    }
    private void refreshCurrentRoomInfo(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        if (roomModifyNtfReq.getModify() == ModifyType.MT_UnKnown) {
            LOGGER.error("room member ntf with unkown modify type, playerUid:{} room:{} ntf:{}", player.getUid(),
                    player.getUserAttr().getRoomInfo(), roomModifyNtfReq);
            return;
        }
        int roomTypeVal = (int) roomModifyNtfReq.getRoomType();
        switch (roomTypeVal) {
            case RoomType.CompPrelimRoom_VALUE:
            case RoomType.CompPointsRoom_VALUE:
            case RoomType.DefaultRoom_VALUE:
                refreshDefaultRoomInfo(roomModifyNtfReq);
                break;
            case RoomType.CustomRoom_VALUE:
                refreshCustomRoomInfo(roomModifyNtfReq);
                break;
            case RoomType.UgcCustomRoom_VALUE:
                refreshUgcCustomRoomInfo(roomModifyNtfReq);
                break;
            case RoomType.CompElimRoom_VALUE:
            case RoomType.FeatureCompetitionRoom_VALUE:
                refreshCompElimCustomRoomInfo(roomModifyNtfReq);
                break;
            default:
                break;
        }
        // 刷新侧边栏外显信息
        refreshDisplayRoomExtraInfo();
        LOGGER.debug("player current room info after refresh, playerUid:{} teamInfo:{} roomInfo:{}", player.getUid(),
                getCurrentTeamInfoBuidler(), getCurrentRoomInfoBuidler());
        if (player.getCurrentMatchType() != roomModifyNtfReq.getRuleInfo()
                .getMatchTypeId()) {
            player.setCurrentMatchType(roomModifyNtfReq.getRuleInfo().getMatchTypeId());
        }
        this.fixCurrMatchType();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("roomModifyNotify start uid:{}, openid: {}, roomModifyNtfReq: {}", player.getUid(),
                    player.getOpenId(), Pb2JsonUtil.getPbMsg(roomModifyNtfReq));
        }
    }

    private void refreshDefaultRoomInfo(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        // TODO 判断roomId和当前属性系统是否一致
        if (getTeamId() > 0 && roomModifyNtfReq.getModify() != ModifyType.MT_Init
                && roomModifyNtfReq.getRoomID() != getTeamId()) {
            LOGGER.error("player recv room ntf but with mismatched room id, playerUid:{} roomId:{} ntf:{}",
                    player.getUid(), getTeamId(), roomModifyNtfReq);
            return;
        }
        // 先更新内存中的队伍信息
        LOGGER.debug("player room info refresh, playerUid:{} roomModifyNtfReq:{}", player.getUid(), roomModifyNtfReq);
        // 避免旧的队伍逻辑没有透传briefinfo
        if (roomModifyNtfReq.getRoomBriefInfo().getRoomID() > 0) {
            getCurrentTeamInfoBuidler().setRoomBriefInfo(roomModifyNtfReq.getRoomBriefInfo());
        } else {
            LOGGER.warn("room member ntf has no room brief info, ntfReq:{}", roomModifyNtfReq);
            getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder().setRoomID(roomModifyNtfReq.getRoomID());
            getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder()
                    .setRoomType(RoomType.forNumber((int) roomModifyNtfReq.getRoomType()));
            getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder()
                    .setPlayID(roomModifyNtfReq.getRuleInfo().getMatchTypeId());
            getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder()
                    .setMemberNumberLimit(roomModifyNtfReq.getMaxMemberNum());
            getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder()
                    .setCurMemberNumber(roomModifyNtfReq.getMemberNum());
            getCurrentTeamInfoBuidler().getRoomBriefInfoBuilder().setLeaderUid(roomModifyNtfReq.getLeaderID());
        }
        // 根据ntf类型，进行下一步操作
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Init) {
            // 将聊天key添加到attr的房间信息及chatMgr中
            player.getUserAttr().getRoomInfo().getChatGroupKey()
                    .setChatType(roomModifyNtfReq.getChatGroupKey().getType().getNumber())
                    .setId(roomModifyNtfReq.getChatGroupKey().getId())
                    .setSubID(roomModifyNtfReq.getChatGroupKey().getSubId());
            player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
            onInitTeam(roomModifyNtfReq);
            return;
        }
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Disband) {
            PublicRoomMgr.getInstance().removeRoomAfterDisband(getTeamId(),
                    getCurrentTeamInfoBuidler().getRoomBriefInfo().getRoomType());
            onPlayerQuitTeam(roomModifyNtfReq, true);
            return;
        }
        if (isDel(roomModifyNtfReq.getModify())) {
            onPlayerQuitTeam(roomModifyNtfReq, false);
            return;
        }
        // 按照房间内的状态更新玩家状态
        memberList.clear();
        memberList.addAll(roomModifyNtfReq.getMemberListList());
        player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo member : memberList) {
                if (member.getUid() == getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid()) {
                    setRoomLeader(member);
                }
                if (member.getUid() == player.getUid()) {
                    player.getPlayerStateMgr().roomStatusRefresh(member.getState());
                    currStarPInfo = member.getStarPInfo();
                    break;
                }
            }
        }
    }

    public MemberBaseInfo getRoomLeader() {
        return roomLeader;
    }

    /**
     * 获取队伍成员
     * @return List
     */
    public List<MemberBaseInfo> getMemberList() {
        return memberList;
    }

    private boolean isMemberInLobby(MemberBaseInfo member) {
        return member.getStatusDetails().getStatusDetails().getOnlineStatus().getStatus() == PlayerStateType.PST_Online &&
                member.getStatusDetails().getStatusDetails().getSceneStatus().getStatus() == PlayerStateType.PST_Lobby;
    }

    /**
     * 获取在广场的队伍成员除了自己，队长在第一个
     * @return List
     */
    public List<MemberBaseInfo> getOtherMemberListInLobby() {
        List<MemberBaseInfo> otherList = new ArrayList<>();
        if (roomLeader != null && isMemberInLobby(roomLeader)) {
            otherList.add(roomLeader);
        }
        for (MemberBaseInfo member : memberList) {
            if (isMemberInLobby(member) && member.getUid() != player.getUid() &&
                    (roomLeader == null || member.getUid() != roomLeader.getUid())) {
                otherList.add(member);
            }
        }
        return otherList;
    }

    private void refreshCustomRoomInfo(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        if (getRoomId() > 0 && roomModifyNtfReq.getModify() != ModifyType.MT_Init
                && roomModifyNtfReq.getRoomID() != getRoomId()) {
            LOGGER.error("player recv room ntf but with mismatched room id, playerUid:{} roomId:{} ntf:{}",
                    player.getUid(), getRoomId(), roomModifyNtfReq);
            return;
        }
        // 先更新内存中的队伍信息
        LOGGER.debug("player room info refresh, playerUid:{} roomModifyNtfReq:{}", player.getUid(), roomModifyNtfReq);
        getCurrentRoomInfoBuidler().setRoomBriefInfo(roomModifyNtfReq.getRoomBriefInfo());
        // 通用初始化
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Init) {
            // 将聊天key添加到attr的房间信息及chatMgr中
            player.getUserAttr().getRoomInfo().getChatGroupKey()
                    .setChatType(roomModifyNtfReq.getChatGroupKey().getType().getNumber())
                    .setId(roomModifyNtfReq.getChatGroupKey().getId())
                    .setSubID(roomModifyNtfReq.getChatGroupKey().getSubId());
            player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
            onInitRoom(roomModifyNtfReq);
            return;
        }
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Disband) {
            PublicRoomMgr.getInstance().removeRoomAfterDisband(roomModifyNtfReq.getRoomID(),
                    getCurrentRoomInfoBuidler().getRoomBriefInfo().getRoomType());
            onPlayerQuitRoom(roomModifyNtfReq, true);
            return;
        }
        if (isDel(roomModifyNtfReq.getModify())) {
            for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                    roomModifyNtfReq.getObserverMemberListList())) {
                for (MemberBaseInfo memberBaseInfo : memberList) {
                    if (memberBaseInfo.getUid() == player.getUid()) {
                        onPlayerQuitRoom(roomModifyNtfReq, false);
                        return;
                    }
                }
            }
        }
        // 按照房间内的状态更新玩家状态
        player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo member : memberList) {
                if (member.getUid() == player.getUid()) {
                    player.getPlayerStateMgr().roomStatusRefresh(member.getState());
                    break;
                }
            }
        }
    }

    private void refreshUgcCustomRoomInfo(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        if (getRoomId() > 0 && roomModifyNtfReq.getModify() != ModifyType.MT_Init
                && roomModifyNtfReq.getRoomID() != getRoomId()) {
            LOGGER.error("player recv room ntf but with mismatched room id, playerUid:{} roomId:{} ntf:{}",
                    player.getUid(), getRoomId(), roomModifyNtfReq);
            return;
        }
        // 先更新内存中的队伍信息
//        LOGGER.debug("player room info refresh, playerUid:{} roomModifyNtfReq:{}", player.getUid(), roomModifyNtfReq);
        getCurrentRoomInfoBuidler().setRoomBriefInfo(roomModifyNtfReq.getRoomBriefInfo());
        getCurrentRoomInfoBuidler().getRoomUgcInfoBuilder().setMapId(roomModifyNtfReq.getUgcBriefInfo().getUgcId());
        // 通用初始化
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Init) {
            // 将聊天key添加到attr的房间信息及chatMgr中
            player.getUserAttr().getRoomInfo().getChatGroupKey()
                    .setChatType(roomModifyNtfReq.getChatGroupKey().getType().getNumber())
                    .setId(roomModifyNtfReq.getChatGroupKey().getId())
                    .setSubID(roomModifyNtfReq.getChatGroupKey().getSubId());
            player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
            onInitRoom(roomModifyNtfReq);
        }
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Disband) {
            PublicRoomMgr.getInstance().removeRoomAfterDisband(roomModifyNtfReq.getRoomID(),
                    getCurrentRoomInfoBuidler().getRoomBriefInfo().getRoomType());
            onPlayerQuitRoom(roomModifyNtfReq, true);
            return;
        }
        if (isDel(roomModifyNtfReq.getModify())) {
            for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                    roomModifyNtfReq.getObserverMemberListList())) {
                for (MemberBaseInfo memberBaseInfo : memberList) {
                    if (memberBaseInfo.getUid() == player.getUid()) {
                        LOGGER.debug("player clear room info when del, playerUid:{}", player.getUid());
                        onPlayerQuitRoom(roomModifyNtfReq, false);
                        return;
                    }
                }
            }
        }
        // 按照房间内的状态更新玩家状态
        player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo member : memberList) {
                if (member.getUid() == player.getUid()) {
                    player.getPlayerStateMgr().roomStatusRefresh(member.getState());
                    break;
                }
            }
        }
    }

    private void refreshCompElimCustomRoomInfo(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        if (roomModifyNtfReq.getModify() != ModifyType.MT_Init
                && roomModifyNtfReq.getRoomID() != getRoomId()) {
            LOGGER.error("player recv room ntf but with mismatched room id, playerUid:{} roomId:{} ntf:{}",
                    player.getUid(), getRoomId(), roomModifyNtfReq);
            return;
        }
        // 先更新内存中的队伍信息
        LOGGER.debug("player room info refresh, playerUid:{} roomModifyNtfReq:{}", player.getUid(), roomModifyNtfReq);
        getCurrentRoomInfoBuidler().setRoomBriefInfo(roomModifyNtfReq.getRoomBriefInfo());
        getCurrentRoomInfoBuidler().setCompElimRoomInfo(roomModifyNtfReq.getCompElimRoomInfo());
        // 通用初始化
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Init) {
            // 将聊天key添加到attr的房间信息及chatMgr中
            player.getUserAttr().getRoomInfo().getChatGroupKey()
                    .setChatType(roomModifyNtfReq.getChatGroupKey().getType().getNumber())
                    .setId(roomModifyNtfReq.getChatGroupKey().getId())
                    .setSubID(roomModifyNtfReq.getChatGroupKey().getSubId());
            player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
            onInitRoom(roomModifyNtfReq);
        }
        if (roomModifyNtfReq.getModify() == ModifyType.MT_Disband) {
            PublicRoomMgr.getInstance().removeRoomAfterDisband(roomModifyNtfReq.getRoomID(),
                    getCurrentRoomInfoBuidler().getRoomBriefInfo().getRoomType());
            onPlayerQuitRoom(roomModifyNtfReq, true);
            return;
        }
        if (isDel(roomModifyNtfReq.getModify())) {
            for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                    roomModifyNtfReq.getObserverMemberListList())) {
                for (MemberBaseInfo memberBaseInfo : memberList) {
                    if (memberBaseInfo.getUid() == player.getUid()) {
                        LOGGER.debug("player clear room info when del, playerUid:{}", player.getUid());
                        onPlayerQuitRoom(roomModifyNtfReq, false);
                        return;
                    }
                }
            }
        }
        // 按照房间内的状态更新玩家状态
        player.getPlayerChatManager().addChatGroupKey(roomModifyNtfReq.getChatGroupKey());
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo member : memberList) {
                if (member.getUid() == player.getUid()) {
                    player.getPlayerStateMgr().roomStatusRefresh(member.getState());
                    break;
                }
            }
        }
    }

    private void onInitTeam(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        player.getUserAttr().getRoomInfo().getTeamInfo().setTeamId(roomModifyNtfReq.getRoomID());
        // 流水相关逻辑
        this.roomMemberCount = roomModifyNtfReq.getMemberListCount();
        String memberStr = "";
        MemberBaseInfo curPlayer = player.getMemberBaseInfo();
        int offlinePlayerCnt = 0;
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo memberBaseInfo : memberList) {
                // 过滤掉队伍及房间内的机器人
                if (memberBaseInfo.getIsRobot()) {
                    continue;
                }
                memberStr += memberBaseInfo.getUid() + "," + player.getFriendManager()
                        .getRelationTypeVal(memberBaseInfo.getUid()) + ";";
                if (memberBaseInfo.getUid() == curPlayer.getUid()) {
                    curPlayer = memberBaseInfo;
                }
                if (memberBaseInfo.getState() == PlayerStateType.PST_Offline) {
                    offlinePlayerCnt++;
                }
            }
        }
        currStarPInfo = curPlayer.getStarPInfo();
        // 第一次加入队伍，肯定超过一个人，同时有加入原因
        if (roomModifyNtfReq.getMemberListCount() != 1 && curPlayer.getJoinReason() > 0) {
            // 加入房间
            TlogFlowMgr.sendTeamFlow(player, roomModifyNtfReq.getRoomID(), TEAM_OP_TYPE.TEAM_JOIN_REQ,
                    curPlayer.getJoinReason(),
                    memberStr, getCurrentTeamInfoBuidler(), offlinePlayerCnt);
            // 安全流水
            MemberBaseInfo.Builder leader = getRoomLeader(roomModifyNtfReq.getRoomID());
            TlogFlowMgr.sendSecRoomFlow(player, 2, roomModifyNtfReq.getRoomID(), roomModifyNtfReq.getMemberListCount(),
                    RoomType.DefaultRoom_VALUE, "", "", leader.getOpenId(), leader.getUid());
        }
        // 当前队伍包含自动邀请机器人，需要同步数据
        if (roomModifyNtfReq.getRoomBriefInfo().getAiTypesList()
                .contains(RobotSourceType.RST_AutoInvitationTrigger_VALUE)) {
            updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.TeamAIInfoUpdate);
        }
        player.getPlayerStateMgr().roomStatusRefresh(curPlayer.getState());
        player.getPlayerStarPMgr().noticeTeamIdChange(getTeamMatchTypeId(), roomModifyNtfReq.getRoomID(),
                currStarPInfo.getCommonInfo().getStarPWorldId());
    }

    private void onInitRoom(RpcRoomMemberModifyNtfReq roomModifyNtfReq) {
        LOGGER.debug("onInitRoom uid:{} roomId:{} briefRoomId:{}", player.getUid(), roomModifyNtfReq.getRoomID(), roomModifyNtfReq.getRoomBriefInfo().getRoomID());
        // 玩家加入就算历史退出房间
        recordRoomIdPlayerLeft(roomModifyNtfReq.getRoomBriefInfo().getRoomID());
        player.getUserAttr().getRoomInfo().getCustomRoomInfo().setRoomId(roomModifyNtfReq.getRoomID());
        player.getUserAttr().getRoomInfo().getCustomRoomInfo().setRoomTypeVal(
                roomModifyNtfReq.getRoomBriefInfo().getRoomType().getNumber());
        // 流水相关逻辑
        this.roomMemberCount = roomModifyNtfReq.getMemberListCount();
        String memberStr = "";
        MemberBaseInfo curPlayer = player.getMemberBaseInfo();
        int offlinePlayerCnt = 0;
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo memberBaseInfo : memberList) {
                // 过滤掉队伍及房间内的机器人
                if (memberBaseInfo.getIsRobot()) {
                    continue;
                }
                memberStr += memberBaseInfo.getUid() + "," + player.getFriendManager()
                        .getRelationTypeVal(memberBaseInfo.getUid()) + ";";
                if (memberBaseInfo.getUid() == curPlayer.getUid()) {
                    curPlayer = memberBaseInfo;
                }
                if (memberBaseInfo.getState() == PlayerStateType.PST_Offline) {
                    offlinePlayerCnt++;
                }
            }
        }
        // 第一次加入队伍，肯定超过一个人，同时有加入原因
        if (roomModifyNtfReq.getMemberListCount() != 1 && curPlayer.getJoinReason() > 0) {
            // 加入房间
            TlogFlowMgr.sendTeamFlow(player, roomModifyNtfReq.getRoomID(), TEAM_OP_TYPE.TEAM_JOIN_REQ,
                    curPlayer.getJoinReason(),
                    memberStr, getCurrentRoomInfoBuidler(), offlinePlayerCnt,
                    roomModifyNtfReq.getState() == RoomStateType.RST_Battle);
            // 安全流水
            MemberBaseInfo.Builder leader = getRoomLeader(roomModifyNtfReq.getRoomID());
            TlogFlowMgr.sendSecRoomFlow(player, 2, roomModifyNtfReq.getRoomID(), roomModifyNtfReq.getMemberListCount(),
                    roomModifyNtfReq.getRoomBriefInfo().getRoomType().getNumber(), "", "",
                    leader.getOpenId(), leader.getUid());
        }
        player.getPlayerStateMgr().roomStatusRefresh(curPlayer.getState());
    }

    private void onPlayerQuitTeam(RpcRoomMemberModifyNtfReq roomModifyNtfReq, boolean isDisband) {
        boolean selfLeave = false;
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo memberBaseInfo : memberList) {
                if (memberBaseInfo.getUid() == player.getUid()) {
                    selfLeave = true;
                    break;
                }
            }
        }
        if (selfLeave || isDisband) {
            LOGGER.debug("clear room info when recv quit ntf, playerUid:{} ntf:{}", player.getUid(), roomModifyNtfReq);
            memberList.clear();
            // 离开房间流水
            UniversalRoom.Builder flowRoomInfoBuilder = UniversalRoom.newBuilder();
            flowRoomInfoBuilder.getRoomBriefInfoBuilder().mergeFrom(roomModifyNtfReq.getRoomBriefInfo());

            int teamOp = TEAM_OP_TYPE.TEAM_LEAVE_REQ;
            int opType = 3;
            if (roomModifyNtfReq.getModify() == ModifyType.MT_SystemKick) {
                teamOp = TEAM_OP_TYPE.TEAM_SYSTEM_DEL_MEM_REQ;
            } else if (roomModifyNtfReq.getModify() == ModifyType.MT_Disband) {
                teamOp = TEAM_OP_TYPE.TEAM_DEL_REQ;
                opType = 4;
            }
            TlogFlowMgr.sendTeamFlow(player, roomModifyNtfReq.getRoomID(), teamOp, 0, "",
                    flowRoomInfoBuilder, roomModifyNtfReq.getRoomTFlowInfo().getOfflineMemberCnt());
            MemberBaseInfo.Builder leader = getRoomLeader(roomModifyNtfReq.getRoomID());
            TlogFlowMgr.sendSecRoomFlow(player, opType, roomModifyNtfReq.getRoomID(), getRoomMemberCount(roomModifyNtfReq.getRoomID()),
                    RoomType.DefaultRoom_VALUE, "", "", leader.getOpenId(), leader.getUid());
            player.getPlayerStarPMgr().noticeTeamIdChange(getTeamMatchTypeId(), 0, currStarPInfo.getCommonInfo().getStarPWorldId());
            // 清理内存
            getCurrentTeamInfoBuidler().clear();
            getAiInvitationComponent().clearInvitation(roomModifyNtfReq.getRoomID());
            // 清理属性系统
            player.getUserAttr().getRoomInfo().getTeamInfo().clear();
            triggerPlayerStatusFlowWhenPlayerQuit();
            currStarPInfo = StarPTeamUserInfo.getDefaultInstance();

            return;
        } else {
            StringBuilder memberListStr = new StringBuilder();
            for (MemberBaseInfo memberBaseInfo : roomModifyNtfReq.getMemberListList()) {
                memberList.removeIf(e -> e.getUid() == memberBaseInfo.getUid());
                memberListStr.append(memberBaseInfo.getUid());
                memberListStr.append(",");
            }
            if (memberListStr.length() > 0) {
                memberListStr.deleteCharAt(memberListStr.length() - 1);
            }
            LOGGER.debug("remove memberList when recv quit ntf, playerUid:{} members:{} ntf:{}",
                    player.getUid(), memberListStr, roomModifyNtfReq);
            if (roomModifyNtfReq.getRoomBriefInfo().getAiTypesList().size() == 0) {
                getAiInvitationComponent().clearInvitation(roomModifyNtfReq.getRoomID());
            }
        }
        // 其他玩家离开，导致队伍还剩一个人也需要重置状态
        triggerPlayerStatusFlowWhenPlayerQuit();
    }

    private void onPlayerQuitRoom(RpcRoomMemberModifyNtfReq roomModifyNtfReq, boolean isDisband) {
        boolean selfLeave = false;
        for (List<MemberBaseInfo> memberList : List.of(roomModifyNtfReq.getMemberListList(),
                roomModifyNtfReq.getObserverMemberListList())) {
            for (MemberBaseInfo memberBaseInfo : memberList) {
                if (memberBaseInfo.getUid() == player.getUid()) {
                    selfLeave = true;
                    break;
                }
            }
        }
        if (selfLeave || isDisband) {
            LOGGER.debug("clear room info when recv quit ntf, playerUid:{} ntf:{}", player.getUid(), roomModifyNtfReq);
            // 离开房间流水
            UniversalRoom.Builder flowRoomInfoBuilder = UniversalRoom.newBuilder();
            flowRoomInfoBuilder.getRoomBriefInfoBuilder().mergeFrom(roomModifyNtfReq.getRoomBriefInfo());

            int teamOp = TEAM_OP_TYPE.TEAM_LEAVE_REQ;
            int opType = 3;
            if (roomModifyNtfReq.getModify() == ModifyType.MT_SystemKick) {
                teamOp = TEAM_OP_TYPE.TEAM_SYSTEM_DEL_MEM_REQ;
            } else if (roomModifyNtfReq.getModify() == ModifyType.MT_Disband) {
                teamOp = TEAM_OP_TYPE.TEAM_DEL_REQ;
                opType = 4;
            }
            TlogFlowMgr.sendTeamFlow(player, roomModifyNtfReq.getRoomID(), teamOp, 0, "",
                    flowRoomInfoBuilder, 0,
                    roomModifyNtfReq.getState() == RoomStateType.RST_Battle);
            MemberBaseInfo.Builder leader = getRoomLeader(roomModifyNtfReq.getRoomID());
            TlogFlowMgr.sendSecRoomFlow(player, opType, roomModifyNtfReq.getRoomID(), getRoomMemberCount(roomModifyNtfReq.getRoomID()),
                    roomModifyNtfReq.getRoomBriefInfo().getRoomType().getNumber(), "", "",
                    leader.getOpenId(), leader.getUid());

            // 清理内存
            getCurrentRoomInfoBuidler().clear();
            // 清理属性系统
            player.getUserAttr().getRoomInfo().getCustomRoomInfo().clear();
            triggerPlayerStatusFlowWhenPlayerQuit();
            return;
        }
        // 其他玩家离开，导致队伍还剩一个人也需要重置状态
        triggerPlayerStatusFlowWhenPlayerQuit();
    }

    private boolean isDel(ModifyType type) {
        return transferModifyType(type) == ModifyType.MT_DEL;
    }

    private ModifyType transferModifyType(ModifyType type) {
        // 非退出类型 直接返回
        if (type != ModifyType.MT_DEL && type != ModifyType.MT_Kick && type != ModifyType.MT_SystemKick) {
            return type;
        }
        return ModifyType.MT_DEL;
    }

    private void triggerPlayerStatusFlowWhenPlayerQuit() {
        player.getPlayerStateMgr().clearRoomStatus();
        // 先清理数据再刷新状态数据
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.ROOM_MODIFY);
    }

    public NKErrorCode roomReady(long roomId, boolean readyOrNot) {
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcReadyMatchReq.Builder req = SsRoomsvr.RpcReadyMatchReq.newBuilder();
            req.setRoomId(currentRoomId);
            req.setUid(player.getUid());
            req.setCancelReady(readyOrNot ? 0 : 1);
            RpcResult<SsRoomsvr.RpcReadyMatchRes.Builder> rpcResult = RoomService.get().rpcReadyMatch(req);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(currentRoomId, "ready match but room not exist");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    private NKErrorCode canPlayerJoinOtherTeams() {
        if (player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Matching ||
                player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_TeamReady ||
                player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Battle) {
            return NKErrorCode.RoomCantJoinCauseInBattle;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomInvitationMsgNotify(SsGamesvr.RpcRoomInvitationMsgNtfReq roomInvitationMsgNtfReq) {
        if (player.getPlayerStateMgr().isStealth() || !player.getPlayerStateMgr().isOnline()) {
            return NKErrorCode.RoomPlayerCantInvited;
        }
        if (player.forbiddenStrangerRoomInvitation() && !player.getFriendManager()
                .isFriend(roomInvitationMsgNtfReq.getInviter().getUid())) {
            LOGGER.info("player forbidden stranger room invitation, player:{}, invitor:{}", player.getUid(),
                    roomInvitationMsgNtfReq.getInviter().getUid());
            return NKErrorCode.RoomCannotInviteCausePlayerForbiddenStrangerInvite;
        }
        if (player.getUserAttr().getXiaoWoInfo().getVisitXiaoWoInfo().getCurrentXiaoWoType()
                == XiaoWoType.XT_SampleRoom_VALUE) {
            // 在样板间内的玩家不能被邀请去组队
            return NKErrorCode.RoomPlayerCantInvited;
        }
        if ((player.getPlayerFarmMgr().getCurrentFarmId() != 0
                || player.getPlayerHouseMgr().getCurrentHouseId() != 0
                || player.getPlayerCookMgr().getCurrentCookId() != 0)
                &&
                BaseGenerator.getUidType(roomInvitationMsgNtfReq.getRoomID()) == GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
            // 在农场内的玩家不能被邀请去房间组队
            return NKErrorCode.FarmCannotInvite;
        }
        if (player.getMatchIsolateID() == MatchIsolateType.MIT_CHEAT_VALUE) {
            LOGGER.info("player cannot be invited when match isolated, player:{}", player.getUid());
            NKErrorCode.RoomCannotBeInvitedCauseIsolated.throwError("isolated");
        }
        var matchTypeId = roomInvitationMsgNtfReq.getRuleInfo().getMatchTypeId();
        // 判断当前被邀请玩家的登录平台是否被该玩法屏蔽
        PlayerClientInfo clientInfo = player.getClientInfoBuilder().build();
        if (roomInvitationMsgNtfReq.getRoomType() == (long) RoomType.CustomRoom_VALUE && CustomRoomData.getInstance()
                .isPlayIdBlocked(matchTypeId, clientInfo.getLoginPlat(), clientInfo.getDevicePlat(),
                        clientInfo.getCloudGamePlat())) {
            return NKErrorCode.RoomInviteFailWhenPlayBlockedByClientInfo;
        }
        // 啾灵世界中不能邀请非啾灵队伍邀请
        NKErrorCode starPRet = checkStarPInvite(roomInvitationMsgNtfReq.getIst());
        if (starPRet.hasError()) {
            return starPRet;
        }
        // 检查与邀请者的版本号关系
        var inviterPlayVersion = player.getPlayVersion(matchTypeId, roomInvitationMsgNtfReq.getInviter());
        var inviteePlayVersion = player.getPlayVersion(matchTypeId);
        // 临时开关-邀请的兼容组检查
        boolean tempCompatibleSwitchOfInvitation = PropertyFileReader.getRealTimeBooleanItem(
                "temp_compatible_switch_of_invitation", false);
        if (tempCompatibleSwitchOfInvitation) {
            int battleVersionGroupOfInviter = VersionCompBattleConf.getInstance()
                    .getBattleVersionGroup(inviterPlayVersion, matchTypeId);
            int battleVersionGroupOfInvitee = VersionCompBattleConf.getInstance()
                    .getBattleVersionGroup(inviteePlayVersion, matchTypeId);
            if (battleVersionGroupOfInviter != battleVersionGroupOfInvitee) {
                LOGGER.debug("inviter play version compatible group mis-match with invitee, inviter:{} invitee:{}",
                        battleVersionGroupOfInviter, battleVersionGroupOfInvitee);
                return NKErrorCode.RoomClientVersionMismatch;
            }
        } else {
            if (inviterPlayVersion < inviteePlayVersion) {
                LOGGER.debug(
                        "version error, matchTypeId:{} inviterUid:{} inviterPlayVersion:{} inviteeUid:{} inviteePlayVersion:{}",
                        matchTypeId, roomInvitationMsgNtfReq.getInviter().getUid(),
                        VersionUtil.decodeClientVersion(inviterPlayVersion),
                        player.getUid(), VersionUtil.decodeClientVersion(inviteePlayVersion));
                return NKErrorCode.RoomClientVersionLowerThanLeader;
            } else if (inviterPlayVersion > inviteePlayVersion) {
                LOGGER.debug(
                        "version error, matchTypeId:{} inviterUid:{} inviterPlayVersion:{} inviteeUid:{} inviteePlayVersion:{}",
                        matchTypeId, roomInvitationMsgNtfReq.getInviter().getUid(),
                        VersionUtil.decodeClientVersion(inviterPlayVersion),
                        player.getUid(), VersionUtil.decodeClientVersion(inviteePlayVersion));
                return NKErrorCode.RoomClientVersionHigherThanLeader;
            }
        }
        NKErrorCode cantJoinReason = canPlayerJoinOtherTeams();
        if (cantJoinReason != NKErrorCode.OK) {
            LOGGER.debug("Received invitation from room:{} uid:{}, but can't reply",
                    roomInvitationMsgNtfReq.getRoomID(), roomInvitationMsgNtfReq.getInviter());
            return cantJoinReason;
        }
        // 判断邀请者是否被受邀者拉黑
        if (player.getBlackManager().isBlack(roomInvitationMsgNtfReq.getInviter().getUid())) {
            LOGGER.info("player receive invitation from other player but blacked, player:{}, room:{}, invitor:{}",
                    player.getUid(), roomInvitationMsgNtfReq.getRoomID(), roomInvitationMsgNtfReq.getInviter().getUid());
            return NKErrorCode.OK;
        }
        MatchType matchType = MatchTypeData.getInstance().get(matchTypeId);
        if (matchType == null) {
            LOGGER.error("match type config from invitation is empty, playerUid:{} roomId:{} matchTypeId:{}", player.getUid(),
                    roomInvitationMsgNtfReq.getRoomID(), matchTypeId);
            return NKErrorCode.ResNotFound;
        }
        if ((matchType.getSettleProc() == MTSC_Rank && player.checkIsBanLadder()) ||
                player.checkIsBanGameMode(matchTypeId)) {
            return NKErrorCode.RoomModePlayerIsBanLadder;
        }
        NKErrorCode ret = player.getPlayerStateMgr().checkConfirm();
        if (ret.hasError()) {
            return ret;
        }

        LOGGER.info("player receive invitation from other player, player:{}, room:{}, invitor:{}", player.getUid(),
                roomInvitationMsgNtfReq.getRoomID(), roomInvitationMsgNtfReq.getInviter().getUid());
        // 获取好友亲密度
        int intimacy = 0;
        intimacy = (int) player.getFriendManager().getFriendIntimacy(roomInvitationMsgNtfReq.getInviter().getUid());
        CsRoom.RoomInvitationNtf.Builder ntfBuilder = CsRoom.RoomInvitationNtf.newBuilder();
        ntfBuilder.setInviterInfo(roomInvitationMsgNtfReq.getInviter());
        ntfBuilder.setRoomID(roomInvitationMsgNtfReq.getRoomID());
        ntfBuilder.setRoomType(roomInvitationMsgNtfReq.getRoomType());
        ntfBuilder.setIntimacy(intimacy);
        ntfBuilder.setRuleInfo(roomInvitationMsgNtfReq.getRuleInfo());
        ntfBuilder.setIst(roomInvitationMsgNtfReq.getIst());
        ntfBuilder.setRoomBriefInfo(roomInvitationMsgNtfReq.getRoomBriefInfo());
        ntfBuilder.setRoomUgcInfo(roomInvitationMsgNtfReq.getRoomUgcInfo());
        ntfBuilder.setTargetRoleType(roomInvitationMsgNtfReq.getTargetRoleType());
        ntfBuilder.setBattleID(roomInvitationMsgNtfReq.getBattleId());
        ntfBuilder.setSendTimestampMs(DateUtils.currentTimeMillis());

        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMINVITATIONNTF, ntfBuilder);
        return NKErrorCode.OK;
    }

    public void roomCancelResultMsgNotify(SsGamesvr.RpcRoomCancelResultNtfReq roomCancelResultNtfReq) {

        CsRoom.RoomCancelResultNtf.Builder ntfBuilder = CsRoom.RoomCancelResultNtf.newBuilder();
        ntfBuilder.setRoomId(roomCancelResultNtfReq.getRoomId());
        if (NKErrorCode.OK.getValue() != roomCancelResultNtfReq.getResult()) {
            player.sendError(MsgTypes.MSG_TYPE_ROOMCANCELRESULTNTF, roomCancelResultNtfReq.getResult());
        } else {
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMCANCELRESULTNTF, ntfBuilder);
            //player.updatePrivateRoomStatus(player, RoomStatus.RS_TEAM_VALUE);
        }

    }

    /**
     * 修改匹配中的队伍信息请求处理完成后的回调通知
     * @param req 通知消息体
     */
    public void onModifyMatchingTeamInfoResultNtf(SsGamesvr.RpcGameModifyMatchingTeamInfoResultNtfReqOrBuilder req) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} recv modify result {} room id {} req id {}",
                    player.getUid(), req.getResult(), req.getRoomId(), req.getRequesterId());
        }

        CsRoom.RoomModifyMatchingTeamInfoResultNtf.Builder ntfBuilder = CsRoom.RoomModifyMatchingTeamInfoResultNtf.newBuilder()
                .setRoomId(req.getRoomId())
                .setRequesterId(req.getRequesterId());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMODIFYMATCHINGTEAMINFORESULTNTF, ntfBuilder);
    }

    public void roomStartBattleNotify(SsGamesvr.RpcStartBattleNtfReq roomStartBattleNtfReq) {
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.ENTER_BATTLE);
        if(player.getPlayerLobbyMgr().getLobbyId() > 0) {
            LOGGER.info("roomStartBattleNotify uid:{} battleId:{} lobbyId:{}", player.getUid(), roomStartBattleNtfReq.getBattleId(), player.getPlayerLobbyMgr().getLobbyId());
        }
        else {
            LOGGER.info("roomStartBattleNotify uid:{} battleId:{}", player.getUid(), roomStartBattleNtfReq.getBattleId());
        }

        if(GSConfig.setBattleIdOnRoomStartBattle()) {
            player.getUserAttr().getBattleInfo().setBattleid(roomStartBattleNtfReq.getBattleId());
            player.getUserAttr().getBattleInfo().setRelatedRoomId(roomStartBattleNtfReq.getRoomId());
        }

        CsRoom.RoomStartBattleNtf.Builder ntfBuilder = CsRoom.RoomStartBattleNtf.newBuilder();
        ntfBuilder.setBattleId(roomStartBattleNtfReq.getBattleId())
                .setGlobalGroupKey(roomStartBattleNtfReq.getGlobalGroupKey())
                .setSideGroupKey(roomStartBattleNtfReq.getSideGroupKey())
                .setRuleInfo(roomStartBattleNtfReq.getRuleInfo())
                .setEnableSecTlog(GSConfig.getEnableSecTlog())
                .addAllBattlePlayerClientInfos(roomStartBattleNtfReq.getBattlePlayerClientInfosList());

        if (NKErrorCode.OK.getValue() != roomStartBattleNtfReq.getResult()) {
            player.sendError(MsgTypes.MSG_TYPE_ROOMSTARTBATTLENTF, roomStartBattleNtfReq.getResult());
        } else {
            // tlog 流水
            TlogFlowMgr.sendPlayerBattleBeginFlow(player, roomStartBattleNtfReq);
            // ugc流水
            long ugcId = Math.max(roomStartBattleNtfReq.getUgcBriefInfo().getUgcId(),
                    roomStartBattleNtfReq.getRuleInfo().getUgcId());
            if (ugcId > 0) {
                if (roomStartBattleNtfReq.hasUgcBriefInfo()) {
                    player.getPlayerUgcManager()
                            .cacheUgcMapTopics(roomStartBattleNtfReq.getUgcBriefInfo().getTopicsList());
                }
                player.getPlayerUgcManager().refreshBattleUgcId(ugcId);
                player.getPlayerUgcManager().ugcBattleStar(ugcId, player.getCreatorId());
                UgcMapEnjoyBeginData enjoyBeginFlowData = new UgcMapEnjoyBeginData(ugcId,
                        roomStartBattleNtfReq.getBattleId());
                MatchRuleInfo matchRuleInfo = roomStartBattleNtfReq.getMemberBaseInfo().getRuleInfo();
                // 处理下新的上报方式
                MatchRuleClientInfo clientInfo = matchRuleInfo.getClientInfo();
                enjoyBeginFlowData.canvasId = clientInfo.getCanvasId();
                enjoyBeginFlowData.isMidJoin = roomStartBattleNtfReq.getMemberBaseInfo().getIsMidJoin();
                enjoyBeginFlowData.matchId = roomStartBattleNtfReq.getMatchId();
                enjoyBeginFlowData.battleType = roomStartBattleNtfReq.getRuleInfo().getMatchTypeId();
                enjoyBeginFlowData.gameSource = TlogFlowMgr.getUgcMapBattleGameSource(
                        roomStartBattleNtfReq.getCreateOps().getMapSource(),
                        roomStartBattleNtfReq.getRuleInfo().getMatchTypeId(), matchRuleInfo);
                enjoyBeginFlowData.memberUidList = roomStartBattleNtfReq.getRoomMemberUidsList();
                enjoyBeginFlowData.algo = roomStartBattleNtfReq.getAlgoInfo();
                enjoyBeginFlowData.setFromRoomCreateOptions(roomStartBattleNtfReq.getCreateOps());
                enjoyBeginFlowData.roomId = roomStartBattleNtfReq.getRoomId();
                enjoyBeginFlowData.mapPoolId = roomStartBattleNtfReq.getUgcMatchCompilations().getMapPoolId();
                enjoyBeginFlowData.lobbyType = matchRuleInfo.getClientInfo().getLobbyType();
                enjoyBeginFlowData.lobbyMapId = matchRuleInfo.getClientInfo().getLobbyMapId();
                enjoyBeginFlowData.tabDesc = clientInfo.getTabDesc();
                enjoyBeginFlowData.searchID = matchRuleInfo.getClientInfo().getSearchID();
                enjoyBeginFlowData.subTabName = matchRuleInfo.getClientInfo().getSubTabName();
                enjoyBeginFlowData.joinReason = roomStartBattleNtfReq.getMemberBaseInfo().getJoinReason();
                enjoyBeginFlowData.midJoinType = CommonUtil.convertJoinReasonToMidJoinSourceType(
                        enjoyBeginFlowData.matchId, enjoyBeginFlowData.isMidJoin, enjoyBeginFlowData.joinReason);
                if (roomStartBattleNtfReq.getRuleInfo().getGameModeType()
                        == GameModeType.GMT_UgcTest) { //UGC多人测试，用的非发布地图
                    TlogFlowMgr.sendUgcTestMapEnjoyBeginFlow(player, enjoyBeginFlowData);
                } else {
                    TlogFlowMgr.sendUgcMapEnjoyBeginFlow(player, enjoyBeginFlowData);
                }
            }

            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMSTARTBATTLENTF, ntfBuilder);
            // 开局事件
            player.getActivitySquadMgr().triggerBattleBegin(roomStartBattleNtfReq.getRuleInfo(),
                    roomStartBattleNtfReq.getRoomMemberUidsList());

            // 对局开始时设置对局聊天上报信息到player身上
            BattleChatReportInfo reportInfo = new BattleChatReportInfo(roomStartBattleNtfReq.getRuleInfo().getMatchTypeId(),
                    roomStartBattleNtfReq.getBattleId());
            player.setBattleChatReportInfo(reportInfo);

            if (wolfKillCheck(roomStartBattleNtfReq.getRuleInfo().getMatchTypeId())) {
                player.getWolfKillMgr().checkComeBackSystemOpen();
                player.getWolfKillMgr().updateLatestTs(DateUtils.currentTimeMillis());
            }

            if (HokConfs.isInHokType(roomStartBattleNtfReq.getRuleInfo().getMatchTypeId())){
                player.getHokMgr().roomStartBattle(roomStartBattleNtfReq.getRuleInfo().getMatchTypeId(), roomStartBattleNtfReq.getRuleInfo());
            }
        }

    }

    public void roomMatchSceneCreateSuccNtf(SsGamesvr.RpcMatchSceneCreateSuccReq matchSceneCreateSuccReq) {
        if (NKErrorCode.OK.getValue() != matchSceneCreateSuccReq.getResult()) {
            player.sendError(MsgTypes.MSG_TYPE_MATCHSCENECREATESUCCNTF, matchSceneCreateSuccReq.getResult());
        } else {
            player.getPlayerSceneMgr().setRoundId(matchSceneCreateSuccReq.getRoundId());
            CsRoom.MatchSceneCreateSuccNtf.Builder ntfBuilder = CsRoom.MatchSceneCreateSuccNtf.newBuilder();
            ntfBuilder.setResult(matchSceneCreateSuccReq.getResult())
                    .setRoundId(matchSceneCreateSuccReq.getRoundId())
                    .setSceneAddr(matchSceneCreateSuccReq.getSceneAddr());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_MATCHSCENECREATESUCCNTF, ntfBuilder);
        }
    }

    public void roomMatchJoinSuccNtf(SsGamesvr.RpcMatchJoinSuccReq matchJoinSuccReq) {
        if (NKErrorCode.OK.getValue() != matchJoinSuccReq.getResult()) {
            player.sendError(MsgTypes.MSG_TYPE_MATCHJOINSUCCNTF, matchJoinSuccReq.getResult());
        } else {
            CsRoom.MatchJoinSuccNtf.Builder ntfBuilder = CsRoom.MatchJoinSuccNtf.newBuilder();
            ntfBuilder.setResult(matchJoinSuccReq.getResult());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_MATCHJOINSUCCNTF, ntfBuilder);
        }
    }

    public int roomCommonNtf(SsGamesvr.RpcRoomCommonNtfReq.Builder rpcRoomCommNtf) {
        //CsRoom.RoomCommonNtf.Builder ntfBuilder = CsRoom.RoomCommonNtf.newBuilder();
        //ntfBuilder.setType(rpcRoomCommNtf.getType());
        player.sendError(MsgTypes.MSG_TYPE_ROOMCOMMONNTF, rpcRoomCommNtf.getErrorCode());
        return NKErrorCode.OK.getValue();
    }

    public void clearRoomInfo(String reason, boolean sendDelNtf) {
        /*AttrRoomInfo roomInfo = player.getUserAttr().getRoomInfo();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("clearRoomInfo called because {}, roomInfo before clear is {} uid {}",
                    reason, roomInfo, player.getUid());
        }
        if (sendDelNtf) {
            LOGGER.info("player give up when reconnect room:{}, uid:{}", roomInfo.getRoomid(), player.getUid());
            CsRoom.RoomMemberModifyNtf.Builder ntfBuilder = CsRoom.RoomMemberModifyNtf.newBuilder()
                    .setRoomID(roomInfo.getRoomid()).setModify(ModifyType.MT_DEL)
                    .addMemberList(MemberBaseInfo.newBuilder().setUid(player.getUid()));
            if (this.currentRoomInfoBuidler.getRoomBriefInfo().getRoomID() > 0) {
                ntfBuilder.setRoomType(this.currentRoomInfoBuidler.getRoomBriefInfo().getRoomType().getNumber());
                ntfBuilder.getRoomBriefInfoBuilder()
                        .setRoomType(this.currentRoomInfoBuidler.getRoomBriefInfo().getRoomType());
            }
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMEMBERMODIFYNTF, ntfBuilder);
        }*/
        /*roomInfo.clear();
        unlockPlayer(roomInfo.getRoomid());
        this.roomMemberCount = 0;
        player.getUserAttr().getPlayerPublicLiveStatus().setRoomStatus(RoomStateType.RST_Wait);
        player.getUserAttr().getPlayerPublicLiveStatus().setRoomId(0);
        player.getUserAttr().getPlayerPublicLiveStatus().setRoomIsFull(false);
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.QUIT_ROOM);*/
    }

    @Deprecated
    public NKErrorCode roomsvrTryLockPlayer(int op, long roomId) {
        if (op == 0) {
            unlockPlayer(roomId);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("unlock player:{} by room:{}", player.getUid(), roomId);
            }
            return NKErrorCode.OK;
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("try to lock player:{} by room:{}", player.getUid(), roomId);
        }
        if (roomId == lockedByRoomId) {
            lockPlayer(roomId);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("reLock player:{} by room:{}", player.getUid(), roomId);
            }
            return NKErrorCode.OK;
        }
        if (isLocked(roomId)) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("try to lock player:{} by room:{} failed, already locked by {}, expire:{}",
                        player.getUid(), roomId, lockedByRoomId, lockedByRoomExpire);
            }
            return NKErrorCode.RoomUserLockedInModServer;
        }
        AttrRoomInfo roomInfo = player.getUserAttr().getRoomInfo();
        if (roomInfo.getRoomid() == 0) { // @Deprecated
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("try to lock player:{} by room:{} succ, player not in room, roomId:{}, status:{}",
                        player.getUid(), roomId, roomInfo.getRoomid(), roomInfo.getStatus()); // @Deprecated
            }
            lockPlayer(roomId);
            return NKErrorCode.OK;
        }

        RoomService roomSvr = RoomService.get();
        if (null == roomSvr) {
            LOGGER.error("try to lock player:{} by room:{} failed, can't get room server",
                    player.getUid(), roomInfo.getRoomid()); // @Deprecated
            return NKErrorCode.RoomServiceError;
        }
        try {
            RpcResult<SsRoomsvr.RpcExitRoomRes.Builder> rpcRet = roomSvr
                    .rpcExitRoom(SsRoomsvr.RpcExitRoomReq.newBuilder()
                            .setRoomId(roomInfo.getRoomid()) // @Deprecated
                            .setUid(player.getUid())
                            .setDoNotExitIfHaveRoommates(true));
            if (0 == rpcRet.getRet() || NKErrorCode.RoomNotExist.getValue() == rpcRet.getRet() ||
                    NKErrorCode.RoomMemberNotExist.getValue() == rpcRet.getRet()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("try to lock player:{} by room:{} succ, member==1, roomId:{}",
                            player.getUid(), roomId, roomInfo.getRoomid()); // @Deprecated
                }
                lockPlayer(roomId);
                return NKErrorCode.OK;
            } else {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("try to lock player:{} by room:{} failed, exit room:{} ret:{}",
                            player.getUid(), roomId, roomInfo.getRoomid(), rpcRet.getRet()); // @Deprecated
                }
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
        } catch (Exception e) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("try to lock player:{} by room:{} failed, exit room:{} err:",
                        player.getUid(), roomId, roomInfo.getRoomid(), e); // @Deprecated
            }
            return NKErrorCode.RoomUserExitOwnRoomBeforeLockedFailed;
        }
    }

    /**
     * 同步玩家最新信息到room
     *
     * @param ignoreWhenOneMemberTeam 单人队伍是否同步
     * @param source
     */
    public void updateRoomMemberBaseInfo(boolean ignoreWhenOneMemberTeam, UpdateMemberBaseInfoSource source) {
        // 统计更新来源
        String[] monitorParams = new String[]{
                source.toString()
        };
        Monitor.getInstance().add.total(MonitorId.attr_roomsvr_player_update_member_base_info_cnt, 1, monitorParams);
        this.memberBaseInfoUpdateHistory.markTriggered(source);
        // 立即尝试一次更新
        roomMemberBaseInfoUpdate();
    }

    private void updateMemberBaseInfoDirectly2Room(MemberBaseInfoUpdateHistory history) {
        try {
            MemberBaseInfoUpdateHistory historyCopy = new MemberBaseInfoUpdateHistory(history);
            CurrentExecutorUtil.runJob(() -> {
                RoomBriefInfo teamBriefInfo = getCurrentTeamInfoBuidler().getRoomBriefInfo();
                if (teamBriefInfo.getRoomID() != 0) {
                    updateMemberBaseInfoToTargetRoom(teamBriefInfo, historyCopy, false); // TODO 集束判断是否需要ignore
                }
                RoomBriefInfo roomBriefInfo = getCurrentRoomInfoBuidler().getRoomBriefInfo();
                if (roomBriefInfo.getRoomID() != 0) {
                    updateMemberBaseInfoToTargetRoom(roomBriefInfo, historyCopy, false);
                }
                return null;
            }, "updateMemberBaseInfoDirectly2Room", true);
        } catch (Exception e) {
            LOGGER.error("updateMemberBaseInfoDirectly2Room run job exception, player:{} e:{}", player.getUid(),
                    e.getMessage());
        }
    }

    /**
     * 同步玩家最新信息到room，旧版
     */
    public void updateRoomMemberBaseInfo() {
        updateRoomMemberBaseInfo(true, UpdateMemberBaseInfoSource.Default);
    }

    /**
     * 同步玩家最新信息到room，新版
     */
    public void updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource source) {
        updateRoomMemberBaseInfo(true, source);
    }

    private void updateMemberBaseInfoToTargetRoom(RoomBriefInfo roomBriefInfo, MemberBaseInfoUpdateHistory history,
            boolean ignoreWhenOneMemberTeam) {
        TxStopWatch stopWatch = NKStopWatch.SW_RoomLoclProc.getStopWatch(
                NKStringFormater.format("room-updateRoomMemberBaseInfo-{}", player.getUid()));
        long roomId = roomBriefInfo.getRoomID();
        if (roomId != 0) {
            GuidType uidType = BaseGenerator.getUidType(roomId);
            // 仅队伍，且需要判断
            if ((uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID)
                    && ignoreWhenOneMemberTeam) {
                // 判断是否只有一个成员，如果只有一个成员，则不需要更新
                if (getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() <= 1) {
                    return;
                }
            }
            try {
                SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoReq.Builder ssReqMsg =
                        SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoReq.newBuilder();
                ssReqMsg.setRoomId(roomId);
                ssReqMsg.setUid(player.getUid());
                MemberBaseInfo.Builder memberBaseInfo = getJoinerMemberBaseInfoBuilder();
                // 按照来源补充额外的数据
                history.fillExtraData(player, memberBaseInfo);
                history.fillExtraPlayData(player, roomBriefInfo, memberBaseInfo);

                ssReqMsg.setMemberBaseInfo(memberBaseInfo);
//                LOGGER.debug("update player member base info to room, player:{} room:{} req:{}",
//                        player.getUid(), roomId, ssReqMsg.toString());
                RpcResult<SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoRes.Builder> rpcResult = RoomService.get()
                        .rpcRoomPlayerUpdateMemberBaseInfo(
                                ssReqMsg);
                if (rpcResult.getRet() != 0) {
                    LOGGER.error("update player member base info to room fail, player:{} room:{} res:{}",
                            player.getUid(), roomId, rpcResult.getRet());
                }
                if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                    LOGGER.error("update player member base info to room fail, player:{} room:{} res:{}",
                            player.getUid(), roomId, rpcResult.getData().getResult());
                }
            } catch (NKTimeoutException | RpcException e) {
                LOGGER.error("update player member base info to room throw exception, player:{} room:{} e:{}",
                        player.getUid(), roomId, e.getErrCode());
            }
        }
        stopWatch.mark("updateRoomMemberBaseInfo_end");
        stopWatch.dump(200);
    }

    // 发送加锁请求
    @Deprecated
    private void inviteeLockInviterReq(long inviterUid) {
        SsGamesvr.RpcInviteeLockInviterReq.Builder inviteeLockReq = SsGamesvr.RpcInviteeLockInviterReq
                .newBuilder()
                .setInviter(inviterUid)
                .setWantToJoinRoomId(this.wantToJoinRoomId)
                .setWantToQuitRoomId(this.wantToQuitRoomId);
        try {
            RpcResult<SsGamesvr.RpcInviteeLockInviterRes.Builder> rpcResult = GameService.get()
                    .rpcInviteeLockInviter(inviteeLockReq);
            if (rpcResult.getData().getResult() != 0) {
                LOGGER.warn("rpcInviteeLockInviter meets lock, uid:{}, roomId:{}, inviter:{}",
                        player.getUid(), this.wantToJoinRoomId, inviterUid);
                unlockInviteeLock();
                LOGGER.warn("locked by rpc invitee locker, uid:{}, join:{}, quit:{}, inviter:{}",
                        player.getUid(), this.wantToJoinRoomId, this.wantToQuitRoomId, inviterUid);
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_reply_invite_player_room, 1);
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_invite_lock_room, 1);
                return;
            }
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_invite_lock_room, 1);
        } catch (Exception e) {
            LOGGER.warn("rpcInviteeLockInviter meets err, uid:{}, roomId:{}, inviter:{}, err:",
                    player.getUid(), this.wantToJoinRoomId, inviterUid, e);
            // Exception 不同于 被锁定, 保守点不要阻断, 继续流程, 于游戏性上更合理
        }
    }

    //受邀者锁定邀请者的房间
    @Deprecated
    public NKErrorCode inviteeLockInviter(long inviteeWantToJoinRoomId, long inviteeWantToQuitRoomId,
                                          long determinant) {
        if (player.getUserAttr().getRoomInfo().getRoomid() != inviteeWantToJoinRoomId) { // @Deprecated
            // 这个情况不会导致接受邀请成环, 所以不处理了, 直接返回true, 保持原有逻辑,
            return NKErrorCode.OK;
        }
        this.inviteeWantToJoinRoomId = inviteeWantToJoinRoomId;
        this.inviteeWantToQuitRoomId = inviteeWantToQuitRoomId;
        this.inviteeLockExpire = DateUtils.currentTimeMillis() + 1500;
        if (isLockedByInvitee()) {
            if (this.wantToQuitRoomId > this.wantToJoinRoomId) {
                return NKErrorCode.RoomUserLockedByInvitee;
            }
        }

        return NKErrorCode.OK;
    }

    @Deprecated
    private boolean isLockedByInvitee() {
        return this.wantToJoinRoomId == this.inviteeWantToQuitRoomId
                && this.wantToQuitRoomId == this.inviteeWantToJoinRoomId
                && DateUtils.currentTimeMillis() < this.inviteeLockExpire;
    }

    @Deprecated
    private void unlockInviteeLock() {
        this.wantToJoinRoomId = 0L;
        this.wantToQuitRoomId = 0L;
    }

    @Deprecated
    private void lockPlayer(long roomId) {
        lockedByRoomId = roomId;
        lockedByRoomExpire = Framework.currentTimeMillis() + LOCK_TIME_MS;
    }

    @Deprecated
    private void unlockPlayer(long roomId) {
        if (roomId == lockedByRoomId) {
            lockedByRoomId = 0L;
            lockedByRoomExpire = 0L;
        }
    }

    @Deprecated
    private boolean isLocked(long roomId) {
        return lockedByRoomId != 0 && lockedByRoomId != roomId && Framework.currentTimeMillis() < lockedByRoomExpire;
    }

    @Deprecated
    public boolean isLocked() {
        return lockedByRoomId != 0 && Framework.currentTimeMillis() < lockedByRoomExpire;
    }

    // 召集其他玩家到我的大厅
    public NKErrorCode gatherOthersToMyLobby(long clientRoomId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        long lobbyId = player.getUserAttr().getSceneInfo().getLobbySceneId();
        if (lobbyId == 0) {
            LOGGER.error("player get lobby id fail, playerUid:{} lobby:{}", player.getUserAttr().getSceneInfo());
            return NKErrorCode.RoomServiceIsBusy;
        }
        try {
            RpcResult<SsRoomsvr.RpcRoomLobbyGatherRes.Builder> rpcRet = RoomService.get()
                    .rpcRoomLobbyGather(SsRoomsvr.RpcRoomLobbyGatherReq.newBuilder()
                            .setRoomId(currentRoomId)
                            .setProposerUid(player.getUid())
                            .setLobbyId(lobbyId));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
        } catch (Exception e) {
            LOGGER.error("room lobby gather fail");
            // TODO errcode定义
            return NKErrorCode.RoomUserExitOwnRoomBeforeLockedFailed;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomLeaderTransit(long clientRoomId, long newPlayerId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }

        LOGGER.info("player transit leader to other player, player:{}, roomId:{}, targetPlayerUid:{}", player.getUid(),
                currentRoomId, newPlayerId);
        try {
            RpcResult<SsRoomsvr.RpcRoomLeaderTransitRes.Builder> rpcRet = RoomService.get()
                    .rpcRoomLeaderTransit(SsRoomsvr.RpcRoomLeaderTransitReq.newBuilder()
                            .setRoomId(currentRoomId)
                            .setOldLeader(player.getUid())
                            .setNewLeader(newPlayerId));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, currentRoomId,
                        StarPTeamOpType.SPIOpType_LEADER_TRANSIT, rpcRet.getData().getResult(),
                        String.valueOf(newPlayerId));
                if (rpcRet.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    LOGGER.info("player transit leader to other member but room not exist, playerUid:{} roomId:{}",
                            player.getUid(), currentRoomId);
                    ntfClientClearRoomInfo(currentRoomId, "room not exist when leader transit");
                    return NKErrorCode.OK;
                }
                if (rpcRet.getData().getResult() == NKErrorCode.RoomMemberIsRobot.getValue()) {
                    return NKErrorCode.RoomLeaderCannotTransitToRobot;
                }
                if (rpcRet.getData().getResult() == NKErrorCode.RoomStateIsWaitingForMatchConfirm.getValue()) {
                    return NKErrorCode.RoomLeaderCannotTransitWhenWaitConfirm;
                }
                return NKErrorCode.forNumber(rpcRet.getData().getResult());
            }
            // 流水
            StringBuilder memberStr = new StringBuilder();
            for (Long uid : rpcRet.getData().getRoomInfo().getRoomTFlowInfo().getRoomMemberUidArray()
                    .getArrayLongList()) {
                memberStr.append(uid).append(",").append(player.getFriendManager().getRelationTypeVal(uid)).append(";");
            }
            TlogFlowMgr.sendTeamFlow(player, currentRoomId, TEAM_OP_TYPE.TEAM_LEADER_TRANSIT, 0,
                    memberStr.toString(), rpcRet.getData().getRoomInfoBuilder(),
                    rpcRet.getData().getRoomInfo().getRoomTFlowInfo().getOfflineMemberCnt());

        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room leader transit fail, roomId:{} playerUid:{} e:{}", currentRoomId, player.getUid(),
                    e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomPositionUpdate(long clientRoomId, int targetRoleType, int targetPosition) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        try {
            RpcResult<SsRoomsvr.RpcRoomPositionUpdateRes.Builder> rpcRet = RoomService.get()
                    .rpcRoomPositionUpdate(SsRoomsvr.RpcRoomPositionUpdateReq.newBuilder()
                            .setRoomId(currentRoomId).setPlayerId(player.getUid())
                            .setTargetRoleType(targetRoleType).setTargetPosition(targetPosition));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                // 处理错误逻辑
                if (rpcRet.getData().getResult() == NKErrorCode.RoomNotExist.getValue()
                        || rpcRet.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(getTeamId(), "position update but room not exist");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcRet.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room position update fail, roomId:{} playerUid:{} position:{} e:{}", currentRoomId,
                    player.getUid(), targetPosition, e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }

        return NKErrorCode.OK;
    }

    public NKErrorCode roomPositionExchange(long clientRoomId, int targetRoleType, int targetPosition, long uniqueId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        try {
            RpcResult<SsRoomsvr.RpcRoomPositionUpdateRes.Builder> rpcRet = RoomService.get()
                    .rpcRoomPositionUpdate(SsRoomsvr.RpcRoomPositionUpdateReq.newBuilder()
                            .setRoomId(currentRoomId).setPlayerId(player.getUid())
                            .setTargetRoleType(targetRoleType).setTargetPosition(targetPosition)
                            .setUniqueId(uniqueId));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                if (rpcRet.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    LOGGER.info("player position exchange to other position but room not exist, playerUid:{} roomId:{}",
                            player.getUid(), currentRoomId);
                    ntfClientClearRoomInfo(currentRoomId, "room not exist when position exchange");
                    return NKErrorCode.OK;
                }
                if (rpcRet.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist when position exchange");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcRet.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room position exchange fail, roomId:{} playerUid:{} position:{} e:{}", currentRoomId,
                    player.getUid(), targetPosition, e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }

        return NKErrorCode.OK;
    }

    public NKErrorCode rejectPositionExchange(long clientRoomId, long uniqueId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        try {
            RpcResult<SsRoomsvr.RpcRoomPositionUpdateRes.Builder> rpcRet = RoomService.get()
                    .rpcRoomPositionUpdate(SsRoomsvr.RpcRoomPositionUpdateReq.newBuilder()
                            .setRoomId(currentRoomId).setPlayerId(player.getUid())
                            .setTargetPosition(0).setUniqueId(uniqueId).setRejectOrNot(true));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                if (rpcRet.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    LOGGER.info("player position exchange to other position but room not exist, playerUid:{} roomId:{}",
                            player.getUid(), currentRoomId);
                    ntfClientClearRoomInfo(currentRoomId, "room not exist when reject position exchange");
                    return NKErrorCode.OK;
                }
                if (rpcRet.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist when reject position exchange");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcRet.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room reject position exchange fail, roomId:{} playerUid:{} e:{}", currentRoomId,
                    player.getUid(), e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }

        return NKErrorCode.OK;
    }

    public NKErrorCode roomPositionExchangeNtf(long roomId, MemberBaseInfo proposal) {
        GuidType uidType = BaseGenerator.getUidType(roomId);
        if (uidType != GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
            LOGGER.error("player recv position exchange ntf from default room, playerUid:{} roomId:{} proposal:{}",
                    player.getUid(), roomId, proposal);
            return NKErrorCode.OK;
        }
        if (player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Battle) {
            LOGGER.debug("player recv position exchange ntf when at battle, player:{} roomId:{}", player.getUid(), roomId);
            return NKErrorCode.OK;
        }
        long currentRoomId = getRoomId();
        if (currentRoomId != roomId) {
            // 与玩家身上房间id不符
            exitRoomForDataFix(roomId);
            return NKErrorCode.RoomMemberNotExist;
        }
        CsRoom.RoomPositionExchangeNtf.Builder ntfBuilder = CsRoom.RoomPositionExchangeNtf.newBuilder();
        ntfBuilder.setRoomId(roomId);
        ntfBuilder.setProposal(proposal);
        ntfBuilder.setUniqueId(proposal.getUid());

        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMPOSITIONEXCHANGENTF, ntfBuilder);
        return NKErrorCode.OK;
    }

    public NKErrorCode roomDisband(long clientRoomId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        LOGGER.info("player disband room, player:{}, roomId:{}", player.getUid(), currentRoomId);
        try {
            RpcResult<SsRoomsvr.RpcRoomDisbandRes.Builder> rpcRet = RoomService.get()
                    .rpcRoomDisband(SsRoomsvr.RpcRoomDisbandReq.newBuilder()
                            .setRoomId(currentRoomId).setOperatorUid(player.getUid())
                            .setOperateType(SsRoomsvr.RoomDisBandOperateType.DISBAND_OPERATE_TYPE_USER_VALUE));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, clientRoomId, StarPTeamOpType.SPIOpType_DIS,
                        rpcRet.getData().getResult());
                if (rpcRet.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    LOGGER.info("player disband room but room not exist, playerUid:{} roomId:{}", player.getUid(),
                            currentRoomId);
                    ntfClientClearRoomInfo(currentRoomId, "room not exist when disband");
                    return NKErrorCode.OK;
                }
                if (rpcRet.getData().getResult() == NKErrorCode.RoomStateIsMatching.getValue()) {
                    return NKErrorCode.RoomCannotDisbandWhenMatching;
                }
                return NKErrorCode.forNumber(rpcRet.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room disband fail, roomId:{} playerUid:{} e:{}", currentRoomId, player.getUid(), e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }
        // 离开房间流水
        TlogFlowMgr.sendSecRoomFlow(player, 4, currentRoomId, getRoomMemberCount(currentRoomId),
                getRoomTypeVal(currentRoomId), "", "", player.getOpenId(), player.getUid());
        return NKErrorCode.OK;
    }

    public NKErrorCode canStartMatch() {
        if (player.getLobbyMatchMgr().isLobbyMatchServiceOpen()
                && !player.getLobbyMatchMgr().isCanNormalMatch()) {
            var lobbyMatchMgr = player.getLobbyMatchMgr();
            LOGGER.info("canStartMatch lobby match state {} {} {} {}", player.getUid(),
                    lobbyMatchMgr.getMatchID(), lobbyMatchMgr.getState(),
                    lobbyMatchMgr.getLobbyMatchInfo().getTimeMillSec());
            return NKErrorCode.MatchLobbyMatchMatchState;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode startMatch(long clientRoomId, MatchRuleInfo targetRuleInfo, MatchContentInfo matchContentInfo) {
        LOGGER.debug("startMatch uid:{} clientRoomId:{} roomId:{} matchTypeId:{}", player.getUid(), clientRoomId, getRoomId(), targetRuleInfo.getMatchTypeId());
        // 七彩石配置玩法下架开关
        if (isMatchTypeUrgentClose(targetRuleInfo.getMatchTypeId())) {
            LOGGER.error("check isMatchTypeUrgentClose, player {} roomId {} ", player.getUid(), getRoomId());
            return NKErrorCode.RoomMatchTypeErrCannotAccess;
        }

        if (player.getLobbyMatchMgr().isLobbyMatchMode(targetRuleInfo.getMatchTypeId())) {
            LOGGER.debug("lobby match {} {}", player.getUid(), targetRuleInfo.getMatchTypeId());
            player.getLobbyMatchMgr().startMatch(targetRuleInfo).throwErrorIfNotOk("start match fail");
            return NKErrorCode.OK;
        }

        NKErrorCode checkRes = canStartMatch();
        if (NKErrorCode.OK != checkRes) {
            LOGGER.warn("startMatch canStartMatch return false {} {} {}", player.getUid(),
                                    checkRes, targetRuleInfo.getMatchTypeId());
            return checkRes;
        }

        // 1. 获取当前的房间号
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            // 2. 不存在房间号则创建
            UniversalRoom roomWithMatchRuleInfo = createRoomWithMatchRuleInfo(targetRuleInfo);
            // 仅队伍开启对局时可以不带队伍id
            currentRoomId = roomWithMatchRuleInfo.getRoomBriefInfo().getRoomID();
        } else {
            // 3. 存在则修改房间到对应模式，判断房间模式是否发生了修改
            GuidType uidType = BaseGenerator.getUidType(currentRoomId);
            // 当前是队伍且指定玩法和当前玩法不一致的时候
            if ((uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID) && RoomUtil.isMatchRuleChange(getCurrentTeamInfoBuidler().getRoomBriefInfo().getRuleInfo(), targetRuleInfo)) {
                if (!checkWereWolfConsumeItem(targetRuleInfo.getMatchTypeId(), targetRuleInfo.getWereWolfSideIdentityInfo())) {
                    LOGGER.error("startMatch checkWereWolfConsumeItem fail player:{} {} {}", player.getUid(),
                            targetRuleInfo.getMatchTypeId(), targetRuleInfo.getWereWolfSideIdentityInfo());
                    NKErrorCode.RoomWereWolfConsumeItemNotEnough.throwError("startMatch checkWereWolfConsumeItem fail");
                }
                this.checkModifyModeWithStarP(targetRuleInfo.toBuilder());
                // 修改房间模式
                SsRoomsvr.RpcRoomModifyGameModeReq.Builder roomReq = SsRoomsvr.RpcRoomModifyGameModeReq.newBuilder();
                roomReq.setRoomid(currentRoomId);
                roomReq.setUid(player.getUid());
                roomReq.setRuleInfo(targetRuleInfo);
                RoomService roomSrv = RoomService.get();
                if (null != roomSrv) {
                    try {
                        RpcResult<SsRoomsvr.RpcRoomModifyGameModeRes.Builder> rpcResult = roomSrv.rpcRoomModifyGameMode(
                                roomReq);
                        if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                            if (NKErrorCode.RoomChangeModeFailureInBattle.getValue() != rpcResult.getData()
                                    .getResult()) {
                                NKErrorCode.throwErrorOf(NKErrorCode.forNumber(rpcResult.getData().getResult()));
                            }
                        }
                    } catch (RpcException | NKTimeoutException e) {
                        LOGGER.error("rpcRoomModifyGameMode error :{}", e);
                    }
                }
            } else if (uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID) {
                checkRoomConfig(targetRuleInfo.toBuilder()).throwErrorIfNotOk("checkRoomConfig error");
            }


        }
        GuidType uidType = BaseGenerator.getUidType(currentRoomId);
        if ((uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID) && getRoomId() > 0) {
            LOGGER.error("player {} roomId {} getRoomId {} uidType:{}",
                    player.getUid(), currentRoomId, getRoomId(), uidType);
            return NKErrorCode.RoomTeamCannotStartMatchWhenInRoom;
        }
        if (!checkRoguelikeDifficulty(targetRuleInfo, matchContentInfo)) {
            return NKErrorCode.InvalidParams;
        }
        if (!checkWereWolfConsumeItem(targetRuleInfo.getMatchTypeId(), targetRuleInfo.getWereWolfSideIdentityInfo())) {
            LOGGER.error("startMatch checkWereWolfConsumeItem fail player:{} {} {}", player.getUid(),
                    targetRuleInfo.getMatchTypeId(), targetRuleInfo.getWereWolfSideIdentityInfo());
            NKErrorCode.RoomWereWolfConsumeItemNotEnough.throwError("startMatch checkWereWolfConsumeItem fail");
        }
        LOGGER.debug("player start match, uid:{}, roomId:{}, uidType:{}", player.getUid(), currentRoomId, uidType);
        // 4. 发起匹配
        NKErrorCode ret = handleStartMatch(currentRoomId, targetRuleInfo, matchContentInfo);
        StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, currentRoomId, StarPTeamOpType.SPIOpType_MATCH,
                ret.getValue());
        if (StarPConfs.isStarPPvpGame(targetRuleInfo.getMatchTypeId())) {
            StarPTlogFlowMgr.sendStarPPVPMatchFlow(player, currentRoomId, SP_PVP_MATCH_OP_TYPE.SP_PVP_MATCH_OP_TYPE_START,
                    targetRuleInfo.getMatchTypeId(), ret.getValue());
        }
        // 5. 如果房间不存在，则清理玩家的组队信息
        if (ret == NKErrorCode.RoomNotExist) {
            LOGGER.debug("RoomStartMsgHandler RoomNotExist room:{}, uid:{}", currentRoomId, player.getUid());
            ntfClientClearRoomInfo(currentRoomId, "start match but room not exist");
            return NKErrorCode.OK;
        }
        if (ret != NKErrorCode.OK) {
            LOGGER.error("start match err room:{}, uid:{}, e:{}", currentRoomId, player.getUid(), ret);
            NKErrorCode.throwErrorOf(ret);
        }
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.MATCH_START);
        MatchDegreeTypeGroupConfig matchDegreeTypeConf = MatchDegreeTypeGroupData.getInstance()
                .get(targetRuleInfo.getMatchTypeId());
        if (matchDegreeTypeConf != null) {
            for (MatchDegreeTypeGroupInfo matchDegreeTypeGroupInfo : matchDegreeTypeConf.getMatchDegreeTypeGroupList()) {
                SeasonCfg battleSeason = QDTSeasonCfgData.getInstance().getCurrSeasonConf(matchDegreeTypeGroupInfo.getQualifyType());
                if (battleSeason == null) {
                    return NKErrorCode.BattleSettlementSeasonCanNotFind;
                }
                if (QDTSeasonCfgData.getInstance().checkPlayerSeasonVersionLower(player.getClientVersion64(),battleSeason)) {
                    return NKErrorCode.BattleSettlementSeasonLower;
                }
            }
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode GMHandlePVPMatch(long currentRoomId, MatchContentInfo matchContentInfo) {
        LOGGER.info("player start match, uid:{}, roomId:{}", player.getUid(), currentRoomId);
        MatchRuleInfo matchRuleInfo = MatchRuleInfo.newBuilder().build();
        // 4. 发起匹配
        NKErrorCode ret = handleStartMatch(currentRoomId, matchRuleInfo, matchContentInfo);
        // 5. 如果房间不存在，则清理玩家的组队信息
        if (ret == NKErrorCode.RoomNotExist) {
            LOGGER.debug("RoomStartMsgHandler RoomNotExist room:{}, uid:{}", currentRoomId, player.getUid());
            ntfClientClearRoomInfo(currentRoomId, "start match but room not exist");
            return NKErrorCode.OK;
        }
        if (ret != NKErrorCode.OK) {
            LOGGER.error("start match err room:{}, uid:{}, e:{}", currentRoomId, player.getUid(), ret);
            NKErrorCode.throwErrorOf(ret);
        }
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.MATCH_START);

        return NKErrorCode.OK;
    }

    public NKErrorCode matchStartConfirmNtf(long roomId, long uniqueId) {
        GuidType uidType = BaseGenerator.getUidType(roomId);
        // 仅队伍匹配确认会收到确认ntf
        if (uidType != GuidType.GUID_TYPE_ROOM_ID && uidType != GuidType.GUID_TYPE_STARP_TEAM_ID) {
            LOGGER.error("player recv match confirm ntf from custom room, playerUid:{} roomId:{}",
                    player.getUid(), roomId);
            return NKErrorCode.OK;
        }
        long currentRoomId = getTeamId();
        if (currentRoomId != roomId) {
            // 与玩家身上队伍id不符
            exitRoomForDataFix(roomId);
            LOGGER.error("player recv match confirm ntf from mismatch room, playerUid:{} curRoomId:{} targetRoomId:{}",
                    player.getUid(), currentRoomId, roomId);
            return NKErrorCode.RoomMemberNotExist;
        }
        // 此时有房间 直接拒绝
        if (getRoomId() > 0) {
            try {
                RpcResult<SsRoomsvr.RpcConfirmStartMatchRes.Builder> rpcRet = RoomService.get()
                        .rpcConfirmStartMatch(SsRoomsvr.RpcConfirmStartMatchReq.newBuilder()
                                .setRoomId(currentRoomId).setUid(player.getUid()).setUniqueId(uniqueId)
                                .setConfirmFlag(false).setMatchCancelReason(MatchCancelReason.MCR_InCustomRoom));
                if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                    LOGGER.error("player auto reject match confirm fail, player:{} roomId:{} ret:{}", player.getUid(),
                            roomId, rpcRet.getRet());
                    return NKErrorCode.OK;
                }
            } catch (NKTimeoutException | RpcException e) {
                LOGGER.error("room confirm start match fail, roomId:{} playerUid:{} e:{}", currentRoomId, player.getUid(), e.getMessage());
                return NKErrorCode.RoomServiceIsBusy;
            }
            return NKErrorCode.OK;
        }

        NKErrorCode ret = player.getPlayerStateMgr().checkConfirm();
        if (ret.hasError()) {
            return ret;
        }

        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.MATCH_START);
        CsRoom.RoomStartNtf.Builder startNtf = CsRoom.RoomStartNtf.newBuilder()
                .setRoomId(roomId).setUniqueId(uniqueId);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMSTARTNTF, startNtf);
        return NKErrorCode.OK;
    }

    public NKErrorCode confirmStartMatch(long clientRoomId, long uniqueId, boolean confirmFlag,MatchCancelReason clientReason) {
        NKErrorCode checkRes = canStartMatch();
        if (NKErrorCode.OK != checkRes) {
            LOGGER.warn("confirmStartMatch canStartMatch return false {} {}", player.getUid(), checkRes);
            return checkRes;
        }
        // 1. 获取当前的房间号
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcConfirmStartMatchReq.Builder rpcBuilder = SsRoomsvr.RpcConfirmStartMatchReq.newBuilder();
            rpcBuilder.setRoomId(currentRoomId).setUid(player.getUid()).setUniqueId(uniqueId)
                    .setConfirmFlag(confirmFlag).setMatchCancelReason(MatchCancelReason.MCR_Click);
            if (clientReason != MatchCancelReason.MCR_Unknown){
                rpcBuilder.setMatchCancelReason(clientReason);
            }
            RpcResult<SsRoomsvr.RpcConfirmStartMatchRes.Builder> rpcRet = RoomService.get()
                    .rpcConfirmStartMatch(rpcBuilder);
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                if (rpcRet.getData().getResult() == NKErrorCode.RoomNotExist.getValue()
                        || rpcRet.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(currentRoomId, "confirm start match but room not exist");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcRet.getRet());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room confirm start match fail, roomId:{} playerUid:{} e:{}", currentRoomId, player.getUid(), e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }
        if (confirmFlag) {
            player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.START_MATCH);
        }
        return NKErrorCode.OK;
    }

    public void onBattleEnd() {
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.QUIT_BATTLE);
    }

    private boolean checkRoguelikeDifficulty(MatchRuleInfo targetRuleInfo, MatchContentInfo matchContentInfo) {
        if (targetRuleInfo.getMatchTypeId() != 513) {
            return true;
        }

        String matchDifficulty = null;
        if (matchContentInfo.hasKvArray()) {
            for (KVEntry kv : matchContentInfo.getKvArray().getArrayList()) {
                if ("roguelikeDifficulty".equals(kv.getKey())) {
                    matchDifficulty = kv.getValue();
                    break;
                }
            }
        }

        if (matchDifficulty == null) {
            return false;
        }

        RoguelikePassLevelInfo passLevelInfo = player.getUserAttr().getRoguelikeInfo().getExtraInfo()
                .getPassLevelRows();
        boolean isEndlessUnlocked = passLevelInfo.getHardPassNumber() >= 1;
        boolean isHardUnlocked = passLevelInfo.getNormalPassNumber() >= 1 || isEndlessUnlocked;
        boolean isNormalUnlocked = passLevelInfo.getEasyPassNumber() >= 1 || isHardUnlocked;
        return matchDifficulty.equals("1")
                || (matchDifficulty.equals("2") && isNormalUnlocked)
                || (matchDifficulty.equals("3") && isHardUnlocked)
                || (matchDifficulty.equals("4") && isEndlessUnlocked);
    }

    public NKErrorCode roomRecruitPublish(CsRoom.TeamRecruitPublish_C2S_Msg reqMsg) {
        return roomRecruitPublish(RoomRecruitVersion.RRV_DEFAULT, reqMsg.getRoomId(), reqMsg.getModeId(), 0 ,0 ,reqMsg.getPlayId(),
                reqMsg.getQualifyingLowLimit(), reqMsg.getTopicId());
    }

    public NKErrorCode roomRecruitPublishWithModeType(CsRoom.TeamRecruitPublishWithModeType_C2S_Msg reqMsg) {
        return roomRecruitPublish(RoomRecruitVersion.RRV_MODETYPE, reqMsg.getRoomId(), 0, reqMsg.getModeType(), reqMsg.getCategoryType(), reqMsg.getPlayId(),
                reqMsg.getQualifyingLowLimit(), reqMsg.getTopicId());
    }

    public NKErrorCode roomRecruitPublish(RoomRecruitVersion version, long roomId, int modeId, int modeType,
            int categoryType, int playId, QualifyingDegreeInfo qualifyingInfoLowLimit, int topicId) {
        // 1. 检查玩家头像
        checkPlayerProfile().throwErrorIfNotOk("check profile fail");

        // 2. 获取当前的房间号
        UniversalRoom roomWithMatchRuleInfo = null;
        long clientRoomId = roomId;
        if (clientRoomId == 0) {
            // 2.1 如果客户端传入的roomId为空，默认使用队伍id
            clientRoomId = player.getPlayerRoomMgr().getTeamId();
        }
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            // 2.2 如果没有队伍信息则按照模式新建一个
            MatchRuleInfo targetRuleInfo = MatchRuleInfo.newBuilder().setMatchTypeId(playId).build();
            roomWithMatchRuleInfo = createRoomWithMatchRuleInfo(targetRuleInfo);
            currentRoomId = roomWithMatchRuleInfo.getRoomBriefInfo().getRoomID();
        }
        LOGGER.info("player publish room recruit, player:{}, roomId:{}", player.getUid(), currentRoomId);
        // 3. 将对应的房间发布出去
        NKErrorCode ret;
        if (RoomRecruitVersion.RRV_MODETYPE.equals(version)) {
            ret = getRecruitComponent().roomRecruitPublishWithModeType(currentRoomId, modeType, categoryType,
                    playId, qualifyingInfoLowLimit, topicId);
        } else {
            ret = getRecruitComponent().roomRecruitPublish(currentRoomId, modeId,
                    playId, qualifyingInfoLowLimit, topicId);
        }
        // 4. 流水
        int ist = InvitationSourceType.IST_ChatRecruit_VALUE;
        if (getRoomId() == currentRoomId) {
            // 当前为房间的时候
            if (getCurrentRoomInfoBuidler().getRoomBriefInfo().getRoomType() == RoomType.CustomRoom) {
                ist = InvitationSourceType.IST_FriendInfoTipCustomRoomFriend_VALUE;
            } else if (getCurrentRoomInfoBuidler().getRoomBriefInfo().getRoomType() == RoomType.UgcCustomRoom) {
                ist = InvitationSourceType.IST_FriendInfoTipUgcCustomRoomFriend_VALUE;
            }
        }
        TlogFlowMgr.sendTeamInviteFlow(player, currentRoomId, ist, 0L, "",
                roomWithMatchRuleInfo == null ? getTargetRoomInfoBuilder(currentRoomId)
                        : roomWithMatchRuleInfo.toBuilder());

        return ret;
    }


    /**
     * 检查玩的头像是否符合完全外显标准
     * 如果是默认头像或者游戏内道具头像则跳过检查
     *
     * @return
     */
    private NKErrorCode checkPlayerProfile() {
        // 遍历当前的装备
        for (DressItemInfo itemInfo : player.getUserAttr().getPlayerPublicEquipments().getDressItemInfo().values()) {
            Item_BackpackItem itemBackpackItem = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemBackpackItem == null) {
                continue;
            }
            if (itemBackpackItem.getType() == ItemType.ItemType_Portrait) {
                LOGGER.debug("player using item profile, no need to tss check, uid:{}", player.getUid());
                return NKErrorCode.OK;
            }
        }
        if (player.getProfile().trim().length() == 0) {
            LOGGER.debug("player using default profile, no need to tss check, uid:{}", player.getUid());
            return NKErrorCode.OK;
        }
        if(!player.getUserAttr().getSafetyCheck().getIsNeedCheckProfileBeforMatch()){  //add by feifeiswu
            LOGGER.debug("player  profile isnoChange , no need to tss check, uid:{}", player.getUid());
            return NKErrorCode.OK;
        }
        try {
            HashMap<String, String> extraData = new HashMap<>();
            boolean ret = PlayerTssUtil.checkPicUrlLawful(player, SUB_SCENE.RECRUIT_PUBLISH_MSG,
                    PICTYPE.PIC_TYPE_HEAD, player.getProfile(), extraData);
            if (!ret) {
                LOGGER.info("room recruit publish fail cause player profile is not premit, player:{} profile:{}",
                        player.getUid(), player.getProfile());
                return NKErrorCode.RoomRecruitCannotPublishCauseProfile;
            }
            player.getUserAttr().getSafetyCheck().setIsNeedCheckProfileBeforMatch(false);
        } catch (TimeoutException e) {
            LOGGER.error("player profile tss check timeout, player:{}", player.getUid());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }



    public RoomRecruitArray roomRecruitQuery(CsRoom.TeamRecruitQuery_C2S_Msg reqMsg) {
        int modeId = reqMsg.getModeId();
        int playId = reqMsg.getPlayId();
        int targetRecruitType = reqMsg.getTargetRecruitType();
        int roomListCountPerPage = PropertyFileReader.getRealTimeIntItem("room_list_count_per_page", 10);
        return getRecruitComponent().roomRecruitQuery(roomListCountPerPage, modeId, playId, targetRecruitType);
    }

    public RoomRecruitArray roomRecruitQueryByModeType(CsRoom.TeamRecruitQueryByModeType_C2S_Msg reqMsg) {
        int modeType = reqMsg.getModeType();
        int categoryType = reqMsg.getCategoryType();
        int playId = reqMsg.getPlayId();
        int targetRecruitType = reqMsg.getTargetRecruitType();
        int roomListCountPerPage = PropertyFileReader.getRealTimeIntItem("room_list_count_per_page", 10);
        return getRecruitComponent().roomRecruitQueryByModeType(roomListCountPerPage, modeType, categoryType, playId, targetRecruitType);
    }

    public RoomRecruitArray roomRecruitQueryByPlayGroup(TeamRecruitQueryByPlayGroup_C2S_Msg reqMsg) {
        int playId = reqMsg.getPlayGroupId();
        int roomListCountPerPage = PropertyFileReader.getRealTimeIntItem("room_list_count_per_page", 10);
        return getRecruitComponent().roomRecruitQueryByPlayGroup(playId, new HashSet<>(), roomListCountPerPage);
    }

    public NKErrorCode roomRecruitJoin(CsRoom.TeamRecruitJoin_C2S_Msg reqMsg) {
        // 退出当前的组队
        exitAllRoom("recruit join");

        // 做JoinType的转换
        RoomJoinType type = RoomJoinType.RJT_Recruit;
        if (reqMsg.getJoinType() / 100 == RoomJoinType.RJT_Recruit_VALUE
                && RoomJoinType.forNumber(reqMsg.getJoinType()) != null) {
            type = RoomJoinType.forNumber(reqMsg.getJoinType());
        }
        return getRecruitComponent().roomRecruitJoin(reqMsg.getRecruit(), type);
    }

    public NKErrorCode roomRecruitQuickJoin(CsRoom.TeamRecruitQuickJoin_C2S_Msg reqMsg) {
        // 退出当前的组队
        exitAllRoom("recruit quick join");

        return getRecruitComponent().roomRecruitQuickJoin(reqMsg.getTargetType());
    }

    public NKErrorCode roomRecruitQuickJoinByPlayGroup(CsRoom.TeamRecruitQuickJoinByPlayGroup_C2S_Msg reqMsg) {
        // 退出当前的组队
        exitAllRoom("recruit quick join");

        return getRecruitComponent().roomRecruitQuickJoinByPlayGroup(reqMsg.getTargetType(), reqMsg.getPlayGroupId());
    }

    public UniversalRoomArray playDetailPageRoomList(CsRoom.PlayDetailPageRoomQueryByPlayGroup_C2S_Msg reqMsg) {
        int playId = reqMsg.getPlayGroupId();
        HashSet<Long> excludeSet = new HashSet<>();
        int roomListCountPerPage = PropertyFileReader.getRealTimeIntItem("room_list_count_per_page", 10);
        UniversalRoomArray roomArray = getRecruitComponent().roomQueryByPlayGroup(playId, excludeSet,
                roomListCountPerPage);
        LOGGER.debug("recruit query, excludeSet:{}", excludeSet);
        if (roomArray.getRoomsCount() < roomListCountPerPage) {
            LOGGER.debug("start to fill data by custom room, player:{} curCnt:{}", player.getUid(),
                    roomArray.getRoomsCount());
            UniversalRoomArray.Builder retBuilder = UniversalRoomArray.newBuilder();
            UniversalRoomArray roomArrayExtra = getPublicRoomListComponent().customRoomQueryByPlayGroup(playId,
                    excludeSet, roomListCountPerPage - roomArray.getRoomsCount(),
                    RoomRecommendListSource.RRLS_Unknown_VALUE, true);
            retBuilder.addAllRooms(roomArray.getRoomsList());
            retBuilder.addAllRooms(roomArrayExtra.getRoomsList());
            roomArray = retBuilder.build();
        }
        return roomArray;
    }

    public NKErrorCode coMatchPropose(CsRoom.RoomCoMatchPropose_C2S_Msg reqMsg) {
        return getCoMatchSubComponent().propose(reqMsg.getRoomId(), reqMsg.getBattleId());
    }

    public NKErrorCode coMatchConfirm(CsRoom.RoomCoMatchConfirm_C2S_Msg reqMsg) {
        CoMatchAgreeFlag defaultFlag = CoMatchAgreeFlag.CMAF_Reject;
        if (CoMatchAgreeFlag.forNumber(reqMsg.getAgreeFlag()) != null) {
            defaultFlag = CoMatchAgreeFlag.forNumber(reqMsg.getAgreeFlag());
        }
        return getCoMatchSubComponent().confirm(reqMsg.getRoomId(), reqMsg.getBattleId(), defaultFlag);
    }

    /**
     * 创建ugc多人房间
     *
     * @param mapId 地图id
     * @param maxMemberLimit 最大房间人数
     * @return
     */
    public UniversalRoom createUgcRoom(int matchTypeId, long mapId, String title, String passwordStr,
            int maxMemberLimit, int someWhereIn, long mapVersion, RoomCreateOptions.Builder createOpts,
            String searchId, MatchRuleClientInfo clientInfo) {
        if (!player.getPlayerCreditScoreMgr().checkCreateRoomScenePlayerCreditScoreIsOk()) {
            NKErrorCode.RoomCannotCreateCauseCreditScoreLow.throwError("credit score check fail");
        }
        try {
            // 这里判断房间是否已经存在
            if (getRoomId() > 0) {
                NKErrorCode.RoomCreateFailWhenInRoom.throwError("already in room");
            }
            //
            boolean needParnterConfirm = false;
            if (getTeamId() > 0 && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
                needParnterConfirm = true;
                if (maxMemberLimit < getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber()) {
                    NKErrorCode.RoomCreateFailTeamMemberBeyondPlayMax.throwError("create room fail");
                }
            }
            long roomId = CustomRoomIdGenerator.getInstance().allocGuid();
            MemberBaseInfo.Builder memberBaseInfo = getCreatorMemberBaseInfoBuilder();
            memberBaseInfo.setRoomID(roomId);
            if (needParnterConfirm) {
                if (DateUtils.currentTimeMillis() - lastCreateRoomWithPrejoinTimestampMs < 15 * 1000L) {
                    NKErrorCode.RoomCannotCreateBeforePartnerConfirmLastCreate.throwError(
                            "cannot create because partner not confirm");
                }
                this.lastCreateRoomWithPrejoinTimestampMs = DateUtils.currentTimeMillis();
                memberBaseInfo.setRoomID(getTeamId());
            }
            if (someWhereIn > 0) {
                memberBaseInfo.getRuleInfoBuilder().setSomeWhereIn(someWhereIn);
            }
            if (!searchId.isEmpty()) {
                memberBaseInfo.getRuleInfoBuilder().getClientInfoBuilder().setSearchID(searchId);
            }
            memberBaseInfo.getRuleInfoBuilder().getClientInfoBuilder().mergeFrom(clientInfo);

            SsRoomsvr.RpcCreateRoomReq.Builder rpcRoomCreate = SsRoomsvr.RpcCreateRoomReq.newBuilder();
            rpcRoomCreate.setRoomid(roomId).setUid(player.getUid());
            // TODO 催策划配表
            rpcRoomCreate.setRuleInfo(
                    MatchRuleInfo.newBuilder().setMatchTypeId(matchTypeId).setSomeWhereIn(someWhereIn));
            rpcRoomCreate.setMemberBaseInfo(memberBaseInfo);
            rpcRoomCreate.setRoomType(RoomType.UgcCustomRoom);
            rpcRoomCreate.setPwdStr(passwordStr);
            rpcRoomCreate.setDisplayInfo(RoomDisplayInfo.newBuilder().setTitle(title).build());
            createOpts.setPreCreate(needParnterConfirm);
            rpcRoomCreate.setCreateOpts(createOpts);
            rpcRoomCreate.setUgcInfo(RoomUgcInfo.newBuilder().setMapId(mapId).setMapVersion(mapVersion));
            rpcRoomCreate.setMaxMemberLimit(maxMemberLimit);

            RpcResult<SsRoomsvr.RpcCreateRoomRes.Builder> rpcCreateRoomResult = RoomService.get()
                    .rpcCreateRoom(rpcRoomCreate);
            rpcCreateRoomResult.throwError();
            if (NKErrorCode.OK.getValue() != rpcCreateRoomResult.getData().getResult()) {
                LOGGER.error("RoomService rpcCreateRoom ret err uid:{}, Error:{}",
                        player.getUid(), rpcCreateRoomResult.getData().getResult());
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
                NKErrorCode.forNumber(rpcCreateRoomResult.getData().getResult()).throwError("create room fail");
            }
            //player.setCurrentRoomId(roomId);
            if (needParnterConfirm) {
                LOGGER.debug("team leader notice partner to confirm join room, playerUid:{} teamId:{} targetRoom:{}",
                        player.getUid(), getTeamId(), rpcCreateRoomResult.getData().getRoomInfo().getRoomBriefInfo());
                NKErrorCode preJoinNoticeRet = preJoinNotice(getTeamId(),
                        rpcCreateRoomResult.getData().getRoomInfo().getRoomBriefInfo(), RoomJoinType.RJT_Default,
                        TeamJoinRoomExtraInfo.getDefaultInstance());
                if (preJoinNoticeRet == NKErrorCode.RoomOnlyHimself) {
                    // 发起后队伍只剩自己了，直接online激活房间即可
                    exitRoomQuietly(getTeamId());
                    asyncOnlineRoom(roomId);
                } else {
                    preJoinNoticeRet
                            .throwErrorIfNotOk("pre join notice fail");
                }
                // 前置预创建的房间 房主是离线状态，整个房间处于不对外服务的状态
                // 过程中有任何操作失败后续都会通过超时回收，
                // 房主会在真正全部成员加入成功后通过MT_Init的ntf才将房间id写入属性系统
            } else {
                PublicRoomMgr.getInstance().addRoomAfterCreate(roomId, RoomPublicType.RPT_UgcCustomLobby);
            }
            //player.getUserAttr().getRoomInfo().setCurrentMatchType(targetRuleInfo.getMatchTypeId());
            LOGGER.info("createUgcRoom uid:{} roomId:{} matchTypeId:{} mapId:{} ugcLayerId:{}", player.getUid(), roomId,
                    matchTypeId, mapId, createOpts.getUgcLayerId());
            // 上报安全所需的文本编辑场景流水
            TlogFlowMgr.sendSecEditFlow(player, title, TlogMacros.ENUMSecEditFlowScenceID.ESEFS_CreateRoom, roomId);
            // 上报房间安全流水
            TlogFlowMgr.sendSecRoomFlow(player, 1, roomId, 1, RoomType.UgcCustomRoom_VALUE,
                    title, "", player.getOpenId(), player.getUid());
            // 发送房间创建运营流水
            TlogFlowMgr.sendTeamFlow(player, roomId, TEAM_OP_TYPE.TEAM_CREATE_REQ, 0,
                    String.format("%s,%d;", player.getUid(),
                            player.getFriendManager().getRelationTypeVal(player.getUid())),
                    rpcCreateRoomResult.getData().getRoomInfoBuilder(), 0);
            return rpcCreateRoomResult.getData().getRoomInfo();
        } catch (NKTimeoutException | RpcException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_create_room, 1);
            LOGGER.error("rpcCreateRoom uid:{}, err:", player.getUid(), e);
            NKErrorCode.RoomServiceIsBusy.throwError("create room fail");
        }
        return null;
    }

    public NKErrorCode ugcRoomChangeMap(long roomId, long mapId, RoomSetting roomSetting) {
        // 前置判断了必须为房间id
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        player.getUserAttr().getPlayerPublicGameSettings().setCustomRoomAutoStartWhenFull(roomSetting.getAutoStartWhenFull());
        try {
            SsRoomsvr.RpcRoomChangeInfoReq.Builder req = SsRoomsvr.RpcRoomChangeInfoReq.newBuilder();
            req.setRoomId(roomId);
            req.setOpSource(OpSource.OS_Player);
            req.setUid(player.getUid());
            req.setUgcInfo(RoomUgcInfo.newBuilder().setMapId(mapId));
            req.setRoomSetting(roomSetting);
            req.setChangeAllUgcRoomInfo(false);
            RpcResult<SsRoomsvr.RpcRoomChangeInfoRes.Builder> rpcResult = RoomService.get().rpcRoomChangeInfo(req);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }
    public NKErrorCode ugcRoomPlayerRecommendMap(long roomId, long mapId) {
        LOGGER.info("ugcRoomPlayerRecommendMap uid:{} roomId:{} mapId:{}", player.getUid(), roomId, mapId);
        // 前置判断了必须为房间id
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            LOGGER.info("ugcRoomPlayerRecommendMap room not exist, uid:{} roomId:{} mapId:{}", player.getUid(), roomId, mapId);
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcUgcRoomPlayerRecommendMapReq.Builder req = SsRoomsvr.RpcUgcRoomPlayerRecommendMapReq.newBuilder();
            req.setRoomId(roomId);
            PlayerPublicInfo.Builder memberInfo = PlayerPublicInfo.newBuilder();
            memberInfo.setUid(player.getUid());
            memberInfo.setName(player.getName());
            memberInfo.setProfile(player.getProfile());
            memberInfo.setGender(player.getGender());
            req.setRecommendPlayerInfo(memberInfo.build());
            req.setMapId(mapId);
            RpcResult<SsRoomsvr.RpcUgcRoomPlayerRecommendMapRes.Builder> rpcResult = RoomService.get().rpcUgcRoomPlayerRecommendMap(req);
            return NKErrorCode.forNumber(rpcResult.getRet());
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
    }

    public NKErrorCode ugcRoomGetPlayerRecommendMaps(long roomId, CsUgc.UgcRoomGetPlayerRecommendMaps_S2C_Msg.Builder resBuilder) { //获取玩家推荐的地图列表
        LOGGER.info("ugcRoomGetPlayerRecommendMaps uid:{} roomId:{}", player.getUid(), roomId);
        // 前置判断了必须为房间id
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            LOGGER.info("ugcRoomGetPlayerRecommendMaps room not exist, uid:{} roomId:{}", player.getUid(), roomId);
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcUgcRoomGetPlayerRecommendMapsReq.Builder req = SsRoomsvr.RpcUgcRoomGetPlayerRecommendMapsReq.newBuilder();
            req.setRoomId(roomId);
            req.setUid(player.getUid());
            RpcResult<SsRoomsvr.RpcUgcRoomGetPlayerRecommendMapsRes.Builder> rpcResult = RoomService.get().rpcUgcRoomGetPlayerRecommendMaps(req);
            if(rpcResult.getRet() == NKErrorCode.OK.getValue()) {
                resBuilder.addAllRecommendInfos(rpcResult.getData().getRecommendInfosList());
            }
            return NKErrorCode.forNumber(rpcResult.getRet());
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
    }
    
    public NKErrorCode ugcRoomGetOfficalRecommendMaps(long roomId, String tabId, int page, CsUgc.UgcRoomGetOfficalRecommendMaps_S2C_Msg.Builder resBuilder) { //获取官方推荐的地图列表
        LOGGER.info("ugcRoomGetOfficalRecommendMaps uid:{} roomId:{} tabId:{} page:{}", player.getUid(), roomId, tabId, page);
        // 不要求房间存在
        try {
          SsUgcsvr.RpcUgcRoomOfficalRecommendListReq.Builder req = SsUgcsvr.RpcUgcRoomOfficalRecommendListReq.newBuilder();
            req.setTabId(tabId);
            req.setPage(page);
            req.setUid(player.getUid());
            req.setOpenId(player.getOpenId());

            RpcResult<SsUgcsvr.RpcUgcRoomOfficalRecommendListRes.Builder> rpcResult = UgcService.get().rpcUgcRoomOfficalRecommendList(req);
            if(rpcResult.getRet() == NKErrorCode.OK.getValue()) {
                resBuilder.addAllRecommendInfos(rpcResult.getData().getRecommendInfosList());
                resBuilder.addAllTabInfos(rpcResult.getData().getTabInfosList());
                resBuilder.setInfo(rpcResult.getData().getInfo());
                resBuilder.setHasMorePage(rpcResult.getData().getHasMorePage());
                resBuilder.setPage(page);
            }
            return NKErrorCode.forNumber(rpcResult.getRet());
            
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
    }
    /**
     * 查询地图房间列表
     *
     * @param mapId 地图id
     * @return
     */
    public UgcRoomArray roomUgcList(long mapId) {
        RoomService roomSvr = RoomService.get();
        try {
            RpcResult<SsRoomsvr.RpcUgcRoomListRes.Builder> rpcRet = roomSvr
                    .rpcUgcRoomList(SsRoomsvr.RpcUgcRoomListReq.newBuilder()
                            .setMapId(mapId));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                NKErrorCode.forNumber(rpcRet.getRet()).throwError("query ugc room list fail");
            }
            return sortUgcRoomByMemberCnt(rpcRet.getData().getRoomList());
        } catch (NKRuntimeException e) {
            LOGGER.error("room ugc list fail: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            LOGGER.error("room ugc list fail");
            return null;
        }
    }

    public NKErrorCode roomJoin(long roomId, String pwd, RoomJoinType type) {
        return roomJoin(roomId, pwd, type, false, TeamJoinRoomExtraInfo.getDefaultInstance());
    }

    /**
     * 房间加入
     * 1.在队伍 在房间 队长 加入队伍： 队长离开队伍、离开房间；队长转交给队员，队员不离开房间
     * 2.在队伍 在房间 非队长 加入队伍：该成员离开队伍、离开房间；其他人不受影响
     * 3.在队伍 不在房间 加入队伍： 退出原队伍，加入新队伍
     * 4.不在队伍 在房间 房主 加入队伍：房主离开房间加入队伍
     * 5.不在队伍 在房间 非房主 加入队伍：离开房间加入队伍
     * 6.不在队伍 不在房间 加入队伍：        加入队伍
     * 7.在队伍 在房间 队长 加入房间： 全队退出原房间，征求全队同意，加入新房间
     * 8.在队伍 在房间 非队长 加入房间： 退队，退出原房间，加入新房间
     * 9.在队伍 不在房间 队长 加入房间： 征求全队同意，加入房间
     * 10.在队伍 不在房间 非队长 加入房间： 退队，加入新房间
     * 11.不在队伍 在房间 房主 加入房间： 退出原房间，加入新房间
     * 12.不在队伍 在房间 非房主 加入房间： 退出原房间，加入新房间
     * 13.不在队伍 不在房间 加入房间： 加入房间
     * @param roomId
     * @param pwd
     * @return
     */
    public NKErrorCode roomJoin(long roomId, String pwd, RoomJoinType type, boolean disableMidJoin,
            TeamJoinRoomExtraInfo extraInfo) {
        GuidType uidType = BaseGenerator.getUidType(roomId);
        // 扫码入口
        if (type == RoomJoinType.RJT_QRCode){
            if (getRoomId() > 0) {
                return NKErrorCode.RoomCannotJoinBecauseInOtherRoom;
            }
            if (player.getPlayerStateMgr().getRoomState() == PlayerStateType.PST_Matching
                    || player.getPlayerStateMgr().getRoomState() == PlayerStateType.PST_TeamMatching
                    || player.getPlayerStateMgr().getRoomState() == PlayerStateType.PST_Battle) {
                return NKErrorCode.RoomCannotJoinCauseSelfAtMatching;
            }
            /*// 目标为队伍
            if (uidType == GuidType.GUID_TYPE_ROOM_ID) {
                if (BaseGenerator.getWorldId(roomId) != Framework.getInstance().getWorldId()) {
                    LOGGER.debug("player attemp to join team at different world by qr code, player:{} teamId:{}",
                            player.getUid(), roomId);
                    return NKErrorCode.RoomTeamNotAtSameWorld;
                }
            }*/
        }

        // @13 当前无队伍/房间时
        if (getTeamId() <= 0 && getRoomId() <= 0) {
            return rpcJoinRoom(roomId, pwd, type, disableMidJoin);
        }
        // 已经在对应的队伍/房间里
        if (getTeamId() == roomId || getRoomId() == roomId) {
            return NKErrorCode.RoomAlreadyIn;
        }
        // 没有房间时
        if (getRoomId() <= 0) {
            if (uidType != GuidType.GUID_TYPE_ROOM_ID && uidType != GuidType.GUID_TYPE_CUSTOM_ROOM_ID && uidType != GuidType.GUID_TYPE_STARP_TEAM_ID) {
                LOGGER.error("room id from client get wrong guid type, playerUid:{} roomId:{} guidType:{}",
                        player.getUid(), roomId, uidType);
                return NKErrorCode.InvalidParams;
            }
            // @9 目标为房间,自己为队伍队长,队伍内还有其他成员时
            if (uidType == GuidType.GUID_TYPE_CUSTOM_ROOM_ID && getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid()
                    == player.getUid() && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
                // 判断目标房间剩余人数
                UniversalRoom roomUniversalInfo = preJoinTargetRoom(roomId, pwd, getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber(), disableMidJoin);
                if (roomUniversalInfo.getRoomBriefInfo().getCurMemberNumber() + getCurrentTeamInfoBuidler().getRoomBriefInfo()
                        .getCurMemberNumber() > roomUniversalInfo.getRoomBriefInfo().getMemberNumberLimit()) {
                    // 超过对方房间的人数限制
                    return NKErrorCode.RoomCannotJoinBecauseSeatNotEnough;
                }
                NKErrorCode preJoinNoticeRet = preJoinNotice(getTeamId(), roomUniversalInfo.getRoomBriefInfo(), type, extraInfo);
                if (preJoinNoticeRet == NKErrorCode.RoomOnlyHimself) {
                    // 发起后队伍只剩自己了，直接加入房间即可
                    exitRoomQuietly(getTeamId());
                    return rpcJoinRoom(roomUniversalInfo.getRoomBriefInfo().getRoomID(), pwd, type, disableMidJoin);
                }
                return preJoinNoticeRet;
            }
            // @3 @6 @10
            // 1.目标队伍时，无论角色，均直接退出并加入对方队伍
            // 2.目标房间是，自己非队长，直接退出当前队伍并加入对方房间
            // 退出原来的队伍
            NKErrorCode extiRet = exitRoom(getTeamId(), RoomExitType.RET_SystemLogicExit);
            if (extiRet != NKErrorCode.OK) {
                return extiRet;
            }
            // 加入新的队伍
            return rpcJoinRoom(roomId, pwd, type, disableMidJoin);
        }
        // @1 @2 @4 @5 @8 @11 @12
        // 目标为队伍、当前没有队伍、不是队长时，都是直接退出，再加入目标队伍/房间
        if (uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID || getTeamId() <= 0 || !isPlayerLeader()) {
            LOGGER.debug("player exit current all team and room info before join, playerUid:{} roomId:{}",
                    player.getUid(), roomId);
            NKErrorCode ret = NKErrorCode.OK;
            // 退出自己所有的队伍/房间
            ret = exitRoom(getTeamId(), RoomExitType.RET_SystemLogicExit);
            if (ret != NKErrorCode.OK) {
                return ret;
            }
            ret = exitRoom(getRoomId(), RoomExitType.RET_SystemLogicExit);
            if (ret != NKErrorCode.OK) {
                return ret;
            }
            return rpcJoinRoom(roomId, pwd, type, disableMidJoin);
        }
        // @7
        // 判断目标房间剩余人数
        UniversalRoom roomUniversalInfo = preJoinTargetRoom(roomId, pwd, getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber(), disableMidJoin);
        if (roomUniversalInfo.getRoomBriefInfo().getCurMemberNumber() + getCurrentTeamInfoBuidler().getRoomBriefInfo()
                .getCurMemberNumber() > roomUniversalInfo.getRoomBriefInfo().getMemberNumberLimit()) {
            // 超过对方房间的人数限制
            return NKErrorCode.RoomCannotJoinBecauseSeatNotEnough;
        }
        // 退出当前房间
        NKErrorCode ret = exitRoom(getRoomId(), RoomExitType.RET_TeamExit);
        if (ret == NKErrorCode.RoomTeamLeaveButQuitHimself) {
            exitRoomQuietly(getTeamId());
            return rpcJoinRoom(roomId, pwd, type, disableMidJoin);
        }
        if (ret != NKErrorCode.OK) {
            return ret;
        }
        NKErrorCode preJoinNoticeRet = preJoinNotice(getTeamId(), roomUniversalInfo.getRoomBriefInfo(), type, extraInfo);
        if (preJoinNoticeRet == NKErrorCode.RoomOnlyHimself) {
            // 发起后队伍只剩自己了，直接加入房间即可
            exitRoomQuietly(getTeamId());
            return rpcJoinRoom(roomId, pwd, type, disableMidJoin);
        }
        return preJoinNoticeRet;
    }

    public NKErrorCode roomJoinByLbsPin(RoomGeoInfo.Builder roomGeoInfoBuilder) {
        // 需要玩家退出当前的队伍和房间
        exitAllRoomQuietly("join room by lbs pin");

        Face2FaceRoomInfo face2FaceRoomInfo = pinReport2Apllo(roomGeoInfoBuilder);
        try {
            SsRoomsvr.RpcJoinRoomReq.Builder rpcJoinRoomReq = SsRoomsvr.RpcJoinRoomReq.newBuilder();
            rpcJoinRoomReq.setUid(player.getUid());
            rpcJoinRoomReq.setRoomId(face2FaceRoomInfo.roomId);
            rpcJoinRoomReq.setMemberBaseInfo(player.getMemberBaseInfo());
            rpcJoinRoomReq.setJoinType(RoomJoinType.RJT_JoinByLbsPin);
            rpcJoinRoomReq.setCreateWhenNotExist(true);
            rpcJoinRoomReq.setRoomGeoInfo(face2FaceRoomInfo.geoInfoBuilder);
            RpcResult<RpcJoinRoomRes.Builder> rpcResult = RoomService.get().rpcJoinRoom(rpcJoinRoomReq);
            if (!rpcResult.isOK()) {
                LOGGER.error("join room fail");
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomisFullJoinFailure.getValue()) {
                    String[] monitorParams = new String[]{
                            roomGeoInfoBuilder.getPinStr()
                    };
                    Monitor.getInstance().add.total(MonitorId.attr_roomsvr_lbs_pin_join_fail_by_full, 1, monitorParams);
                    NKErrorCode.RoomFace2FaceJoinFailCauseIsFull.throwError("join by lbs pin fail");
                }
                // 非手机端是否可以使用面对面建房
                if (rpcResult.getData().getResult() == NKErrorCode.RoomPlayIdBlockedByClientInfo.getValue()) {
                    return NKErrorCode.RoomJoinFailWhenPlayBlockedByClientInfo;
                }
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room join fail, err:{}", e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    private Face2FaceRoomInfo pinReport2Apllo(RoomGeoInfo.Builder roomGeoInfoBuilder) {
        int roomPinCodeAliveLength = MiscConf.getInstance().getMiscConf().getRoomPinCodeAliveLength();
        if (roomPinCodeAliveLength <= 0) {
            roomPinCodeAliveLength = 5 * 60 * 1_000;
        }

        // 去apllo获取托管的roomId
        SsLbssvr.RpcF2FRoomCreateReq.Builder rpcF2FRoomCreateReq = SsLbssvr.RpcF2FRoomCreateReq.newBuilder();
        FaceToFaceRoomType.Builder f2fRoomTypeBuilder = FaceToFaceRoomType.newBuilder();
        f2fRoomTypeBuilder.setMapType(0);
        f2fRoomTypeBuilder.setMapId(0);
        f2fRoomTypeBuilder.setRoomType(RoomType.CustomRoom_VALUE);
        f2fRoomTypeBuilder.setRadius(1000); // TODO 配置化
        rpcF2FRoomCreateReq.setType(f2fRoomTypeBuilder);

        rpcF2FRoomCreateReq.setPin(roomGeoInfoBuilder.getPin());
        rpcF2FRoomCreateReq.setUid(player.getUid());
        rpcF2FRoomCreateReq.setLatitudeE6(roomGeoInfoBuilder.getLatitudeE6());
        rpcF2FRoomCreateReq.setLongitudeE6(roomGeoInfoBuilder.getLongitudeE6());
        rpcF2FRoomCreateReq.setExpireSec(roomPinCodeAliveLength / 1_000);
        try {
            RpcResult<SsLbssvr.RpcF2FRoomCreateRes.Builder> rpcResult = LbsService.get()
                    .rpcF2FRoomCreate(rpcF2FRoomCreateReq);
            if (!rpcResult.isOK()) {
                LOGGER.error("rpcF2FRoomCreate fail, playerUid:{} pin:{}", player.getUid(),
                        roomGeoInfoBuilder.getPin());
                NKErrorCode.RoomServiceIsBusy.throwError("rpcF2FRoomCreate fail");
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                NKErrorCode.forNumber(rpcResult.getData().getResult()).throwError("create room by pin fail");
            }
            if (rpcResult.getData().getRoomId() <= 0) {
                // 需要额外超时去重新获取结果
                NKErrorCode.RoomFace2FaceNotReady.throwError("room not ready now");
            }
            // 设置房间的创建者
            roomGeoInfoBuilder.setCreator(rpcResult.getData().getLeaderUid());
            return new Face2FaceRoomInfo(rpcResult.getData().getRoomId(), roomGeoInfoBuilder);
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room join fail, err:{}", e.getEnumErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError("rpcF2FRoomCreate fail");
        }
        return null;
    }

    /**
     * 预加入目标房间
     *
     * @param targetRoomId 目标房间id
     * @param pwd 密码
     * @return
     */
    private UniversalRoom preJoinTargetRoom(long targetRoomId, String pwd, int teamMemberCnt, boolean disableMidJoin) {
        UniversalRoom.Builder roomUniversalInfo = getRoomUniversalInfo(targetRoomId);
        if (roomUniversalInfo == null) {
            NKErrorCode.RoomMultiPlayerRoomNotExist.throwError("target room not exist");
        }
        if (roomUniversalInfo.getRoomBriefInfo().getCurMemberNumber() + teamMemberCnt >
                roomUniversalInfo.getRoomBriefInfo().getMemberNumberLimit()) {
            NKErrorCode.RoomCannotJoinBecauseSeatNotEnough.throwError("target room has not enough seat");
        }
        if (roomUniversalInfo.getRoomBriefInfo().getHasPass()) {
            RoomSpecialSetting specialSetting = null;
            for (RoomSpecialSetting roomSpecialSetting : roomUniversalInfo.getRoomSettingInfo()
                    .getSpecialSettingsList()) {
                if (roomSpecialSetting.getType() == RoomSpecialSettingType.RSST_Pwd_VALUE) {
                    specialSetting = roomSpecialSetting;
                    break;
                }
            }
            if (specialSetting != null && specialSetting.getKvArray().getArrayCount() > 0) {
                KVEntry entry = specialSetting.getKvArray().getArray(0);
                if (!RoomUtil.checkPwd(pwd, entry)) {
                    NKErrorCode.RoomPasswordMismatch.throwError("pwd not match");
                }
            } else {
                LOGGER.debug("room has not pwd about setting info, let it to request roomsvr to check pwd, playerUid:{}, roomId:{}",
                        player.getUid(), targetRoomId);
            }
        }
        SsRoomsvr.RpcPreJoinWithTeamReq.Builder preJoinReq = SsRoomsvr.RpcPreJoinWithTeamReq.newBuilder();
        preJoinReq.setRoomId(targetRoomId).setPwd(pwd).setEnableMidJoin(!disableMidJoin);
        LongArray.Builder longArray = LongArray.newBuilder();
        for (int i = 0; i < getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber(); i++) {
            longArray.addArray(player.getUid()); // 目前以当前玩家填充列表
            // TODO 后续以当前队伍所有成员uid列表传递到目标房间，避免队伍有成员在目标房间内
        }
        try {
            RpcResult<SsRoomsvr.RpcPreJoinWithTeamRes.Builder> roomInfoRsp = RoomService.get()
                    .rpcPreJoinWithTeam(preJoinReq);
            if (roomInfoRsp.getRet() != NKErrorCode.OK.getValue()) {
                // TODO 是否需要替换错误码
                if (roomInfoRsp.getRet() == NKErrorCode.RoomNotExist.getValue()) {
                    NKErrorCode.RoomMultiPlayerRoomNotExist.throwError("room not exist");
                }
                NKErrorCode.forNumber(roomInfoRsp.getRet()).throwError("room prejoin notice fail");
            }
            if (roomInfoRsp.getData().getResult() != NKErrorCode.OK.getValue()) {
                NKErrorCode.forNumber(roomInfoRsp.getData().getResult()).throwError("room prejoin notice fail");
            }
            return roomInfoRsp.getData().getRoomInfo();
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("player-{} preJoinTargetRoom fail : roomid:{} e:{}", player.getUid(), targetRoomId, e.getErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError("room pre join room fail");
        }
        return null;
    }

    private NKErrorCode preJoinNotice(long selfRoomId, RoomBriefInfo targetRoomBriefInfo, RoomJoinType type, TeamJoinRoomExtraInfo extraInfo) {
        SsRoomsvr.RpcRoomPreJoinMemberNoticeReq.Builder preJoinNoticeReq = SsRoomsvr.RpcRoomPreJoinMemberNoticeReq.newBuilder();
        preJoinNoticeReq.setRoomId(selfRoomId)
                .setOpPlayerUid(player.getUid())
                .setRoomBriefInfo(targetRoomBriefInfo)
                .setJoinType(type)
                .setExtraInfo(extraInfo);
        try {
            RpcResult<SsRoomsvr.RpcRoomPreJoinMemberNoticeRes.Builder> roomInfoRsp = RoomService.get()
                    .rpcRoomPreJoinMemberNotice(preJoinNoticeReq);
            if (roomInfoRsp.getData().getResult() != NKErrorCode.OK.getValue()) {
                // TODO 是否需要替换错误码
                if (roomInfoRsp.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(selfRoomId, "pre join notice but room not exist");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(roomInfoRsp.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("player-{} preJoinNotice fail : roomid:{} e:{}", player.getUid(), selfRoomId, e.getErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError("room pre join notice fail");
        }
        return NKErrorCode.OK;
    }

    public void preJoinNoticeNtf(long uniqueId, RoomBriefInfo targetRoomBriefInfo, TeamJoinRoomExtraInfo extraInfo) {
        if (player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Battle) {
            confirmPreJoin(getTeamId(), uniqueId, false);
            return;
        }
        CsRoom.TeamJoinRoomConfirmNtf.Builder ntfBuilder = CsRoom.TeamJoinRoomConfirmNtf.newBuilder();
        ntfBuilder.setUniqueId(uniqueId);
        ntfBuilder.setRoomBriefInfo(targetRoomBriefInfo);
        ntfBuilder.setExtraInfo(extraInfo);
        LOGGER.debug("player recv team join room confirm ntf, player:{} ntf:{}", player.getUid(), ntfBuilder);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_TEAMJOINROOMCONFIRMNTF, ntfBuilder);
    }

    public NKErrorCode confirmPreJoin(long clientRoomId, long uniqueId, boolean confirmOrNot) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcRoomPreJoinNoticeConfirmReq.Builder preJoinNoticeConfirmReq = SsRoomsvr.RpcRoomPreJoinNoticeConfirmReq.newBuilder();
            preJoinNoticeConfirmReq.setRoomId(currentRoomId).setOpPlayerUid(player.getUid())
                    .setUniqueId(uniqueId).setConfirmOrNot(confirmOrNot);
            RpcResult<SsRoomsvr.RpcRoomPreJoinNoticeConfirmRes.Builder> roomInfoRsp = RoomService.get()
                    .rpcRoomPreJoinNoticeConfirm(preJoinNoticeConfirmReq);
            if (roomInfoRsp.getRet() != NKErrorCode.OK.getValue()) {
                // TODO 是否需要替换错误码
                if (roomInfoRsp.getRet() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(clientRoomId, "confirm pre join notice but room not exist");
                    return NKErrorCode.OK;
                }
                NKErrorCode.forNumber(roomInfoRsp.getRet()).throwError("room prejoin notice confirm fail");
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("player-{} confirmPreJoin fail : roomId:{} e:{}", player.getUid(), currentRoomId, e.getErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError("room pre join confirm fail");
        }
        return NKErrorCode.OK;
    }

    /**
     * 通过分享链接加入
     *
     * @param roomId 房间id
     * @param type   分享类型
     * @return
     */
    public NKErrorCode roomJoinFromShare(long roomId, RoomShareType type) {
        RoomJoinType joinType = RoomJoinType.RJT_Default;
        if (type == RoomShareType.RST_ShortcutRoom) {
            joinType = RoomJoinType.RJT_ShortcutRoom;
            // 前置客户端做了拦截，玩家只要有显式的组队或者房间行为都不会触发此逻辑
            // 所以此处做一个保护，直接清空队伍、房间信息
            exitAllRoomQuietly("join from share");
            return rpcJoinRoom(roomId, "", joinType);
        } else if (type == RoomShareType.RST_QRCode) {
            if (player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Battle) {
                NKErrorCode.RoomCannotJoinBecauseSelfInBattle.throwError("self in battle when join by qrcode");
            }
            joinType = RoomJoinType.RJT_QRCode;
        }
        return roomJoin(roomId, "", joinType);
    }

    public NKErrorCode ugcRoomQuickJoin(long mapId) {
        RoomService roomSvr = RoomService.get();
        try {
            SsRoomsvr.RpcUgcRoomQuickJoinReq.Builder rpcReq = SsRoomsvr.RpcUgcRoomQuickJoinReq.newBuilder();
            rpcReq.setMapId(mapId);
            rpcReq.setJoinPlayer(player.getMemberBaseInfo());
            RpcResult<RpcUgcRoomQuickJoinRes.Builder> rpcResult = roomSvr.rpcUgcRoomQuickJoin(rpcReq);
            if (!rpcResult.isOK()) {
                LOGGER.error("quick join ugc custom room fail");
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (Exception e) {
            LOGGER.error("quick join ugc custom room fail");
            // TODO errcode定义 及错误抛出
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    @Deprecated
    public NKErrorCode ugcRoomPreStart() {
        if (player.getCurrentRoomId() == 0L) { // @Deprecated
            return NKErrorCode.RoomNotExist;
        }
        try {
            RpcResult<SsRoomsvr.RpcUgcRoomPreStartRes.Builder> rpcRet = RoomService.get()
                    .rpcUgcRoomPreStart(SsRoomsvr.RpcUgcRoomPreStartReq.newBuilder()
                            .setRoomId(player.getCurrentRoomId()).setOpPlayerUid(player.getUid())); // @Deprecated
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                NKErrorCode.forNumber(rpcRet.getRet()).throwError("ugc room pre start fail");
            }
        } catch (NKRuntimeException e) {
            LOGGER.error("room ugc pre start fail: {}", e.getMessage());
            return NKErrorCode.RoomError;
        } catch (Exception e) {
            LOGGER.error("room ugc pre start fail");
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    /**
     * 获取房间的推荐列表
     *
     * @param type 房间类型：后续主玩法、ugc均走此接口
     * @return
     */
    public NKPair<UniversalRoomArray, NKErrorCode> roomReccomendList(RoomType type) {
        RoomService roomSvr = RoomService.get();
        try {
            RpcResult<SsRoomsvr.RpcUgcRoomListRes.Builder> rpcRet = roomSvr
                    .rpcUgcRoomList(SsRoomsvr.RpcUgcRoomListReq.newBuilder()
                            .setMapId(-1L));
            if (NKErrorCode.OK.getValue() != rpcRet.getRet()) {
                // 处理错误逻辑
                NKErrorCode.forNumber(rpcRet.getRet()).throwError("query room list fail");
            }
            UniversalRoomArray.Builder listBuilder = UniversalRoomArray.newBuilder();
            for (UgcRoomInfo ugcRoomInfo : rpcRet.getData().getRoomList().getUgcRoomsList()) {
                listBuilder.addRooms(
                        UniversalRoom.newBuilder().setRoomBriefInfo(ugcRoomInfo.getRoomBriefInfo()).build());
            }
            return new NKPair<>(listBuilder.build(), NKErrorCode.OK);
        } catch (NKRuntimeException e) {
            LOGGER.error("room ugc list fail: {}", e.getMessage());
            return new NKPair(null, NKErrorCode.forNumber(e.getErrCode()));
        } catch (Exception e) {
            LOGGER.error("room ugc list fail : {}", e.getMessage());
            return new NKPair(null, NKErrorCode.RoomError);
        }
    }

    public UniversalRoomArray getUgcRoomRecommendList(long ugcId, int num, RecListType type, long mapType, long mapTag,
            int source) {
        UniversalRoomArray recommendUgcCustomRoom = UniversalRoomArray.newBuilder().build();
        // 如果是星世界推荐-一起来玩-房间列表 就不要密码房
        boolean noPwd = source == RoomRecommendListSource.RRLS_UgcStarWorld_VALUE || source == RoomRecommendListSource.RRLS_UgcMapDetail_VALUE;
        switch (type) {
            case RLT_Map:
                if (ugcId <= 0) {
                    // 兼容无筛选条件时的请求
                    recommendUgcCustomRoom = player.getPlayerRoomMgr()
                            .getRecommendUgcCustomRoomByMapType(num, 0, source, noPwd, false);
                    break;
                }
                // 这里不需要判断是不是不要密码房，因为指定的地图是需要带密码房的
                recommendUgcCustomRoom = player.getPlayerRoomMgr().getRecommendUgcCustomRoomList(num, ugcId, source, false);
                break;
            case RLT_Type:
                recommendUgcCustomRoom = player.getPlayerRoomMgr().getRecommendUgcCustomRoomByMapType(num, mapType, source, noPwd, false);
                break;
            case RLT_Tag:
                recommendUgcCustomRoom = player.getPlayerRoomMgr().getRecommendUgcCustomRoomByMapTag(num, mapTag, source, noPwd, false);
                break;
        }
        this.lastUgcCustomRoomResultList = recommendUgcCustomRoom;
        return recommendUgcCustomRoom;
        /*if (recommendUgcCustomRoomList == null) {
            return retBuilder.build();
        }
        if (ugcId > 0 && recommendUgcCustomRoomList.getRoomsCount() > 0) {
            // 查询地图信息
            UgcBaseInfo ugcBaseInfo = SsRpcUgcManager.getInstance()
                    .ugcGetBaseInfo(player, ugcId);
            if (ugcBaseInfo == null) {
                LOGGER.error("cannot ger ugc map info, ugcId:{}", ugcId);
                return retBuilder.build();
            }
            UgcBriefInfo.Builder ugcBuilder = UgcBriefInfo.newBuilder().setUgcId(ugcId);
            ugcBuilder.setName(ugcBaseInfo.getName());
            ugcBuilder.setDesc(ugcBaseInfo.getDesc());
            ugcBuilder.setCreateTime(ugcBaseInfo.getCreateTime());
            ugcBuilder.setEditorName(ugcBaseInfo.getEditorName());
            ugcBuilder.setTemplateId(ugcBaseInfo.getTemplateId());
            ugcBuilder.setOldUgcId(ugcBaseInfo.getOldUgcId());
            ugcBuilder.setTags(ugcBaseInfo.getTags());
            ugcBuilder.setBucket();
            for (UgcMapMetaInfo ugcMapMetaInfo : ugcBaseInfo.getMdList().getInfoList()) {
                if (ugcMapMetaInfo.getMsgType() == UgcMapMetaInfoMsgType.METAINFO_MSG_TYPE_PREVIEW_PIC_VALUE) {
                    ugcBuilder.setCoverMetaInfo(ugcMapMetaInfo);
                }
            }
            ugcBuilder.setEditorAvatar(ugcBaseInfo.getEditorAvatar());
            ugcBuilder.setUgcType(ugcBaseInfo.getUgcType());
            ugcBuilder.setMdList(ugcBaseInfo.getMdList());
            for (UniversalRoom universalRoom : recommendUgcCustomRoomList.getRoomsList()) {
                UniversalRoom.Builder roomBuilder = universalRoom.toBuilder();
                roomBuilder.getRoomUgcInfoBuilder().setUgcBriefInfo(ugcBuilder);
                retBuilder.addRooms(roomBuilder);
            }
        } else {
            for (UniversalRoom universalRoom : recommendUgcCustomRoomList.getRoomsList()) {
                UniversalRoom.Builder roomBuilder = universalRoom.toBuilder();
                UgcBaseInfo ugcBaseInfo = SsRpcUgcManager.getInstance()
                        .ugcGetBaseInfo(player, roomBuilder.getRoomUgcInfo().getMapId());
                if (ugcBaseInfo == null) {
                    LOGGER.error("cannot ger ugc map info, ugcId:{}", ugcId);
                    continue;
                }
                UgcBriefInfo.Builder ugcBuilder = UgcBriefInfo.newBuilder()
                        .setUgcId(roomBuilder.getRoomUgcInfo().getMapId());
                ugcBuilder.setName(ugcBaseInfo.getName());
                ugcBuilder.setDesc(ugcBaseInfo.getDesc());
                ugcBuilder.setCreateTime(ugcBaseInfo.getCreateTime());
                ugcBuilder.setEditorName(ugcBaseInfo.getEditorName());
                ugcBuilder.setTemplateId(ugcBaseInfo.getTemplateId());
                ugcBuilder.setOldUgcId(ugcBaseInfo.getOldUgcId());
                ugcBuilder.setTags(ugcBaseInfo.getTags());
                for (UgcMapMetaInfo ugcMapMetaInfo : ugcBaseInfo.getMdList().getInfoList()) {
                    if (ugcMapMetaInfo.getMsgType() == UgcMapMetaInfoMsgType.METAINFO_MSG_TYPE_PREVIEW_PIC_VALUE) {
                        ugcBuilder.setCoverMetaInfo(ugcMapMetaInfo);
                    }
                }
                ugcBuilder.setEditorAvatar(ugcBaseInfo.getEditorAvatar());
                ugcBuilder.setUgcType(ugcBaseInfo.getUgcType());
                ugcBuilder.setMdList(ugcBaseInfo.getMdList());
                roomBuilder.getRoomUgcInfoBuilder().setUgcBriefInfo(ugcBuilder);
                retBuilder.addRooms(roomBuilder);
            }
        }

        return retBuilder.build();*/
    }

    public NKErrorCode roomQuickJoin(RoomType type, long... node) {
        exitAllRoom("room quick join");
        // 通过PublicRoomMgr进行快速加入
        Set<Long> historyRoomIds = new HashSet<>();
        RoomPublicType publicType = RoomPublicType.RPT_Unknown;
        switch (type) {
            case CustomRoom:
                publicType = RoomPublicType.RPT_CustomLobby;
                if (this.lastCustomRoomResultList.getRoomsCount() > 0) {
                    for (UniversalRoom universalRoom : this.lastCustomRoomResultList.getRoomsList()) {
                        historyRoomIds.add(universalRoom.getRoomBriefInfo().getRoomID());
                    }
                }
                break;
            case UgcCustomRoom:
                publicType = RoomPublicType.RPT_UgcCustomLobby;
                if (this.lastUgcCustomRoomResultList.getRoomsCount() > 0) {
                    for (UniversalRoom universalRoom : this.lastUgcCustomRoomResultList.getRoomsList()) {
                        historyRoomIds.add(universalRoom.getRoomBriefInfo().getRoomID());
                    }
                }
                break;
            default:

        }
        if (publicType == RoomPublicType.RPT_Unknown) {
            NKErrorCode.InvalidParams.throwError("wrong room type for quick join");
        }
        return PublicRoomMgr.getInstance().quickJoin(player, publicType, historyRoomIds, node);
    }

    private NKPair<RoomPublicType, List<PublicRoom>> getQuickJoinHistoryRoomIds(RoomType type) {
        List<PublicRoom> historyRoom = new ArrayList<>();
        RoomPublicType publicType = RoomPublicType.RPT_Unknown;
        switch (type) {
            case UgcCustomRoom:
                publicType = RoomPublicType.RPT_UgcCustomLobby;
                if (this.lastUgcCustomRoomResultList.getRoomsCount() > 0) {
                    for (UniversalRoom universalRoom : this.lastUgcCustomRoomResultList.getRoomsList()) {
                        PublicRoom publicRoom = new PublicRoom(UniversalRoom.newBuilder().mergeFrom(universalRoom));
                        historyRoom.add(publicRoom);
                    }
                }
                break;
            default:
                break;
        }
        if (publicType == RoomPublicType.RPT_Unknown) {
            NKErrorCode.InvalidParams.throwError("wrong room type for quick join");
        }
        return new NKPair<>(publicType, historyRoom);
    }

    public NKErrorCode roomQuickJoinSetExtraInfo(RoomType type, TeamJoinRoomExtraInfo.Builder extraInfo, long... node) {
        if (type == RoomType.UgcCustomRoom) {
            if (node.length > 0 && node[0] != 0) {
                if (this.lastUgcCustomRoomResultList.getRoomsCount() == 0) {
                    LOGGER.error("no enough room");
                    return NKErrorCode.RoomQuickJoinFailNoAvailableChoice;
                }
                var universalRoom = this.lastUgcCustomRoomResultList.getRoomsList().get(0);
                if (universalRoom == null
                        || universalRoom.getRoomUgcInfo().getUgcBriefInfo().getUgcId() != node[0]) {
                    LOGGER.error("ugcId check fail");
                    return NKErrorCode.RoomQuickJoinFailNoAvailableChoice;
                }
                extraInfo.getQuickJoinInfoBuilder().getUgcInfoBuilder().setUgcId(node[0])
                        .setName(universalRoom.getRoomUgcInfo().getUgcBriefInfo().getName());
            }
        }
        return NKErrorCode.OK;
    }

    // 允许中途加入的快速加入
    public NKErrorCode roomQuickJoinWithMidJoin(RoomType type, boolean wentMidJoin, long... node) {
        if (player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Matching
                || player.getPlayerStateMgr().getRoomState() == PlayerStateType.PST_TeamMatching
                || player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Battle) {
            // 玩家状态不允许快速加入了
            LOGGER.error("player:{} state error", player.getUid());
            return NKErrorCode.RoomCannotJoinCauseSelfAtMatching;
        }

        // 构建快速加入信息
        List<Long> params = Arrays.stream(node)
                .boxed()
                .collect(Collectors.toList());
        TeamJoinRoomExtraInfo.Builder extraInfo = TeamJoinRoomExtraInfo.newBuilder();
        extraInfo.setType(TeamJoinRoomExtraInfoType.TJREIT_QuickJoinInfo_VALUE);
        extraInfo.getQuickJoinInfoBuilder()
                .setIsQuickJoin(true)
                .addAllParams(params)
                .setRoomType(type)
                .setWentMidJoin(wentMidJoin);

        // 检查是否组队
        if (getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid() == player.getUid()
                && getCurrentTeamInfoBuidler().getRoomBriefInfo().getCurMemberNumber() > 1) {
            // 判断下开关
            if (PropertyFileReader.getRealTimeBooleanItem("room_quick_join_team_disable", false)) {
                LOGGER.info("disable team quick join");
                return NKErrorCode.RoomQuickJoinTeamDisable;
            }

            // 先退出房间
            LOGGER.debug("player exit team and room, playerUid:{} reason:{}", player.getUid(), "room quick join");
            // 只退出房间
            exitRoom(getRoomId(), RoomExitType.RET_SucceedJoin).throwErrorIfNotOk("exit room fail");

            // 执行正常流程
            RoomJoinType joinType = RoomJoinType.RJT_CustomRoomQuickJoin;
            if (type == RoomType.UgcCustomRoom) {
                joinType = RoomJoinType.RJT_UgcCustomRoomQuickJoin;
            }
            var ec = roomQuickJoinSetExtraInfo(type, extraInfo, node);
            if (ec != NKErrorCode.OK) {
                String[] monitorParams = { String.valueOf(joinType) };
                Monitor.getInstance().add.fail(MonitorId.attr_room_quick_join_pre, 1, monitorParams);
                return ec;
            }

            LOGGER.info("player:{} try pre quick join with team", player.getUid());
            // 开始执行预加入逻辑
            return preJoinQuickJoinNotice(getTeamId(), extraInfo.build(), joinType);
        }

        LOGGER.debug("player exit team and room, playerUid:{} reason:{}", player.getUid(), "room quick join");
        // 如果是单人的就都要退出
        exitRoom(getRoomId(), RoomExitType.RET_SucceedJoin).throwErrorIfNotOk("exit room fail");
        exitRoom(getTeamId(), RoomExitType.RET_SucceedJoin).throwErrorIfNotOk("exit team fail");

        LOGGER.info("player:{} try quick join single", player.getUid());
        // 执行加入逻辑
        Set<Long> uidList = new HashSet<>();
        uidList.add(player.getUid());
        return doRoomQuickJoinWithMidJoin(type, uidList, wentMidJoin, TeamJoinRoomExtraInfo.getDefaultInstance(), 0, node);
    }

    private NKErrorCode preJoinQuickJoinNotice(long selfRoomId, TeamJoinRoomExtraInfo extraInfo, RoomJoinType type) {
        SsRoomsvr.RpcRoomPreJoinMemberNoticeReq.Builder preJoinNoticeReq = SsRoomsvr.RpcRoomPreJoinMemberNoticeReq.newBuilder();
        preJoinNoticeReq.setRoomId(selfRoomId)
                .setOpPlayerUid(player.getUid())
                .setExtraInfo(extraInfo)
                .setJoinType(type);
        try {
            RpcResult<SsRoomsvr.RpcRoomPreJoinMemberNoticeRes.Builder> roomInfoRsp = RoomService.get()
                    .rpcRoomPreJoinMemberNotice(preJoinNoticeReq);
            if (roomInfoRsp.getData().getResult() != NKErrorCode.OK.getValue()) {
                // TODO 是否需要替换错误码
                if (roomInfoRsp.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    ntfClientClearRoomInfo(selfRoomId, "pre join notice but room not exist");
                    return NKErrorCode.OK;
                }
                // 打上监控
                if (roomInfoRsp.getData().getResult() != NKErrorCode.OK.getValue()) {
                    String[] monitorParams = {String.valueOf(type)};
                    Monitor.getInstance().add.fail(MonitorId.attr_room_quick_join_pre, 1, monitorParams);
                }
                // 返回结果
                return NKErrorCode.forNumber(roomInfoRsp.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            String[] monitorParams = { String.valueOf(type) };
            Monitor.getInstance().add.fail(MonitorId.attr_room_quick_join_pre, 1, monitorParams);
            // 报错
            LOGGER.error("player-{} preJoinNotice fail : roomid:{} e:{}", player.getUid(), selfRoomId, e.getErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError("room pre join notice fail");
        }
        return NKErrorCode.OK;
    }

    // 执行快速房间加入逻辑
    private NKErrorCode doRoomQuickJoinWithMidJoin(RoomType type, Set<Long> uidList, boolean allowMidJoin,
            TeamJoinRoomExtraInfo extraInfo, long uniqueId, long... node) {
        var historyRoom = getQuickJoinHistoryRoomIds(type);

        // quickJoin添加一个extraInfo 用来处理直接加入房间
        var joinRet = PublicRoomMgr.getInstance()
                .quickJoinWithMidJoin(player, historyRoom.getKey(), historyRoom.getValue(), uidList, allowMidJoin, extraInfo,
                        node);
        LOGGER.info("player:{} ec:{} roomId:{}", player.getUid(), joinRet.getKey(), joinRet.getValue());

        // 打个监控
        String[] monitorParams = { String.valueOf(type) };
        if (joinRet.getKey() != NKErrorCode.OK) {
            Monitor.getInstance().add.fail(MonitorId.attr_room_quick_join, 1, monitorParams);
        } else {
            Monitor.getInstance().add.succ(MonitorId.attr_room_quick_join, 1, monitorParams);
        }

        // 发个结果通知
        ntfQuickJoinWithMidJoinResult(type, joinRet.getKey().getValue(), joinRet.getValue(), uniqueId);
        return joinRet.getKey();
    }

    // 执行快速房间加入逻辑
    public void doRoomQuickJoinWithMidJoin(Set<Long> uidList, TeamQuickJoinRoomInfo quickJoinInfo, long uniqueId) {
        if (player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Matching
                || player.getPlayerStateMgr().getRoomState() == PlayerStateType.PST_TeamMatching
                || player.getPlayerStateMgr().getPlayerState() == PlayerStateType.PST_Battle) {
            // 玩家状态不允许快速加入了
            LOGGER.error("player:{} state error", player.getUid());
            return;
        }

        // 开始加入房间
        RoomType type = quickJoinInfo.getRoomType();
        long[] node = quickJoinInfo.getParamsList().stream().mapToLong(Long::longValue).toArray();

        // 这里因为是远程多人回来的，所以只退出房间
        LOGGER.debug("player exit team and room, playerUid:{} reason:{}", player.getUid(), "room quick join");
        NKErrorCode ec = exitRoom(getRoomId(), RoomExitType.RET_SucceedJoin);
        if (ec != NKErrorCode.OK) {
            // 发个结果通知
            ntfQuickJoinWithMidJoinResult(type, ec.getValue(), 0, uniqueId);
            // 打个监控
            String[] monitorParams = { String.valueOf(type) };
            Monitor.getInstance().add.fail(MonitorId.attr_room_quick_join, 1, monitorParams);
            return;
        }

        TeamJoinRoomExtraInfo.Builder extraInfo = TeamJoinRoomExtraInfo.newBuilder();
        extraInfo.setType(TeamJoinRoomExtraInfoType.TJREIT_DirectJoin_VALUE);
        extraInfo.getDirectInfoBuilder().setAllowMidJoin(quickJoinInfo.getWentMidJoin());
        doRoomQuickJoinWithMidJoin(type, uidList, quickJoinInfo.getWentMidJoin(), extraInfo.build(), uniqueId, node);
    }

    private void ntfQuickJoinWithMidJoinResult(RoomType type, int errorCode, long roomId, long uniqueId) {
        if (type == RoomType.UgcCustomRoom) {
            CsUgc.UgcQuickJoinWithMidJoinResultNtf.Builder resultNtf = CsUgc.UgcQuickJoinWithMidJoinResultNtf.newBuilder();
            resultNtf.setErrCode(errorCode);
            resultNtf.setRoomId(roomId);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_UGCQUICKJOINWITHMIDJOINRESULTNTF, resultNtf);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("type:{} roomId:{} errorCode:{}", type, roomId, errorCode);
            }
        } else {
            LOGGER.error("room roomId:{} errorCode:{} type:{} error", roomId, errorCode, type);
        }
        // 通知下room这次的组队确认已经完成了
        teamQuickJoinFinishNtf(uniqueId);
    }

    private void teamQuickJoinFinishNtf(long uniqueId) {
        long selfRoomId = getTeamId();
        if (selfRoomId == 0 || uniqueId <= 0) {
            return;
        }
        SsRoomsvr.RpcTeamQuickJoinFinishNtfReq.Builder quickJoinFinishNtf = SsRoomsvr.RpcTeamQuickJoinFinishNtfReq.newBuilder();
        quickJoinFinishNtf.setRoomId(selfRoomId).setUniqueId(uniqueId);
        try {
            RoomService.get().rpcTeamQuickJoinFinishNtf(quickJoinFinishNtf);
        } catch (Exception e) {
            LOGGER.error("player-{} teamQuickJoinFinishNtf fail : selfRoomId:{} e:{}", player.getUid(), selfRoomId, e);
        }
    }

    private NKErrorCode rpcJoinRoom(long roomId, String pwd, RoomJoinType type) {
        return rpcJoinRoom(roomId, pwd, type, false);
    }

    /**
     * 加入房间
     *
     * @param roomId
     * @param pwd
     * @return
     */
    private NKErrorCode rpcJoinRoom(long roomId, String pwd, RoomJoinType type, boolean disableMidJoin) {
        RoomService roomSvr = RoomService.get();
        try {
            SsRoomsvr.RpcJoinRoomReq.Builder rpcJoinRoomReq = SsRoomsvr.RpcJoinRoomReq.newBuilder();
            rpcJoinRoomReq.setUid(player.getUid());
            rpcJoinRoomReq.setRoomId(roomId);
            rpcJoinRoomReq.setMemberBaseInfo(player.getMemberBaseInfo());
            rpcJoinRoomReq.setPwd(pwd);
            rpcJoinRoomReq.setJoinType(type);
            rpcJoinRoomReq.setDisableMidJoin(disableMidJoin);
            RpcResult<RpcJoinRoomRes.Builder> rpcResult = roomSvr.rpcJoinRoom(rpcJoinRoomReq);
            if (!rpcResult.isOK()) {
                LOGGER.error("join room fail");
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()) {
                    getRoomNotExistErrorCode(roomId).throwError("room not exist");
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomModePlayerIsBanLadder.getValue()) {
                    // ntf提醒玩家被封禁
                    // TODO 根据res返回的目标房间的matchType，如果非0需要调用checkIsBanGameModeAndNotify
                    player.checkIsBanLadderAndNotify();
//                    player.checkIsBanGameModeAndNotify();
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomStateIsInBattle.getValue()) {
                    NKErrorCode.RoomCannotJoinCauseTargetRoomIsInBattle.throwError("target room in battle");
                }
                if (rpcResult.getData().getResult() == NKErrorCode.RoomPlayIdBlockedByClientInfo.getValue()) {
                    return NKErrorCode.RoomJoinFailWhenPlayBlockedByClientInfo;
                }
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException |RpcException e) {
            LOGGER.error("room join fail, err:{}", e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomAddRobot(long roomId, boolean oneOrAll, int position) {
        // 前置判断了必须为房间id
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        // 后两种均需要请求roomsvr触发当前房间的ntf补发，只是id不同时跳过判断直接补发
        SsRoomsvr.RpcRoomAddRobotReq.Builder reqBuilder = SsRoomsvr.RpcRoomAddRobotReq.newBuilder()
                .setRoomId(currentRoomId);
        reqBuilder.setOpPlayerUid(player.getUid());
        reqBuilder.setOneOrAll(oneOrAll);
        reqBuilder.setPosition(position);
        try {
            RpcResult<SsRoomsvr.RpcRoomAddRobotRes.Builder> rpcResult = RoomService.get().rpcRoomAddRobot(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room add robot fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomError;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room add robot fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomAddRobotByGm(long currentRoomId, boolean oneOrAll, List<CompetitionData> competitionDataList) {
        // 后两种均需要请求roomsvr触发当前房间的ntf补发，只是id不同时跳过判断直接补发
        SsRoomsvr.RpcRoomAddRobotReq.Builder reqBuilder = SsRoomsvr.RpcRoomAddRobotReq.newBuilder()
                .setRoomId(currentRoomId);
        reqBuilder.setOpPlayerUid(0);
        reqBuilder.setOneOrAll(oneOrAll);
        reqBuilder.addAllCompetitionData(competitionDataList);
//        reqBuilder.setPosition(position);
        try {
            RpcResult<SsRoomsvr.RpcRoomAddRobotRes.Builder> rpcResult = RoomService.get().rpcRoomAddRobot(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room add robot fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomError;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room add robot fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomRemoveRobot(long roomId, boolean oneOrAll, int position) {
        // 前置判断了必须为房间id
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        // 后两种均需要请求roomsvr触发当前房间的ntf补发，只是id不同时跳过判断直接补发
        SsRoomsvr.RpcRoomRemoveRobotReq.Builder reqBuilder = SsRoomsvr.RpcRoomRemoveRobotReq.newBuilder()
                .setRoomId(currentRoomId);
        reqBuilder.setOpPlayerUid(player.getUid());
        reqBuilder.setOneOrAll(oneOrAll);
        reqBuilder.setPosition(position);
        try {
            RpcResult<SsRoomsvr.RpcRoomRemoveRobotRes.Builder> rpcResult = RoomService.get()
                    .rpcRoomRemoveRobot(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room add robot fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomError;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room remove robot fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode updateMapDownloadingProcess(long roomId, long mapId, int percent) {
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        SsRoomsvr.RpcRoomMapDownloadProcessBroadcastReq.Builder reqBuilder = SsRoomsvr.RpcRoomMapDownloadProcessBroadcastReq
                .newBuilder().setRoomId(currentRoomId);
        reqBuilder.setSender(player.getUid());
        reqBuilder.setMapId(mapId);
        reqBuilder.setPercent(percent);
        try {
            RpcResult<SsRoomsvr.RpcRoomMapDownloadProcessBroadcastRes.Builder> rpcResult = RoomService.get().
                    rpcRoomMapDownloadProcessBroadcast(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room broadcast fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomError;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room broadcast fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error(e);
            return NKErrorCode.RoomError;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode reminderMapDownload(long roomId, long mapId) {
        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(roomId, "");
            return NKErrorCode.OK;
        }
        SsRoomsvr.RpcRoomMapDownloadReminderReq.Builder reqBuilder = SsRoomsvr.RpcRoomMapDownloadReminderReq
                .newBuilder().setRoomId(currentRoomId).setSender(player.getUid());
        reqBuilder.setSender(player.getUid());
        reqBuilder.setMapId(mapId);
        try {
            RpcResult<SsRoomsvr.RpcRoomMapDownloadReminderRes.Builder> rpcResult = RoomService.get().
                    rpcRoomMapDownloadReminder(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room broadcast fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomServiceIsBusy;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room map download reminder fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room map download reminder fail, playerUid:{} roomId:{} e:{}", player.getUid(), roomId,
                    e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode reminderPlayerReady(long clientRoomId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        SsRoomsvr.RpcRoomPlayerReadyReminderReq.Builder reqBuilder = SsRoomsvr.RpcRoomPlayerReadyReminderReq.newBuilder();
        reqBuilder.setRoomId(currentRoomId);
        reqBuilder.setOpPlayerUid(player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcRoomPlayerReadyReminderRes.Builder> rpcResult = RoomService.get().
                    rpcRoomPlayerReadyReminder(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room player ready remind fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomServiceIsBusy;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room map download reminder fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player ready reminder fail, playerUid:{} roomId:{} e:{}",player.getUid(), currentRoomId,
                    e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomMapStateSync(long clientRoomId, long mapId, int type, int process) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        SsRoomsvr.RpcRoomMapStateSyncReq.Builder reqBuilder = SsRoomsvr.RpcRoomMapStateSyncReq.newBuilder();
        reqBuilder.setRoomId(currentRoomId);
        reqBuilder.setMapId(mapId);
        reqBuilder.setPlayerUid(player.getUid());
        reqBuilder.setType(type);
        reqBuilder.setProcess(process);
        try {
            RpcResult<SsRoomsvr.RpcRoomMapStateSyncRes.Builder> rpcResult = RoomService.get().
                    rpcRoomMapStateSync(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room map state sync fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomServiceIsBusy;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room map state sync fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player ready reminder fail, playerUid:{} roomId:{} e:{}", player.getUid(), currentRoomId,
                    e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode roomBroadcaseData(long clientRoomId, RoomBroadcastInfo data) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        if(data.getType() == RoomBroadcastInfoType.RBIT_PlayerClientData) {
            if(!GSConfig.openRoomClientMemberInfoSync()) {
                return NKErrorCode.Unsupported;
            }
            List<RoomPlayerClientInfo> list = data.getRoomPlayerClientInfoList().getInfoListList();
            if(list.size() == 1) {
                RoomPlayerClientInfo roomPlayerClientInfo = list.get(0);
                if(roomPlayerClientInfo.getUid() == player.getUid()) {
                    //队伍语音状态，在玩家身上保存下，在创建自定义房间的时候能带进去
                    player.getUserAttr().getRoomInfo().getRoomMemberClientInfo().setVoiceState(roomPlayerClientInfo.getRoomMemberClientInfo().getVoiceState());
                    player.getUserAttr().getRoomInfo().getRoomMemberClientInfo().setVoiceRoomTag(roomPlayerClientInfo.getRoomMemberClientInfo().getVoiceRoomTag());
                }
            }
            else {
                LOGGER.error("broadcastData data invalid, uid:{} data:{}", player.getUid(), data);
                return NKErrorCode.InvalidParams;
            }
        }
        SsRoomsvr.RpcRoomBroadcastChannelReq.Builder reqBuilder = SsRoomsvr.RpcRoomBroadcastChannelReq.newBuilder();
        reqBuilder.setRoomId(currentRoomId);
        reqBuilder.setBroadcastData(data);
        reqBuilder.setSender(player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcRoomBroadcastChannelRes.Builder> rpcResult = RoomService.get().
                    rpcRoomBroadcastChannel(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room data broadcast fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomServiceIsBusy;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room data broadcast fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player data broadcast fail, playerUid:{} roomId:{} e:{}", player.getUid(), currentRoomId,
                    e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    public SsGamesvr.RpcNoticeTeamPartnerJoinMiniGameRes.Builder noticeTeamPartnerJoinMiniGame(MiniGameNoticeInfo noticeInfo) {
        SsGamesvr.RpcNoticeTeamPartnerJoinMiniGameRes.Builder resBuilder = SsGamesvr.RpcNoticeTeamPartnerJoinMiniGameRes.newBuilder();
        resBuilder.setResult(NKErrorCode.OK.getValue());
        long currentTeamId = getTeamId();
        if (currentTeamId <= 0) {
            // TODO no team errorcode
            return resBuilder;
        }
        // 拼装广播数据
        RoomBroadcastInfo.Builder dataBuilder = RoomBroadcastInfo.newBuilder();
        dataBuilder.setType(RoomBroadcastInfoType.RBIT_MiniGameInvitation);
        RoomMiniGameInfo.Builder miniGameInfoBuilder = RoomMiniGameInfo.newBuilder();
        miniGameInfoBuilder.setProposal(player.getUid());
        PlatMiniGameNoticeInfo.Builder platMiniGameNoticeBuilder = PlatMiniGameNoticeInfo.newBuilder();
        platMiniGameNoticeBuilder.setAppid(noticeInfo.getAppid());
        platMiniGameNoticeBuilder.setPlayId(noticeInfo.getPlayId());
        platMiniGameNoticeBuilder.setExtData(noticeInfo.getExtData());
        miniGameInfoBuilder.setNoticeInfo(platMiniGameNoticeBuilder);
        dataBuilder.setRoomMiniGameInfo(miniGameInfoBuilder);

        SsRoomsvr.RpcRoomBroadcastChannelReq.Builder reqBuilder = SsRoomsvr.RpcRoomBroadcastChannelReq.newBuilder();
        reqBuilder.setRoomId(currentTeamId);
        reqBuilder.setBroadcastData(dataBuilder);
        reqBuilder.setSender(player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcRoomBroadcastChannelRes.Builder> rpcResult = RoomService.get().
                    rpcRoomBroadcastChannel(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room data broadcast fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentTeamId);
                resBuilder.setResult(NKErrorCode.RoomServiceIsBusy.getValue());
                return resBuilder;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    // TODO no team errorcode
                    return resBuilder;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    // TODO no team errorcode
                    return resBuilder;
                }
                LOGGER.error("player-{} room data broadcast fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentTeamId);
                resBuilder.setResult(rpcResult.getData().getResult());
                return resBuilder;
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player data broadcast fail, playerUid:{} roomId:{} e:{}", player.getUid(), currentTeamId,
                    e.getEnumErrCode());
            resBuilder.setResult(NKErrorCode.RoomServiceIsBusy.getValue());
            return resBuilder;
        }
        return resBuilder;
    }

    public NKErrorCode handleRoomBroadcast(long roomId, RoomBroadcastInfo data) {
        GuidType uidType = BaseGenerator.getUidType(roomId);
        long currentRoomId = getTeamId();
        if (uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID) {
            currentRoomId = getTeamId();
        } else if (uidType == GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
            currentRoomId = getRoomId();
        } else {
            LOGGER.error("player recv broadcase data with wrong type roomId, playerUid:{} roomId:{}", player.getUid(), roomId);
            return NKErrorCode.RoomIDDiscord;
        }
        if (currentRoomId != roomId) {
            // 与玩家身上房间id不符
            exitRoomForDataFix(roomId);
            return NKErrorCode.RoomMemberNotExist;
        }
        if (data.getType() == RoomBroadcastInfoType.RBIT_MapDataDownloadProcess) {
            RoomMapDownloadStatNtf.Builder ntfBuilder = RoomMapDownloadStatNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setMapId(data.getMapDataDownloadProcessInfo().getMapId());
            ntfBuilder.addAllPlayerProcessInfoList(data.getMapDataDownloadProcessInfo().getPlayerProcessInfoListList());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMAPDOWNLOADSTATNTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_MapDownloadReminder) {
            RoomMapDownloadReminderNtf.Builder ntfBuilder = RoomMapDownloadReminderNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setMapId(data.getMapDownloadRemindInfo().getMapId());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMAPDOWNLOADREMINDERNTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_PlayerReadyReminder) {
            RoomPlayerReadyReminderNtf.Builder ntfBuilder = RoomPlayerReadyReminderNtf.newBuilder();
            ntfBuilder.setRoomId(data.getPlayerReadyRemindInfo().getRoomId());
            ntfBuilder.setRemindStartTimeMs(data.getPlayerReadyRemindInfo().getRemindStartTimeMs());
            ntfBuilder.setType(data.getPlayerReadyRemindInfo().getType());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMPLAYERREADYREMINDERNTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_MapStateSync) {
            RoomMapStateSyncNtf.Builder ntfBuilder = RoomMapStateSyncNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setMapId(data.getMapStateSyncInfo().getMapId());
            ntfBuilder.addAllPlayerMapStateSyncInfo(data.getMapStateSyncInfo().getPlayerMapStateSyncInfoList());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("ntf:{}", ntfBuilder);
            }
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMAPSTATESYNCNTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_PlayerOperationStatus) {
            RoomBroadcastInfoNtf.Builder ntfBuilder = RoomBroadcastInfoNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setBroadcastInfo(data);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMBROADCASTINFONTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_PlayerClientData) {
            RoomBroadcastInfoNtf.Builder ntfBuilder = RoomBroadcastInfoNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setBroadcastInfo(data);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMBROADCASTINFONTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_MiniGameInvitation) {
            NKErrorCode ret = player.getPlayerStateMgr().checkConfirm();
            if (ret != NKErrorCode.OK) {
                LOGGER.debug("player in mini game state, no need to send invitation, player:{}, invitor:{}",
                        player.getUid(), data.getRoomMiniGameInfo().getProposal());
                return NKErrorCode.OK;
            }
            RoomMiniGameInvitationNtf.Builder ntfBuilder = RoomMiniGameInvitationNtf.newBuilder();
            ntfBuilder.setRoomId(currentRoomId);
            ntfBuilder.setInvitor(data.getRoomMiniGameInfo().getProposal());
            ntfBuilder.setNoticeInfo(data.getRoomMiniGameInfo().getNoticeInfo());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMINIGAMEINVITATIONNTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_CommonInfo) {
            RoomBroadcastInfoNtf.Builder ntfBuilder = RoomBroadcastInfoNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setBroadcastInfo(data);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMBROADCASTINFONTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_SPBroadcastInfo) {
            if (!data.getRoomSPBroadcastData().getBroadcastSelf()
                    && data.getRoomSPBroadcastData().getUid() == player.getUid()) {
                return NKErrorCode.OK;
            }
            RoomSPBroadcastInfoNtf.Builder ntfBuilder = RoomSPBroadcastInfoNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setBroadcastData(data.getRoomSPBroadcastData());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMSPBROADCASTINFONTF, ntfBuilder);
        } else if (data.getType() == RoomBroadcastInfoType.RBIT_DetailPakData) {
            RoomBroadcastInfoNtf.Builder ntfBuilder = RoomBroadcastInfoNtf.newBuilder();
            ntfBuilder.setRoomId(roomId);
            ntfBuilder.setBroadcastInfo(data);
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMBROADCASTINFONTF, ntfBuilder);
        }
        return NKErrorCode.OK;
    }

    public RoomRoundInfo getCurRoomRoundInfo(long clientRoomId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return null;
        }
        SsRoomsvr.RpcRoomGetCurRoundInfoReq.Builder reqBuilder = SsRoomsvr.RpcRoomGetCurRoundInfoReq.newBuilder();
        reqBuilder.setRoomId(currentRoomId);
        reqBuilder.setUid(player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcRoomGetCurRoundInfoRes.Builder> rpcResult = RoomService.get().
                    rpcRoomGetCurRoundInfo(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room end settlement state fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getRet(),
                        currentRoomId);
                NKErrorCode.RoomServiceIsBusy.throwError("");
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return null;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return null;
                }
                LOGGER.error("player-{} room end settlement state fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                NKErrorCode.forNumber(rpcResult.getData().getResult()).throwError("");
            }
            return rpcResult.getData().getRoundInfo();
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player end settlement state fail, playerUid:{} roomId:{} e:{}", player.getUid(),
                    currentRoomId,
                    e.getEnumErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError();
        }
        return null;
    }

    public RoomRoundInfo ugcMultiRoundCoinUse(long clientRoomId, int coinNum) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return null;
        }
        SsRoomsvr.RpcUgcRoomMultiRoundCoinUseReq.Builder reqBuilder = SsRoomsvr.RpcUgcRoomMultiRoundCoinUseReq.newBuilder();
        reqBuilder.setRoomId(currentRoomId);
        reqBuilder.setUid(player.getUid());
        reqBuilder.setCoin(coinNum);
        try {
            RpcResult<SsRoomsvr.RpcUgcRoomMultiRoundCoinUseRes.Builder> rpcResult = RoomService.get().
                    rpcUgcRoomMultiRoundCoinUse(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room end settlement state fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getRet(),
                        currentRoomId);
                NKErrorCode.RoomServiceIsBusy.throwError("");
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return null;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return null;
                }
                LOGGER.error("player-{} room end settlement state fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                NKErrorCode.forNumber(rpcResult.getData().getResult()).throwError("");
            }
            return rpcResult.getData().getRoundInfo();
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player end settlement state fail, playerUid:{} roomId:{} e:{}", player.getUid(),
                    currentRoomId,
                    e.getEnumErrCode());
            NKErrorCode.RoomServiceIsBusy.throwError();
        }
        return null;
    }

    public UniversalRoom.Builder queryRoomByRoomNo(long roomNo) {
        String queryRoomIdStr = "";
        try {
            // 根据短号查房间id
            SsSeqsvr.RpcQuerySeqInfoReq.Builder reqBuilder = SsSeqsvr.RpcQuerySeqInfoReq.newBuilder();
            reqBuilder.setSeqType(SeqType.ROOM_SEQ_VALUE);
            reqBuilder.setSeqId(roomNo);
            reqBuilder.setVirtualUuid(SeqUtil.getVirtualHashKey(SeqType.ROOM_SEQ, roomNo));
            RpcResult<SsSeqsvr.RpcQuerySeqInfoRes.Builder> rpcResult = SeqService.get().rpcQuerySeqInfo(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room info query by room no fail : {}, roomNo:{}", player.getUid(),
                        rpcResult.getRet(),
                        roomNo);
                if (rpcResult.getRet() == NKErrorCode.SeqQueryFailed.getValue()) {
                    NKErrorCode.RoomNotExistQueryByRoomNo.throwError("room no query fail");
                }
                NKErrorCode.RoomServiceIsBusy.throwError("room no query fail");
            }
            SeqInfo seqInfo = rpcResult.getData().getSeqInfo();
            if (seqInfo.getDesc().length() == 0) {
                NKErrorCode.RoomNotExistQueryByRoomNo.throwError("room not exist");
            }
            queryRoomIdStr = seqInfo.getDesc();
            long roomId = Long.parseLong(queryRoomIdStr);

            if (BaseGenerator.getUidType(roomId) == GuidType.GUID_TYPE_BATTLE_ID) {
                return player.getPlayerBattleMgr().getRoomUniversalInfo(roomId);
            } else {
                return getRoomUniversalInfo(roomId);
            }

        } catch (NumberFormatException e) {
            LOGGER.error("player-{} room info query by room no error : {}, roomNo:{}, result:{}", player.getUid(),
                    roomNo, queryRoomIdStr);
            NKErrorCode.RoomError.throwError("room no query fail");
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("player-{} queryRoomByRoomNo fail : roomNo:{} e:{}", player.getUid(), roomNo, e.getErrCode());
            NKErrorCode.RoomError.throwError("room no query fail");
        }
        return null;
    }


    public NKErrorCode roomPlayerEndSettlement(long clientRoomId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        SsRoomsvr.RpcRoomPlayerEndSettlementReq.Builder reqBuilder = SsRoomsvr.RpcRoomPlayerEndSettlementReq.newBuilder();
        reqBuilder.setRoomId(currentRoomId);
        reqBuilder.setPlayerUid(player.getUid());
        try {
            RpcResult<SsRoomsvr.RpcRoomPlayerEndSettlementRes.Builder> rpcResult = RoomService.get().
                    rpcRoomPlayerEndSettlement(reqBuilder);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                LOGGER.error("player-{} room end settlement state fail : {}, roomId:{}", player.getUid(), rpcResult.getRet(),
                        currentRoomId);
                return NKErrorCode.RoomServiceIsBusy;
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                // 房间不存在
                if (NKErrorCode.RoomNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room not exist");
                    return NKErrorCode.OK;
                }
                // 玩家不在当前房间内
                if (NKErrorCode.RoomMemberNotExist.getValue() == rpcResult.getData().getResult()) {
                    ntfClientClearRoomInfo(currentRoomId, "room member not exist");
                    return NKErrorCode.OK;
                }
                LOGGER.error("player-{} room end settlement state fail : {}, roomId:{}", player.getUid(),
                        rpcResult.getData().getResult(), currentRoomId);
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room player end settlement state fail, playerUid:{} roomId:{} e:{}", player.getUid(), currentRoomId,
                    e.getEnumErrCode());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }

    /**
     * 改到走redis查询
     *
     * @param roomId
     * @return
     */
    public UniversalRoom.Builder getRoomUniversalInfo(long roomId) {
        UniversalRoom.Builder retBuilder = UniversalRoom.newBuilder();
        int getRet = PublicCustomRoomInfoDao.getRoom(retBuilder, roomId);
        if (getRet != 0) {
            LOGGER.debug("public room refresh by redis get fail, roomId:{} err:{}", roomId, getRet);
            try {
                SsRoomsvr.RpcRoomInfoReq.Builder roomInfoReqBuilder = SsRoomsvr.RpcRoomInfoReq.newBuilder().setRoomId(roomId);
                RpcResult<SsRoomsvr.RpcRoomInfoRes.Builder> roomInfoRsp = RoomService.get().rpcRoomInfo(roomInfoReqBuilder);
                if (roomInfoRsp.getRet() != NKErrorCode.OK.getValue()) {
                    if (roomInfoRsp.getRet() == NKErrorCode.RoomNotExist.getValue()) {
                        return null;
                    }
                    NKErrorCode.forNumber(roomInfoRsp.getRet()).throwError("room query by room id fail");
                }
                retBuilder = roomInfoRsp.getData().getRoom().toBuilder();
            } catch (NKTimeoutException | RpcException e) {
                LOGGER.error("player-{} getRoomUniversalInfo fail : roomNo:{} e:{}", player.getUid(), roomId, e.getErrCode());
                NKErrorCode.RoomServiceIsBusy.throwError("room query from redis fail");
            }
        }
        if (retBuilder.getRoomBriefInfo().getRoomID() == 0) {
            LOGGER.debug("public room refresh by redis get empty room info, roomId:{} err:{}", roomId, getRet);
            getRoomNotExistErrorCode(roomId).throwError("room not exist");
        }
        return retBuilder;
    }

    public UniversalRoomArray getRecommendCustomRoomList(int number, int modeId, int playId, int source, boolean noBattle) {
        UniversalRoomArray randomCustomRoom = UniversalRoomArray.newBuilder().build();
        if (modeId == 0 && playId == 0) {
            // 不限制查询条件时，进行房间和ugc房间的拼配
            randomCustomRoom = PublicRoomMgr.getInstance().randomAllRoom(player, number, noBattle);
            this.lastCustomRoomResultList = randomCustomRoom;
            return randomCustomRoom;
        }
        if (MatchTypeData.getInstance().isUgcMode(modeId)) {
            randomCustomRoom = getRecommendUgcCustomRoomByMapType(number, 0, source, noBattle);
        } else {
            randomCustomRoom = PublicRoomMgr.getInstance().randomCustomRoom(player, number, modeId, playId, source, noBattle);
        }
        this.lastCustomRoomResultList = randomCustomRoom;
        return randomCustomRoom;
    }

    public UniversalRoomArray getRecommendCustomRoomListWithModeType(int number, int modeType, int categoryType, int playId, int source, boolean noBattle) {
        UniversalRoomArray randomCustomRoom = UniversalRoomArray.newBuilder().build();
        if (modeType == 0) {
            // 不限制查询条件时，进行房间和ugc房间的拼配
            randomCustomRoom = PublicRoomMgr.getInstance().randomAllRoom(player, number, noBattle);
            this.lastCustomRoomResultList = randomCustomRoom;
            return randomCustomRoom;
        }
        if (modeType == TeamRecruitModeType.TRMT_AllUGCNotRecommend.getNumber()) {
            randomCustomRoom = getRecommendUgcCustomRoomByMapType(number, 0, source, noBattle);
        } else {
            randomCustomRoom = PublicRoomMgr.getInstance().randomCustomRoomWithModeType(player, number, modeType, categoryType, playId, source, noBattle);
        }
        this.lastCustomRoomResultList = randomCustomRoom;
        return randomCustomRoom;
    }

    public UniversalRoomArray getRecommendUgcCustomRoomList(int number, long ugcId, int source, boolean noBattle) {
        if (ugcId < 0) {
            return null;
        }
        return PublicRoomMgr.getInstance().randomUgcCustomRoom(player, number, ugcId, source, noBattle);
    }

    public UniversalRoomArray getRecommendUgcCustomRoomByMapType(int number, long mapType, int source, boolean noBattle) {
        return getRecommendUgcCustomRoomByMapType(number, mapType, source, false, noBattle);
    }

    public UniversalRoomArray getRecommendUgcCustomRoomByMapType(int number, long mapType, int source, boolean noPwd, boolean noBattle) {
        if (number <= 0) {
            number = PropertyFileReader.getRealTimeIntItem("room_list_count_per_page", 10);
        }
        return PublicRoomMgr.getInstance().randomUgcCustomRoomWithType(player, number, mapType, source, noPwd, noBattle);
    }

    public UniversalRoomArray getRecommendUgcCustomRoomByMapTag(int number, long tag, int source, boolean noPwd, boolean noBattle) {
        return PublicRoomMgr.getInstance().randomUgcCustomRoomWithTag(player, number, tag, source, noPwd, noBattle);
    }

    public void setDsDevEnvId(long dsDevEnvId, boolean needRpcRoom, String caller) {
        if (!ServerEngine.getInstance().isBusiness() && ServerEngine.getInstance().isDsStartToken()) {
            LOGGER.debug("player({}:{}:{}) devEnvId({}) needRpcRoom({}) caller({})",
                    player.getUid(), player.getOpenId(), player.getName(), dsDevEnvId, needRpcRoom, caller);
            player.getUserAttr().getPlayerPublicGameSettings().setDSVersion64(dsDevEnvId);
            if (needRpcRoom) {
                sendRpcRoomPlayerOnline();
            }
        }
    }

    /**
     * 按照当前房间内人数降序排列
     *
     * @param originalArray 原房间列表
     * @return
     */
    private UgcRoomArray sortUgcRoomByMemberCnt(UgcRoomArray originalArray) {
        UgcRoomArray.Builder retBuilder = UgcRoomArray.newBuilder();
        if (originalArray.getUgcRoomsCount() == 0) {
            return retBuilder.build();
        }
        List<UgcRoomSortUnit> sortList = new ArrayList<>();
        for (UgcRoomInfo ugcRoomInfo : originalArray.getUgcRoomsList()) {
            sortList.add(new UgcRoomSortUnit(ugcRoomInfo));
        }
        // 按照人数降序排列
        Collections.sort(sortList);
        for (UgcRoomSortUnit ugcRoomSortUnit : sortList) {
            retBuilder.addUgcRooms(ugcRoomSortUnit.room);
        }
        return retBuilder.build();
    }

    private void clearUnLockConditionAttr() {
        for (int matchType : player.getUserAttr().getUnLockGameModeSet().getValues()) {
            if (matchTypeConditionAttr.getAttrConditionGroup(matchType) != null) {
                matchTypeConditionAttr.removeAttrConditionGroup(matchType);
            }
        }
    }

    private void clearUnLockConditionAttr(int matchType) {
        if (matchTypeConditionAttr.getAttrConditionGroup(matchType) != null) {
            matchTypeConditionAttr.removeAttrConditionGroup(matchType);
        }
    }

    private void sendRoomDisbandNtfWhenRoomNotExist() {
        /*CsRoom.RoomMemberModifyNtf.Builder ntfBuilder = CsRoom.RoomMemberModifyNtf.newBuilder();
        ntfBuilder.addMemberList(player.getMemberBaseInfo());
        if (this.currentRoomInfoBuidler.getRoomBriefInfo().getRoomID() > 0) {
            ntfBuilder.setRoomType(this.currentRoomInfoBuidler.getRoomBriefInfo().getRoomType().getNumber());
            ntfBuilder.getRoomBriefInfoBuilder()
                    .setRoomType(this.currentRoomInfoBuidler.getRoomBriefInfo().getRoomType());
        }
        ntfBuilder.setModify(ModifyType.MT_Disband);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMEMBERMODIFYNTF, ntfBuilder);*/
    }

    /** 玩家队伍/房间数据维护 **/
    /**
     * 获取队伍id
     *
     * @return
     */
    public long getTeamId() {
        return player.getUserAttr().getRoomInfo().getTeamInfo().getTeamId();
    }

    /**
     * 获取自定义房间id
     *  不区分类型
     * @return
     */
    public long getRoomId() {
        return player.getUserAttr().getRoomInfo().getCustomRoomInfo().getRoomId();
    }

    /**
     * 获取常规玩法自定义房间id
     *
     * @return
     */
    public long getCustomRoomId() {
        AttrCustomRoomInfo customRoomInfo = player.getUserAttr().getRoomInfo().getCustomRoomInfo();
        if (customRoomInfo.getRoomTypeVal() == RoomType.CustomRoom_VALUE) {
            return customRoomInfo.getRoomId();
        }
        return 0L;
    }

    /**
     * 获取ugc自定义房间id
     *
     * @return
     */
    public long getUgcCustomRoomId() {
        AttrCustomRoomInfo customRoomInfo = player.getUserAttr().getRoomInfo().getCustomRoomInfo();
        if (customRoomInfo.getRoomTypeVal() == RoomType.UgcCustomRoom_VALUE) {
            return customRoomInfo.getRoomId();
        }
        return 0L;
    }

    public SsGamesvr.RpcGetPlayerRoomInfoRes.Builder getPlayerPlatRoomInfo(int queryType) {
        SsGamesvr.RpcGetPlayerRoomInfoRes.Builder res = SsGamesvr.RpcGetPlayerRoomInfoRes.newBuilder();
        // 获取队伍信息
        if (queryType == PlayerRoomQueryType.PRQT_All_VALUE || queryType == PlayerRoomQueryType.PRQT_Team_VALUE) {
            long teamId = getTeamId();
            if (teamId > 0) {
                RoomInfo.Builder platTeamInfo = getPlatRoomInfo(teamId);
                res.setTeam(platTeamInfo);
            }

        }

        if (queryType == PlayerRoomQueryType.PRQT_All_VALUE || queryType == PlayerRoomQueryType.PRQT_Room_VALUE) {
            long roomId = getRoomId();
            if (roomId > 0) {
                RoomInfo.Builder platRoomInfo = getPlatRoomInfo(roomId);
                res.setRoom(platRoomInfo);
            }
        }

        return res;
    }

    private RoomInfo.Builder getPlatRoomInfo(long roomId) {
        RoomInfo.Builder roomBuilder = RoomInfo.newBuilder();
        SsRoomsvr.RpcPlatRoomInfoReq.Builder roomInfoReqBuilder = SsRoomsvr.RpcPlatRoomInfoReq.newBuilder();
        roomInfoReqBuilder.setRoomId(roomId);
        roomInfoReqBuilder.setWorldId(BaseGenerator.getWorldId(roomId));
        try {
            RpcResult<SsRoomsvr.RpcPlatRoomInfoRes.Builder> rpcResult = RoomService.get().rpcPlatRoomInfo(roomInfoReqBuilder);
            if (!rpcResult.isOK()) {
                LOGGER.info("player query plat room info fail, player:{} roomId:{} ret:{}", player.getUid(), roomId,
                        rpcResult.getRet());
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                if (rpcResult.getData().getResult() != NKErrorCode.RoomNotExist.getValue()) {
                    LOGGER.info("player query plat room info fail, player:{} roomId:{} ret:{}", player.getUid(), roomId,
                            rpcResult.getData().getResult());
                }
            } else {
                roomBuilder.mergeFrom(rpcResult.getData().getRoom());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.info("player query plat room info fail, player:{} roomId:{} ret:{}", player.getUid(), roomId,
                    e.getErrCode());
        }
        return roomBuilder;
    }

    /**
     * 获取当前队伍信息Builder
     *
     * @return
     */
    @Nullable
    private UniversalRoom.Builder getCurrentTeamInfoBuidler() {
        return this.currentTeamInfoBuidler;
    }

    /**
     * 获取当前房间信息Builder
     *
     * @return
     */
    @Nullable
    private UniversalRoom.Builder getCurrentRoomInfoBuidler() {
        return this.currentRoomInfoBuidler;
    }

    /**
     * 退出指定房间
     *  如果玩家同时存在于多个队伍/房间时，需要退出其中旧的以确保玩家同时只能存在于一个队伍或者房间
     *
     * @param targetRoomId
     */
    private void exitRoomForDataFix(long targetRoomId) {
        if (targetRoomId == 0) {
            return;
        }
        LOGGER.error("client request with mismatch room id, playerUid:{} clientRoomId:{} currentTeamId:{} currentRoomId:{}", 
            player.getUid(), targetRoomId, getTeamId(), getRoomId());
        
        Monitor.getInstance().add.total(MonitorId.attr_roomsvr_exit_room_for_data_fix, 1);
        try {
            CurrentExecutorUtil.runJob(() -> {
                exitRoom(targetRoomId, RoomExitType.RET_SystemLogicExit);
                return null;
            }, "playerExitRoomForDataFix", true);
        } catch (NKCheckedException e) {
            LOGGER.error("async exit room for data fix fail, err:{}", e.getEnumErrCode());
        }
    }

    /**
     * 根据客户端传入队伍/房间id获取当前玩家对应的id
     *  如果传入id与属性系统不符则默认使用队伍id
     *
     * @param clientRoomId 客户端传入的id
     * @return
     */
    private long getCurrentRoomId(long clientRoomId) {
        if (clientRoomId == getTeamId() || clientRoomId == getRoomId()) {
            return clientRoomId;
        }
        // 首先确保玩家退出错误的队伍或房间，确保玩家不会遗留
        exitRoomForDataFix(clientRoomId);
        // 其次从属性系统中获取对应的id，触发ntf提供给客户端进行信息刷新
        if (clientRoomId > 0) {
            GuidType uidType = BaseGenerator.getUidType(clientRoomId);
            long currentRoomId = 0L;
            if (uidType == GuidType.GUID_TYPE_ROOM_ID || uidType == GuidType.GUID_TYPE_STARP_TEAM_ID) {
                currentRoomId = getTeamId();
            } else if (uidType == GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
                currentRoomId = getRoomId();
            }
            if (currentRoomId > 0) {
                triggerRoomInfoNotice(currentRoomId);
            } else {
                ntfClientClearRoomInfo(clientRoomId, "");
            }
            // 直接断开后续流程，正式包不做外显提示
            NKErrorCode.RoomClientInfoMismatch.throwError("client room id mismatch");
        }
        // 最后如果客户端未传入有效id，则由后续逻辑处理，
        // 此处确保自定义房间所有请求一定会有id，且有明确的创建时机，所有后续逻辑有且仅有队伍的创建
        return 0;
    }

    public NKErrorCode exitRoom(long roomId, RoomExitType exitType) {
        if (roomId <= 0) {
            return NKErrorCode.OK;
        }
        LOGGER.info("player exit room, playerUid:{} roomId:{} exitType:{}", player.getUid(), roomId, exitType);
        RpcExitRoomReq.Builder rpcExitRoomReq = RpcExitRoomReq.newBuilder();
        rpcExitRoomReq.setRoomId(roomId);
        rpcExitRoomReq.setUid(player.getUid());
        rpcExitRoomReq.setExitType(exitType);
        try {
            RpcResult<RpcExitRoomRes.Builder> rpcRes = RoomService.get().rpcExitRoom(rpcExitRoomReq);
            if (rpcRes.getData().getResult() != 0 &&
                    rpcRes.getData().getResult() != NKErrorCode.RoomNotExist.getValue() &&
                    rpcRes.getData().getResult() != NKErrorCode.RoomMemberNotExist.getValue()) {
                if (rpcRes.getData().getResult() == NKErrorCode.RoomTeamLeaveButQuitHimself.getValue()) {
                    // 不算错误，直接返回，由调用方进一步处理
                    LOGGER.debug("player exit room with team, but only quit himself, roomId:{} player:{}", roomId, player.getUid());
                    return NKErrorCode.RoomTeamLeaveButQuitHimself;
                }
                Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_leave_room, 1);
                LOGGER.error("exit room fail, uid:{} roomId:{} error:{}", player.getUid(), roomId, rpcRes.getData().getResult());
                // 仅非玩家逻辑时退出不抛出错误
                if (exitType != RoomExitType.RET_SystemLogicExit) {
                    return NKErrorCode.forNumber(rpcRes.getData().getResult());
                }
            } else {
                Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_leave_room, 1);
                //ntfClientClearRoomInfo(roomId, "exit room"); 由roomsvr通知过来给客户端ntf
                // 及时清理内存中的数据，以免后续逻辑还在使用内存中错误的数据
                if (getTeamId() == roomId) {
                    getCurrentTeamInfoBuidler().clear();
                } else if (getRoomId() == roomId) {
                    getCurrentRoomInfoBuidler().clear();
                }
            }
            // 流水
            StringBuilder memberStr = new StringBuilder();
            for (Long uid : rpcRes.getData().getRoomInfo().getRoomTFlowInfo().getRoomMemberUidArray()
                    .getArrayLongList()) {
                memberStr.append(uid).append(",").append(player.getFriendManager().getRelationTypeVal(uid)).append(";");
            }
            TlogFlowMgr.sendTeamFlow(player, roomId, TEAM_OP_TYPE.TEAM_ACTIVE_LEAVE, 0, memberStr.toString(),
                    rpcRes.getData().getRoomInfoBuilder(),
                    rpcRes.getData().getRoomInfoBuilder().getRoomTFlowInfo().getOfflineMemberCnt());
            Monitor.getInstance().add.succ(MonitorId.attr_gamesvr_kick_player_room, 1);
        } catch (NKTimeoutException |RpcException e) {
            Monitor.getInstance().add.fail(MonitorId.attr_gamesvr_leave_room, 1);
            LOGGER.error("exit room fail, uid:{} roomId:{} error:{}", player.getUid(), roomId, e.getEnumErrCode().getValue());
            return e.getEnumErrCode();
        }
        return NKErrorCode.OK;
    }

    /**
     * 以队伍的形式退出房间
     *
     * @param roomId
     * @return
     */
    private NKErrorCode exitRoomWithTeam(long roomId) {
        return exitRoom(roomId, RoomExitType.RET_TeamExit);
    }

    public void exitAllRoom(String reason) {
        LOGGER.debug("player exit team and room, playerUid:{} reason:{}", player.getUid(), reason);
        // 如果两者同时存在时，仅房间有对局的可能，所以先退出房间
        exitRoom(getRoomId(), RoomExitType.RET_SucceedJoin).throwErrorIfNotOk("exit room fail");
        exitRoom(getTeamId(), RoomExitType.RET_SucceedJoin).throwErrorIfNotOk("exit team fail");
    }

    /**
     * 退出当前所有的队伍/房间
     */
    public void exitAllRoomQuietly(String reason) {
        LOGGER.debug("player exit team and room quietly, playerUid:{} reason:{}", player.getUid(), reason);
        exitRoomQuietly(getTeamId());
        exitRoomQuietly(getRoomId());
    }

    /**
     * 静默退出
     *  无感退出队伍/房间
     *
     * @param roomId 房间id
     */
    private void exitRoomQuietly(long roomId) {
        if (roomId == 0) {
            return;
        }
        LOGGER.info("leave room quietly, playerUid:{} roomId:{}", player.getUid(), roomId);
        try {
            CurrentExecutorUtil.runJob(() -> {
                exitRoom(roomId, RoomExitType.RET_SystemLogicExit);
                return null;
            }, "exitRoomQuietly", true);
        } catch (NKCheckedException e) {
            LOGGER.error("async exit room quietly fail, err:{}", e.getEnumErrCode());
        }
    }

    private void triggerRoomInfoNotice(long roomId) {
        // TODO add ss protocol
    }

    private void clearRoomInfoWithNtfOrNot(long clientRoomId, boolean ntfOrNot, String reason) {
        if (clientRoomId <= 0) {
            return ;
        }
        LOGGER.info("player clear room info, player:{} clientRoomId:{} currentTeamId:{} currentRoomId:{} reason:{}", 
            player.getUid(), clientRoomId, getTeamId(), getRoomId(), reason);
        CsRoom.RoomMemberModifyNtf.Builder ntfBuilder = CsRoom.RoomMemberModifyNtf.newBuilder()
                .setRoomID(clientRoomId).setModify(ModifyType.MT_Disband);
        if (getTeamId() == clientRoomId) {
            ntfBuilder.setModify(ModifyType.MT_DEL);
            ntfBuilder.setRoomType(getCurrentTeamInfoBuidler().getRoomBriefInfo().getRoomType().getNumber());
            ntfBuilder.getRoomBriefInfoBuilder().setRoomID(clientRoomId)
                    .setRoomType(getCurrentTeamInfoBuidler().getRoomBriefInfo().getRoomType());
            ntfBuilder.addMemberList(getJoinerMemberBaseInfoBuilder());
            player.getUserAttr().getRoomInfo().getTeamInfo().clear();
        } else if (getRoomId() == clientRoomId) {
            ntfBuilder.setModify(ModifyType.MT_DEL);
            ntfBuilder.setRoomType(player.getUserAttr().getRoomInfo().getCustomRoomInfo().getRoomTypeVal());
            ntfBuilder.getRoomBriefInfoBuilder().setRoomID(clientRoomId)
                    .setRoomType(RoomType.forNumber(player.getUserAttr().getRoomInfo().getCustomRoomInfo().getRoomTypeVal()));
            ntfBuilder.addMemberList(getJoinerMemberBaseInfoBuilder());
            player.getUserAttr().getRoomInfo().getCustomRoomInfo().clear();
        }
        fixCurrMatchType();
        // 是否通知给玩家
        if (ntfOrNot) {
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMEMBERMODIFYNTF, ntfBuilder);
        }
    }

    /**
     * 刷新对外展示的房间详细信息
     */
    private void refreshDisplayRoomExtraInfo() {
        if (getRoomId() > 0) {
            if (isDisplayRoomExtraInfoNeedUpdate(getCurrentRoomInfoBuidler())) {
                UniversalRoom.Builder currentRoomInfoBuilder = getCurrentRoomInfoBuidler();
                // 刷新外显公开信息
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setRoomType(currentRoomInfoBuilder.getRoomBriefInfo().getRoomType().getNumber());
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setPlayId(currentRoomInfoBuilder.getRoomBriefInfo().getRuleInfo().getMatchTypeId());
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setCurMemberNum(currentRoomInfoBuilder.getRoomBriefInfo().getCurMemberNumber());
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setMaxMemberNum(currentRoomInfoBuilder.getRoomBriefInfo().getMemberNumberLimit());
                player.getFriendManager().addChangeField(PlayerPublicInfoField.ROOM_EXTRA_INFO);
            }
        } else if (getTeamId() > 0) {
            if (isDisplayRoomExtraInfoNeedUpdate(getCurrentTeamInfoBuidler())) {
                UniversalRoom.Builder currentTeamInfoBuilder = getCurrentTeamInfoBuidler();
                // 刷新外显公开信息
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setRoomType(currentTeamInfoBuilder.getRoomBriefInfo().getRoomType().getNumber());
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setPlayId(currentTeamInfoBuilder.getRoomBriefInfo().getRuleInfo().getMatchTypeId());
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setCurMemberNum(currentTeamInfoBuilder.getRoomBriefInfo().getCurMemberNumber());
                player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo()
                        .setMaxMemberNum(getDisplayTeamMaxMemberNumber(
                                currentTeamInfoBuilder.getRoomBriefInfo().getRuleInfo().getMatchTypeId(),
                                currentTeamInfoBuilder.getRoomBriefInfo().getMemberNumberLimit()));
                player.getFriendManager().addChangeField(PlayerPublicInfoField.ROOM_EXTRA_INFO);
            }
        } else {
            player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo().clear();
            player.getFriendManager().addChangeField(PlayerPublicInfoField.ROOM_EXTRA_INFO);
        }
    }

    /**
     * 根据玩法获取配置中的最大队伍人数
     *
     * @param playId 玩法id
     * @param originalMaxNum 原来的队伍最大人数
     * @return
     */
    private int getDisplayTeamMaxMemberNumber(int playId, int originalMaxNum) {
        MatchType matchType = MatchTypeData.getInstance().get(playId);
        if (matchType == null) {
            LOGGER.warn("cannot find config for play, playId:{}", playId);
            return originalMaxNum;
        }
        if (matchType.getMatchTeamNumCount() == 0) {
            LOGGER.warn("play has no match team num config, playId:{}", playId);
            return originalMaxNum;
        }
        int targetNum = 0;
        for (int memberNumberSupported : matchType.getMatchTeamNumList()) {
            targetNum = Math.max(targetNum, memberNumberSupported);
        }
        if (targetNum == 0) {
            LOGGER.warn("play has wrong match team num config, playId:{} numberList:{}", playId,
                    matchType.getMatchTeamNumList());
            return originalMaxNum;
        }
        return targetNum;
    }

    private boolean isDisplayRoomExtraInfoNeedUpdate(UniversalRoom.Builder currentRoomInfoBuilder) {
        RoomExtraInfo roomExtraInfo = player.getUserAttr().getPlayerPublicLiveStatus().getRoomExtraInfo();
        if (currentRoomInfoBuilder.getRoomBriefInfo().getRoomType().getNumber() != roomExtraInfo.getRoomType()
                || currentRoomInfoBuilder.getRoomBriefInfo().getRuleInfo().getMatchTypeId()
                != roomExtraInfo.getPlayId()
                || currentRoomInfoBuilder.getRoomBriefInfo().getCurMemberNumber() != roomExtraInfo.getCurMemberNum()
                || currentRoomInfoBuilder.getRoomBriefInfo().getMemberNumberLimit()
                != roomExtraInfo.getMaxMemberNum()) {
            return true;
        }
        return false;
    }

    public void ntfClientClearRoomInfo(long clientRoomId, String reason) {
        clearRoomInfoWithNtfOrNot(clientRoomId, true, reason);
    }

    private void noticeMemberLeave(RoomType type, List<MemberBaseInfo> leaveMembers,
            List<MemberBaseInfo> leaveObservers) {
        if (type != RoomType.DefaultRoom || (leaveMembers.isEmpty() && leaveObservers.isEmpty())) {
            return ;
        }
        if ((leaveMembers.size() + leaveObservers.size()) > 1) {
            LOGGER.error("notice leave member but has more than 1 players, members:{} ob:{}",
                    leaveMembers, leaveObservers);
        }
        MemberBaseInfo leaver = null;
        if (leaveObservers.isEmpty()) {
            leaver = leaveMembers.get(0);
        } else {
            leaver = leaveObservers.get(0);
        }

        CsPlayer.PlayerNoticeMsgNtf.Builder ntfMsg = CsPlayer.PlayerNoticeMsgNtf.newBuilder();
        ntfMsg.addParams(leaver.getName());
        ntfMsg.setType(PlayerNoticeMsgType.PNT_PARTNER_LEAVE_ROOM);
        ntfMsg.setNotice(leaver.getName() + "离开了队伍"); //备注：多语言后，客户端直接根据PlayerNoticeMsgType去做映射，不再使用notice内容
        ntfMsg.setMsgQueued(true);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERNOTICEMSGNTF, ntfMsg);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("send notice to {}, body:{}", player.getUid(), ntfMsg);
        }
    }

    public void roomSettlement(boolean isGiveUp, long roomId, List<MemberBaseInfo> memberList) {
        try {
            LOGGER.debug("player room settlement, player:{} roomId:{}", player.getUid(), roomId);
            if (isGiveUp) {
                LOGGER.debug("player ignore qrcode calculation because give up, player:{} roomId:{}", player.getUid(), roomId);
                return;
            }
            int qrCodeMember = 0;
            for (MemberBaseInfo memberBaseInfo : memberList) {
                if (memberBaseInfo.getJoinReason() == RoomJoinType.RJT_QRCode_VALUE) {
                    qrCodeMember ++;
                }
            }
            LOGGER.debug("player settlememnt, player:{} roomId:{} qrcodeCnt:{}", player.getUid(), roomId, qrCodeMember);
            if (qrCodeMember > 0) {
                addQRCodePoint(qrCodeMember);
            }
        } catch (Exception e) {
            LOGGER.error("player room settlement throw exception, player:{} roomId:{}", player.getUid(), roomId);
        }
    }

    public void addQRCodePoint(int qrcodeCnt) {
        if (qrcodeCnt <= 0) {
            return ;
        }
        qrcodeCnt ++; // 默认加上房主
        int point = 0;
        QRCodeCalculationRule qrCodeCalculationRule = QRCodeCalculationRuleData.getInstance().getOrMax(qrcodeCnt);
        if (qrCodeCalculationRule == null) {
            LOGGER.error("cannot find caculation rule for count, count:{}", qrcodeCnt);
        } else {
            point = qrCodeCalculationRule.getPoint();
        }
        if (point == 0) {
            return;
        }
        long weekEndTimeMs = DateUtils.getNextWeekNumTime(DateUtils.currentTimeMillis(), 1) - 1;
        LOGGER.debug("player add qrcode point till week end, player:{} point:{} timeMs:{}",player.getUid(), point, weekEndTimeMs);
        long pointAdded = player.getLimitManager()
                .addValueAndGet(CommonLimitType.CLT_QRCodeDailyAcquireLimit, player.getUid(), point, weekEndTimeMs);
        if (pointAdded == 0) {
            return;
        }
        player.getUserAttr().getModNote().setQrCodePoint(player.getUserAttr().getModNote().getQrCodePoint() + (int)pointAdded);
        LOGGER.debug("player add qrcode point success, player:{} pointAdded:{} curPoint:{}",player.getUid(),
                pointAdded, player.getUserAttr().getModNote().getQrCodePoint());
        new PlayerQRCodePointEvent(player).dispatch();
    }
    public NKErrorCode roomMapVote(long clientRoomId, long mapId) {
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }

        try {
            SsRoomsvr.RpcRoomMapVoteReq.Builder req = SsRoomsvr.RpcRoomMapVoteReq.newBuilder();
            req.setRoomId(currentRoomId);
            req.setPlayerUid(player.getUid());
            req.setMapId(mapId);
            RpcResult<SsRoomsvr.RpcRoomMapVoteRes.Builder> rpcResult = RoomService.get().rpcRoomMapVote(req);
            if (rpcResult.getRet()  != NKErrorCode.OK.getValue()) {
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (rpcResult.getData().getResult() != NKErrorCode.OK.getValue()) {
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("roomMapVote error, ex:{}", FunctionUtil.exceptionToString(e));
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }
    private int getQRCodePointDailyAcquiredLimit() {
        return MiscConf.getInstance().getMiscConf().getQrCodeDailyAcquireLimit();
    }

    public void recordRoomIdPlayerLeft(long roomId) {
        this.lastLeaveRoomIds.offer(roomId);
        while (this.lastLeaveRoomIds.size() > 3) {
            this.lastLeaveRoomIds.poll();
        }
        Iterator<Long> iterator = this.lastLeaveRoomIds.iterator();
        while(iterator.hasNext()) {
            long historyRoomId = iterator.next();
            LOGGER.debug("player current record, playerUid:{} roomId:{}", player.getUid(), historyRoomId);
        }
    }

    public boolean playerLeftRecent(long roomId) {
        Iterator<Long> iterator = this.lastLeaveRoomIds.iterator();
        while(iterator.hasNext()) {
            long historyRoomId = iterator.next();
            if (historyRoomId == roomId) {
                return true;
            }
        }
        return false;
    }
    public NKErrorCode handleSendRoomMemberToMemberNtfMsg(CsPlayer.SendRoomMemberToMemberNtf_C2S_Msg reqMsg) {
        long currentRoomId = getCurrentRoomId(reqMsg.getRoomId());
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(reqMsg.getRoomId(), "");
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcSendRoomMemberToMemberNtfReq.Builder ssReqMsg = SsRoomsvr.RpcSendRoomMemberToMemberNtfReq.newBuilder();
            ssReqMsg.setRoomId(currentRoomId);
            ssReqMsg.setUid(player.getUid());
            ssReqMsg.setNtfType(reqMsg.getNtfType());
            ssReqMsg.setTargetUid(reqMsg.getTargetUid());
            RpcResult<SsRoomsvr.RpcSendRoomMemberToMemberNtfRes.Builder> rpcResult = RoomService.get().rpcSendRoomMemberToMemberNtf(ssReqMsg);
            if (rpcResult.getRet() != 0) {
                LOGGER.error("player room online fail, uid:{} roomId:{} ntfType:{} targetUid:{}", player.getUid(), reqMsg.getRoomId(), reqMsg.getNtfType(), reqMsg.getTargetUid());
            }
            return NKErrorCode.forNumber(rpcResult.getRet());
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("player room online fail, uid:{} roomId:{} ntfType:{} targetUid:{} e:{}", player.getUid(), reqMsg.getRoomId(), reqMsg.getNtfType(), reqMsg.getTargetUid(),  FunctionUtil.exceptionToString(e));
            return NKErrorCode.RoomServiceIsBusy;
        }
    }

    public void rpcSendRoomMemberToMemberNtfToPlayer(SsGamesvr.RpcSendRoomMemberToMemberNtfToPlayerReq.Builder req) {
        // 拼接返回给客户端的数据
        CsPlayer.RoomMemberToMemberNtf.Builder ntfBuilder = CsPlayer.RoomMemberToMemberNtf.newBuilder();
        ntfBuilder.setNtfType(req.getNtfType());
        ntfBuilder.setSenderUid(req.getSenderUid());
        ntfBuilder.setRoomId(req.getRoomId());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMMEMBERTOMEMBERNTF, ntfBuilder);
    }

    public NKErrorCode changeTeamBackgroundTheme(long clientTeamId, int backgroundTheme) {
        long currentRoomId = getCurrentRoomId(clientTeamId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientTeamId, "");
            return NKErrorCode.OK;
        }
        if (currentRoomId != getTeamId()) {
            return NKErrorCode.OK;
        }

        try {
            SsRoomsvr.RpcRoomChangeInfoReq.Builder req = SsRoomsvr.RpcRoomChangeInfoReq.newBuilder();
            req.setRoomId(getTeamId());
            req.setOpSource(OpSource.OS_Player);
            req.setUid(player.getUid());
            req.setBackgroundTheme(backgroundTheme);
            RpcResult<SsRoomsvr.RpcRoomChangeInfoRes.Builder> rpcResult = RoomService.get().rpcRoomChangeInfo(req);
            if (NKErrorCode.OK.getValue() != rpcResult.getRet()) {
                return NKErrorCode.forNumber(rpcResult.getRet());
            }
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                if (rpcResult.getData().getResult() == NKErrorCode.RoomNotExist.getValue()
                        || rpcResult.getData().getResult() == NKErrorCode.RoomMemberNotExist.getValue()) {
                    ntfClientClearRoomInfo(getTeamId(), "team background theme change but room not exist");
                    return NKErrorCode.OK;
                }
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("changeTeamBackgroundTheme fail, player:{} teamId:{}", player.getUid(), getTeamId());
            return NKErrorCode.RoomServiceIsBusy;
        }
        return NKErrorCode.OK;
    }
    public int getTeamMatchTypeId(){
        return this.getCurrentTeamInfoBuidler().getRoomBriefInfo().getRuleInfo().getMatchTypeId();
    }

    /**
     * 进入啾灵之前退出所有队伍!
     *
     * - 1.进入啾灵世界时,无条件退出任何元梦组队 (退出队伍不能影响进入啾灵世界)
     * - 2.进入啾灵世界时,当前无组队,直接进入啾灵世界
     * - 3.进入啾灵世界时,存在非当前啾灵世界组队,直接退出队伍  (退出队伍不能影响进入啾灵世界)
     * - 4.进入啾灵世界时,存在当前啾灵世界组队,是否需要退出? (不需要退出队伍)
     */
    public void exitAllRoomBeforeEnterStarP(String reason, long starPWorldId, int source) {
        boolean needQuit = !(isStarPTeam() && starPWorldId == currStarPInfo.getCommonInfo().getStarPWorldId());
        LOGGER.debug("player exit team and room, playerUid:{} reason:{} source:{} starPTeam:{}",
                player.getUid(), reason, source, needQuit);
        // 检查source来源 是否需要退出
        if (needQuit) {
            // 无感退出不能报错
            exitAllRoomQuietly(reason);
        }
    }

    /**
     * 更新组队里的 啾灵成员数据
     */
    public void updateStarPPlayerInfo(StarPTeamUserInfo starPInfo) {
        if (!isStarPTeam()) {
            LOGGER.info("updateStarPPlayerInfo::not in team, uid:{}, starPId:{}", player.getUid(),
                    starPInfo.getCommonInfo().getStarPWorldId());
            player.getPlayerStarPMgr().setStarPSyncTeamFlag(starPInfo);
            return;
        }
        if (currStarPInfo.getCommonInfo().getStarPWorldId() == 0
                || starPInfo.getCommonInfo().getStarPWorldId() != currStarPInfo.getCommonInfo().getStarPWorldId()) {
            LOGGER.info("Room updateStarPPlayerInfo debug , not same spId! player:{} starPWorldId:{}",
                    player.getUid(), starPInfo.getCommonInfo().getStarPWorldId());
            player.getPlayerStarPMgr().setStarPSyncTeamFlag(starPInfo);        
            return;
        }
        // rpc 更新
        try {
            long teamId = getTeamId();
            SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoReq.Builder ssReqMsg =
                    SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoReq.newBuilder();
            ssReqMsg.setRoomId(teamId);
            MemberBaseInfo.Builder memberBaseInfo = MemberBaseInfo.newBuilder();
            memberBaseInfo.setStarPInfo(starPInfo).setUid(player.getUid());
            ssReqMsg.setMemberBaseInfo(memberBaseInfo);
            if (LOGGER.isDebugEnabled()){
                LOGGER.debug("Room updateStarPPlayerInfo debug , player:{} room:{} req:{}",
                        player.getUid(), teamId, ssReqMsg.toString());
            }
            RpcResult<SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoRes.Builder> rpcResult = RoomService.get()
                    .rpcRoomPlayerUpdateMemberBaseInfo(ssReqMsg);
            if (rpcResult.getRet() != 0) {
                LOGGER.error("Room updateStarPPlayerInfo failed , player:{} room:{} res:{}",
                        player.getUid(), teamId, rpcResult.getRet());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("updateStarPPlayerInfo error! uid:{}", player.getUid(), e);
        }
    }

    /**
     * 更新SP队伍成员状态
     */
    public void updateStarPPlayerStatus(int status) {
        if (!isStarPTeam()) {
            LOGGER.debug("updateStarPPlayerStatus::not in sp team, uid:{}", player.getUid());
            return;
        }

        long teamId = getTeamId();
        SsStarproomsvr.RpcRoomPlayerStarPInfoUpdateReq.Builder updateReq = SsStarproomsvr.RpcRoomPlayerStarPInfoUpdateReq.newBuilder();
        StarPTeamUserInfo.Builder info = StarPTeamUserInfo.newBuilder();
        info.getCommonInfoBuilder().setStatus(status).setStatusTime(Framework.currentTimeSec());
        updateReq.setUid(player.getUid()).setRoomId(teamId).setUpdateType(EnmStarPTeamUpdateType.ENM_STARP_TEAM_UPDATE_STATUS_VALUE).setStarPInfo(info.build());
        try {
            StarproomService.get().rpcRoomPlayerStarPInfoUpdate(updateReq);
        }
        catch (RpcException e) {
            LOGGER.error("updateStarPPlayerStatus::exp, uid:{}, exp:{}", player.getUid(), e);
        }
    }

    /**
     * 校验 & 请求啾灵 角色数据
     */
    public void fillAndCheckStarP(MemberBaseInfo.Builder memberBaseInfo, MatchRuleInfo.Builder rule) {
        boolean starPGame = StarPConfs.isStarPGame(rule.getMatchTypeId());
        // 压测不走校验
        if (!PropertyFileReader.getRealTimeBooleanItem("starp_hook", false)) {
            boolean inStarP = player.getPlayerStarPMgr().isInStarP();
            if (!inStarP && starPGame) {
                // 元梦中不允许创建啾灵模式,仅打日志告警
                LOGGER.error("fillAndCheckStarP error::not in starp, uid:{} matchTypeId:{}",
                        player.getUid(), rule.getMatchTypeId());
            }
            if (inStarP && !starPGame) {
                // 啾灵中禁止创建元梦玩法,仅打日志告警
                LOGGER.error("fillAndCheckStarP error::in starp, uid:{} matchTypeId:{}",
                        player.getUid(), rule.getMatchTypeId());
            }
        }
        if (!starPGame) {
            return;
        }
        if (!PropertyFileReader.getRealTimeBooleanItem("sp_team_switch", false)) {
            NKErrorCode.FeatureLocked.throwErrorNoStackIfNotOk();
            return;
        }
        long spId = rule.getStarPWorldId();
        int diffId = rule.getDifficultyId();

        // TODO:角色跨世界转移的临时代码，客户端传入starPRoleId后要删除
        long starPRoleId = rule.getStarPRoleId();
        if (starPRoleId == 0 && null != player.getPlayerStarPMgr()) {
            starPRoleId = player.getPlayerStarPMgr().getLastRole(player.getUid(), 0);
        }

        StarPTeamUserInfo playerInfo = player.getPlayerStarPMgr().checkStarPMode(rule.getMatchTypeId(), spId, diffId, starPRoleId);
        if (memberBaseInfo != null && playerInfo != StarPTeamUserInfo.getDefaultInstance()) {
            memberBaseInfo.setStarPInfo(playerInfo);
            //memberBaseInfo.setStarPBanTime(player.getPlayerStarPMgr().getBanTime());
            //this.clearStarPUnnecessaryData(memberBaseInfo);
        }
    }

    /**
     * 更新啾灵 ban 时间
     */
    public void updateStarPBanTime() {
        long teamId = getTeamId();
        if (!isStarPTeam() || currStarPInfo.getCommonInfo().getStarPWorldId() == 0) {
            LOGGER.debug("updateStarPBanTime::ignore one, uid:{} teamId:{} starPId:{}", player.getUid(), teamId, currStarPInfo.getCommonInfo().getStarPWorldId());
            return;
        }
        int matchTypeId = getTeamMatchTypeId();
        if (!StarPConfs.isStarPGame(matchTypeId)) {
            LOGGER.debug("updateStarPBanTime::ignore two, uid:{} teamId:{} matchTypeId:{}", player.getUid(), teamId, matchTypeId);
            return;
        }
        // rpc 更新
        try {
            SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoReq.Builder ssReqMsg =
                    SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoReq.newBuilder();
            ssReqMsg.setRoomId(teamId);
            MemberBaseInfo.Builder memberBaseInfo = MemberBaseInfo.newBuilder();
            memberBaseInfo.setUid(player.getUid()).setStarPBanTime(player.getPlayerStarPMgr().getBanTime());
            ssReqMsg.setMemberBaseInfo(memberBaseInfo);
            if (LOGGER.isDebugEnabled()){
                LOGGER.debug("Room updateStarPBanTime debug , player:{} room:{} req:{}",
                        player.getUid(), teamId, ssReqMsg.toString());
            }
            RpcResult<SsRoomsvr.RpcRoomPlayerUpdateMemberBaseInfoRes.Builder> rpcResult = RoomService.get()
                    .rpcRoomPlayerUpdateMemberBaseInfo(ssReqMsg);
            if (rpcResult.getRet() != 0) {
                LOGGER.error("Room updateStarPBanTime failed , player:{} room:{} res:{}",
                        player.getUid(), teamId, rpcResult.getRet());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("updateStarPBanTime error! uid:{}", player.getUid(), e);
        }
    }

    /**
     * 啾灵模式切换 校验
     */
    public void checkModifyModeWithStarP(MatchRuleInfo.Builder ruleInfo) {
        // 如果当前队伍是啾灵模式,不允许切换到 元梦玩法模式
        int matchType = getTeamMatchTypeId();
        boolean starPType = StarPConfs.isStarPGame(ruleInfo.getMatchTypeId());
        if (StarPConfs.isStarPGame(matchType)) {
            if (getTeamId() <= 0) {
                fixCurrMatchType();
                LOGGER.error("checkModifyModeWithStarP::teamId invalid, matchType:{} newMatchType:{} teamId:{}", matchType, ruleInfo.getMatchTypeId(), getTeamId());
                NKErrorCode.RoomInviteRuleConfigInvalid.throwError("checkRoomConfig error");
            }
            // 啾灵模式只能切换到啾灵模式
            if (!starPType) {
                LOGGER.error("checkModifyModeWithStarP::switch to non sp fail, matchType:{} newMatchType:{} teamId:{}", matchType, ruleInfo.getMatchTypeId(), getTeamId());
                NKErrorCode.StarPModeSwitchError.throwError("checkRoomConfig error");
            }
            // fillAndCheckStarP(null, ruleInfo);
        } else if (starPType) {
            // 从元梦模式 切换到 啾灵模式,不允许!!!
            LOGGER.error("checkModifyModeWithStarP::switch to sp fail, matchType:{} newMatchType:{} teamId:{}", matchType, ruleInfo.getMatchTypeId(), getTeamId());
            NKErrorCode.StarPModeSwitchError.throwError("checkRoomConfig error");
        }
    }

    /**
     * 加入啾灵组队之前校验下 & 获取 啾灵角色数据!
     */
    public NKErrorCode checkBeforeJoinStarP(long starPWorldId, long roomId, MemberBaseInfo.Builder memberInfo, long starPRoleId) {
        if (starPWorldId <= 0) {
            return NKErrorCode.OK;
        }
        RpcRoomInfoReq.Builder rpcReq = RpcRoomInfoReq.newBuilder().setRoomId(roomId);
        try {
            RpcResult<RpcRoomInfoRes.Builder> roomInfo = RoomService.get().rpcRoomInfo(rpcReq);
            if (!roomInfo.getData().hasRoom()) {
                NKErrorCode.RoomNotExist.throwError("room not exist");
            }
            RpcRoomInfoRes.Builder roomInfoData = roomInfo.getData();
            MatchRuleInfo.Builder ruleInfo = roomInfoData.getRoomBuilder().getRoomBriefInfoBuilder()
                    .getRuleInfoBuilder();
            ruleInfo.setStarPWorldId(starPWorldId);
            ruleInfo.setStarPRoleId(starPRoleId);
            this.fillAndCheckStarP(memberInfo, ruleInfo);
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("checkBeforeJoinStarP room catch exception : {}", e.getMessage());
            NKErrorCode.RoomError.throwError("checkBeforeJoinStarP failed");
        }
        return NKErrorCode.OK;
    }

    /**
     * 修正匹配模式(预防无队伍却显示啾灵玩法模式!!!)
     */
    public void fixCurrMatchType() {
        if (getTeamId() > 0 || getRoomId() > 0) {
            return;
        }
        int beforeMatchType = getTeamMatchTypeId();
        if (beforeMatchType == 0){
            beforeMatchType = player.getCurrentMatchType();
        }
        if (!StarPConfs.isStarPGame(beforeMatchType)) {
            return;
        }
        // 没队伍但还是 啾灵模式,异常,进行修正!!
        int lastMatchTypeId = player.getUserAttr().getRoomInfo().getLastMatchType();
        if (lastMatchTypeId == 0) {
            // 使用玩家已解锁列表中的默认玩法
            MatchType defaultMatchTypeConfigPersonal = MatchTypeData.getInstance().getDefaultMatchTypeConfigPersonal(
                    player.getUserAttr().getUnLockGameModeSet().getValues());
            if (defaultMatchTypeConfigPersonal != null) {
                player.setCurrentMatchType(defaultMatchTypeConfigPersonal.getId());
                lastMatchTypeId = defaultMatchTypeConfigPersonal.getId();
            }
        }
        player.setCurrentMatchType(lastMatchTypeId);
    }

    /**
     * 啾灵进入 进入对局 确认ntf
     */
    public NKErrorCode roomConfirmEnterNtf(long roomId, long uniqueId,List<MemberBaseInfo> matchMember,int resultType) {
        long currentRoomId = player.getPlayerRoomMgr().getCurrentRoomId(roomId);
        long currentTeamId = getTeamId();
        if (LOGGER.isDebugEnabled()){
            LOGGER.debug("StarPPvp roomConfirmEnterNtf roomId:{} uniqueId:{} currentRoomId:{} currentTeamId:{} resultType:{} matchMember:{}",
                    uniqueId, uniqueId, currentRoomId, currentTeamId, resultType, matchMember.toString());
        }
        if (currentRoomId != roomId && roomId != currentTeamId) {
            // 与玩家身上队伍id不符
            exitRoomForDataFix(roomId);
            LOGGER.error("player recv match confirm ntf from mismatch room, playerUid:{} curRoomId:{} targetRoomId:{}",
                    player.getUid(), currentRoomId, roomId);
            return NKErrorCode.RoomMemberNotExist;
        }

        NKErrorCode ret = player.getPlayerStateMgr().checkConfirm();
        if (ret.hasError()) {
            LOGGER.error("roomConfirmEnterNtf checkConfirm has error:{}. uid:{} roomId:{} uniqueId:{}, resultType:{}, matchMember:{}",
                    ret, player.getUid(), roomId, uniqueId, resultType, matchMember.toString());
            return ret;
        }
        player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.MATCH_START);
        CsRoom.RoomConfirmEnterNtf.Builder startNtf = CsRoom.RoomConfirmEnterNtf.newBuilder()
                .setRoomId(roomId).setUniqueId(uniqueId).addAllMatchMember(matchMember)
                .setResultType(resultType);
        if (LOGGER.isDebugEnabled()){
            LOGGER.debug("roomConfirmEnterNtf to client, uid:{} roomId:{} uniqueId:{}, resultType:{}, matchMember:{}",
                    player.getUid(), roomId, uniqueId, resultType, matchMember.toString());
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMCONFIRMENTERNTF, startNtf);
        if (player.getPlayerStarPMgr().getGmEnterDs()) {
            player.getPlayerRoomMgr().confirmEnterStarP(roomId, uniqueId, true);
        }
        return NKErrorCode.OK;
    }

    public void StarPEnterGameNtf(StarPRoomPveInfo info) {
        long roomId = info.getRoomId();
        long currentRoomId = getTeamId();
        if (currentRoomId != roomId) {
            // 与玩家身上队伍id不符
            exitRoomForDataFix(roomId);
            LOGGER.error("player recv starp enter game ntf from mismatch room, playerUid:{} curRoomId:{} targetRoomId:{}", player.getUid(), currentRoomId, roomId);
            return;
        }
        CsRoom.RoomStarPEnterGameNtf.Builder ntf = CsRoom.RoomStarPEnterGameNtf.newBuilder().setPveInfo(info);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMSTARPENTERGAMENTF, ntf);
    }

    /**
     * 记录最近一次非 啾灵模式 玩法,后续用来恢复
     */
    public void setLastMatchTypeId(int id) {
        if (StarPConfs.isStarPGame(id)
                || StarPConfs.is920StarP(id)
                || id <= 0) {
            return;
        }
        player.getUserAttr().getRoomInfo().setLastMatchType(id);
    }

    /**
     * 确认进入 啾灵副本对局
     */
    public NKErrorCode confirmEnterStarP(long clientRoomId, long uniqueId, boolean confirmFlag) {
        // 1. 获取当前的房间号
        long currentRoomId = getCurrentRoomId(clientRoomId);
        if (currentRoomId == 0) {
            ntfClientClearRoomInfo(clientRoomId, "");
            return NKErrorCode.OK;
        }
        try {
            RpcResult<SsRoomsvr.RpcConfirmEnterRes.Builder> rpcRet = RoomService.get()
                    .rpcConfirmEnter(SsRoomsvr.RpcConfirmEnterReq.newBuilder()
                            .setRoomId(currentRoomId).setUid(player.getUid()).setUniqueId(uniqueId)
                            .setConfirmFlag(confirmFlag));
            if (NKErrorCode.OK.getValue() != rpcRet.getData().getResult()) {
                StarPTlogFlowMgr.sendStarPTeamOperateErrorFlow(player, currentRoomId, StarPTeamOpType.SPIOpType_CONFIRM,
                        rpcRet.getData().getResult());
                // 处理错误逻辑
                return NKErrorCode.forNumber(rpcRet.getData().getResult());
            }
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("room confirm enter fail, roomId:{} playerUid:{} e:{}", currentRoomId, player.getUid(),
                    e.getMessage());
            return NKErrorCode.RoomServiceIsBusy;
        }
        if (confirmFlag) {
            player.getPlayerStateMgr().circulationPlayerState(PlayerStateAction.MATCH_START);
        }
        return NKErrorCode.OK;
    }

    /**
     * 是否需要退出 啾灵队伍 (离线 非高塔/PVE组队直接退出!)
     */
    public boolean needLeaveStarPTeam() {
        //if (!isStarPTeam()) {
        //    return false;
        //}
        //int teamMatchTypeId = getTeamMatchTypeId();
        //if (teamMatchTypeId == 0) {
        //    teamMatchTypeId = player.getCurrentMatchType();
        //}
        // 啾灵 高塔 & 组队PVE 在 battleongoning处 处理离队
        // return !StarPConfs.isStarPAllowMatch(teamMatchTypeId);
        return isStarPTeam();
    }

    public void exitStarPTeam(long currentTeamId, RoomExitType exitType) {
        exitRoom(currentTeamId, exitType);
    }


    /**
     * 啾灵世界不能收到 非啾灵世界邀请
     */
    public NKErrorCode checkStarPInvite(InvitationSourceType sourceType) {
        int number = sourceType.getNumber();
        boolean starPInvite = number >= InvitationSourceType.IST_SP_FriendList_VALUE
                && number <= InvitationSourceType.IST_SP_Unknown_VALUE;
        boolean inStarP = player.getPlayerStarPMgr().isInStarP();
        boolean ok = (inStarP & starPInvite) || (!inStarP && !starPInvite);
        if (!ok) {
            // 目前inStarP不是特别精准,为了防止不影响元梦业务,仅打个日志记录下
            LOGGER.error("checkStarPInvite warning! uid:{} StarPId:{} starPInvite:{} sourceType:{}",
                    player.getUid(), player.getPlayerStarPMgr().getCurrStarPId(), starPInvite, sourceType.name());
        }
        return NKErrorCode.OK;
    }

    public void TestStarPRoomSvr() {
        LOGGER.error("TestStarPRoomSvr::begin, uid:{}", player.getUid());
        SsStarproomsvr.RpcTestRoomReq.Builder rpcTestReq = SsStarproomsvr.RpcTestRoomReq.newBuilder();
        rpcTestReq.setUid(player.getUid()).setRoomid(123456);
        try {
            RpcResult<SsStarproomsvr.RpcTestRoomRes.Builder> rpcTestRes = StarproomService.get()
                    .rpcTestRoom(rpcTestReq);
            if (NKErrorCode.OK.getValue() != rpcTestRes.getData().getResult()) {
                LOGGER.error("TestStarPRoomSvr::fail, uid:{}, result:{}", player.getUid(),
                        rpcTestRes.getData().getResult());
            }
        }
        catch (NKTimeoutException | RpcException e) {
            LOGGER.error("TestStarPRoomSvr::exp, uid:{}, exp:{}", player.getUid(), e);
        }
    }

    /**
     * 当前是否啾灵组队,不一定准确
     */
    public boolean isStarPTeam() {
        long teamId = getTeamId();
        if (teamId <= 0) {
            return false;
        }
        if (!RpcServiceMgr.isStarPServiceClose()) {
            GuidType idType = BaseGenerator.getUidType(teamId);
            return idType == GuidType.GUID_TYPE_STARP_TEAM_ID;
        }
        return getTeamId() > 0 && StarPConfs.isStarPGame(getTeamMatchTypeId());
    }


    /**
     * 优化 啾灵不需要的成员字段
     */
    public void clearStarPUnnecessaryData(MemberBaseInfo.Builder memberBuilder) {
        memberBuilder.clearUnLockGameModeSet()
                .clearIsLadderBan()
                .clearRecentRounds()
                .clearFashionValue()
                .clearDressUpItems()
                .clearWarmRoundInfo()
                .clearBattleRecords()
                .clearRecentLevelIds()
                .clearQualifyingInfo()
                .clearPrivilegeLevel()
                .clearLevelGuideCount()
                .clearMatchIsolateInfo()
                .clearLevelGuideGroupId()
                .clearReturningPrivilege()
                .clearCreatorAccountInfo()
                .clearWolfKillReputationScore();
        Set<Integer> starPModeIds = StarPModeConf.getInstance().getStarPModeIds();
        for (Integer modeId : starPModeIds) {
            memberBuilder.addUnLockGameModeSet(modeId);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("clearStarPUnnecessaryData data:{}", memberBuilder.toString());
        }
    }

    public void confirmEnterProgressNtf(RpcRoomConfirmEnterProgressNtfReq.Builder ntf) {
        if (LOGGER.isDebugEnabled()) {
           LOGGER.debug("confirmEnterProgressNtf ntf:{}", ntf.toString());
        }
        CsRoom.RoomStarPConfirmEnterProgressNtf.Builder startNtf = CsRoom.RoomStarPConfirmEnterProgressNtf.newBuilder()
                .setRoomId(ntf.getRoomId()).setCancel(ntf.getCancel()).setOpUid(ntf.getOpUid())
                .setConfirmCnt(ntf.getConfirmCnt()).setPlayerCnt(ntf.getPlayerCnt());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ROOMSTARPCONFIRMENTERPROGRESSNTF, startNtf);
    }

    public RoomService getRoomService(boolean isStarP) {
        if (isStarP) {
            return StarproomService.get();
        }
        return RoomService.get();
    }

    /**
     * 啾灵世界退出 处理
     */
    public void handleStarpQuit(long quitCode, Map<String, String> callbackData) {
        if (callbackData != null && callbackData.containsKey("battleId")) {
            return;
        }
        if (!isStarPTeam()) {
            return;
        }
        boolean quit = needLeaveStarPTeam();
        if (quit) {
            exitAllRoomQuietly("Starp quit");
        }
        LOGGER.debug("handleStarpQuit uid:{} quitCode:{} quit:{} state:{}",
                player.getUid(), quitCode, quit, player.getPlayerStateMgr().getPlayerState().name());
    }

    // 处理啾灵队伍
    public void afterPlayerEnter(ExitLastSceneReason exitCode, int matchTypeId) {
        if (exitCode == ExitLastSceneReason.ELSR_EnterStarPWorld) {
            return;
        }
        if (StarPConfs.isStarPGame(matchTypeId)) {
            return;
        }
        // 说明是 大厅/小窝/农场/元梦对局 的进入通知
        if (!isStarPTeam()) {
            return;
        }
        boolean leaveStarPTeam = needLeaveStarPTeam();
        LOGGER.debug("afterPlayerEnter uid:{} exitCode:{} matchTypeId:{} teamId:{} leaveStarPTeam:{}",
                player.getUid(), exitCode.name(), matchTypeId, getTeamId(), leaveStarPTeam);
        if (leaveStarPTeam) {
            // 玩家不处于啾灵世界中,需要退出啾灵队伍
            exitStarPTeam(getTeamId(), RoomExitType.RET_SP_LeaveSP);
        }
    }
    public void onConfirmEnterResult(RpcRoomConfirmEnterProgressNtfReq.Builder ntf) {
        long roomId = ntf.getRoomId();
        int matchTypeId = getTeamMatchTypeId();
        if (!StarPConfs.isStarPPvpGame(matchTypeId))
        {
            return;
        }

        long selfUId = player.getUid();
        boolean isCancel = ntf.getCancel();
        LOGGER.debug("onConfirmEnterResult uid:{} roomId:{} cancel:{} matchTypeId:{}",
                selfUId, roomId, isCancel, matchTypeId);

        long currentRoomId = getCurrentRoomId(roomId);
        if (currentRoomId == 0)
        {
            LOGGER.warn("onConfirmEnterResult currentRoomId == 0. uid:{} ntf:{}", selfUId, ntf.toString());
            return;
        }

        long opUId = ntf.getOpUid();
        List<Long> confirmedUidList = ntf.getConfirmUidListList();

        if (opUId == 0)
        {
            boolean isSelfConfirmed = confirmedUidList.contains(selfUId);
            LOGGER.info("onConfirmEnterResult uid:{} confirm timeout. isSelfConfirmed:{} ntf:{}", selfUId, isSelfConfirmed, ntf.toString());

            // 确认超时
            if (!isSelfConfirmed)
            {
                LOGGER.info("onConfirmEnterResult uid:{} self confirm timeout, incPvpCancelCount.", selfUId);
                player.getPlayerStarPMgr().incPvpCancelCount(1);
            }

            return;
        }

        if (opUId != selfUId)
        {
            return;
        }

        if (!isCancel)
        {
            // 成功确认匹配，清除连续计数
            int pvpCancelCount = player.getPlayerStarPMgr().getPvpCancelCount();
            if (pvpCancelCount != 0) {
                LOGGER.info("onConfirmEnterResult uid:{} confirmed enter, clearPvpCancelCount:{}. ntf:{}", selfUId, pvpCancelCount, ntf.toString());
                player.getPlayerStarPMgr().clearPvpCancelCount();
            }

            return;
        }

        LOGGER.info("onConfirmEnterResult uid:{} unconfirmed, incPvpCancelCount", selfUId);
        player.getPlayerStarPMgr().incPvpCancelCount(1);
    }

    public NKErrorCode readyForCoMatch(long coMatchId) {
        if (coMatchId <= 0) {
            LOGGER.error("player ready for invalid co match id, player:{} coMatchId:{}", player.getUid(), coMatchId);
            return NKErrorCode.OK;
        }
        try {
            SsRoomsvr.RpcCoMatchPlayerReadyReq.Builder req = SsRoomsvr.RpcCoMatchPlayerReadyReq.newBuilder();
            req.setCoMatchId(coMatchId);
            req.setUid(player.getUid());
            req.setReadyPlayerInfo(getJoinerMemberBaseInfoBuilder());
            RpcResult<SsRoomsvr.RpcCoMatchPlayerReadyRes.Builder> rpcResult = RoomService.get()
                    .rpcCoMatchPlayerReady(req);
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                LOGGER.error("player ready for invalid co match id, player:{} coMatchId:{} ret:{}", player.getUid(),
                        coMatchId, rpcResult.getData().getResult());

            }
            return NKErrorCode.OK;
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("readyForCoMatch fail, player:{} teamId:{}", player.getUid(), getTeamId());
            return NKErrorCode.RoomServiceIsBusy;
        }
    }

    public NKErrorCode gmJoinMatchUnit(long teamId, long coMatchId) {
        try {
            SsRoomsvr.RpcRoomJoinMatchUnitReq.Builder req = SsRoomsvr.RpcRoomJoinMatchUnitReq.newBuilder();
            req.setRoomId(teamId);
            req.setCoMatchId(coMatchId);
            req.setUid(player.getUid());
            RpcResult<SsRoomsvr.RpcRoomJoinMatchUnitRes.Builder> rpcResult = RoomService.get()
                    .rpcRoomJoinMatchUnit(req);
            if (NKErrorCode.OK.getValue() != rpcResult.getData().getResult()) {
                LOGGER.error("player join invalid co match id, player:{} coMatchId:{} ret:{}", player.getUid(),
                        coMatchId, rpcResult.getData().getResult());
                return NKErrorCode.forNumber(rpcResult.getData().getResult());
            }
            return NKErrorCode.OK;
        } catch (NKTimeoutException | RpcException e) {
            LOGGER.error("gmJoinMatchUnit fail, player:{} teamId:{}", player.getUid(), getTeamId());
            return NKErrorCode.RoomServiceIsBusy;
        }
    }

    public void sendTeamShowBackgroundChangeThemeFlow(int oldTheme, int newTheme) {
        boolean isLeader = true; // 默认单人都是队长
        if (getTeamId() > 0) {
            // 有队伍数据时，比对一下队长uid
            isLeader = getCurrentTeamInfoBuidler().getRoomBriefInfo().getLeaderUid() == player.getUid();
        }
        TlogFlowMgr.sendTeamBackgroundThemeChangeFlow(player, isLeader, newTheme, oldTheme, getTeamId());
    }

    static class UgcRoomSortUnit implements Comparable {

        int memberCnt = 0;
        UgcRoomInfo room;

        public UgcRoomSortUnit(UgcRoomInfo room) {
            this.room = room;
            this.memberCnt = room.getRoomBriefInfo().getCurMemberNumber();
        }

        @Override
        public int compareTo(@NotNull Object o) {
            if (o instanceof UgcRoomSortUnit) {
                if (this.memberCnt >= ((UgcRoomSortUnit) o).memberCnt) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return 0;
        }
    }

    static class AskToJoinRequestInfo {

        long roomId;
        long proposal;

        public AskToJoinRequestInfo(long roomId, long proposal) {
            this.roomId = roomId;
            this.proposal = proposal;
        }
    }

    static class Face2FaceRoomInfo {

        long roomId;
        RoomGeoInfo.Builder geoInfoBuilder;

        public Face2FaceRoomInfo(long roomId, RoomGeoInfo.Builder geoInfoBuilder) {
            this.roomId = roomId;
            this.geoInfoBuilder = geoInfoBuilder;
        }
    }

    static class MemberBaseInfoUpdateHistory {

        long lastUpdateTimestamp;
        AtomicBoolean ignoreWhenOneMemberTeam;

        AtomicBoolean triggered;
        AtomicBoolean isUpdating;

        List<UpdateMemberBaseInfoSource> historyPerInterval = new ArrayList<>();

        public MemberBaseInfoUpdateHistory(long lastUpdateTimestamp) {
            this.lastUpdateTimestamp = lastUpdateTimestamp;
            this.ignoreWhenOneMemberTeam = new AtomicBoolean(false);
            this.triggered = new AtomicBoolean(false);
            this.isUpdating = new AtomicBoolean(false);
        }

        public MemberBaseInfoUpdateHistory(MemberBaseInfoUpdateHistory history) {
            this.lastUpdateTimestamp = history.lastUpdateTimestamp;
            this.historyPerInterval = new ArrayList<>(history.historyPerInterval);
        }

        /**
         * 标记需要更新
         *
         * @param source
         */
        public void markTriggered(UpdateMemberBaseInfoSource source) {
            this.historyPerInterval.add(source);
            this.triggered.compareAndSet(false, true);
        }

        /**
         * 是否需要更新
         *
         * @param uid 玩家uid，仅用于日志输出
         * @return
         */
        public boolean startUpdate(long uid) {
            long playerMemberBaseInfoUpdateInterval = PropertyFileReader.getRealTimeLongItem(
                    "player_member_base_info_update_interval", 1_000);
            if (DateUtils.currentTimeMillis() - this.lastUpdateTimestamp > playerMemberBaseInfoUpdateInterval
                    && this.triggered.get()) {
                if (this.isUpdating.compareAndSet(false, true)) {
                    LOGGER.debug("player need update member info to room, player:{} historySource:{}", uid,
                            historyPerInterval);
                    return true;
                }
            }
            return false;
        }

        /**
         * 标记更新完毕
         */
        public void markUpdated() {
            this.historyPerInterval.clear();
            this.lastUpdateTimestamp = DateUtils.currentTimeMillis();
            this.isUpdating.compareAndSet(true, false);
            this.triggered.compareAndSet(true, false);
        }

        public void fillExtraData(Player player, MemberBaseInfo.Builder builder) {
            for (UpdateMemberBaseInfoSource updateMemberBaseInfoSource : this.historyPerInterval) {
                fill(updateMemberBaseInfoSource, player, builder);
            }
        }

        /**
         * 按照来源对MemberBaseInfo进行填充
         *
         * @param source 来源
         * @param player 玩家对象
         * @param builder 接收数据的builder
         * @return 是否填充的是完整数据
         */
        private void fill(UpdateMemberBaseInfoSource source, Player player, MemberBaseInfo.Builder builder) {
            switch (source) {
                default:
            }
        }

        /**
         * 填充额外的玩法相关数据
         *
         * @param player
         * @param roomBriefInfo
         * @param memberBaseInfo
         */
        public void fillExtraPlayData(Player player, RoomBriefInfo roomBriefInfo,
                MemberBaseInfo.Builder memberBaseInfo) {
            for (UpdateMemberBaseInfoSource updateMemberBaseInfoSource : this.historyPerInterval) {
                fillPlayerData(updateMemberBaseInfoSource, player, roomBriefInfo, memberBaseInfo);
            }
        }

        private void fillPlayerData(UpdateMemberBaseInfoSource source, Player player, RoomBriefInfo roomBriefInfo,
                MemberBaseInfo.Builder memberBaseInfo) {
            switch (source) {
                case TeamAIInfoUpdate:
                    if (roomBriefInfo.getRoomType() == RoomType.DefaultRoom && HokConfs.isHokRankGame(
                            roomBriefInfo.getRuleInfo().getMatchTypeId())) {
                        AIInvitationData.Builder builder = AIInvitationData.newBuilder();
                        builder.setUpdateTimestampMs(DateUtils.currentTimeMillis());
                        builder.setAvailableMatchCnt(player.getHokMgr().getAiInviteLeftTimes());
                        memberBaseInfo.setAiInvitationData(builder);
                    }
                    break;
                default:
            }
        }
    }

    private NKErrorCode getRoomNotExistErrorCode(long roomId) {
        GuidType uidType = BaseGenerator.getUidType(roomId);
        if (uidType == GuidType.GUID_TYPE_CUSTOM_ROOM_ID) {
            return NKErrorCode.RoomMultiPlayerRoomNotExist;
        }

        return NKErrorCode.RoomNotExist;
    }
}
