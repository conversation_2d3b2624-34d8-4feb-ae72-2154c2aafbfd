package com.tencent.wea.playerservice.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.condition.PlayerConditionProgress;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.PlayerQRCodePointEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;

/**
 * @program: WeA
 * @description: 玩家二维码同玩积分
 * @author: nichtsun
 * @create: 2023-12-05
 **/

public class ConditionPlayerQRCodePoint extends BasePlayerCondition {

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerQRCodePoint_VALUE;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerQRCodePoint)
    protected void onEvent(PlayerQRCodePointEvent event) {
        super.handleEvent(event);
    }

    @Override
    public long getInitConditionProgress(Player player, PlayerConditionProgress conditionProgress) {
        return player.getUserAttr().getModNote().getQrCodePoint();
    }

    @Override
    public boolean handleProgress(BasePlayerEvent event, ConditionOperation progress) {
        return progress.setValue(event.getPlayer().getUserAttr().getModNote().getQrCodePoint());
    }
}
