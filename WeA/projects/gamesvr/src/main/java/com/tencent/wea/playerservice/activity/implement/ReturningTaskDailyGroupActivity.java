package com.tencent.wea.playerservice.activity.implement;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.ActivityMainConfig;
import com.tencent.resourceloader.resclass.TaskConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.ReturnActivity;
import com.tencent.wea.attr.TaskLifeTime;
import com.tencent.wea.outputcontrol.BaseOutputModule;
import com.tencent.wea.outputcontrol.OutputModuleUtil;
import com.tencent.wea.playerservice.outputcontrol.PlayerTaskOutputModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.GeneralOutputPeriodType;
import com.tencent.wea.xlsRes.keywords.TaskType;
import java.util.concurrent.TimeUnit;

/**
 * 回流每日刷新任务活动，按照回流进度，每日刷出不同任务组
 */
public class ReturningTaskDailyGroupActivity extends TaskActivity {

    private boolean syncDeleteTaskOnTaskRemove = false;

    public ReturningTaskDailyGroupActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATReturningDailyGroupTask;
    }

    @Override
    protected int getTaskGroupNumber() {
        return getActivityMainConfig().getActivityTaskGroupCount();
    }

    @Override
    public void refreshActivityTask() {
        ResActivity.ActivityMainConfig mainConfig = ActivityMainConfig.getInstance().get(this.activityId);
        if (mainConfig == null) {
            NKErrorCode.ActivityMainConfigNotExist.throwError("ActivityMainConfig {} not exist", this.activityId);
            return;
        }

        for (int i = 0; i < mainConfig.getActivityTaskGroupCount(); ++i) {
            refreshGroupTask(i, mainConfig.getActivityTaskGroup(i));
        }
    }

    private void refreshGroupTask(int taskGroupIndex, int taskGroupId) {
        ResTask.TaskGroup taskGroupConfig = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
        if (taskGroupConfig == null) {
            NKErrorCode.ActivityTaskNotExist.throwError("Activity:{} taskGroup:{} null", this.activityId, taskGroupId);
            return;
        }

        var taskMgr = player.getTaskManager();
        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();
        long dayOffsetSecond = TimeUnit.DAYS.toSeconds(taskGroupIndex);
        final long beginTimeSec = DateUtils.getDayBeginTimeSec(returnActivity.getBeginTime()) + dayOffsetSecond;
        final long endTimeSec = DateUtils.getDayEndTimeSec(returnActivity.getBeginTime()) + dayOffsetSecond;
        if (Framework.currentTimeSec() > endTimeSec) {
            return;
        }

        final long beginTime = TimeUnit.SECONDS.toMillis(beginTimeSec);
        final long endTime = TimeUnit.SECONDS.toMillis(endTimeSec);

        TaskLifeTime taskLifeTime = new TaskLifeTime().setShowBeginTime(beginTime).setShowEndTime(endTime)
                .setDoBeginTime(beginTime).setDoEndTime(endTime);

        for (int taskId : taskGroupConfig.getTaskIdListList()) {
            ResTask.TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
            if (taskConf == null) {
                NKErrorCode.ActivityTaskNotExist.throwError("Activity {} task group {} task {} config not exist",
                        this.activityId, taskGroupId, taskId);
                return;
            }
            var task = taskMgr.getTask(taskId);
            if (null == task) {
                LOGGER.debug("uid-{} activity-{} register task-{} time-{}", player.getUid(), this.activityId,
                        taskConf.getId(), taskLifeTime);
                taskMgr.registerTask(taskConf.getId(), taskConf, taskLifeTime);
            } else {
                LOGGER.debug("uid-{} activity-{} update task-{} time-{}", player.getUid(), this.activityId,
                        taskConf.getId(), taskLifeTime);
                taskMgr.updateLifeTime(taskId, taskLifeTime);
            }
        }
    }


    public void setSyncDeleteTaskOnTaskRemove(boolean syncDeleteTaskOnTaskRemove) {
        this.syncDeleteTaskOnTaskRemove = syncDeleteTaskOnTaskRemove;
    }

    @Override
    public void removeTask(int taskId) {
        super.removeTask(taskId);
        if (syncDeleteTaskOnTaskRemove) {
            player.getTaskManager().deleteRunningTask(taskId);
        }
    }

    public static class TaskOutputFunc implements PlayerTaskOutputModule.CustomActivityTaskFunc {

        // 计算相关任务的产出控制周期key
        @Override
        public long calcTaskOutputPeriodKey(Player player, int activityId, int taskId, long atSec) {
            TaskType taskType = TaskConfData.getInstance().getTaskType(taskId);
            GeneralOutputPeriodType periodType = OutputModuleUtil.getPeriodTypeByTaskType(taskType.getNumber());
            if (periodType != GeneralOutputPeriodType.GOPT_Lifelong) {
                return BaseOutputModule.calcPeriodKeyByPeriodType(periodType, atSec);
            }
            // 使用本次回流档期开始时间
            return player.getUserAttr().getReturningInfo().getReturnActivity().getBeginTime();
        }
    }

}
