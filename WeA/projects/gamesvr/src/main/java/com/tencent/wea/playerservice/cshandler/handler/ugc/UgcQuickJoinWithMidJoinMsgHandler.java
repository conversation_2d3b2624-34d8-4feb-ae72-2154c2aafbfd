package com.tencent.wea.playerservice.cshandler.handler.ugc;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsUgc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class UgcQuickJoinWithMidJoinMsgHandler extends AbstractGsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(UgcQuickJoinWithMidJoinMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsUgc.UgcQuickJoinWithMidJoin_C2S_Msg reqMsg = (CsUgc.UgcQuickJoinWithMidJoin_C2S_Msg) request;
        return player.getPlayerUgcManager().quickJoinWithMidJoin(reqMsg);
    }
}
