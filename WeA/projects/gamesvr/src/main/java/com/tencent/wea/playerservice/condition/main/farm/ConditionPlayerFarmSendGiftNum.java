package com.tencent.wea.playerservice.condition.main.farm;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;

import com.tencent.wea.playerservice.event.common.farm.PlayerFarmSendGiftEvent;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ConditionPlayerFarmSendGiftNum  extends BasePlayerCondition {
    protected static final Logger LOGGER = LogManager.getLogger(ConditionPlayerFarmSendGiftNum.class);
    public ConditionPlayerFarmSendGiftNum() { super(); }

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerFarmSendGiftNum_VALUE;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerFarmSendGift)
    private void onEvent(PlayerFarmSendGiftEvent event) throws NKRuntimeException {
        LOGGER.debug("on event send gift {}", event);
        super.handleEvent(event);
    }
}
