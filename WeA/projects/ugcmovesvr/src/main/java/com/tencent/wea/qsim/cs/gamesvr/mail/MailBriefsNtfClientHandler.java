package com.tencent.wea.qsim.cs.gamesvr.mail;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.*;
import com.tencent.wea.qsim.com.player.SimPlayer;
import com.tencent.wea.qsim.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class MailBriefsNtfClientHandler extends AbstractPbMsgClientNtfHandler {
    private static final Logger LOGGER = LogManager.getLogger(MailBriefsNtfClientHandler.class);

    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        CsMail.MailBriefsNtf body = (CsMail.MailBriefsNtf) bodyMsg;
        for (CsMail.MailBrief mailBrief : body.getMailBriefsList()) {
            player.getModel().addMail(mailBrief);
        }
    }
}