package com.tencent.wea.matchservice.matchdata.matchProcService.matchSpecialRule.randomside;

import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.wea.matchservice.matchdata.MatchTeamInfo;
import com.tencent.wea.matchservice.matchdata.matchProcService.matchProcCommon.MatchAlgorithmUtils;
import com.tencent.wea.matchservice.matchdata.matchProcService.matchProcCommon.matchFillStrategy.MatchFillStrategyContext;
import com.tencent.wea.xlsRes.ResMatch;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 撮合带随机阵营的
 * 基本按照WereWoldSideIdentityRule复刻
 */
public class PreSelectSideMatchRule {

    private static final Logger LOGGER = LogManager.getLogger(PreSelectSideMatchRule.class);

    public static PreSelectSideMatchRule getInstance() {
        return PreSelectSideMatchRule.InstanceHolder.instance;
    }

    private PreSelectSideMatchRule() {
    }

    /**
     * 撮合匹配
     * @param context 匹配上下文 匹配过程中可能会修改
     * @return 是否完成匹配
     */
    public boolean fillResultWithMatchSideList(MatchFillStrategyContext context) {
        // 参数context.matchSideList是不可变更的配置
        // 处理中需要一个拷贝版本
        List<ResMatch.MatchSideInfo> curMatchSideList = new ArrayList<>(context.matchSideList);

        // 过滤掉playerCnt为0的matchSide
        filterEmptyPlayerCntMatchSideInf(context.matchParams.baseTeamInfo.getRoomID(), curMatchSideList);
        if (curMatchSideList.size() != context.matchSideList.size()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.info("room {} leader {} roomInfo {} curMatchSideList change {} -> {}",
                        context.matchParams.baseTeamInfo.getRoomID(),
                        context.matchParams.baseTeamInfo.getLeaderID(),
                        context.roomCfg.getId(),
                        NKStringFormater.formatShortPbCollections(context.matchSideList),
                        NKStringFormater.formatShortPbCollections(curMatchSideList));
            }
        }

        SingleTeamMatchResult baseTeamResult = assignBaseTeamSide(curMatchSideList, context.matchParams);
        if (!baseTeamResult.isThisTeamMatchSucc) {
            // baseTeam处理失败 直接返回失败
            LOGGER.info("room {} leader {} roomInfo {} assignBaseTeamSide fail",
                    context.matchParams.baseTeamInfo.getRoomID(),
                    context.matchParams.baseTeamInfo.getLeaderID(),
                    context.roomCfg.getId());
            return false;
        }

        // baseTeam处理完成
        context.matchParams.matchResult.add(context.matchParams.baseTeamInfo);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("room {} leader {} roomInfo {} assignBaseTeamSide succ",
                    context.matchParams.baseTeamInfo.getRoomID(),
                    context.matchParams.baseTeamInfo.getLeaderID(),
                    context.roomCfg.getId());
        }

        if (baseTeamResult.isAllMatchSucc) {
            // 如果整个对局已经撮合完成 则直接返回成功
            LOGGER.info("room {} leader {} roomInfo {} assignBaseTeamSide direct succ",
                    context.matchParams.baseTeamInfo.getRoomID(),
                    context.matchParams.baseTeamInfo.getLeaderID(),
                    context.roomCfg.getId());
            return true;
        }

        // 要有序(按匹配时间从大到小排的 前面的要优先, 所以用LinkedHashSet)
        Set<MatchTeamInfo> randSideTeamSet = new LinkedHashSet<>();
        Set<MatchTeamInfo> noRandSideTeamSet = new LinkedHashSet<>();
        for (MatchTeamInfo teamInfo : context.matchParams.preResult) {
            // 目前大王和躲猫猫都是0表示随机
            final int RANDOM_SIDE_ID = 0;
            if (teamInfo.getSide() != RANDOM_SIDE_ID) {
                noRandSideTeamSet.add(teamInfo);
            } else {
                randSideTeamSet.add(teamInfo);
            }
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("fillResultWithMatchSideList baseRoomID:{} leader:{} roomInfoID:{} " +
                            "noRandSideTeamSet:{} randSideTeamSet:{}",
                    context.matchParams.baseTeamInfo.getRoomID(),
                    context.matchParams.baseTeamInfo.getLeaderID(),
                    context.roomCfg.getId(),
                    noRandSideTeamSet, randSideTeamSet);
        }

        if (processCandidateTeamSet(curMatchSideList, context.matchParams, context.roomCfg, noRandSideTeamSet, false)) {
            return true;
        }
        if (processCandidateTeamSet(curMatchSideList, context.matchParams, context.roomCfg, randSideTeamSet, true)) {
            return true;
        }

        LOGGER.info("fillResultWithMatchSideList fail baseRoomID:{} leader:{} roomInfoID:{} " +
                        "noRandSideTeamSet.size:{} randSideTeamSet.size:{}",
                context.matchParams.baseTeamInfo.getRoomID(),
                context.matchParams.baseTeamInfo.getLeaderID(),
                context.roomCfg.getId(), noRandSideTeamSet.size(), randSideTeamSet.size());
        return false;
    }

    private void filterEmptyPlayerCntMatchSideInf(long uidForLog, List<ResMatch.MatchSideInfo> curMatchSideList) {
        for (Iterator<ResMatch.MatchSideInfo> it = curMatchSideList.iterator(); it.hasNext(); ) {
            ResMatch.MatchSideInfo matchSideInfo = it.next();
            if (matchSideInfo.getTeamPlayers() == 0) {
                it.remove();

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("room {} side {} teamPlayer == 0 so remove",
                            uidForLog, matchSideInfo.getSideID());
                }
            }
        }
    }

    private boolean processCandidateTeamSet(List<ResMatch.MatchSideInfo> curMatchSideList,
                                            MatchAlgorithmUtils.MatchParams matchParams,
                                            ResMatch.MatchRoomInfo roomCfg,
                                            Set<MatchTeamInfo> teamSet,
                                            boolean isRandomSideSet) {
        if (teamSet.isEmpty()) {
            return false;
        }


        if (doProcCandidateTeamSet(curMatchSideList, matchParams, roomCfg, teamSet)) {
            LOGGER.info("baseRoomID:{} leader:{} isRandomSet:{} procCandidateTeamSet succ roomInfoID:{} teamSet.size:{}",
                    matchParams.baseTeamInfo.getRoomID(),
                    matchParams.baseTeamInfo.getLeaderID(),
                    isRandomSideSet,
                    roomCfg.getId(),
                    teamSet.size());
            return true;
        }

        return false;
    }

    /**
     * 处理base team
     * @param matchSideList 要更新的队伍到阵营的id 注意如果匹配成功 matchSideList的内容会被更新
     * @param matchParams 匹配参数
     * @return 当前撮合是否成功 所有撮合是否都完成
     */
    private SingleTeamMatchResult assignBaseTeamSide(List<ResMatch.MatchSideInfo> matchSideList,
                                                     MatchAlgorithmUtils.MatchParams matchParams) {
        return getMatchTeamMatchResult(matchSideList, matchParams.baseTeamInfo, matchParams.teamId2AssignedSideMap);
    }


    /**
     * 单个队伍撮合结果
     */
    private static class SingleTeamMatchResult {
        // 单个队伍撮合结果
        public final boolean isThisTeamMatchSucc;
        // 是否所有队伍都撮合成功(side都填充满了)
        public final boolean isAllMatchSucc;

        private SingleTeamMatchResult(boolean isThisTeamMatchSucc, boolean isAllMatchSucc) {
            this.isThisTeamMatchSucc = isThisTeamMatchSucc;
            this.isAllMatchSucc = isAllMatchSucc;
        }
    }

    /**
     * 获取单个队伍撮合结果
     * @param matchSideList 阵营id 注意如果匹配成功 matchSideList的内容会被更新
     * @param candidateTeamInfo 候选队伍
     * @param teamId2SideMap 要更新的队伍到阵营的id 注意如果匹配成功 matchSideList的内容会被更新
     * @return 当前撮合是否成功, 所有撮合是否都完成
     */
    private SingleTeamMatchResult getMatchTeamMatchResult(List<ResMatch.MatchSideInfo> matchSideList,
                                                          MatchTeamInfo candidateTeamInfo,
                                                          Map<Long, Integer> teamId2SideMap) {
        // 保存中途阵营结果
        List<ResMatch.MatchSideInfo> newSideInfoListAfterCurTeamJoined = new ArrayList<>(matchSideList);

        // 处理阵营
        if (!procMatchSide(newSideInfoListAfterCurTeamJoined, candidateTeamInfo, teamId2SideMap)) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("room {} leader {} procMatchSide fail matchSideList {}",
                        candidateTeamInfo.getRoomID(), candidateTeamInfo.getLeaderID(),
                        NKStringFormater.formatShortPbCollections(matchSideList));
            }
            return new SingleTeamMatchResult(false, false);
        }

        // 处理成功把最新的阵营信息赋值回去
        // 不过在java搞入参 有点奇怪
        matchSideList.clear();
        matchSideList.addAll(newSideInfoListAfterCurTeamJoined);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("room {} leader {} succ matchSideList {}",
                    candidateTeamInfo.getRoomID(), candidateTeamInfo.getLeaderID(),
                    NKStringFormater.formatShortPbCollections(matchSideList));
        }

        return new SingleTeamMatchResult(true, matchSideList.isEmpty());
    }

    // 获取单个队伍撮合结果
    private boolean procMatchSide(List<ResMatch.MatchSideInfo> matchSideList,
                                  MatchTeamInfo candidateTeamInfo,
                                  Map<Long, Integer> teamId2SideMap) {
        Iterator<ResMatch.MatchSideInfo> iterator = matchSideList.iterator();
        while (iterator.hasNext()) {
            ResMatch.MatchSideInfo matchSideInfo = iterator.next();
            if (0 == candidateTeamInfo.getSide() || matchSideInfo.getSideID() == candidateTeamInfo.getSide()) {
                if (matchSideInfo.getTeamPlayers() >= candidateTeamInfo.getMemberCnt()) {
                    // 虽然在for中调用了remove 但是remove后一定return 不会继续for 因此不会异常
                    iterator.remove();
                    int leftSlotCnt = matchSideInfo.getTeamPlayers() - candidateTeamInfo.getMemberCnt();

                    // 还有剩余加回去吧
                    if (leftSlotCnt > 0) {
                        ResMatch.MatchSideInfo.Builder newSideBuilder = ResMatch.MatchSideInfo.newBuilder(matchSideInfo);
                        newSideBuilder.setTeamPlayers(leftSlotCnt);
                        matchSideList.add(newSideBuilder.build());

                        // 重新按照side排序 确保遍历时 填充队伍时稳定
                        matchSideList.sort(Comparator.comparingInt(ResMatch.MatchSideInfo::getSideID));
                    }

                    teamId2SideMap.put(candidateTeamInfo.getRoomID(), matchSideInfo.getSideID());
                    return true;
                }
            }
        }

        return false;
    }

    // 处理候选队伍
    private boolean doProcCandidateTeamSet(List<ResMatch.MatchSideInfo> matchSideList,
                                           MatchAlgorithmUtils.MatchParams matchParams,
                                           ResMatch.MatchRoomInfo roomCfg,
                                           Set<MatchTeamInfo> candidateTeamSet) {
        for (MatchTeamInfo matchTeamInfo : candidateTeamSet) {
            SingleTeamMatchResult res =
                    getMatchTeamMatchResult(matchSideList, matchTeamInfo, matchParams.teamId2AssignedSideMap);
            if (!res.isThisTeamMatchSucc) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("getMatchTeamMatchResult key fail roomID:{} " +
                                    "leader {} roomInfoID:{} matchTypeID:{} matchSideList:{}" +
                                    "matchResult.size:{} candidateTeamSet.size:{}",
                            matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                            roomCfg.getId(), matchParams.baseTeamInfo.getMatchType(),
                            NKStringFormater.formatShortPbCollections(matchSideList),
                            matchParams.matchResult.size(),
                            candidateTeamSet.size());
                }
                continue;
            }

            // 队伍添加到结果中
            matchParams.matchResult.add(matchTeamInfo);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("getMatchTeamMatchResult key succ roomID:{} " +
                                "leader {} roomInfoID:{} matchTypeID:{} matchSideList:{}" +
                                "matchResul.size:{}",
                        matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                        roomCfg.getId(), matchParams.baseTeamInfo.getMatchType(),
                        NKStringFormater.formatShortPbCollections(matchSideList),
                        matchParams.matchResult.size());
            } else {
                LOGGER.info("getMatchTeamMatchResult key succ roomID:{} " +
                                "leader:{} matchTypeID:{} matchResult.size:{}",
                        matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                        matchParams.baseTeamInfo.getMatchType(), matchParams.matchResult.size());
            }

            if (res.isAllMatchSucc) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("getMatchTeamMatchResult value succ roomID:{} leader:{} " +
                                    "roomInfoID:{} matchTypeID:{} matchSideList:{} matchResult:{} candidateTeamSet.size:{}",
                            matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                            roomCfg.getId(), matchParams.baseTeamInfo.getMatchType(),
                            NKStringFormater.formatShortPbCollections(matchSideList),
                            matchParams.matchResult, candidateTeamSet.size());
                } else {
                    LOGGER.info("procCandidateTeamSet value succ roomID:{} leader:{} matchTypeID:{} " +
                                    "matchResult.size:{} candidateTeamSet.size:{}",
                            matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                            matchParams.baseTeamInfo.getMatchType(), matchParams.matchResult.size(), candidateTeamSet.size());
                }

                return true;
            }
        }

        return false;
    }

    private static class InstanceHolder {

        public static PreSelectSideMatchRule instance = new PreSelectSideMatchRule();
    }
}
