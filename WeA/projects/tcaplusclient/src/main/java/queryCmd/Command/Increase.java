package queryCmd.Command;

import static com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants.TCAPLUS_CMD_INCREASE_REQ;
import static exceptionHandle.exceptionConstant.ILLEGAL_INCREASE_TYPE;

import Metadata.Field;
import Metadata.Table;
import SQLprocess.InputQuery;
import Util.Connection;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import exceptionHandle.ClientException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import tableTransfrom.fieldToRecord;

public class Increase {

    private static Logger logger = LogManager.getLogger("Get.class");

    public static void increase(Table tb, long seq, InputQuery inputQuery) throws ClientException {
        Request req = Connection.getRequest();
        req.setTableName(tb.getName());
        req.setCmd(TCAPLUS_CMD_INCREASE_REQ);
        Record record = req.addRecord();
        for (Field entry : tb.getFields()) {
            if (entry.getType() > 4) {
                throw new ClientException(ILLEGAL_INCREASE_TYPE,
                        String.format("\"%s\" is not valid increase Type. ", entry.getName()));
            }
            if (entry.isKey() && entry.getValueString() != null) {
                if (entry.getBlobColumeSplit() != null) {
                    fieldToRecord.setSplitRecordBlob(record, entry);
                } else {
                    fieldToRecord.SetRecordKey(record, entry);
                }
            } else if (!entry.isKey() && entry.getValueString() != null) {
                if (entry.getBlobColumeSplit() != null) {
                    fieldToRecord.setSplitRecordBlob(record, entry);
                } else {
                    fieldToRecord.SetRecordValue(record, entry);
                }
            }
        }
        if (seq == -1) {
            Response response = Connection.getClient().poll(req);
            int res = response.getResult();
            if (res != 0) {
                System.err.println("ERROR [Cmd(INCREASE)]:" + response.getErrDetail());
                logger.error("Increase Error-->{}", response.getResult());
            } else {
                System.out.println("SUCCESS [Cmd(INCREASE)]");
            }
        } else {
            Connection.getClient().post(req, response -> {
                int res = response.getResult();
                int index = (int) seq % inputQuery.getSize();
                String asynRes = "";
                if (res != 0) {
                    asynRes += String.format("ERROR [Seq(%d) Cmd(INCREASE)]:,Record not exited in tcaplus," +
                            "please check your input%n", seq);
                    logger.error("Increase Error-->{}", response.getResult());
                } else {
                    asynRes += String.format("SUCCESS[Seq(%d) Cmd(INCREASE)]%n", seq);
                }
                inputQuery.getBuffer()[index] = asynRes;
            });
        }
    }
}
