package com.tencent.wea.squadservice;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadConfirmQuitReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadConfirmQuitRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadCreateReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadCreateRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadInfoReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadInfoRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadJoinReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadJoinRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadPreQuitReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadPreQuitRes;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadUpdateMemberInfoReq;
import com.tencent.wea.protocol.SsRoomsvr.RpcSquadUpdateMemberInfoRes;
import com.tencent.wea.protocol.common.ActivitySquad;
import com.tencent.wea.roomservice.roomdata.squad.logic.SquadMgr;

/**
 * @program: WeA
 * @description: 小队逻辑服务
 * @author: nichtsun
 * @create: 2023-03-01
 **/

public class LocalSquadServiceImpl  implements LocalSquadService {
    /**
     * 本地房间服务实现
     *
     * @param routeId 路线id
     */
    public LocalSquadServiceImpl(long routeId) {
    }

    @Override
    public RpcSquadCreateRes.Builder squadCreate(RpcSquadCreateReq.Builder req) throws NKCheckedException {
        RpcSquadCreateRes.Builder ret = RpcSquadCreateRes.newBuilder();
        NKPair<NKErrorCode, ActivitySquad> createRet = SquadMgr.getInstance().create(req.getSquadId(), req.getCreator());
        ret.setResult(createRet.getKey().getValue());
        if (createRet.getKey() == NKErrorCode.OK) {
            ret.setSquadInfo(createRet.getValue());
        }
        return ret;
    }

    @Override
    public RpcSquadJoinRes.Builder squadJoin(RpcSquadJoinReq.Builder req) throws NKCheckedException {
        RpcSquadJoinRes.Builder ret = RpcSquadJoinRes.newBuilder();
        NKPair<NKErrorCode, ActivitySquad> joinRet = SquadMgr.getInstance().join(req.getSquadId(), req.getJoinPlayer());
        ret.setResult(joinRet.getKey().getValue());
        if (joinRet.getKey() == NKErrorCode.OK) {
            ret.setSquad(joinRet.getValue());
        }
        return ret;
    }

    @Override
    public RpcSquadInfoRes.Builder squadInfo(RpcSquadInfoReq.Builder req) throws NKCheckedException {
        RpcSquadInfoRes.Builder ret = RpcSquadInfoRes.newBuilder();
        NKPair<NKErrorCode, ActivitySquad> getRet = SquadMgr.getInstance().get(req.getSquadId());
        ret.setResult(getRet.getKey().getValue());
        if (getRet.getKey() == NKErrorCode.OK) {
            ret.setSquadInfo(getRet.getValue());
        }
        return ret;
    }

    @Override
    public RpcSquadUpdateMemberInfoRes.Builder squadUpdateMember(RpcSquadUpdateMemberInfoReq.Builder req) throws NKCheckedException {
        RpcSquadUpdateMemberInfoRes.Builder ret = RpcSquadUpdateMemberInfoRes.newBuilder();
        NKPair<NKErrorCode, ActivitySquad> updateRet = SquadMgr.getInstance().updateMember(req.getSquadId(), req.getTargetPlayer());
        ret.setResult(updateRet.getKey().getValue());
        if (updateRet.getKey() == NKErrorCode.OK) {
            ret.setSquad(updateRet.getValue());
        }
        return ret;
    }

    @Override
    public RpcSquadPreQuitRes.Builder squadPreQuit(RpcSquadPreQuitReq.Builder req) throws NKCheckedException {
        RpcSquadPreQuitRes.Builder ret = RpcSquadPreQuitRes.newBuilder();
        NKErrorCode updateRet = SquadMgr.getInstance().preQuit(req.getSquadId(), req.getQuitterId());
        ret.setResult(updateRet.getValue());
        return ret;
    }

    @Override
    public RpcSquadConfirmQuitRes.Builder squadConfirmQuit(RpcSquadConfirmQuitReq.Builder req)
            throws NKCheckedException {
        RpcSquadConfirmQuitRes.Builder ret = RpcSquadConfirmQuitRes.newBuilder();
        NKErrorCode updateRet = SquadMgr.getInstance().confirmQuit(req.getSquadId(), req.getQuitterId());
        ret.setResult(updateRet.getValue());
        return ret;
    }
}
