package com.tencent.wea.coczoneservice;

import com.tencent.timiCoroutine.LocalService;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.wea.coczoneservice.managers.CocVersionControlMgr;
import com.tencent.wea.protocol.common.LocalServiceType;

/**
 * <AUTHOR>
 * @date 2024/8/16
 */
public class CocZoneService extends LocalService {

    public CocZoneService() {
        super(LocalServiceType.LOCAL_COCSVR_ZONE_SERVICE);

        // LocalZoneService只可以有一个线程
        generateExecutorGroupWithNewContainer("cocZoneService", 1000, 1000, getLocalServiceType());
    }

    @Override
    protected void executorLocalInit(int executorServiceIndex) {
        super.executorLocalInit(executorServiceIndex);
        // todo
        CocVersionControlMgr.getInstance().init();
    }

    @Override
    protected int executorLocalReload(int executorServiceIndex) {
        super.executorLocalReload(executorServiceIndex);
        CocVersionControlMgr.getInstance().onReload();
        return 0;
    }

    @Override
    protected int executorLocalProc(int executorServiceIndex, TxStopWatch stopWatchHandle) {
        super.executorLocalProc(executorServiceIndex, stopWatchHandle);
        CocVersionControlMgr.getInstance().proc();
        return 0;
    }
}
