package com.tencent.wea.cocplayerservice.util.instantcondition.impl;

import com.tencent.wea.cocplayerservice.player.CocPlayer;
import com.tencent.wea.cocplayerservice.util.instantcondition.factory.ICocInstantCondition;
import com.tencent.wea.xlsRes.ResCOCCommon;
import com.tencent.wea.xlsRes.keywords.COCBuildingType;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
public class CocInstantCondBuildingUpgradeCnt implements ICocInstantCondition<ResCOCCommon.COCConditionBuildingUpgradeCnt> {

    @Override
    public boolean check(CocPlayer player, ResCOCCommon.COCConditionBuildingUpgradeCnt conditionInfo) {
        int cnt = 0;
        if (conditionInfo.getBuildingTypeId() > 0) {
            cnt = player.getCityBuildingManager().getBuildingUpgradeCntByType(COCBuildingType.forNumber(conditionInfo.getBuildingTypeId()));
        } else if (conditionInfo.getTypeId() > 0) {
            cnt = player.getCityBuildingManager().getBuildingUpgradeCntByType(conditionInfo.getTypeId());
        }
        return cnt >= conditionInfo.getCnt();
    }
}
