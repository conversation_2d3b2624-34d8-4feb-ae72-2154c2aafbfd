package com.tencent.wea.scripts


import com.tencent.nk.groovy.GroovyScript
import com.tencent.timiCoroutine.LocalService
import com.tencent.wea.cocplayerservice.player.CocPlayerRegister
import com.tencent.wea.framework.CocSvrEngine
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

import java.util.concurrent.Callable

/**
 * <AUTHOR>
 * @date 2024/12/23
 */
class CocPlayerRegisterTest implements GroovyScript {

    private static final Logger LOGGER = LogManager.getLogger("groovyScripts")

    static void runOnCocPlayerService(long index, Callable<Void> callable) {
        def playerService = CocSvrEngine.getSpecInstance().getPlayerService()
        LocalService.DoNotDirectCall.GroovyTest.runJob(playerService, index, callable, "groovyCall", false)
    }

    class Profiler {
        private volatile long allJobStartTime
        private volatile long jobCount

        private long finishedJobCounter
        private long failedJobCounter;

        private long totalJobCostMs
        private long maxJobCostMs
        private long minJobCostMs = -1

        private long totalRequestCostMs
        private long maxRequestCostMs
        private long minRequestCostMs = -1


        public Profiler(int jobCount) {
            this.allJobStartTime = System.currentTimeMillis()
            this.jobCount = jobCount
        }

        public synchronized onJobFinish(boolean success, long jobCostTimeMs, long requestCostTimeMs) {
            finishedJobCounter++

            if (success) {
                totalJobCostMs += jobCostTimeMs
                if (jobCostTimeMs > maxJobCostMs) {
                    maxJobCostMs = jobCostTimeMs
                }
                if (minJobCostMs == -1 || jobCostTimeMs < minJobCostMs) {
                    minJobCostMs = jobCostTimeMs
                }

                totalRequestCostMs += requestCostTimeMs
                if (requestCostTimeMs > maxRequestCostMs) {
                    maxRequestCostMs = requestCostTimeMs
                }
                if (minRequestCostMs == -1 || requestCostTimeMs < minRequestCostMs) {
                    minRequestCostMs = requestCostTimeMs
                }
            } else {
                failedJobCounter++
            }

            if (finishedJobCounter == jobCount) {
                // 全部完成
                def totalCostMs = System.currentTimeMillis() - allJobStartTime;
                
                def successCnt = finishedJobCounter - failedJobCounter
                long avgJobCost = successCnt == 0 ? 0 : (long) (totalJobCostMs / successCnt)
                long avgRequestCost = successCnt == 0 ? 0 : (long) (totalRequestCostMs / successCnt)
                long tps = (long) (1000L * (finishedJobCounter) / totalCostMs)
                LOGGER.error("all job finished! totalCount:[{}] failedCount:[{}] totalCostMs:[{}] tps:[{}]", jobCount, failedJobCounter, totalCostMs, tps)
                LOGGER.error("maxJobCostMs:[{}] minJobCostMs:[{}] avgJobCostMs:[{}]", maxJobCostMs, minJobCostMs, avgJobCost)
                LOGGER.error("maxRequestCostMs:[{}] minRequestCostMs:[{}] avgRequestCostMs:[{}]", maxRequestCostMs, minRequestCostMs, avgRequestCost)
            }
        }
    }


    @Override
    String runScript(String[] args) {
        return registerTest()
    }

    def registerTest() {
        int count = 30000
        long beginIndex = 131480
        long endIndex = beginIndex + count

        def profiler = new Profiler(count)

        for(long i = beginIndex; i < endIndex; i++) {
            long uid = i;
            long jobEnQueueTime = System.currentTimeMillis()
            runOnCocPlayerService(uid, () -> {
                long jobBeginTime = System.currentTimeMillis()

                boolean success = true
                try {
                    CocPlayerRegister.registerPlayer(uid, "groovyTest")
                } catch (Exception e) {
                    LOGGER.error("groovy test register cocPlayer failed! uid:[{}]", uid, e)
                    success = false
                }

                def finishTime = System.currentTimeMillis()
                long jobCostTimeMs = finishTime - jobBeginTime
                long requestCostTimeMs = finishTime - jobEnQueueTime

                profiler.onJobFinish(success, jobCostTimeMs, requestCostTimeMs)

                return null
            })
        }
    }

}
