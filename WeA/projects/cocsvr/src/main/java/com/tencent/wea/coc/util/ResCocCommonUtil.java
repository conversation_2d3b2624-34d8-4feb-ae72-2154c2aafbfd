package com.tencent.wea.coc.util;

import com.tencent.timiutil.tool.CollectionUtil;
import com.tencent.wea.protocol.common.CocCommon;
import com.tencent.wea.protocol.common.KeyValueInt64;
import com.tencent.wea.xlsRes.ResCOCCommon;
import com.tencent.wea.xlsRes.keywords.COCResourceType;
import it.unimi.dsi.fastutil.ints.Int2LongArrayMap;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.util.Strings;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
public class ResCocCommonUtil {
    public static class ResourceSummaryInfo {

        private final Map<Integer, Long> itemMap = new Int2LongArrayMap();
        private final Map<COCResourceType, Long> resourceMap = new EnumMap<>(COCResourceType.class);

        public ResourceSummaryInfo() {
        }

        public ResourceSummaryInfo(ResCOCCommon.CommonResConf commonResConf) {
            this(commonResConf.getItemsList(), commonResConf.getResourcesList());
        }

        public ResourceSummaryInfo(List<ResCOCCommon.COCItemEntry> itemList, List<ResCOCCommon.COCResourceEntry> resourceList) {
            if (null != itemList && !itemList.isEmpty()) {
                for (ResCOCCommon.COCItemEntry itemEntry : itemList) {
                    long itemNum = itemEntry.getItemNum();
                    int itemId = itemEntry.getItemId();
                    mergeItem(itemId, itemNum);
                }
            }
            if (null != resourceList && !resourceList.isEmpty()) {
                for (ResCOCCommon.COCResourceEntry resourceEntry : resourceList) {
                    COCResourceType resType = resourceEntry.getResType();
                    long resValue = resourceEntry.getResValue();
                    mergeResource(resType, resValue);
                }
            }
        }

        public ResourceSummaryInfo merge(ResourceSummaryInfo other) {
            mergeItemsMap(other.getItemMap());
            mergeResourceMap(other.getResourcesMap());

            return this;
        }

        public ResourceSummaryInfo mergeItemsMap(Map<Integer, Long> other) {
            if (!CollectionUtil.isEmpty(other)) {
                for (Map.Entry<Integer, Long> itemEntry : other.entrySet()) {
                    mergeItem(itemEntry.getKey(), itemEntry.getValue());
                }
            }
            return this;
        }

        public ResourceSummaryInfo mergeItem(int itemId, long itemCount) {
            if (itemCount != 0) {
                itemMap.merge(itemId, itemCount, Long::sum);
            }
            return this;
        }


        public ResourceSummaryInfo mergeResourceMap(Map<COCResourceType, Long> other) {
            if (!CollectionUtil.isEmpty(other)) {
                for (Map.Entry<COCResourceType, Long> resourceEntry : other.entrySet()) {
                    mergeResource(resourceEntry.getKey(), resourceEntry.getValue());
                }
            }
            return this;
        }

        public ResourceSummaryInfo mergeResource(COCResourceType resourceType, long resourceNum) {
            if (resourceNum != 0) {
                resourceMap.merge(resourceType, resourceNum, Long::sum);
            }
            return this;
        }

        public Map<Integer, Long> getItemMap() {
            return itemMap;
        }

        public Map<COCResourceType, Long> getResourcesMap() {
            return resourceMap;
        }

        public CocCommon.CocCommonResource toProto() {
            CocCommon.CocCommonResource.Builder builder = CocCommon.CocCommonResource.newBuilder();
            for (Map.Entry<Integer, Long> entry : itemMap.entrySet()) {
                builder.addItems(KeyValueInt64.newBuilder().setKey(entry.getKey()).setValue(entry.getValue()).build());
            }
            for (Map.Entry<COCResourceType, Long> entry : resourceMap.entrySet()) {
                builder.addResources(KeyValueInt64.newBuilder().setKey(entry.getKey().getNumber()).setValue(entry.getValue()).build());
            }
            return builder.build();
        }

        public boolean isEmpty() {
            return itemMap.isEmpty() && resourceMap.isEmpty();
        }

        @Override
        public String toString() {
            return "ResourceSummaryInfo{" +
                    "itemMap=" + itemMap +
                    ", resourceMap=" + resourceMap +
                    '}';
        }

        public String getResourceSting() {
            if (resourceMap.isEmpty()) {
                return Strings.EMPTY;
            }

            StringBuilder sb = new StringBuilder();
            for (Map.Entry<COCResourceType, Long> entry : resourceMap.entrySet()) {
                sb.append(entry.getKey().getNumber()).append(",").append(entry.getValue()).append(";");
            }
            return sb.toString();
        }

        public String getItemString() {
            if (itemMap.isEmpty()) {
                return Strings.EMPTY;
            }

            StringBuilder sb = new StringBuilder();
            for (Map.Entry<Integer, Long> entry : itemMap.entrySet()) {
                sb.append(entry.getKey()).append(",").append(entry.getValue()).append(";");
            }
            return sb.toString();
        }
    }
}
