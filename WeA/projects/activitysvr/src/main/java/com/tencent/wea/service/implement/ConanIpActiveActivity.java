package com.tencent.wea.service.implement;

import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.ActivityConanIpCardConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.ConanIpActiveData;
import com.tencent.wea.attr.RewardCompensateTaskStatus;
import com.tencent.wea.attr.RewardItemInfo;
import com.tencent.wea.protocol.common.ActivityGeneralReqInfo;
import com.tencent.wea.protocol.common.ConanPlazaActiveBuffRspInfo;
import com.tencent.wea.protocol.common.ConanPlazaActiveChallengeAwardReqInfo;
import com.tencent.wea.protocol.common.ConanPlazaActiveChallengeAwardRspInfo;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.service.module.TaskModule;
import com.tencent.wea.service.object.PlayerActivity;
import com.tencent.wea.service.task.ActivityTask;
import com.tencent.wea.util.MsgSendUtil;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.RewardCompensateType;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 柯南二期广场配套ip活跃活动
 */

public class ConanIpActiveActivity extends TaskActivity implements EventConsumer {
    private static final Logger LOGGER = LogManager.getLogger(ConanIpActiveActivity.class);


    public ConanIpActiveActivity(PlayerActivity player, ActivityUnit activityUnit) {
        super(player, activityUnit);
    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATConanIpActive;
    }

    @Override
    public void init(boolean isNew) {
        super.init(isNew);
        closeRedDot();
    }

    public ConanIpActiveData getConanIpActiveData() {
        return activityUnit.getDetailData().getConanIpActiveData();
    }

    /**
     * 获取每日领取奖励限制次数
     *
     * @return
     */
    public int getAwardCountLimit() {
        if (getActivityMainConfig().getActivityParamCount() == 0) {
            LOGGER.error("getAwardCountLimit error ,activityId:{}", getActivityId());
            return 0;
        }
        return getActivityMainConfig().getActivityParam(0);
    }

    public int addBuff(ActivityGeneralReqInfo reqInfo, ConanPlazaActiveBuffRspInfo.Builder rspInfo) {
        ConanIpActiveData conanIpActiveData = getConanIpActiveData();
        if (conanIpActiveData.getBuff()) {
            return NKErrorCode.OK.getValue();
        }
        LOGGER.debug("add buff,playerId:{},activityId:{},conanIpActiveData:{}", player.getUid(), getActivityId(), conanIpActiveData);
        conanIpActiveData.setBuff(true);
        rspInfo.setBuff(true);
        LOGGER.info("add buff,playerId:{},activityId:{},conanIpActiveData:{}", player.getUid(), getActivityId(), conanIpActiveData);
        Monitor.getInstance().add.succ(MonitorId.attr_activitysvr_conanIp_AddBuff_size, 1, new String[]{String.valueOf(activityId)});
        return NKErrorCode.OK.getValue();
    }

    public int receiveCardAward(ConanPlazaActiveChallengeAwardReqInfo awardReqInfo, ConanPlazaActiveChallengeAwardRspInfo.Builder rspInfo) {
        int configId = awardReqInfo.getChoose();
        boolean buff = awardReqInfo.getBuff();
        ConanIpActiveData conanIpActiveData = getConanIpActiveData();
        LOGGER.debug("receive card award,playerId:{},activityId:{},configId:{},conanIpActiveData:{},buff:{}",
                player.getUid(), getActivityId(), configId, conanIpActiveData, buff);
        if (conanIpActiveData.getAwardCount() >= getAwardCountLimit()) {
            LOGGER.info("receive card award limit:{},playerId:{},activityId:{},configId:{}", getAwardCountLimit(), player.getUid(), getActivityId(), configId);
            return NKErrorCode.ActivityConanIpActiveAwardLimit.getValue();

        }
        ResActivity.ActivityConanIpCardConf conf = ActivityConanIpCardConfData.getInstance().get(configId);
        if (conf == null) {
            LOGGER.error("receive card award conf is null,playerId:{},activityId:{},configId:{}", player.getUid(), getActivityId(), configId);
            return NKErrorCode.ActivityConanIpActiveConfIsNull.getValue();

        }
        if (conf.getActivityId() != activityId) {
            LOGGER.error("receive card award conf activityId error,playerId:{},activityId:{},configId:{}", player.getUid(), getActivityId(), configId);
            return NKErrorCode.ActivityConanIpActiveIdError.getValue();
        }
        conanIpActiveData.addAwardCount(1);
        List<ItemInfo> itemList = new ArrayList<>();
        for (ResCommon.Item item : conf.getRewardInfoList()) {
            int itemNum = item.getItemNum();
            if (conanIpActiveData.getBuff() || buff) {
                itemNum *= 2;
            }
            ItemInfo itemInfo = ItemInfo.newBuilder().setItemId(item.getItemId()).setItemNum(itemNum).build();
            itemList.add(itemInfo);
        }
        // 给玩家发送奖励
        var itemMap = MsgSendUtil.fillActivityItemMap(activityId, itemList);
        MsgSendUtil.notifyGameSvrToAwardPlayer(itemMap, player.getUid(), MsgSendUtil.fillPiiActivitySvrRewardParamsLocal(activityId, getType().getNumber(), itemMap));

        LOGGER.info("receive card award,playerId:{},activityId:{},configId:{},conanIpActiveData:{}", player.getUid(), getActivityId(), configId, conanIpActiveData);
        rspInfo.setRewardCount(conanIpActiveData.getAwardCount());
        Monitor.getInstance().add.succ(MonitorId.attr_activitysvr_conanIp_ReceiveCardAward_size, 1, new String[]{String.valueOf(activityId), String.valueOf(buff), String.valueOf(configId)});
        checkActivityRedPoint();
        return NKErrorCode.OK.getValue();
    }

    @Override
    public void onMidNight() {
        super.onMidNight();
        resetData();
    }

    public void resetData() {
        LOGGER.debug("resetData data,playerId:{},activityId:{}", player.getUid(), getActivityId());
        ConanIpActiveData conanIpActiveData = getConanIpActiveData();
        conanIpActiveData.clear();
    }


    @Override
    public void onExpire() {
//        sendRewardMail(RewardCompensateType.RCT_CONAN_ACTIVE_TASK);
    }

    @Override
    public void onLoadExpire() {
//        sendRewardMail(RewardCompensateType.RCT_CONAN_ACTIVE_TASK);
    }

    @Override
    public void checkActivityRedPoint() {
        boolean delRedDot = activityUnit.getRedDotShow();
        for (int taskId : getActivityTaskIdList()) {
            if (activityUnit.getClearRedDotInfo().getFilterTask().contains(taskId)) {
                LOGGER.debug("check task filter, uid:{} activityId:{} taskId:{}", player.getUid(), activityId,
                        taskId);
                continue;
            }
            ActivityTask task = player.getTaskManager().getTask(taskId);
            if (task != null && task.getStatus() == TaskStatus.TS_Completed && task.getTaskIsExistReward()) {
                // 完成任务，可领奖, 且任务包含奖励, 才去设置红点
                LOGGER.debug("red dot reason, uid:{} id:{} reason:task task:{}, isExistReward:{}",
                        player.getUid(), activityId, task.getId(), task.getTaskIsExistReward());
                openRedDot();
                return;
            }
        }
        ConanIpActiveData conanIpActiveData = getConanIpActiveData();
        if(conanIpActiveData.getAwardCount() == 0){
            LOGGER.debug("red dot reason,uid:{},activityId:{},conanIpActiveData:{}", player.getUid(), getActivityId(), conanIpActiveData);
            openRedDot();
            return;
        }
        if (delRedDot) {
            closeRedDot();
        }
    }


    /**
     * 奖励补发
     * 加载中时，找不到任务
     * @param type
     */
    private void sendRewardMail(RewardCompensateType type) {
        LOGGER.info("send reward mail,playerId:{},activityId:{},type:{}", player.getUid(), getActivityId(), type);
        if (!player.getRewardCompensateManager().isSwitchOpen(type)) {
            return;
        }
        RewardCompensateTaskStatus rewardCompensateStatus = player.getAttr().getRewardCompensate().getTypeToTasks().computeIfAbsent(type,
                k -> new RewardCompensateTaskStatus().setType(type));

        for (int taskGroupId : getModule(TaskModule.class).getTaskGroupIdList()) {
            ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
            if (taskGroup != null && taskGroup.getTaskIdListCount() > 0) {
                for (int taskId : taskGroup.getTaskIdListList()) {
                    ActivityTask task = player.getTaskManager().getTask(taskId);
                    if (task != null && task.getStatus() == TaskStatus.TS_Completed && task.getTaskIsExistReward()) {
                        // 完成任务，可领奖, 且任务包含奖励
                        LOGGER.debug("sendRewardMail, uid:{} id:{} reason:task task:{}, isExistReward:{}",
                                player.getUid(), activityId, task.getId(), task.getTaskIsExistReward());
                        List<ItemInfo> rewardList = task.receiveReward();
                        if (!rewardList.isEmpty()) {
                            for (ItemInfo item : rewardList) {
                                int itemId = item.getItemId();
                                long num = item.getItemNum();
                                RewardItemInfo itemInfo = rewardCompensateStatus.getRewards().get((long) itemId);
                                if (itemInfo == null) {
                                    itemInfo = new RewardItemInfo();
                                    itemInfo.setId(itemId);
                                }
                                itemInfo.addNum(num);
                                itemInfo.setExpireTimeMs(item.getExpireTimeMs());
                                itemInfo.setExpireType(item.getExpireType());
                                rewardCompensateStatus.getRewards().put((long) itemId, itemInfo);
                            }
                        }
                    }
                }
            }
        }
        LOGGER.info("send reward mail,playerId:{},activityId:{},type:{} awardInfo:{}", player.getUid(), getActivityId(), type, rewardCompensateStatus.getRewards());
        player.getRewardCompensateManager().SendRewardMail(type);
    }
}