package com.tencent.wea.util;

import com.tencent.cache.Cache;
import com.tencent.cache.CacheUtil;
import com.tencent.coRedis.CoRedisCmd;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.guid.ActivitySquadIdGenerator;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.protocol.AttrActivitySquadData;
import com.tencent.wea.protocol.AttrSquadMember;
import com.tencent.wea.service.interfaces.ActivityTeamInterface;
import com.tencent.wea.service.object.ActivityEnum;
import com.tencent.wea.service.db.ActivityTeamData;
import com.tencent.wea.service.object.BaseActivity;
import com.tencent.wea.xlsRes.ResActivityTeam;
import com.tencent.wea.service.object.PlayerActivity;
import com.tencent.resourceloader.resclass.ActivityTeamConfig;
import com.tencent.wea.protocol.AttrSquadMember.proto_SquadMember;
import com.tencent.wea.protocol.AttrActivitySquadData.proto_ActivitySquadData;
import com.tencent.wea.protocol.AttrActivitySquadDetail.proto_ActivitySquadDetail;
import com.tencent.wea.protocol.AttrActivitySquadDetail.proto_ActivitySquadDetail.Builder;
import com.tencent.wea.tcaplus.TcaplusDb.ActivityPlayerTeamTable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

import com.tencent.wea.xlsRes.ResActivity;

/**
 * <AUTHOR>
 * @date 2025/03/79
 * @desc 活动小队工具函数
 */
public class ActivityTeamUtil {
    private static final Logger LOGGER = LogManager.getLogger(ActivityTeamUtil.class);

    // 分配小队ID
    public static long allocTeamId() {
        return ActivitySquadIdGenerator.getInstance().allocGuid();
    }

    // 尝试获取队伍锁
    public static boolean tryLockTeamId(long teamId, long uid) {
        try {
            CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
            String redisKey = CacheUtil.SquadJoinLock.getKey(teamId);
            String res = coRedisCmd.setnxex(redisKey, String.valueOf(uid), 6);
            if ("OK".equals(res)) {
                LOGGER.debug("tryLockTeamId success,teamId:{},uid{}", teamId, uid);
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("tryLockTeamId error!", e);
        }
        LOGGER.debug("tryLockTeamId fail,teamId:{},uid{}", teamId, uid);
        return false;
    }

    // 解锁小队
    public static void unlockTeamId(long teamId, long uid) {
        try {
            String key = CacheUtil.SquadJoinLock.getKey(teamId);
            boolean isDeleted = Cache.delKeyIfValueEqualTo(key, String.valueOf(uid));
            if (!isDeleted) {
                LOGGER.error("{} is not held by {}", teamId, uid);
            }
        } catch (Exception e) {
            LOGGER.error("unlockTeamId error!", e);
        }

        LOGGER.debug("redis unlock squad start:{}-{}", teamId, uid);
    }

    // 是否已加入了该队伍
    public static boolean isInTeam(long uid, ActivityTeamData team) {
        if (team == null) return false;

        var data = team.getDataBuilder();
        for (var member : data.getMemberList().getListList()) {
            if (member.hasUid() && member.getUid() == uid) {
                return true;
            }
        }

        return false;
    }

    public static boolean isTeamFull(ActivityTeamData team, ResActivityTeam.ActivityTeam cfg) {
        boolean res = true;
        int maxCount = cfg.getMaxJoinNum();
        var data = team.getDataBuilder();
        int curCount = data.getMemberList().getListCount();

        return curCount >= maxCount;
    }

    // 根据Player对象,生成小队成员对象数据
    public static proto_SquadMember.Builder getPlayerMemberInfoBuilder(PlayerActivity player) {
        return proto_SquadMember.newBuilder()
                .setUid(player.getUid())
                .setOpenid(player.getOpenId())
                .setPlatId(player.getPlatId())
                .setLastUpdateTimeMs(DateUtils.currentTimeMillis())
                .setJoinTimeMs(DateUtils.currentTimeMillis())
                .setMemberSquadData(AttrActivitySquadData.proto_ActivitySquadData.newBuilder());
    }

    private static ActivityTeamInterface getTeamActivity(BaseActivity baseActivity, PlayerActivity player, int activityId) {
        if (baseActivity == null) {
            LOGGER.error("baseActivity is null! player[{}] activityId[{}]", player.getUid(), activityId);
            return null;
        }

        if (baseActivity instanceof ActivityTeamInterface) {
            return (ActivityTeamInterface) baseActivity;
        } else {
            LOGGER.error("not have ActivityTeamInterface! player[{}] activityId[{}]", player.getUid(), activityId);
            return null;
        }
    }


    /**
     * 测试添加玩家到队伍
     *
     * @param teamData 队伍数据
     * @param player   玩家对象
     * @return 测试结果，OK表示可以添加
     */
    public static NKErrorCode testAddPlayerToTeam(ActivityTeamData teamData, PlayerActivity player, int activityId) {
        if (teamData == null || player == null) {
            LOGGER.error("testAddPlayerToTeam: invalid input");
            return NKErrorCode.ActivityTeamDataNotFound;
        }

        // 检查玩家是否已经在队伍中
        if (isInTeam(player.getUid(), teamData)) {
            LOGGER.warn("testAddPlayerToTeam: player[{}] already in team[{}]", player.getUid(), teamData.getTeamId());
            return NKErrorCode.ActivityTeamAlreadyInTeam;
        }

        // 检查队伍是否已满
        var teamCfg = ActivityTeamConfig.getInstance().getActivityTeamConfig(activityId);
        if (teamCfg != null && isTeamFull(teamData, teamCfg)) {
            LOGGER.warn("testAddPlayerToTeam: team[{}] is full", teamData.getTeamId());
            return NKErrorCode.ActivityTeamIsFull;
        }

        return NKErrorCode.OK;
    }

    /**
     * 测试从队伍移除玩家
     *
     * @param teamData 队伍数据
     * @param player   玩家对象
     * @return 测试结果，OK表示可以移除
     */
    public static NKErrorCode testRemovePlayerToTeam(ActivityTeamData teamData, PlayerActivity player) {
        if (teamData == null || player == null) {
            LOGGER.error("testRemovePlayerToTeam: invalid input");
            return NKErrorCode.UnknownError;
        }

        // 检查玩家是否在队伍中
        if (!isInTeam(player.getUid(), teamData)) {
            LOGGER.warn("testRemovePlayerToTeam: player[{}] not in team[{}]", player.getUid(), teamData.getTeamId());
            return NKErrorCode.ActivityTeamNotInTeam;
        }

        return NKErrorCode.OK;
    }

    /**
     * 向队伍添加玩家
     */
    public static NKErrorCode addPlayerToTeam(ActivityTeamData teamData, PlayerActivity player, int activityId) {
        // 先进行测试
        NKErrorCode testRes = testAddPlayerToTeam(teamData, player, activityId);
        if (testRes != NKErrorCode.OK) {
            LOGGER.error("addPlayerToTeam test failed! player[{}] team[{}] error[{}]",
                    player.getUid(), teamData.getTeamId(), testRes);
            return testRes;
        }

        // 添加玩家到小队成员列表
        proto_SquadMember.Builder memberBuilder = ActivityTeamUtil.getPlayerMemberInfoBuilder(player);
        teamData.getDataBuilder().getMemberListBuilder().addList(memberBuilder);

        LOGGER.info("joinTeam success! player[{}] joined team[{}]", player.getUid(), teamData.getTeamId());
        return NKErrorCode.OK;
    }

    /**
     * 从队伍移除玩家
     */
    public static NKErrorCode removePlayerToTeam(ActivityTeamData teamData, PlayerActivity player) {
        // 先进行测试
        NKErrorCode testRes = testRemovePlayerToTeam(teamData, player);
        if (testRes != NKErrorCode.OK) {
            LOGGER.error("removePlayerToTeam test failed! player[{}] team[{}] error[{}]",
                    player.getUid(), teamData.getTeamId(), testRes);
            return testRes;
        }

        var memberListBuilder = teamData.getDataBuilder().getMemberListBuilder();
        int memberCount = memberListBuilder.getListCount();
        for (int i = memberCount - 1; i >= 0; i--) {
            if (memberListBuilder.getList(i).getUid() == player.getUid()) {
                memberListBuilder.removeList(i);
                LOGGER.info("remove success! player[{}] left team[{}]", player.getUid(), teamData.getTeamId());
                return NKErrorCode.OK;
            }
        }

        LOGGER.error("player[{}] not found in team[{}]", player.getUid(), teamData.getTeamId());
        return NKErrorCode.OK;
    }

    // 是否是空队伍
    public static boolean isTeamEmpty(ActivityTeamData teamData) {
        if (teamData == null) {
            LOGGER.warn("isTeamEmpty: teamData is null");
            return true;
        }

        // 获取成员列表数量
        int memberCount = teamData.getDataBuilder().getMemberListBuilder().getListCount();
        return memberCount == 0;
    }

    public static proto_ActivitySquadDetail buildActivitySquadDetail(ActivityPlayerTeamTable teamTable) {
        proto_ActivitySquadDetail.Builder detailBuilder = proto_ActivitySquadDetail.newBuilder();
        detailBuilder.setSquadId(teamTable.getId());
        detailBuilder.setActivityId(teamTable.getActivityId());
        detailBuilder.setSquadCreateTimeMs(teamTable.getCreateTimeMs());
        detailBuilder.setLeaderUid(teamTable.getLeaderUid());

        if (teamTable.hasData()) {
            detailBuilder.setExtraData(teamTable.getData());
        }

        // 处理成员列表
        if (teamTable.hasMemberList() && teamTable.getMemberList().getListCount() > 0) {
            List<proto_SquadMember> squadMembers = teamTable.getMemberList().getListList();
            detailBuilder.addAllMembers(squadMembers);
        }

        // 返回构建好的 proto_ActivitySquadDetail 对象
        return detailBuilder.build();
    }

    ///////////////////////////////////小队接口回调实现///////////////////////////////////
    // 设置队伍ID
    public static void setTeamId(PlayerActivity player, int activityId, long newTeamId) {
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return;
        }

        long oldTeamId = -1;
        try {
            oldTeamId = teamActivity.getTeamId();
            teamActivity.setTeamId(newTeamId);
        } catch (Exception e) {
            LOGGER.error("onCreateSuccess is exception! player[{}] activityId[{}]",
                    activityId, player.getUid());
        }

        LOGGER.info("setTeamId ok! player[{}] activityId[{}] newTeamId[{}] oldTeamId[{}]",
                player.getUid(), activityId, newTeamId, oldTeamId);
    }

    public static long getTeamId(PlayerActivity player, int activityId) {
        // 获取玩家当前运行的活动
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        // 转换为ActivityTeamInterface接口
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return -1; // 返回无效的teamId
        }

        long teamId = -1;
        try {
            // 调用接口方法获取队伍ID
            teamId = teamActivity.getTeamId();
        } catch (Exception e) {
            LOGGER.error("getTeamId is exception! player[{}] activityId[{}]",
                    player.getUid(), activityId);
        }

        LOGGER.debug("getTeamId ok! player[{}] activityId[{}] teamId[{}]",
                player.getUid(), activityId, teamId);
        return teamId;
    }

    // 创建前置检查
    public static boolean createCheck(PlayerActivity player, int activityId) {
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return false;
        }

        boolean res = false;
        try {
            res = teamActivity.checkCanCreate();
        } catch (Exception e) {
            LOGGER.error("checkCanCreate is exception! player[{}] activityId[{}]",
                    activityId, player.getUid());
            return false;
        }

        LOGGER.info("createCheck player[{}] activityId[{}] res[{}]", player.getUid(), activityId, res);
        return res;
    }

    // 队伍创建成功回调
    public static void createSuccess(PlayerActivity player, int activityId, ActivityTeamData team) {
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return;
        }

        try {
            teamActivity.onCreateSuccess(team);
        } catch (Exception e) {
            LOGGER.error("onCreateSuccess is exception! player[{}] activityId[{}]",
                    activityId, player.getUid());
        }

        LOGGER.info("createSuccess ok! player[{}] activityId[{}]", player.getUid(), activityId);
    }

    /*
     * 加入队伍前回调
     * return fasle 表示检查失败,不允许加入
     */
    public static NKErrorCode joinTeamBefore(PlayerActivity player, int activityId,
                                             ActivityTeamData joinTeam, ActivityTeamData selfTeam) {
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return NKErrorCode.UnknownError;
        }

        NKErrorCode res;
        try {
            res = teamActivity.onJoinTeamBefore(joinTeam, selfTeam);
        } catch (Exception e) {
            LOGGER.error("onJoinTeamBefore is exception! player[{}] activityId[{}]", player.getUid(), activityId);
            return NKErrorCode.UnknownError;
        }

        LOGGER.error("not have ActivityTeamInterface! player[{}] activityId[{}] res[{}]",
                player.getUid(), activityId, res);
        return res;
    }

    // 加入队伍后
    public static void joinTeamAfter(PlayerActivity player, int activityId, ActivityTeamData oldTeam, ActivityTeamData curObject) {
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return;
        }

        try {
            teamActivity.onJoinTeamAfter(oldTeam, curObject);
        } catch (Exception e) {
            LOGGER.error("onJoinTeamAfter is exception! player[{}] activityId[{}]", player.getUid(), activityId);
        }

        LOGGER.info("joinTeamAfter ok! player[{}] activityId[{}]", player.getUid(), activityId);
    }

    // 离开当前小队
    public static void leaveTeam(PlayerActivity player, int activityId, long
            leaveTeamId, ActivityEnum.TeamQuitType reason) {
        BaseActivity baseActivity = player.getRunningActivity(activityId);
        ActivityTeamInterface teamActivity = getTeamActivity(baseActivity, player, activityId);
        if (teamActivity == null) {
            LOGGER.error("teamActivity is null! player[{}] activityId[{}]",
                    player.getUid(), activityId);
            return;
        }

        try {
            NKErrorCode errorCode = teamActivity.onLeaveTeam(leaveTeamId, reason);
            if (errorCode != NKErrorCode.OK) {
                LOGGER.error("leaveTeam failed! player[{}] activityId[{}], error[{}]",
                        player.getUid(), activityId, errorCode);
            }
        } catch (Exception e) {
            LOGGER.error("onLeaveTeam is exception! player[{}] activityId[{}]",
                    player.getUid(), activityId);
        }

        LOGGER.info("leaveTeam ok! player[{}] activityId[{}]", player.getUid(), activityId);
    }
}