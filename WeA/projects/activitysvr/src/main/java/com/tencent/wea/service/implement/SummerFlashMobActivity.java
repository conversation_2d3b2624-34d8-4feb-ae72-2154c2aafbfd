package com.tencent.wea.service.implement;

import com.tencent.resourceloader.resclass.*;
import com.tencent.wea.attr.Task;
import com.tencent.wea.protocol.AttrTaskLifeTime;
import com.tencent.wea.protocol.CsTask;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.service.task.ActivityTask;
import com.tencent.wea.service.task.ActivityTaskManager;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.util.MsgSendUtil;

import java.util.Collection;
import java.util.Set;
import java.util.ArrayList;
import com.tencent.condition.event.player.common.TaskFinishEvent;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.FarmAnwserData;
import com.tencent.wea.protocol.CsBp;
import com.tencent.wea.protocol.common.SummerRewardType;
import com.tencent.wea.service.module.QuizModule;
import com.tencent.wea.service.object.PlayerActivity;
import com.tencent.wea.xlsRes.ResBattlePass;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.tencent.wea.attr.SummerFlashMobData;
import com.tencent.wea.xlsRes.ResActivity;
import java.util.List;


import com.tencent.timiutil.time.DateUtils;

import static com.tencent.timiutil.time.DateUtils.currentTimeMillis;

/**
 * description: 暑期快闪活动
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
public class SummerFlashMobActivity extends TaskActivity {

    private static final Logger LOGGER = LogManager.getLogger(SummerFlashMobActivity.class);

    public SummerFlashMobActivity(PlayerActivity player,
                                  ActivityUnit activityUnit) {
        super(player, activityUnit);
    }


    @Override
    public ActivityType getType() {
        return ActivityType.ATSummerFlashMob;
    }


    // 玩家活动数据
    private SummerFlashMobData getActivityAttr() {
        return activityUnit.getDetailData().getSummerFlashMobData();
    }


    //获取活动配置
    private ResActivity.SummerFlashPhotoConf  getSummerFlashMobDataByIndex(int groupIndex){

        if(SummerFlashPhotoConf.getInstance().getArrayList()==null || groupIndex>=SummerFlashPhotoConf.getInstance().getArrayList().size()){
            LOGGER.error("init taskGroupId {} error not exist", groupIndex);
            return  null;
        }
        return SummerFlashPhotoConf.getInstance().getArrayList().get(groupIndex);

    }

    //获取当前时间 ， 任务时间 就是当前时间的 周一0点 到周天0点
    private  AttrTaskLifeTime.proto_TaskLifeTime.Builder getCurrentWeek(){


        ResActivity.ActivityMainConfig mainConfig = ActivityMainConfig.getInstance().get(activityId);
        if (mainConfig == null) {
            return null;
        }

        long taskBeginTime= mainConfig.getTimeInfo().getBeginTime().getSeconds()*1000;
        long taskEndTime= mainConfig.getTimeInfo().getEndTime().getSeconds()*1000;

        AttrTaskLifeTime.proto_TaskLifeTime.Builder taskLifeTime = AttrTaskLifeTime.proto_TaskLifeTime.newBuilder();
        taskLifeTime.setShowBeginTime(taskBeginTime)
                .setShowEndTime(taskEndTime)
                .setDoBeginTime(taskBeginTime)
                .setDoEndTime((taskEndTime));
        return  taskLifeTime;
    }


    //添加任务，以任务组为单位添加
    private void addTask(int groupId){

        //获取当前时间 ， 任务时间 就是当前时间的 周一0点 到周天0点
       // AttrTaskLifeTime.proto_TaskLifeTime.Builder taskLifeTime =getCurrentWeek();





        ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(groupId);

        AttrTaskLifeTime.proto_TaskLifeTime.Builder taskLifeTime = AttrTaskLifeTime.proto_TaskLifeTime.newBuilder()
                .setShowBeginTime(taskGroup.getBeginDoTime().getSeconds()*1000)
                .setShowEndTime(taskGroup.getEndDoTime().getSeconds()*1000)
                .setDoBeginTime(taskGroup.getBeginDoTime().getSeconds()*1000)
                .setDoEndTime((taskGroup.getEndDoTime().getSeconds()*1000));

        if (taskGroup != null && taskGroup.getTaskIdListCount() > 0) {
            for (int taskId : taskGroup.getTaskIdListList()) {
                ResTask.TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
                if (taskConf == null) {
                    LOGGER.error("init task activity {} error,  task {} not exist",
                            activityId, taskId);
                    return;
                }

                getPlayer().getTaskManager().registerTask(activityId, taskConf, taskLifeTime.build());
            }
        }else {
            LOGGER.error("init task activity {} error, task group {} not exist",
                    activityId, groupId);
        }


    }




    //初始化活动数据
    @Override
    public void init(boolean isNew) {
        super.init(isNew);
        int currentTaskGroupIndex = getActivityAttr().getTaskWeek();
        if(isNew){

            if (currentTaskGroupIndex == 0){

                getActivityAttr().setTaskWeek(1);
                addTask(getSummerFlashMobDataByIndex(0).getGroupId());
                markNeedSyncData();
                return;

            }
        }
        if(currentTaskGroupIndex !=0 ){
            addTask(getSummerFlashMobDataByIndex(currentTaskGroupIndex-1).getGroupId());
        }
        markNeedSyncData();
    }




    //每周开始处理任务进度
    @Override
    public void onWeekStart() {


        SummerFlashPhotoConf summerFlashPhotoConf = SummerFlashPhotoConf.getInstance();
        List<ResActivity.SummerFlashPhotoConf> summerFlashPhotoConfList = summerFlashPhotoConf.getArrayList();

        // 获取当前进度
        int taskWeek = activityUnit.getDetailData().getSummerFlashMobData().getTaskWeek();

        //拿到当前任务组判断是否有任务没做完。
        int groupId=getSummerFlashMobDataByIndex(taskWeek-1).getGroupId();
        ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(groupId);

        boolean updateWeek = true;
        if (taskGroup != null && taskGroup.getTaskIdListCount() > 0) {
            for (int taskId : taskGroup.getTaskIdListList()) {
                ActivityTask task = player.getTaskManager().getTask(taskId);
                if (task == null){
                    LOGGER.error(" onWeekStart task activity {} error, task  {} not exist",
                            activityId, taskId);
                    return;
                }
                if (task.getStatus() != TaskStatus.TS_Finish && task.getStatus() != TaskStatus.TS_Completed) {
                    updateWeek = false;
                    break;
                }
            }
        }
        if (updateWeek) {
            //做完了--设置进度--做下一组
           getActivityAttr().setTaskWeek(taskWeek+1);
           int groupIds =getSummerFlashMobDataByIndex(taskWeek).getGroupId();
           addTask(groupIds);
        }

        LOGGER.debug(" onWeekStart taskWeek={}" , taskWeek);
    }



    @Override
    public void afterLoginFinish() {

        int taskWeek = getActivityAttr().getTaskWeek();

        SummerFlashPhotoConf summerFlashPhotoConf = SummerFlashPhotoConf.getInstance();
        List<ResActivity.SummerFlashPhotoConf> summerFlashPhotoConfList = summerFlashPhotoConf.getArrayList();

        SummerFlashMobTaskRsqInfo.Builder summerFlashMobTaskRsqInfo=SummerFlashMobTaskRsqInfo.newBuilder();
        //前面的任务不用发。在玩家身上
        Collection<TaskStatusInfo> taskList = new ArrayList<>();


        Collection<Integer> taskListIds = new ArrayList<>();

        for (int i = taskWeek; i < summerFlashPhotoConfList.size(); i++) {

            int groupIds = summerFlashPhotoConfList.get(taskWeek).getRewardGroupId();
            ResTask.TaskGroup taskGroups = TaskGroupData.getInstance().getTaskGroup(groupIds);
            if (taskGroups != null && taskGroups.getTaskIdListCount() > 0) {
                for (int taskId : taskGroups.getTaskIdListList()) {
                    TaskStatusInfo.Builder taskstatusInfo=TaskStatusInfo.newBuilder();
                    taskstatusInfo.setId(taskId);
                    taskstatusInfo.setStatus(TaskStatus.TS_Init);
                    taskList.add(taskstatusInfo.build());
                    taskListIds.add(taskId);
                    summerFlashMobTaskRsqInfo.addAllTaskList(taskstatusInfo.build());

                }
            }

        }


        LOGGER.debug(" afterLoginFinish taskListIds={}" , taskListIds.toString());


        player.getTaskManager().sendTaskChangeNtf(taskList);
    }



    @Override
    public void onMidNight() {

        //做排行榜发奖逻辑

    }


    @Override
    public void checkActivityRedPoint() {
        super.checkActivityRedPoint();

    }

}
