package com.tencent.wea.service.gm;

import com.tencent.reddot.ModuleRedDotCenter;
import com.tencent.wea.service.object.PlayerActivity;
import com.tencent.wea.xlsRes.keywords.GeneralRedDotModuleType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * @author:snowzhuang
 * @date:2025/01/03 10:53
 */
public class GmDeleteGeneralRedDot implements GMTargetHandler {

    private static final Logger LOGGER = LogManager.getLogger(GmDeleteGeneralRedDot.class);

    /**
     * gm处理逻辑接口
     *
     * @param player 玩家
     * @param parma  gm参数列表
     * @return int
     */
    @Override
    public int handle(PlayerActivity player, List<String> parma) {
        if (parma.size() >= 3) {
            int moduleType = Integer.parseInt(parma.get(0));
            int moduleId = Integer.parseInt(parma.get(1));
            int redDotType = Integer.parseInt(parma.get(2));
            long redDotId = 0L;
            if (parma.size() >= 4 && !parma.get(3).isBlank()) {
                redDotId = Long.parseLong(parma.get(3));
            }
            ModuleRedDotCenter moduleRedDotCenter = player.getActivityRedDotMgr().getModuleRedDotCenter(GeneralRedDotModuleType.forNumber(moduleType), moduleId);
            if (null == moduleRedDotCenter) {
                LOGGER.warn("getredDot Center failed: {}, {}, {}", player.getUid(), moduleType, moduleId);
                return -1;
            }
            boolean deleteRet = moduleRedDotCenter.deleteRedDotId(redDotType, redDotId, true);
            if (!deleteRet) {
                return -1;
            }
            return 0;
        }
        return -1;
    }
}
