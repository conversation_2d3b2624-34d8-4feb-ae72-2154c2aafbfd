package com.tencent.wea.action.servlet;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.protobuf.Message;
import com.tencent.cache.Cache;
import com.tencent.cache.CacheUtil;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.util.JsonUtil;
import com.tencent.wea.action.BaseAction;
import com.tencent.wea.protocol.SsUgcplatsvr;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

public class UgcCopyProgressAction extends BaseAction<SsUgcplatsvr.UgcServletMsgReq.Builder> {

    private static final Logger LOGGER = LogManager.getLogger(UgcCopyProgressAction.class);

    @Override
    public Message.Builder execute(SsUgcplatsvr.UgcServletMsgReq.Builder builder) {
        SsUgcplatsvr.UgcServletMsgRes.Builder ret = SsUgcplatsvr.UgcServletMsgRes.newBuilder();

        String body = builder.getBody();
        JsonObject asJsonObject;
        JSONObject retJsonObject = new JSONObject();
        try {
            asJsonObject = JsonParser.parseString(body).getAsJsonObject();
        } catch (RuntimeException e) {
            LOGGER.error("UgcCopyProgressAction: jsonStr:{} {}", body, e);
            retJsonObject.put("code", NKErrorCode.UgcPlatInvalidJson.getValue());
            retJsonObject.put("msg", "req should be followd json");
            ret.setContent(retJsonObject.toString());
            return ret;
        }
        JsonArray ugcIdList = JsonUtil.getJsonArray(retJsonObject, asJsonObject, "ugc_id");
        JSONArray mapList = new JSONArray();
        for (JsonElement element : ugcIdList) {
            JSONObject mapInfo = new JSONObject();
            String ugcId = element.getAsString();
            String key = CacheUtil.UgcAppCopyMapStatus.getKey(ugcId);
            Cache.CacheResult<String> result = CacheUtil.UgcAppCopyMapStatus.getCacheString(key);
            mapInfo.put("ugc_id",ugcId);
            if (result.isOk()) {
                if (StringUtils.isNotBlank(result.val)) {
                    mapInfo.put("progress",result.val);
                }else {
                    mapInfo.put("progress","2");
                }
            }else{
                mapInfo.put("progress","2");
            }
        }

        retJsonObject.put("code", 0);
        retJsonObject.put("msg", "success");
        retJsonObject.put("ret_list", mapList);
        ret.setContent(retJsonObject.toString());
        return ret;
    }

    @Override
    public void executeRun(SsUgcplatsvr.UgcServletMsgReq.Builder builder) {

    }

    @Override
    public Message.Builder errorExecute(Exception e) {
        return null;
    }
}
