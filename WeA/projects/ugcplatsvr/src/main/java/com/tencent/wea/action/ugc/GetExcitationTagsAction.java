package com.tencent.wea.action.ugc;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.protobuf.Message;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.action.BaseAction;
import com.tencent.wea.manager.ReportDataMgr;
import com.tencent.wea.protocol.SsUgcplatsvr;
import com.tencent.wea.protocol.common.UgcExcitationTags;
import com.tencent.wea.xlsRes.keywords.UgcCommandUrlType;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

public class GetExcitationTagsAction extends BaseAction<SsUgcplatsvr.RpcGetExcitationTagsReq.Builder> {

    private static final Logger LOGGER = LogManager.getLogger(UgcCheckWhiteListAction.class);

    @Override
    public Message.Builder execute(SsUgcplatsvr.RpcGetExcitationTagsReq.Builder req) {
        SsUgcplatsvr.RpcGetExcitationTagsRes.Builder res = SsUgcplatsvr.RpcGetExcitationTagsRes.newBuilder();

        JSONObject jsonObject = new JSONObject();
        String env = ReportDataMgr.getInstance().getEnvKey();
        long timestamp = Framework.currentTimeSec();
        long nonce = Framework.currentTimeMillis();
        String openId = "";
        long uid = 0;
        Map<String, String> headMap = new HashMap<String, String>();
        headMap.put("env", env);
        headMap.put("uid", String.valueOf(uid));
        headMap.put("open_id", openId);
        headMap.put("timestamp", String.valueOf(timestamp));
        headMap.put("nonce", String.valueOf(nonce));
        String signature = env + "_" + openId + "_" + uid + "-" + timestamp + "_" + nonce;
        headMap.put("signature", DigestUtils.md5Hex(signature));

        try {
            String httpRes = ReportDataMgr.getInstance()
                    .httpPostReqWithHead(UgcCommandUrlType.UCUT_UrlGetExcitationTags, jsonObject.toString(), headMap,
                            this.getClass().getSimpleName());
            LOGGER.debug("httpRes: {}", httpRes);
            JsonObject asJsonObject = JsonParser.parseString(httpRes).getAsJsonObject();
            int code = asJsonObject.get("code").getAsInt();
            String msg = "";
            if (asJsonObject.has("msg")) {
                msg = asJsonObject.get("msg").getAsString();
            }
            LOGGER.debug("GetExcitationTags code:{}, msg:{}", code, msg);
            if (!asJsonObject.has("data")) {
                LOGGER.error("GetExcitationTags no body in ret");
                res.setResult(NKErrorCode.UgcMapError.getValue());
                return res;
            }

            UgcExcitationTags.Builder tagsInfo = UgcExcitationTags.newBuilder();
            JsonObject body = asJsonObject.getAsJsonObject("data");
            if (!body.has("top_tags")) {
                LOGGER.warn("GetExcitationTags no top_tags in ret");
            } else {
                JsonArray tagJsonArray = body.getAsJsonArray("top_tags");
                Set<Integer> tagSet = new HashSet<>();
                for (JsonElement elm : tagJsonArray) {
                    tagSet.add(elm.getAsInt());
                }
                tagsInfo.addAllTags(tagSet);
            }
            if (!body.has("top_tag_text")) {
                LOGGER.warn("GetExcitationTags no top_tag_text in ret");
            } else {
                tagsInfo.setMsg(body.get("top_tag_text").getAsString());
            }
            res.setResult(NKErrorCode.OK.getValue());
            res.setTagInfo(tagsInfo);
        } catch (Exception e) {
            LOGGER.error("GetExcitationTags: Exception:", e);
            res.setResult(NKErrorCode.UgcMapError.getValue());
        }
        return res;
    }

    @Override
    public void executeRun(SsUgcplatsvr.RpcGetExcitationTagsReq.Builder builder) {

    }

    @Override
    public Message.Builder errorExecute(Exception e) {
        return SsUgcplatsvr.RpcGetExcitationTagsRes.newBuilder().setResult(NKErrorCode.UgcMapError.getValue());
    }
}
