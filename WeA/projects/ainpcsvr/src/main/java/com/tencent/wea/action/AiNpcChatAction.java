package com.tencent.wea.action;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.google.protobuf.Message.Builder;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.manager.AiNpcManager;
import com.tencent.wea.protocol.SsAinpcsvr.RpcAiNpcChatNtfReq;
import com.tencent.wea.protocol.common.AigcHttpResParam;

/**
 * <AUTHOR>
 */
public class AiNpcChatAction extends BaseAction {

    private static final Logger LOGGER = LogManager.getLogger(AiNpcChatAction.class);
    @Override
    public Builder execute(Builder builder) {
        RpcAiNpcChatNtfReq req = (RpcAiNpcChatNtfReq) builder.build();
        try {
            AiNpcManager.AsyncAiChatReq reqAsync = AiNpcManager.getInstance().new AsyncAiChatReq(req);
            long timeout = PropertyFileReader.getIntItem("ai_npc_chat_timeout", 120*1000);
            AigcHttpResParam.Builder param = reqAsync.run(timeout);
        } catch (Exception e) {
            LOGGER.error("AiNpcChatAction execute error", e);
        }
        return null;
    }
    @Override
    public Builder errorExecute() {
        return null;
    }
}
