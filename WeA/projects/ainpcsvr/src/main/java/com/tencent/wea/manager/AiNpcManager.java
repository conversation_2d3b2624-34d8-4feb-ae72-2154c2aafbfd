package com.tencent.wea.manager;

import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jboss.cache.util.concurrent.ConcurrentHashSet;

import com.google.protobuf.Message.Builder;
import com.tencent.ainpc.AiNpcConst.ChatCanvas;
import com.tencent.ainpc.AiNpcConst.DialogboxAction;
import com.tencent.ainpc.AiNpcConst.ResetState;
import com.tencent.lease.LeaseManager;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.guid.BaseGenerator;
import com.tencent.resourceloader.resclass.AigcNpcData;
import com.tencent.tcaplus.TcaplusErrorCode;
import com.tencent.tcaplus.dao.AiNpcTableDao;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.coroutine.CoroutineAsync;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.attr.AiNpcData;
import com.tencent.wea.data.AiNpcCache;
import com.tencent.wea.data.ChatRequestCache;
import com.tencent.wea.protocol.SsAigcsvr;
import com.tencent.wea.protocol.SsAinpcsvr.RpcAiNpcChatNtfReq;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.common.AigcHttpResParam;
import com.tencent.wea.protocol.common.AinpcCommon.AiNpcActionState;
import com.tencent.wea.protocol.common.ToneChatResponseContent;
import com.tencent.wea.protocol.common.UgcAiGenAnswerResponseHttpContent;
import com.tencent.wea.proxy.ActionProxy.AiNpcActionType;
import com.tencent.wea.rpc.service.GameService;
import com.tencent.wea.tcaplus.TcaplusDbWrapper;
import com.tencent.wea.tlog.TlogFlowMgr;
import com.tencent.wea.util.AiNpcUtil;
import com.tencent.wea.xlsRes.ResAIGCNPC.AiNpcChatPushConf;
import com.tencent.wea.xlsRes.ResAIGCNPC.AiNpcChatPushTextConf;
import com.tencent.wea.xlsRes.ResAIGCNPC.AigcNpcConf;

/**
 * <AUTHOR>
 */
public class AiNpcManager extends LeaseManager<Long, AiNpcCache, TcaplusDbWrapper.AiNpcTable> {
    private static final Logger LOGGER = LogManager.getLogger(AiNpcManager.class);
    private static final AiNpcManager INSTANCE = new AiNpcManager();
    public static AiNpcManager getInstance() {
        return INSTANCE;
    }

    private long lastLogTime = 0;
    private final long TICK_LOG_INTERVAL_MS = 10000;

    /**
     * request缓存
     */
    private final ConcurrentHashMap<String, ChatRequestCache> requestCache = new ConcurrentHashMap<>();

    /**
     * tick队列
     * key: uid
     */
    private final ConcurrentLinkedQueue<Long> aiProcQueue = new ConcurrentLinkedQueue<>();

    /**
     * 准备队列
     * key: uid
     */
    private final ConcurrentLinkedQueue<Long> aiPrepareQueue = new ConcurrentLinkedQueue<>();

    /**
     * ai激活列表
     */
    private final ConcurrentHashSet<Long> aiSet = new ConcurrentHashSet<>();

    private final int PER_TICK_AI_PROC_MAX = 100;
    private final String SYSTEM_REQUEST = "system";

    @Override
    protected NKPair<TcaplusErrorCode, TcaplusDbWrapper.AiNpcTable> onLoadFromDb(Long key) {
        return AiNpcTableDao.getTcaplusAiNpc(key);
    }

    @Override
    protected void onRemoveFromCache(Long key, AiNpcCache cache) {
        stopAi(key);
    }

    @Override
    protected TcaplusErrorCode onInsertToDb(Long key, AiNpcCache lease) {
        lease.setUid(key);
        lease.getAiNpcAttr().setUid(key);
        return lease.insert();
    }

    @Override
    protected void loadFromRecord(AiNpcCache cache, TcaplusDbWrapper.AiNpcTable record) {
        cache.load(record);
    }

    @Override
    protected NKPair<Integer, Long> getLeaseInfo(TcaplusDbWrapper.AiNpcTable record) {
        return new NKPair<>(record.getInstanceId(), record.getInstanceLease());
    }

    @Override
    protected AiNpcCache genNewLeaseCache(Long uid, int svrId, long endMs) {
        return new AiNpcCache(uid, svrId, endMs);
    }

    @Override
    protected Builder executeRpc(Builder req, String method) throws NKCheckedException {
        return AiNpcActionType.valueOf(method).runTrigger(req);
    }

    public void startAi(long uid) {
        if (!PropertyFileReader.getRealTimeBooleanItem("ainpc_ai_enable", true)) {
            return;
        }

        if (!aiSet.contains(uid)) {
            aiSet.add(uid);
            aiProcQueue.add(uid);
            LOGGER.info("startAi: {}", uid);
        }
    }

    public void stopAi(long uid) {
        aiSet.remove(uid);
        // queue不用删, proc的时候会自己处理
        LOGGER.info("stopAi: {}", uid);
    }

    public void procAi() {
        if (aiSet.isEmpty()) {
            return;
        }

        if (!aiPrepareQueue.isEmpty()) {
            aiProcQueue.addAll(aiPrepareQueue);
            aiPrepareQueue.clear();
        }

        long now = Framework.currentTimeMillis();
        if (lastLogTime + TICK_LOG_INTERVAL_MS < now) {
            lastLogTime = now;
            LOGGER.debug("procAi aiProcQueue: {}, aiSet: {}", aiProcQueue.size(), aiSet.size());
        }

        int procNum = 0;
        Long uid;
        while (procNum < PER_TICK_AI_PROC_MAX && (uid = aiProcQueue.poll()) != null) {
            if (!aiSet.contains(uid)) {
                continue;
            }

            AiNpcCache cache = getIfPresent(uid);
            if (cache == null) {
                aiSet.remove(uid);
                LOGGER.info("procAi remove with null cache :", uid);
                continue;
            }

            aiPrepareQueue.add(uid);
            ++procNum;

            // npc的AI处理
            cache.procAi();
        }
    }

    /**
     * 此函数由http线程回调，不要在里面加任何阻塞线程的操作
     */
    public void receiveToneChatResponse(SsAigcsvr.ToneChatResponse.Builder res) {
        LOGGER.debug("receiveToneChatResponse: {}", res.toString());
        ChatRequestCache cache = requestCache.get(res.getRequestId());
        if (cache == null) {
            return;
        }

        cache.mergeResponse(res.build());

        // 检查是否符合发送条件
        String remain = cache.getRemain();
        int len = PropertyFileReader.getRealTimeIntItem("ainpc_remain_min", 10);
        if (AiNpcUtil.checkString(remain, len)) {
            sendToneChatNtf(cache, false);
        }
    }

    public void sendToneChatNtf(ChatRequestCache cache, boolean isFull) {
        LOGGER.debug("sendToneChatNtf {} with flag {}", cache.getRequestId(), isFull);
        SsAigcsvr.ToneChatResponse.Builder res = cache.getResponse();
        if (res == null) {
            return;
        }

        SsGamesvr.RpcAigcGenAnswerResNtfReq.Builder ntf = SsGamesvr.RpcAigcGenAnswerResNtfReq.newBuilder();
        AigcHttpResParam.Builder aigcHttpRes = AigcHttpResParam.newBuilder();
        UgcAiGenAnswerResponseHttpContent.Builder resContent = UgcAiGenAnswerResponseHttpContent.newBuilder();

        ntf.setUid(cache.getUid()).setRoleid(cache.getRoleId()).setQuery(cache.getQuery());
        if (res.getRetCode() == 0) {
            aigcHttpRes.setErrorCode(0);
        } else {
            aigcHttpRes.setErrorCode(res.getRetCode()).setErrorMsg("AiNpcChat chunk error");
        }
        resContent.setAnswer(cache.getContent());
        ntf.setRequestId(res.getRequestId())
                .setRoundId(res.getRoundId())
                .setSessionId(res.getSessionId())
                .setHttpRes(aigcHttpRes)
                .setContent(resContent)
                .setResponse(res.getResponse())
                .setFull(isFull);

        cache.clearRemain();

        if (isFull) {
            TlogFlowMgr.sendAINPCReplyFlow(cache.getUid(), cache.getOpenId(),
                    cache.getContent(), cache.getRequestId(), res.getRoundId(), ChatCanvas.Chat.getValue(), res.getResponse().getTopic(), res.getResponse().getTopicId());
        }

        // 异步发送
        NtfProcManager.getInstance().addSendQueue(ntf);
    }

    public void sendToneChatNtf(String requestId) {
        LOGGER.debug("sendToneChatNtf with full: {}", requestId);
        ChatRequestCache cache = requestCache.get(requestId);
        if (cache == null) {
            return;
        }
        sendToneChatNtf(cache, true);
    }

    /**
     * 发送ainpcsvr发起的请求回复
     * @param cache
     * @param data
     * @param res
     * @param text        推送文字
     * @param pushConfId  聊天推送配置ID
     */
    public void sendFullToneChatNtf(AiNpcCache cache, AiNpcData data,
            SsAigcsvr.ToneChatResponse.Builder res, String text, int pushConfId) {
        sendFullToneChatNtf(cache, data, res, text, pushConfId, 0);
    }

    /**
     *
     * @param cache
     * @param data
     * @param res
     * @param text        推送文字
     * @param pushConfId  聊天推送配置ID
     * @param textConfId  推送文字配置ID
     */
    public void sendFullToneChatNtf(AiNpcCache cache, AiNpcData data,
            SsAigcsvr.ToneChatResponse.Builder res, String text, int pushConfId, int textConfId) {
        LOGGER.debug("sendFullToneChatNtf : {}", res.getRequestId());
        SsGamesvr.RpcAigcGenAnswerResNtfReq.Builder ntf = SsGamesvr.RpcAigcGenAnswerResNtfReq.newBuilder();
        AigcHttpResParam.Builder aigcHttpRes = AigcHttpResParam.newBuilder();
        UgcAiGenAnswerResponseHttpContent.Builder resContent = UgcAiGenAnswerResponseHttpContent.newBuilder();

        ntf.setUid(cache.getUid()).setRoleid(data.getRoleId());
        if (res.getRetCode() == 0) {
            aigcHttpRes.setErrorCode(0);
        } else {
            aigcHttpRes.setErrorCode(res.getRetCode()).setErrorMsg("AiNpcChat chunk error");
        }

        resContent.setAnswer(text);
        ntf.setRequestId(res.getRequestId())
                .setRoundId(res.getRoundId())
                .setSessionId(res.getSessionId())
                .setHttpRes(aigcHttpRes)
                .setContent(resContent)
                .setResponse(res.getResponse())
                .setFull(true)
                .setSystemPush(true)
                .setPushConfId(pushConfId)
                .setTextConfId(textConfId);

        TlogFlowMgr.sendAINPCReplyFlow(cache.getUid(), cache.getAiNpcAttr().getOpenId(),
                text, res.getRequestId(), res.getRoundId(), ChatCanvas.Chat.getValue(), res.getResponse().getTopic(), res.getResponse().getTopicId());

        // 异步发送
        NtfProcManager.getInstance().addSendQueue(ntf);
    }

    public void addRequestCache(String requestId, long uid, String openId, String roleId, String query) {
        ChatRequestCache cache = new ChatRequestCache(requestId, uid, openId, roleId, query);
        requestCache.put(requestId, cache);
    }

    public void removeRequestCache(String requestId) {
        requestCache.remove(requestId);
    }

    public void resetActionState(AiNpcCache cache) {
        for (AiNpcData npcData : cache.getAiNpcAttr().getNpcData().values()) {
            if (npcData.getActionState() != AiNpcActionState.AiNpcAS_None_VALUE) {
                // npcData.setActionState(AiNpcActionState.AiNpcAS_None_VALUE);
                try {
                    CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                        SessionMgr.getInstance().aiNpcChatResetState(cache, npcData, ResetState.PUZZLE);
                        return null;
                    }, "resetActionState", true);
                } catch (Exception e) {
                    LOGGER.error("resetActionState runJob exception", e);
                }
            }
        }
    }

    /**
     * 向ailab发起主动聊天请求
     */
    public void requestDialogboxAction(long uid, int npcId, DialogboxAction action, int pushConfId) {
        AiNpcCache cache = getIfPresent(uid);
        if (cache == null) {
            return;
        }

        AigcNpcConf npcConf = AigcNpcData.getInstance().getConfByNpcId(npcId);
        if (npcConf == null) {
            return;
        }

        AiNpcData npcData = cache.getAiNpcAttr().getNpcData(npcConf.getId());
        if (npcData == null) {
            return;
        }

        requestDialogboxAction(cache, npcData, action, pushConfId);
    }

    public void requestDialogboxAction(long uid, String roleId, DialogboxAction action, int pushConfId) {
        AiNpcCache cache = getIfPresent(uid);
        if (cache == null) {
            return;
        }

        AiNpcData npcData = cache.getAiNpcAttr().getNpcData(roleId);
        if (npcData == null) {
            return;
        }

        requestDialogboxAction(cache, npcData, action, pushConfId);
    }

    public void requestDialogboxAction(AiNpcCache cache, AiNpcData npcData, DialogboxAction action, int pushConfId) {
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                SessionMgr.getInstance().aiNpcChatDialogboxAction(cache, npcData, action, pushConfId);
                return null;
            }, "requestDialogboxAction", true);
        } catch (Exception e) {
            LOGGER.error("requestDialogboxAction runJob exception", e);
        }
    }

    /**
     * 给玩家发送模拟的npc聊天
     */
    public void sendSystemChatResponse(long uid, int npcId, String text) {
        AiNpcCache cache = getIfPresent(uid);
        if (cache == null) {
            return;
        }

        AigcNpcConf npcConf = AigcNpcData.getInstance().getConfByNpcId(npcId);
        if (npcConf == null) {
            return;
        }

        AiNpcData npcData = cache.getAiNpcAttr().getNpcData(npcConf.getId());
        if (npcData == null) {
            return;
        }

        sendSystemChatResponse(cache, npcData, text);
    }

    public void sendSystemChatResponse(long uid, String roleId, String text) {
        AiNpcCache cache = getIfPresent(uid);
        if (cache == null) {
            return;
        }

        AiNpcData npcData = cache.getAiNpcAttr().getNpcData(roleId);
        if (npcData == null) {
            return;
        }

        sendSystemChatResponse(cache, npcData, text);
    }

    public void sendSystemChatResponse(AiNpcCache cache, AiNpcData npcData, String text) {
        SsAigcsvr.ToneChatResponse.Builder response = SsAigcsvr.ToneChatResponse.newBuilder();
        response.setRetCode(0)
                .setRequestId(SYSTEM_REQUEST);
        ToneChatResponseContent.Builder content = response.getResponseBuilder();
        content.setContent(text);

        sendFullToneChatNtf(cache, npcData, response, text, 0);
    }

    public void sendSystemChatPush(AiNpcCache cache, AiNpcData npcData, AiNpcChatPushTextConf textConf, AiNpcChatPushConf conf) {
        SsAigcsvr.ToneChatResponse.Builder response = SsAigcsvr.ToneChatResponse.newBuilder();
        response.setRetCode(0)
                .setRequestId(SYSTEM_REQUEST);
        ToneChatResponseContent.Builder content = response.getResponseBuilder();
        content.setContent(textConf.getText());

        sendFullToneChatNtf(cache, npcData, response, textConf.getText(), conf.getId(), textConf.getId());
    }

     // async ai chat
    public class AsyncAiChatReq extends CoroutineAsync<AigcHttpResParam.Builder, RuntimeException> {
        public AsyncAiChatReq(RpcAiNpcChatNtfReq req) {
            super(null);
            setRunnable(new Runnable() {
                @Override
                public void run() {
                    SsGamesvr.RpcAigcGenAnswerResNtfReq.Builder ntf = SsGamesvr.RpcAigcGenAnswerResNtfReq.newBuilder();
                    AigcHttpResParam.Builder aigcHttpRes = AigcHttpResParam.newBuilder();
                    UgcAiGenAnswerResponseHttpContent.Builder resContent = UgcAiGenAnswerResponseHttpContent.newBuilder();
                    long uid = req.getUid();
                    String roleId = req.getRoleId();
                    boolean needSend = true;

                    AiNpcCache cache = AiNpcManager.getInstance().get(req.getUid());
                    if (cache == null)
                    {
                        aigcHttpRes.setErrorCode(NKErrorCode.AinpcsvrCacheNotFound.getValue());
                        aigcHttpRes.setErrorMsg("cache not found");
                        return ;
                    }

                    AigcNpcConf npcConf = AigcNpcData.getInstance().get(roleId);
                    if (npcConf == null) {
                        aigcHttpRes.setErrorCode(NKErrorCode.ResNotFound.getValue());
                        aigcHttpRes.setErrorMsg("res not found");
                        return ;
                    }

                    // 返回原始参数供客户端区分
                    ntf.setUid(uid).setRoleid(roleId).setQuery(req.getQuery());
                    try {
                        // do post
                        SsAigcsvr.ToneChatResponse.Builder res = SessionMgr.getInstance().aiNpcChatHttpPostReq(
                                cache,
                                roleId,
                                req.getQuery(),
                                req.getAgentsParam(),
                                false,
                                false,
                                req.getIsQuickReply());
                        if (res == null) {
                            needSend = false;
                            return ;
                        }

                        // 处理返回包
                        if (res.getRetCode() == 0) {
                            aigcHttpRes.setErrorCode(0);
                        } else {
                            aigcHttpRes.setErrorCode(res.getRetCode()).setErrorMsg("AiNpcChat reponse error");
                        }
                        resContent.setAnswer(res.getResponse().getContent());
                        ntf.setRequestId(res.getRequestId())
                                .setRoundId(res.getRoundId())
                                .setSessionId(res.getSessionId())
                                .setResponse(res.getResponse())
                                .setFull(true);

                    } catch (Exception e) {
                        LOGGER.error("AiNpcChat exception, e:{}, uid:{}, worldId:{}", e, uid, BaseGenerator.getWorldId(uid));
                        aigcHttpRes.setErrorCode(-1);
                        aigcHttpRes.setErrorMsg("AiNpcChat exception");
                    } finally {
                        if (needSend) {
                            ntf.setHttpRes(aigcHttpRes);
                            ntf.setContent(resContent);

                            // 给gamesvr发送结果
                            try {
                                GameService.get().rpcAigcGenAnswerResNtf(ntf);
                            } catch (RpcException e) {
                                LOGGER.error("AiNpcChat exception, req:{}, e:{}, uid:{}, worldId:{}", req.toString(), e, uid, BaseGenerator.getWorldId(uid));
                            }
                        }
                    }
                }
            });
        }
    }
}
