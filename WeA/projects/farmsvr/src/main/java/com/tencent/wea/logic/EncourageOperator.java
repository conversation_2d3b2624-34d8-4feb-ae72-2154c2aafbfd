package com.tencent.wea.logic;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.Tuple;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.farmservice.managers.FarmlandManager;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.common.TLogFarmReason;
import com.tencent.wea.tlog.TlogFlowMgr;
import com.tencent.wea.xlsRes.keywords.FarmCropCategory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class EncourageOperator extends GridOperatorBase {
    private static final Logger LOGGER = LogManager.getLogger(EncourageOperator.class);
    final private int gridId;
    public EncourageOperator(Farm farm, int gridId) {
        super(farm);
        this.gridId = gridId;
    }

    public Tuple.T2<Integer, FarmlandManager.CropCtx> encourage() {
        var ret = checkCanEncourage(farm, gridId);
        ret.throwErrorIfNotOk("");
        var ctx = farm.getFarmlandManager().getCropCtx(gridId).value;
        var currentTimeSec = Framework.currentTimeSec();
        var currentGrowValue = FarmlandManager.calcCurrentGrowValue(ctx);
        var currentCareValue = FarmlandManager.calcCurrentCareValue(ctx);
        // 开始助产
        int addGrowValue = FarmlandManager.calcMaxGrowValue(ctx) - currentGrowValue;
        ctx.crop.setLastUpdateTime(currentTimeSec);
        ctx.crop.setCareValue(currentCareValue);
        ctx.crop.setGrowValue(currentGrowValue+addGrowValue);
        // 助产完成
        LOGGER.debug("farm crop, encourage finish");
        // 打各种流水
        TlogFlowMgr.sendFarmCropOpFlow(tlogRequiredFields, farm.getUid(), ctx.grid.getId(), TLogFarmReason.TFO_ENCOURAGE, ctx.crop, null, null, opSource, farm.getBuildingMgr().getMainHouseLevel(),
                0, FarmlandManager.calcCurrentCareValue(ctx), addGrowValue, 0, FarmlandManager.calcCropRipeTime(ctx));
        TlogFlowMgr.sendFarmCropServerFlow(tlogRequiredFields, farm.getUid(), farm.getUid(), TLogFarmReason.TFO_ENCOURAGE, null, null, ctx.grid, ctx.crop, 0.0f, "");
        Monitor.getInstance().add.total(MonitorId.attr_farmsvr_crop_encourage, 1);
        return Tuple.New(addGrowValue, ctx);
    }

    // 检查
    public static NKErrorCode checkCanEncourage(Farm farm, int gridId) {
        // 检查：养殖物存在
        var ret = farm.getFarmlandManager().getCropCtx(gridId);
        if (!ret.key.isOk()) {
            return ret.key;
        }
        var ctx = ret.value;
        // 检查：是动物地格
        if (ctx.conf.getCropCategory() != FarmCropCategory.FCC_ANIMAL) {
            return NKErrorCode.FarmCropEncourageFail;
        }
        var currentGrowValue = FarmlandManager.calcCurrentGrowValue(ctx);
        // 检查：已经到达可助产阈值
        if (currentGrowValue < FarmlandManager.calcAnimalEncourageNeedGrowValue(ctx, farm.getBuffMgr())) {
            return NKErrorCode.FarmCropEncourageFail;
        }
        // 检查：还未成熟
        if (currentGrowValue >= FarmlandManager.calcMaxGrowValue(ctx)) {
            return NKErrorCode.FarmCropEncourageFail;
        }
        return NKErrorCode.OK;
    }
}
