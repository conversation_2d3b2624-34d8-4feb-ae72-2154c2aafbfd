package com.tencent.wea.csforward.handler;

import com.google.protobuf.Message;
import com.tencent.wea.csforward.FarmAbstractForwardClientRequestHandler;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


public class FarmBuildingFirstFlagMsgHandler extends FarmAbstractForwardClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(FarmBuildingFirstFlagMsgHandler.class);

    @Override
    public Message.Builder specHandle(Farm farm, Message request, long playerUid, TlogRequiredFields tlogRequiredFields) {
        CsFarm.FarmBuildingFirstFlag_C2S_Msg reqMsg = (CsFarm.FarmBuildingFirstFlag_C2S_Msg) request;
        CsFarm.FarmBuildingFirstFlag_S2C_Msg.Builder rspMsg = CsFarm.FarmBuildingFirstFlag_S2C_Msg.newBuilder();
        farm.getBuildingMgr().FirstFlag(reqMsg.getConfId()).throwErrorIfNotOk("");
        return rspMsg;
    }
}