package com.tencent.wea.cshandler;

import com.tencent.nk.util.TypeUtil;
import com.tencent.wea.protocol.MsgTypes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

public class StreamSvrMsgHandlerFactory{
    private static final Logger LOGGER = LogManager.getLogger(StreamSvrMsgHandlerFactory.class);
    private static final HashMap<Integer, AbstractStreamClientRequestHandler> msgHandlerInstances = new HashMap<>();
    static {
        for (Map.Entry<Integer, Class<? extends AbstractStreamClientRequestHandler>>
                entry : StreamsvrPbMsgHandlerFactory.MSG_HANDLERS.entrySet()) {
            if (TypeUtil.hasNonStaticFields(entry.getValue())) {
                LOGGER.info("msg: {} handler {} has non static fields", MsgTypes.getMsgName(entry.getKey()), entry.getValue().getSimpleName());
                continue;
            }
            try {
                msgHandlerInstances.put(entry.getKey(), entry.getValue().getDeclaredConstructor().newInstance());
            } catch (IllegalAccessException | InstantiationException |
                     InvocationTargetException | NoSuchMethodException e) {
                LOGGER.error("{}.newInstance() failed {}", entry.getValue().getSimpleName(), e);
            }
        }
    }

    public StreamSvrMsgHandlerFactory() {
    }

    public static Class<? extends AbstractStreamClientRequestHandler> getMsgHandlerClass(int msgType) {
        return StreamsvrPbMsgHandlerFactory.getMsgHandler(msgType);
    }

    public static AbstractStreamClientRequestHandler getMsgHandlerInstance(int msgType) {
        AbstractStreamClientRequestHandler handler = msgHandlerInstances.get(msgType);
        if (handler != null) {
            return handler;
        }
        Class<? extends AbstractStreamClientRequestHandler> clazz = getMsgHandlerClass(msgType);
        if (clazz != null) {
            try {
                return clazz.getDeclaredConstructor().newInstance();
            } catch (IllegalAccessException | InstantiationException |
                     InvocationTargetException | NoSuchMethodException e) {
                LOGGER.error("{}.newInstance() failed {}", clazz.getSimpleName(), e);
            }
        }
        return null;
    }
}
