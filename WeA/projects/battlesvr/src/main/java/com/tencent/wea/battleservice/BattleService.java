package com.tencent.wea.battleservice;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.timer.TimerType;
import com.tencent.nk.timer.TimerUtil;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.rpc.RpcRoutingUtil;
import com.tencent.servicesmgr.nodemgr.ClientNodeMgr;
import com.tencent.timeoutproc.TimeOutProc;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiCoroutine.LocalService;
import com.tencent.timiCoroutine.LocalServiceSequentialWrapper;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.battleservice.battledata.BattleMgr;
import com.tencent.wea.battleservice.battledata.DSMigrateMgr;
import com.tencent.wea.battleservice.battlestatistics.BattleDSCLoadMgr;
import com.tencent.wea.battleservice.battlestatistics.BattleStatisticsMgr;
import com.tencent.wea.battleservice.heartbeat.HeartbeatMgr;
import com.tencent.wea.battleservice.wujiconfig.WujiConfigMgr;
import com.tencent.wea.interaction.BattleSvrInteractionHandler;
import com.tencent.wea.protocol.common.LocalServiceType;
import com.tencent.wea.namedenum.servertype.WeAServerType;
import com.tencent.wea.xlsRes.ResMatch.MatchType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 房间服务
 *
 * <AUTHOR>
 * @date 2021/08/17
 */
public class BattleService extends LocalService {

    private static final Logger LOGGER = LogManager.getLogger(BattleService.class);
    private static long battleUploadPayloadPeroid;
    private static boolean isApplicationRandomLevel;
    private final TimeOutProc timeOutProc = new TimeOutProc();
    private long lastOnlineCntTick = Framework.currentTimeMillis();
    private final long lastUploadPayloadTick = Framework.currentTimeMillis();
    private Set<Integer> lastMatchTypeSet = null;
    private Set<Integer> lastLevelTypeSet = null;
    private Set<Integer> lastLevelIdSet = null;

    public BattleService() {
        super(LocalServiceType.LOCAL_BATTLESVR_ROOM_SERVICE);

        setExecutorServiceCount(1);
        generateExecutorGroupWithNewContainer("BattleService", 10000, 2000, getLocalServiceType());
    }

    public static long getBattleUploadPayloadPeroid() {
        return battleUploadPayloadPeroid;
    }

    private static void loadCfg() {
        battleUploadPayloadPeroid = PropertyFileReader.getRealTimeLongItem("battleUploadPayloadPeroid", 3) * 1000;
        isApplicationRandomLevel = PropertyFileReader.getRealTimeBooleanItem("isApplicationRandomLevel", false);
    }

    public boolean getIsApplicationRandomLevel() {
        return isApplicationRandomLevel;
    }

    private void initListenServicesNode() {
        ClientNodeMgr clientNodeMgr = new ClientNodeMgr();
        ArrayList<Integer> observerSvrs = new ArrayList<>();
        observerSvrs.add(WeAServerType.ST_BattleServer_VALUE);
        clientNodeMgr.addListenNodes(observerSvrs);
        RpcRoutingUtil.setClientNodeMgr(clientNodeMgr);
    }

    @Override
    protected void executorLocalInit(int executorServiceIndex) {
        super.executorLocalInit(executorServiceIndex);
        if (BattleMgr.getInstance().onInit() != 0) {
            LOGGER.error("BattleMgr init error");
            return;
        }

        BattleDSCLoadMgr.getInstance().init();

        loadCfg();

        initListenServicesNode();

        CurrentExecutorUtil.addRepeatTimer(TimerType.ExecutorLocalProcTimer, 5,
                TimerUtil.msToTick(1000), false, this::executorLocalProc);

        CurrentExecutorUtil.addRepeatTimer(TimerType.ServerInteractionTimer, 5,
                TimerUtil.msToTick(1000), true,
                BattleSvrInteractionHandler.getInstance()::handleAll);

        LocalServiceSequentialWrapper.setCanRemoveCallBack(this, executorServiceIndex, BattleService::checkCanRemove);
        CurrentExecutorUtil.addRepeatTimer(TimerType.LocalServiceSequentialCheckTimer, 5,
                TimerUtil.msToTick(5000), false, ()->serviceSequentialCheckTimer(executorServiceIndex));

        CurrentExecutorUtil.addRepeatTimer(TimerType.GetDscLoadTimer, 5,
                TimerUtil.msToTick(1000), false, BattleDSCLoadMgr.getInstance()::updateDscPayload);

        CurrentExecutorUtil.addRepeatTimer(TimerType.SaveDscLoadTimer, 5,
                TimerUtil.msToTick(500), false, BattleStatisticsMgr.getInstance()::updateStatics);

        DSMigrateMgr.getInstance().init();

        LOGGER.info("BattleService started ServerInteractionTimer");
    }

    public static boolean checkCanRemove(long id) {
        return (null == BattleMgr.getInstance().getBattleInfoFromCache(id));
    }

    public void serviceSequentialCheckTimer(int idx) {
        LocalServiceSequentialWrapper.serviceSequentialCheckTimer(this, idx);
    }

    @Override
    protected int executorLocalReload(int executorServiceIndex) {
        if (BattleMgr.getInstance().onReload() != 0) {
            LOGGER.error("BattleMgr reload error");
        }

        loadCfg();

        DSMigrateMgr.getInstance().reload();

        return 0;
    }
    private void addMatchTypeMonitor(Map<Integer, Integer> matchTypeMap, MonitorId monitorId) {
        for (Map.Entry<Integer, Integer> entry : matchTypeMap.entrySet()) {
            int matchTypeId = entry.getKey();
            String playName = "unknown";
            MatchType matchTypeCfg = MatchTypeData.getInstance().get(matchTypeId);
            if(matchTypeCfg  != null) {
                playName = matchTypeCfg.getPlayName();
            }
            String[] monitorParams = new String[]{
                String.valueOf(matchTypeId),
                playName
            };
            Monitor.getInstance().set.total(monitorId, entry.getValue(), monitorParams);
        }
    }
    private void addLevelTypeMonitor(Map<Integer, Integer> levelTypeMap, MonitorId monitorId) {
        for (Map.Entry<Integer, Integer> entry : levelTypeMap.entrySet()) {
            int levelType = entry.getKey();
            String[] monitorParams = new String[]{
                String.valueOf(levelType)
            };
            Monitor.getInstance().set.total(monitorId, entry.getValue(), monitorParams);
        }
    }
    private void addLevelIdMonitor(Map<Integer, Integer> levelIdMap, MonitorId monitorId) {
      for (Map.Entry<Integer, Integer> entry : levelIdMap.entrySet()) {
          int levelId = entry.getKey();
          String[] monitorParams = new String[]{
              String.valueOf(levelId)
          };
          Monitor.getInstance().set.total(monitorId, entry.getValue(), monitorParams);
      }
  }
    protected int executorLocalProc() {
        //RpcRoutingUtil.onProc();
        timeOutProc.Loop();

        long now = Framework.currentTimeMillis();
        if (now - lastOnlineCntTick > 30 * 1000L) {
            lastOnlineCntTick = now;
            Set<Integer> currentMatchTypeSet = new HashSet<>();
            Map<Integer, Integer> matchTypeSizeMap = new HashMap<>();
            Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_size,
                    BattleMgr.getInstance().getBattleSize(matchTypeSizeMap, currentMatchTypeSet));
             
            addMatchTypeMonitor(matchTypeSizeMap, MonitorId.attr_battlesvr_battle_matchtype_size);
           
            Map<Integer, Integer> matchTypePlayerSizeMap = new HashMap<>();
            Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_membersize,
                    BattleMgr.getInstance().getBattleMemeberSize(matchTypePlayerSizeMap));
            addMatchTypeMonitor(matchTypePlayerSizeMap, MonitorId.attr_battlesvr_battle_matchtype_membersize);

            Map<Integer, Integer> matchTypeAlivePlayerSizeMap = new HashMap<>();
            Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_alive_player_size,
                    BattleMgr.getInstance().getBattleAlivePlayerSize(matchTypeAlivePlayerSizeMap));
            addMatchTypeMonitor(matchTypeAlivePlayerSizeMap, MonitorId.attr_battlesvr_battle_matchtype_alive_player_size);


            Map<Integer, Integer> matchTypeRobotSizeMap = new HashMap<>();
            Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_robotsize,
                    BattleMgr.getInstance().getBattleRobotSize(matchTypeRobotSizeMap));
            addMatchTypeMonitor(matchTypeRobotSizeMap, MonitorId.attr_battlesvr_battle_matchtype_robotsize);
            
            Map<Integer, Integer> matchTypeCurrentRobotNumMap = new HashMap<>();
            int totalRobotNum = BattleMgr.getInstance().getMatchTypeRobotNum(matchTypeCurrentRobotNumMap);
            Monitor.getInstance().set.total(MonitorId.attr_battlesvr_current_robot_num, totalRobotNum);
            LOGGER.debug("reportRobotNum totalRobotNum:{}", totalRobotNum);
            addMatchTypeMonitor(matchTypeCurrentRobotNumMap, MonitorId.attr_battlesvr_matchtype_current_robot_num);

            Map<Integer, Integer> battleSceneMatchTypeSizeMap = new HashMap<>();
            Map<Integer, Integer> battleSceneMatchTypeSubSceneSizeMap = new HashMap<>();
            Monitor.getInstance().set.total(MonitorId.attr_battle_multi_scene_cnt,
                    BattleMgr.getInstance().getBattleSceneSize(battleSceneMatchTypeSizeMap, battleSceneMatchTypeSubSceneSizeMap));
            addMatchTypeMonitor(battleSceneMatchTypeSizeMap, MonitorId.attr_battle_multi_scene_matchtype_cnt);
            addMatchTypeMonitor(battleSceneMatchTypeSubSceneSizeMap, MonitorId.attr_battle_multi_scene_matchtype_sub_scene_cnt);

            if (lastMatchTypeSet != null) {
                lastMatchTypeSet.removeAll(currentMatchTypeSet);
                for (Integer disappearMatchType : lastMatchTypeSet) {
                    LOGGER.trace("report disappear match type:{}", disappearMatchType);
                    String[] monitorParams = new String[]{
                            String.valueOf(disappearMatchType)
                    };
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_matchtype_size, 0, monitorParams);
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_matchtype_membersize, 0, monitorParams);
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_matchtype_alive_player_size, 0, monitorParams);
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_battle_matchtype_robotsize, 0, monitorParams);
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_matchtype_current_robot_num, 0, monitorParams);
                }
            }
            lastMatchTypeSet = currentMatchTypeSet;

            Map<Integer, Integer> levelTypeCurrentRobotNumMap = new HashMap<>();
            Set<Integer> currentLevelTypeSet = new HashSet<>();
            BattleMgr.getInstance().getLevelTypeRobotNum(levelTypeCurrentRobotNumMap, currentLevelTypeSet);
            addLevelTypeMonitor(levelTypeCurrentRobotNumMap, MonitorId.attr_battlesvr_leveltype_current_robot_num);

            if (lastLevelTypeSet != null) {
                lastLevelTypeSet.removeAll(currentLevelTypeSet);
                for (Integer disappearLevelType : lastLevelTypeSet) {
                    String[] monitorParams = new String[]{
                            String.valueOf(disappearLevelType)
                    };
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_leveltype_current_robot_num, 0, monitorParams);
                }
            }
            lastLevelTypeSet = currentLevelTypeSet;


            Map<Integer, Integer> levelIdCurrentRobotNumMap = new HashMap<>();
            Set<Integer> currentLevelIdSet = new HashSet<>();
            BattleMgr.getInstance().getLevelIdRobotNum(levelIdCurrentRobotNumMap, currentLevelIdSet);
            addLevelIdMonitor(levelIdCurrentRobotNumMap, MonitorId.attr_battlesvr_levelid_current_robot_num);

            if (lastLevelIdSet != null) {
                lastLevelIdSet.removeAll(currentLevelIdSet);
                for (Integer disappearLevelId : lastLevelIdSet) {
                    String[] monitorParams = new String[]{
                            String.valueOf(disappearLevelId)
                    };
                    Monitor.getInstance().set.total(MonitorId.attr_battlesvr_levelid_current_robot_num, 0, monitorParams);
                }
            }
            lastLevelIdSet = currentLevelIdSet;

        }
        HeartbeatMgr.getInstance().onProc();
        WujiConfigMgr.getInstance().onProc();
        return 0;
    }
}