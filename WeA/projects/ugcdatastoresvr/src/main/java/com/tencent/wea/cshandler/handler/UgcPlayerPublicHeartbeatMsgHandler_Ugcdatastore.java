package com.tencent.wea.cshandler.handler;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.cshandler.AbstractUgcdatastoreClientRequestHandler;
import com.tencent.wea.mgr.UgcPlayerPublicMgr;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.common.CsCommonForwardHeader;
import com.tencent.wea.protocol.CsUgcdatastore;
import com.tencent.timiutil.monitor.MonitorId;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class UgcPlayerPublicHeartbeatMsgHandler_Ugcdatastore extends AbstractUgcdatastoreClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(UgcPlayerPublicHeartbeatMsgHandler_Ugcdatastore.class);

    @Override
    public Message.Builder handle(CsHead.CSHeader header, CsCommonForwardHeader forwardHeader, Message request)  {
        CsUgcdatastore.UgcPlayerPublicHeartbeat_C2S_Msg reqMsg = (CsUgcdatastore.UgcPlayerPublicHeartbeat_C2S_Msg)request;
        CsUgcdatastore.UgcPlayerPublicHeartbeat_S2C_Msg.Builder rspMsg = CsUgcdatastore.UgcPlayerPublicHeartbeat_S2C_Msg.newBuilder();
        if(forwardHeader.getUid() != reqMsg.getUid()) {
            LOGGER.error("invalid param, uid:{} reqUid:{}", forwardHeader.getUid(), reqMsg.getUid());
            rspMsg.setResult(NKErrorCode.InvalidParams.getValue());
        }
        else {
            NKErrorCode errorCode = UgcPlayerPublicMgr.getInstance().rpcUgcPlayerPublicHeartbeat(reqMsg.getUgcId(), reqMsg.getUid(), reqMsg.getUid());
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_heartbeat, 1, errorCode.getValue());
            rspMsg.setResult(errorCode.getValue());
        }
        return rspMsg;
    }
}