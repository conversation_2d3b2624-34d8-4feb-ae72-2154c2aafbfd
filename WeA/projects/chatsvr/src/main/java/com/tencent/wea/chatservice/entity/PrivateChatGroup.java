package com.tencent.wea.chatservice.entity;

import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.chatservice.cache.CachedChatSession;
import com.tencent.wea.chatservice.store.ChatGroup;
import com.tencent.wea.chatservice.store.ChatSession;
import com.tencent.wea.protocol.common.ChatMsgData;
import com.tencent.wea.protocol.common.GeneralChatMsg;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.tcaplus.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @program: WeA
 * @description: 私聊
 * @author: nichtsun
 * @create: 2022-07-19
 **/

public class PrivateChatGroup extends BaseChatGroup {

    private static final Logger LOGGER = LogManager.getLogger(PrivateChatGroup.class);

    public PrivateChatGroup(ChatGroup chatGroup) {
        super(chatGroup);
    }

    @Override
    public GeneralChatMsg sendMsg(MemberBaseInfo senderInfo, ChatMsgData content, boolean isSystem, long sendTime) {
        long senderUid = senderInfo.getUid();
        GeneralChatMsg msgSent = super.sendMsg(senderInfo, content, isSystem, sendTime);
        if (msgSent != null) {
            // 设置发送方已读
            updateSenderSession(senderInfo.getUid(), msgSent.getSeqId());
        }

        return msgSent;
    }

    @Override
    protected boolean needSenderSnapshot() {
        return false;
    }

    private void updateSenderSession(long senderUid, long seqId) {
        ChatSession chatSession = new ChatSession(TcaplusDb.ChatSession.newBuilder()
                .setGroupType(getChatGroup().getKey().type)
                .setGroupId(getChatGroup().getKey().id)
                .setGroupSubId(getChatGroup().getKey().subId)
                .setUid(senderUid)
                .setLastReadMsgSeqId(seqId).build());

        boolean insertSucc = CachedChatSession.getInstance().update(chatSession.getKey(), chatSession);
        if (!insertSucc) {
            Monitor.getInstance().add.fail(MonitorId.attr_chatsvr_chat_session_insert, 1);
            LOGGER.error("replace player-{} session in cache to {} fail : {}, {}, {}", seqId, senderUid,
                    getChatGroup().getKey().type, getChatGroup().getKey().id, getChatGroup().getKey().subId);
        }
    }
}
