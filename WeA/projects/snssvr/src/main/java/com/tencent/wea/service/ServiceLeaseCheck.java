package com.tencent.wea.service;

import com.tencent.timiCoroutine.LocalService;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.wea.manager.PlayerSnsManager;
import com.tencent.wea.protocol.common.LocalServiceType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * <AUTHOR>
 */
public class ServiceLeaseCheck extends LocalService {

    private static final Logger LOGGER = LogManager.getLogger(ServiceLeaseCheck.class);

    public ServiceLeaseCheck() {
        super(LocalServiceType.LOCAL_SNS_LEASE_CHECK_SERVICE);

        setExecutorServiceCount(1);
        generateExecutorGroupWithNewContainer("LeaseCheckService", 1000, 1000, getLocalServiceType());
    }

    @Override
    protected void executorLocalInit(int executorServiceIndex) {
        super.executorLocalInit(executorServiceIndex);
    }

    @Override
    protected int executorLocalReload(int executorServiceIndex) {
        return 0;
    }

    @Override
    protected int executorLocalProc(int executorServiceIndex, TxStopWatch stopWatchHandle) {
        PlayerSnsManager.getInstance().leaseCheckProc();
        return 0;
    }
}
