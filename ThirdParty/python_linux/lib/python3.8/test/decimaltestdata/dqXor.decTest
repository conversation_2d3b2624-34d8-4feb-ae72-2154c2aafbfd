------------------------------------------------------------------------
-- dqXor.decTest -- digitwise logical XOR for decQuads                --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check (truth table)
dqxor001 xor             0    0 ->    0
dqxor002 xor             0    1 ->    1
dqxor003 xor             1    0 ->    1
dqxor004 xor             1    1 ->    0
dqxor005 xor          1100 1010 ->  110
-- and at msd and msd-1
dqxor006 xor 0000000000000000000000000000000000 0000000000000000000000000000000000 ->           0
dqxor007 xor 0000000000000000000000000000000000 1000000000000000000000000000000000 ->   1000000000000000000000000000000000
dqxor008 xor 1000000000000000000000000000000000 0000000000000000000000000000000000 ->   1000000000000000000000000000000000
dqxor009 xor 1000000000000000000000000000000000 1000000000000000000000000000000000 ->           0
dqxor010 xor 0000000000000000000000000000000000 0000000000000000000000000000000000 ->           0
dqxor011 xor 0000000000000000000000000000000000 0100000000000000000000000000000000 ->    100000000000000000000000000000000
dqxor012 xor 0100000000000000000000000000000000 0000000000000000000000000000000000 ->    100000000000000000000000000000000
dqxor013 xor 0100000000000000000000000000000000 0100000000000000000000000000000000 ->           0

-- Various lengths
--           1234567890123456789012345678901234
dqxor601 xor 0111111111111111111111111111111111 1111111111111111111111111111111111  -> 1000000000000000000000000000000000
dqxor602 xor 1011111111111111111111111111111111 1111111111111111111111111111111111  ->  100000000000000000000000000000000
dqxor603 xor 1101111111111111111111111111111111 1111111111111111111111111111111111  ->   10000000000000000000000000000000
dqxor604 xor 1110111111111111111111111111111111 1111111111111111111111111111111111  ->    1000000000000000000000000000000
dqxor605 xor 1111011111111111111111111111111111 1111111111111111111111111111111111  ->     100000000000000000000000000000
dqxor606 xor 1111101111111111111111111111111111 1111111111111111111111111111111111  ->      10000000000000000000000000000
dqxor607 xor 1111110111111111111111111111111111 1111111111111111111111111111111111  ->       1000000000000000000000000000
dqxor608 xor 1111111011111111111111111111111111 1111111111111111111111111111111111  ->        100000000000000000000000000
dqxor609 xor 1111111101111111111111111111111111 1111111111111111111111111111111111  ->         10000000000000000000000000
dqxor610 xor 1111111110111111111111111111111111 1111111111111111111111111111111111  ->          1000000000000000000000000
dqxor611 xor 1111111111011111111111111111111111 1111111111111111111111111111111111  ->           100000000000000000000000
dqxor612 xor 1111111111101111111111111111111111 1111111111111111111111111111111111  ->            10000000000000000000000
dqxor613 xor 1111111111110111111111111111111111 1111111111111111111111111111111111  ->             1000000000000000000000
dqxor614 xor 1111111111111011111111111111111111 1111111111111111111111111111111111  ->              100000000000000000000
dqxor615 xor 1111111111111101111111111111111111 1111111111111111111111111111111111  ->               10000000000000000000
dqxor616 xor 1111111111111110111111111111111111 1111111111111111111111111111111111  ->                1000000000000000000
dqxor617 xor 1111111111111111011111111111111111 1111111111111111111111111111111111  ->                 100000000000000000
dqxor618 xor 1111111111111111101111111111111111 1111111111111111111111111111111111  ->                  10000000000000000
dqxor619 xor 1111111111111111110111111111111111 1111111111111111111111111111111111  ->                   1000000000000000
dqxor620 xor 1111111111111111111011111111111111 1111111111111111111111111111111111  ->                    100000000000000
dqxor621 xor 1111111111111111111101111111111111 1111111111111111111111111111111111  ->                     10000000000000
dqxor622 xor 1111111111111111111110111111111111 1111111111111111111111111111111111  ->                      1000000000000
dqxor623 xor 1111111111111111111111011111111111 1111111111111111111111111111111111  ->                       100000000000
dqxor624 xor 1111111111111111111111101111111111 1111111111111111111111111111111111  ->                        10000000000
dqxor625 xor 1111111111111111111111110111111111 1111111111111111111111111111111111  ->                         1000000000
dqxor626 xor 1111111111111111111111111011111111 1111111111111111111111111111111111  ->                          100000000
dqxor627 xor 1111111111111111111111111101111111 1111111111111111111111111111111111  ->                           10000000
dqxor628 xor 1111111111111111111111111110111111 1111111111111111111111111111111111  ->                            1000000
dqxor629 xor 1111111111111111111111111111011111 1111111111111111111111111111111111  ->                             100000
dqxor630 xor 1111111111111111111111111111101111 1111111111111111111111111111111111  ->                              10000
dqxor631 xor 1111111111111111111111111111110111 1111111111111111111111111111111111  ->                               1000
dqxor632 xor 1111111111111111111111111111111011 1111111111111111111111111111111111  ->                                100
dqxor633 xor 1111111111111111111111111111111101 1111111111111111111111111111111111  ->                                 10
dqxor634 xor 1111111111111111111111111111111110 1111111111111111111111111111111111  ->                                  1

dqxor641 xor 1111111111111111111111111111111111 0111111111111111111111111111111111  -> 1000000000000000000000000000000000
dqxor642 xor 1111111111111111111111111111111111 1011111111111111111111111111111111  ->  100000000000000000000000000000000
dqxor643 xor 1111111111111111111111111111111111 1101111111111111111111111111111111  ->   10000000000000000000000000000000
dqxor644 xor 1111111111111111111111111111111111 1110111111111111111111111111111111  ->    1000000000000000000000000000000
dqxor645 xor 1111111111111111111111111111111111 1111011111111111111111111111111111  ->     100000000000000000000000000000
dqxor646 xor 1111111111111111111111111111111111 1111101111111111111111111111111111  ->      10000000000000000000000000000
dqxor647 xor 1111111111111111111111111111111111 1111110111111111111111111111111111  ->       1000000000000000000000000000
dqxor648 xor 1111111111111111111111111111111111 1111111011111111111111111111111111  ->        100000000000000000000000000
dqxor649 xor 1111111111111111111111111111111111 1111111101111111111111111111111111  ->         10000000000000000000000000
dqxor650 xor 1111111111111111111111111111111111 1111111110111111111111111111111111  ->          1000000000000000000000000
dqxor651 xor 1111111111111111111111111111111111 1111111111011111111111111111111111  ->           100000000000000000000000
dqxor652 xor 1111111111111111111111111111111111 1111111111101111111111111111111111  ->            10000000000000000000000
dqxor653 xor 1111111111111111111111111111111111 1111111111110111111111111111111111  ->             1000000000000000000000
dqxor654 xor 1111111111111111111111111111111111 1111111111111011111111111111111111  ->              100000000000000000000
dqxor655 xor 1111111111111111111111111111111111 1111111111111101111111111111111111  ->               10000000000000000000
dqxor656 xor 1111111111111111111111111111111111 1111111111111110111111111111111111  ->                1000000000000000000
dqxor657 xor 1111111111111111111111111111111111 1111111111111111011111111111111111  ->                 100000000000000000
dqxor658 xor 1111111111111111111111111111111111 1111111111111111101111111111111111  ->                  10000000000000000
dqxor659 xor 1111111111111111111111111111111111 1111111111111111110111111111111111  ->                   1000000000000000
dqxor660 xor 1111111111111111111111111111111111 1111111111111111111011111111111111  ->                    100000000000000
dqxor661 xor 1111111111111111111111111111111111 1111111111111111111101111111111111  ->                     10000000000000
dqxor662 xor 1111111111111111111111111111111111 1111111111111111111110111111111111  ->                      1000000000000
dqxor663 xor 1111111111111111111111111111111111 1111111111111111111111011111111111  ->                       100000000000
dqxor664 xor 1111111111111111111111111111111111 1111111111111111111111101111111111  ->                        10000000000
dqxor665 xor 1111111111111111111111111111111111 1111111111111111111111110111111111  ->                         1000000000
dqxor666 xor 1111111111111111111111111111111111 1111111111111111111111111011111111  ->                          100000000
dqxor667 xor 1111111111111111111111111111111111 1111111111111111111111111101111111  ->                           10000000
dqxor668 xor 1111111111111111111111111111111111 1111111111111111111111111110111111  ->                            1000000
dqxor669 xor 1111111111111111111111111111111111 1111111111111111111111111111011111  ->                             100000
dqxor670 xor 1111111111111111111111111111111111 1111111111111111111111111111101111  ->                              10000
dqxor671 xor 1111111111111111111111111111111111 1111111111111111111111111111110111  ->                               1000
dqxor672 xor 1111111111111111111111111111111111 1111111111111111111111111111111011  ->                                100
dqxor673 xor 1111111111111111111111111111111111 1111111111111111111111111111111101  ->                                 10
dqxor674 xor 1111111111111111111111111111111111 1111111111111111111111111111111110  ->                                  1
dqxor675 xor 0111111111111111111111111111111111 1111111111111111111111111111111110  -> 1000000000000000000000000000000001
dqxor676 xor 1111111111111111111111111111111111 1111111111111111111111111111111110  ->                                  1


dqxor021 xor 1111111110000000     1111111110000000  ->  0
dqxor022 xor  111111110000000      111111110000000  ->  0
dqxor023 xor   11111110000000       11111110000000  ->  0
dqxor024 xor    1111110000000        1111110000000  ->  0
dqxor025 xor     111110000000         111110000000  ->  0
dqxor026 xor      11110000000          11110000000  ->  0
dqxor027 xor       1110000000           1110000000  ->  0
dqxor028 xor        110000000            110000000  ->  0
dqxor029 xor         10000000             10000000  ->  0
dqxor030 xor          1000000              1000000  ->  0
dqxor031 xor           100000               100000  ->  0
dqxor032 xor            10000                10000  ->  0
dqxor033 xor             1000                 1000  ->  0
dqxor034 xor              100                  100  ->  0
dqxor035 xor               10                   10  ->  0
dqxor036 xor                1                    1  ->  0

dqxor040 xor 111111111  111111111111  ->  111000000000
dqxor041 xor  11111111  111111111111  ->  111100000000
dqxor042 xor  11111111     111111111  ->  100000000
dqxor043 xor   1111111     100000010  ->  101111101
dqxor044 xor    111111     100000100  ->  100111011
dqxor045 xor     11111     100001000  ->  100010111
dqxor046 xor      1111     100010000  ->  100011111
dqxor047 xor       111     100100000  ->  100100111
dqxor048 xor        11     101000000  ->  101000011
dqxor049 xor         1     110000000  ->  110000001

dqxor050 xor 1111111111  1  ->  1111111110
dqxor051 xor  111111111  1  ->  111111110
dqxor052 xor   11111111  1  ->  11111110
dqxor053 xor    1111111  1  ->  1111110
dqxor054 xor     111111  1  ->  111110
dqxor055 xor      11111  1  ->  11110
dqxor056 xor       1111  1  ->  1110
dqxor057 xor        111  1  ->  110
dqxor058 xor         11  1  ->  10
dqxor059 xor          1  1  ->  0

dqxor060 xor 1111111111  0  ->  1111111111
dqxor061 xor  111111111  0  ->  111111111
dqxor062 xor   11111111  0  ->  11111111
dqxor063 xor    1111111  0  ->  1111111
dqxor064 xor     111111  0  ->  111111
dqxor065 xor      11111  0  ->  11111
dqxor066 xor       1111  0  ->  1111
dqxor067 xor        111  0  ->  111
dqxor068 xor         11  0  ->  11
dqxor069 xor          1  0  ->  1

dqxor070 xor 1  1111111111  ->  1111111110
dqxor071 xor 1   111111111  ->  111111110
dqxor072 xor 1    11111111  ->  11111110
dqxor073 xor 1     1111111  ->  1111110
dqxor074 xor 1      111111  ->  111110
dqxor075 xor 1       11111  ->  11110
dqxor076 xor 1        1111  ->  1110
dqxor077 xor 1         111  ->  110
dqxor078 xor 1          11  ->  10
dqxor079 xor 1           1  ->  0

dqxor080 xor 0  1111111111  ->  1111111111
dqxor081 xor 0   111111111  ->  111111111
dqxor082 xor 0    11111111  ->  11111111
dqxor083 xor 0     1111111  ->  1111111
dqxor084 xor 0      111111  ->  111111
dqxor085 xor 0       11111  ->  11111
dqxor086 xor 0        1111  ->  1111
dqxor087 xor 0         111  ->  111
dqxor088 xor 0          11  ->  11
dqxor089 xor 0           1  ->  1

dqxor090 xor 011111111  111101111  ->  100010000
dqxor091 xor 101111111  111101111  ->   10010000
dqxor092 xor 110111111  111101111  ->    1010000
dqxor093 xor 111011111  111101111  ->     110000
dqxor094 xor 111101111  111101111  ->          0
dqxor095 xor 111110111  111101111  ->      11000
dqxor096 xor 111111011  111101111  ->      10100
dqxor097 xor 111111101  111101111  ->      10010
dqxor098 xor 111111110  111101111  ->      10001

dqxor100 xor 111101111  011111111  ->  100010000
dqxor101 xor 111101111  101111111  ->   10010000
dqxor102 xor 111101111  110111111  ->    1010000
dqxor103 xor 111101111  111011111  ->     110000
dqxor104 xor 111101111  111101111  ->          0
dqxor105 xor 111101111  111110111  ->      11000
dqxor106 xor 111101111  111111011  ->      10100
dqxor107 xor 111101111  111111101  ->      10010
dqxor108 xor 111101111  111111110  ->      10001

-- non-0/1 should not be accepted, nor should signs
dqxor220 xor 111111112  111111111  ->  NaN Invalid_operation
dqxor221 xor 333333333  333333333  ->  NaN Invalid_operation
dqxor222 xor 555555555  555555555  ->  NaN Invalid_operation
dqxor223 xor 777777777  777777777  ->  NaN Invalid_operation
dqxor224 xor 999999999  999999999  ->  NaN Invalid_operation
dqxor225 xor 222222222  999999999  ->  NaN Invalid_operation
dqxor226 xor 444444444  999999999  ->  NaN Invalid_operation
dqxor227 xor 666666666  999999999  ->  NaN Invalid_operation
dqxor228 xor 888888888  999999999  ->  NaN Invalid_operation
dqxor229 xor 999999999  222222222  ->  NaN Invalid_operation
dqxor230 xor 999999999  444444444  ->  NaN Invalid_operation
dqxor231 xor 999999999  666666666  ->  NaN Invalid_operation
dqxor232 xor 999999999  888888888  ->  NaN Invalid_operation
-- a few randoms
dqxor240 xor  567468689 -934981942 ->  NaN Invalid_operation
dqxor241 xor  567367689  934981942 ->  NaN Invalid_operation
dqxor242 xor -631917772 -706014634 ->  NaN Invalid_operation
dqxor243 xor -756253257  138579234 ->  NaN Invalid_operation
dqxor244 xor  835590149  567435400 ->  NaN Invalid_operation
-- test MSD
dqxor250 xor  2000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor251 xor  7000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor252 xor  8000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor253 xor  9000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor254 xor  2000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor255 xor  7000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor256 xor  8000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor257 xor  9000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor258 xor  1000000111000111000111000000000000 2000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor259 xor  1000000111000111000111000000000000 7000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor260 xor  1000000111000111000111000000000000 8000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor261 xor  1000000111000111000111000000000000 9000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor262 xor  0000000111000111000111000000000000 2000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor263 xor  0000000111000111000111000000000000 7000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor264 xor  0000000111000111000111000000000000 8000000111000111000111000000000000 ->  NaN Invalid_operation
dqxor265 xor  0000000111000111000111000000000000 9000000111000111000111000000000000 ->  NaN Invalid_operation
-- test MSD-1
dqxor270 xor  0200000111000111000111001000000000 1000000111000111000111100000000010 ->  NaN Invalid_operation
dqxor271 xor  0700000111000111000111000100000000 1000000111000111000111010000000100 ->  NaN Invalid_operation
dqxor272 xor  0800000111000111000111000010000000 1000000111000111000111001000001000 ->  NaN Invalid_operation
dqxor273 xor  0900000111000111000111000001000000 1000000111000111000111000100010000 ->  NaN Invalid_operation
dqxor274 xor  1000000111000111000111000000100000 0200000111000111000111000010100000 ->  NaN Invalid_operation
dqxor275 xor  1000000111000111000111000000010000 0700000111000111000111000001000000 ->  NaN Invalid_operation
dqxor276 xor  1000000111000111000111000000001000 0800000111000111000111000010100000 ->  NaN Invalid_operation
dqxor277 xor  1000000111000111000111000000000100 0900000111000111000111000000010000 ->  NaN Invalid_operation
-- test LSD
dqxor280 xor  0010000111000111000111000000000002 1000000111000111000111000100000001 ->  NaN Invalid_operation
dqxor281 xor  0001000111000111000111000000000007 1000000111000111000111001000000011 ->  NaN Invalid_operation
dqxor282 xor  0000000111000111000111100000000008 1000000111000111000111010000000001 ->  NaN Invalid_operation
dqxor283 xor  0000000111000111000111010000000009 1000000111000111000111100000000001 ->  NaN Invalid_operation
dqxor284 xor  1000000111000111000111001000000000 0001000111000111000111000000000002 ->  NaN Invalid_operation
dqxor285 xor  1000000111000111000111000100000000 0010000111000111000111000000000007 ->  NaN Invalid_operation
dqxor286 xor  1000000111000111000111000010000000 0100000111000111000111000000000008 ->  NaN Invalid_operation
dqxor287 xor  1000000111000111000111000001000000 1000000111000111000111000000000009 ->  NaN Invalid_operation
-- test Middie
dqxor288 xor  0010000111000111000111000020000000 1000000111000111000111001000000000 ->  NaN Invalid_operation
dqxor289 xor  0001000111000111000111000070000001 1000000111000111000111000100000000 ->  NaN Invalid_operation
dqxor290 xor  0000000111000111000111100080000010 1000000111000111000111000010000000 ->  NaN Invalid_operation
dqxor291 xor  0000000111000111000111010090000100 1000000111000111000111000001000000 ->  NaN Invalid_operation
dqxor292 xor  1000000111000111000111001000001000 0000000111000111000111000020100000 ->  NaN Invalid_operation
dqxor293 xor  1000000111000111000111000100010000 0000000111000111000111000070010000 ->  NaN Invalid_operation
dqxor294 xor  1000000111000111000111000010100000 0000000111000111000111000080001000 ->  NaN Invalid_operation
dqxor295 xor  1000000111000111000111000001000000 0000000111000111000111000090000100 ->  NaN Invalid_operation
-- signs
dqxor296 xor -1000000111000111000111000001000000 -0000001110001110001110010000000100 ->  NaN Invalid_operation
dqxor297 xor -1000000111000111000111000001000000  0000001110001110001110000010000100 ->  NaN Invalid_operation
dqxor298 xor  1000000111000111000111000001000000 -0000001110001110001110001000000100 ->  NaN Invalid_operation
dqxor299 xor  1000000111000111000111000001000000  0000001110001110001110000011000100 ->  1000001001001001001001000010000100

-- Nmax, Nmin, Ntiny-like
dqxor331 xor  2   9.99999999E+999     -> NaN Invalid_operation
dqxor332 xor  3   1E-999              -> NaN Invalid_operation
dqxor333 xor  4   1.00000000E-2821    -> NaN Invalid_operation
dqxor334 xor  5   1E-900              -> NaN Invalid_operation
dqxor335 xor  6   -1E-900             -> NaN Invalid_operation
dqxor336 xor  7   -1.00000000E-999    -> NaN Invalid_operation
dqxor337 xor  8   -1E-999             -> NaN Invalid_operation
dqxor338 xor  9   -9.99999999E+999    -> NaN Invalid_operation
dqxor341 xor  9.99999999E+999     -18 -> NaN Invalid_operation
dqxor342 xor  1E-999               01 -> NaN Invalid_operation
dqxor343 xor  1.00000000E-999     -18 -> NaN Invalid_operation
dqxor344 xor  1E-908               18 -> NaN Invalid_operation
dqxor345 xor  -1E-907             -10 -> NaN Invalid_operation
dqxor346 xor  -1.00000000E-999     18 -> NaN Invalid_operation
dqxor347 xor  -1E-999              10 -> NaN Invalid_operation
dqxor348 xor  -9.99999999E+2991   -18 -> NaN Invalid_operation

-- A few other non-integers
dqxor361 xor  1.0                  1  -> NaN Invalid_operation
dqxor362 xor  1E+1                 1  -> NaN Invalid_operation
dqxor363 xor  0.0                  1  -> NaN Invalid_operation
dqxor364 xor  0E+1                 1  -> NaN Invalid_operation
dqxor365 xor  9.9                  1  -> NaN Invalid_operation
dqxor366 xor  9E+1                 1  -> NaN Invalid_operation
dqxor371 xor  0 1.0                   -> NaN Invalid_operation
dqxor372 xor  0 1E+1                  -> NaN Invalid_operation
dqxor373 xor  0 0.0                   -> NaN Invalid_operation
dqxor374 xor  0 0E+1                  -> NaN Invalid_operation
dqxor375 xor  0 9.9                   -> NaN Invalid_operation
dqxor376 xor  0 9E+1                  -> NaN Invalid_operation

-- All Specials are in error
dqxor780 xor -Inf  -Inf   -> NaN Invalid_operation
dqxor781 xor -Inf  -1000  -> NaN Invalid_operation
dqxor782 xor -Inf  -1     -> NaN Invalid_operation
dqxor783 xor -Inf  -0     -> NaN Invalid_operation
dqxor784 xor -Inf   0     -> NaN Invalid_operation
dqxor785 xor -Inf   1     -> NaN Invalid_operation
dqxor786 xor -Inf   1000  -> NaN Invalid_operation
dqxor787 xor -1000 -Inf   -> NaN Invalid_operation
dqxor788 xor -Inf  -Inf   -> NaN Invalid_operation
dqxor789 xor -1    -Inf   -> NaN Invalid_operation
dqxor790 xor -0    -Inf   -> NaN Invalid_operation
dqxor791 xor  0    -Inf   -> NaN Invalid_operation
dqxor792 xor  1    -Inf   -> NaN Invalid_operation
dqxor793 xor  1000 -Inf   -> NaN Invalid_operation
dqxor794 xor  Inf  -Inf   -> NaN Invalid_operation

dqxor800 xor  Inf  -Inf   -> NaN Invalid_operation
dqxor801 xor  Inf  -1000  -> NaN Invalid_operation
dqxor802 xor  Inf  -1     -> NaN Invalid_operation
dqxor803 xor  Inf  -0     -> NaN Invalid_operation
dqxor804 xor  Inf   0     -> NaN Invalid_operation
dqxor805 xor  Inf   1     -> NaN Invalid_operation
dqxor806 xor  Inf   1000  -> NaN Invalid_operation
dqxor807 xor  Inf   Inf   -> NaN Invalid_operation
dqxor808 xor -1000  Inf   -> NaN Invalid_operation
dqxor809 xor -Inf   Inf   -> NaN Invalid_operation
dqxor810 xor -1     Inf   -> NaN Invalid_operation
dqxor811 xor -0     Inf   -> NaN Invalid_operation
dqxor812 xor  0     Inf   -> NaN Invalid_operation
dqxor813 xor  1     Inf   -> NaN Invalid_operation
dqxor814 xor  1000  Inf   -> NaN Invalid_operation
dqxor815 xor  Inf   Inf   -> NaN Invalid_operation

dqxor821 xor  NaN -Inf    -> NaN Invalid_operation
dqxor822 xor  NaN -1000   -> NaN Invalid_operation
dqxor823 xor  NaN -1      -> NaN Invalid_operation
dqxor824 xor  NaN -0      -> NaN Invalid_operation
dqxor825 xor  NaN  0      -> NaN Invalid_operation
dqxor826 xor  NaN  1      -> NaN Invalid_operation
dqxor827 xor  NaN  1000   -> NaN Invalid_operation
dqxor828 xor  NaN  Inf    -> NaN Invalid_operation
dqxor829 xor  NaN  NaN    -> NaN Invalid_operation
dqxor830 xor -Inf  NaN    -> NaN Invalid_operation
dqxor831 xor -1000 NaN    -> NaN Invalid_operation
dqxor832 xor -1    NaN    -> NaN Invalid_operation
dqxor833 xor -0    NaN    -> NaN Invalid_operation
dqxor834 xor  0    NaN    -> NaN Invalid_operation
dqxor835 xor  1    NaN    -> NaN Invalid_operation
dqxor836 xor  1000 NaN    -> NaN Invalid_operation
dqxor837 xor  Inf  NaN    -> NaN Invalid_operation

dqxor841 xor  sNaN -Inf   ->  NaN  Invalid_operation
dqxor842 xor  sNaN -1000  ->  NaN  Invalid_operation
dqxor843 xor  sNaN -1     ->  NaN  Invalid_operation
dqxor844 xor  sNaN -0     ->  NaN  Invalid_operation
dqxor845 xor  sNaN  0     ->  NaN  Invalid_operation
dqxor846 xor  sNaN  1     ->  NaN  Invalid_operation
dqxor847 xor  sNaN  1000  ->  NaN  Invalid_operation
dqxor848 xor  sNaN  NaN   ->  NaN  Invalid_operation
dqxor849 xor  sNaN sNaN   ->  NaN  Invalid_operation
dqxor850 xor  NaN  sNaN   ->  NaN  Invalid_operation
dqxor851 xor -Inf  sNaN   ->  NaN  Invalid_operation
dqxor852 xor -1000 sNaN   ->  NaN  Invalid_operation
dqxor853 xor -1    sNaN   ->  NaN  Invalid_operation
dqxor854 xor -0    sNaN   ->  NaN  Invalid_operation
dqxor855 xor  0    sNaN   ->  NaN  Invalid_operation
dqxor856 xor  1    sNaN   ->  NaN  Invalid_operation
dqxor857 xor  1000 sNaN   ->  NaN  Invalid_operation
dqxor858 xor  Inf  sNaN   ->  NaN  Invalid_operation
dqxor859 xor  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqxor861 xor  NaN1   -Inf    -> NaN Invalid_operation
dqxor862 xor +NaN2   -1000   -> NaN Invalid_operation
dqxor863 xor  NaN3    1000   -> NaN Invalid_operation
dqxor864 xor  NaN4    Inf    -> NaN Invalid_operation
dqxor865 xor  NaN5   +NaN6   -> NaN Invalid_operation
dqxor866 xor -Inf     NaN7   -> NaN Invalid_operation
dqxor867 xor -1000    NaN8   -> NaN Invalid_operation
dqxor868 xor  1000    NaN9   -> NaN Invalid_operation
dqxor869 xor  Inf    +NaN10  -> NaN Invalid_operation
dqxor871 xor  sNaN11  -Inf   -> NaN Invalid_operation
dqxor872 xor  sNaN12  -1000  -> NaN Invalid_operation
dqxor873 xor  sNaN13   1000  -> NaN Invalid_operation
dqxor874 xor  sNaN14   NaN17 -> NaN Invalid_operation
dqxor875 xor  sNaN15  sNaN18 -> NaN Invalid_operation
dqxor876 xor  NaN16   sNaN19 -> NaN Invalid_operation
dqxor877 xor -Inf    +sNaN20 -> NaN Invalid_operation
dqxor878 xor -1000    sNaN21 -> NaN Invalid_operation
dqxor879 xor  1000    sNaN22 -> NaN Invalid_operation
dqxor880 xor  Inf     sNaN23 -> NaN Invalid_operation
dqxor881 xor +NaN25  +sNaN24 -> NaN Invalid_operation
dqxor882 xor -NaN26    NaN28 -> NaN Invalid_operation
dqxor883 xor -sNaN27  sNaN29 -> NaN Invalid_operation
dqxor884 xor  1000    -NaN30 -> NaN Invalid_operation
dqxor885 xor  1000   -sNaN31 -> NaN Invalid_operation
