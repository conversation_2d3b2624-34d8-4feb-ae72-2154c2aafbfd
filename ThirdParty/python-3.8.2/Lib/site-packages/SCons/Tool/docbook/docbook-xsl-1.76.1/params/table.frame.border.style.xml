<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="table.frame.border.style">
<refmeta>
<refentrytitle>table.frame.border.style</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">list</refmiscinfo> 
<refmiscinfo class="other" otherclass="value">none</refmiscinfo>
<refmiscinfo class="other" otherclass="value">solid</refmiscinfo>
<refmiscinfo class="other" otherclass="value">dotted</refmiscinfo>
<refmiscinfo class="other" otherclass="value">dashed</refmiscinfo>
<refmiscinfo class="other" otherclass="value">double</refmiscinfo>
<refmiscinfo class="other" otherclass="value">groove</refmiscinfo>                       
<refmiscinfo class="other" otherclass="value">ridge</refmiscinfo>
<refmiscinfo class="other" otherclass="value">inset</refmiscinfo>
<refmiscinfo class="other" otherclass="value">outset</refmiscinfo>          
<refmiscinfo class="other" otherclass="value">solid</refmiscinfo>
</refmeta>
<refnamediv>
<refname>table.frame.border.style</refname>
<refpurpose>Specifies the border style of table frames</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="table.frame.border.style.frag">
<xsl:param name="table.frame.border.style">solid</xsl:param>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>Specifies the border style of table frames.</para>

</refsection>
</refentry>
