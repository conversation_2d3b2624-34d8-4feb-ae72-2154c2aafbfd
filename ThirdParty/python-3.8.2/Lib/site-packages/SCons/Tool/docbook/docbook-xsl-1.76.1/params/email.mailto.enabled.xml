<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="email.mailto.enabled">
<refmeta>
<refentrytitle>email.mailto.enabled</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">boolean</refmiscinfo>
</refmeta>
<refnamediv>
<refname>email.mailto.enabled</refname>
<refpurpose>Generate mailto: links for email addresses?</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="email.mailto.enabled.frag">
<xsl:param name="email.mailto.enabled" select="0"/>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>If non-zero the generated output for the <tag>email</tag> element
will be a clickable mailto: link that brings up the default mail client
on the system.</para>

</refsection>
</refentry>
