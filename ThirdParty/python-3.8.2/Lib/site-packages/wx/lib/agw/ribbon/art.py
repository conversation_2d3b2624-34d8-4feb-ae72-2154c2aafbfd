# -*- coding: utf-8 -*-
#----------------------------------------------------------------------------
# Name:         art.py
# Purpose:
#
# Author:       <PERSON> <<EMAIL>>
#
# Created:
# Version:
# Date:
# Licence:      wxWindows license
# Tags:         phoenix-port, unittest, documented, py3-port
#----------------------------------------------------------------------------
"""
Contains the constants used by the ribbon package.
"""
# RibbonArtSetting
RIBBON_ART_TAB_SEPARATION_SIZE = 1
RIBBON_ART_PAGE_BORDER_LEFT_SIZE = 2
RIBBON_ART_PAGE_BORDER_TOP_SIZE = 3
RIBBON_ART_PAGE_BORDER_RIGHT_SIZE = 4
RIBBON_ART_PAGE_BORDER_BOTTOM_SIZE = 5
RIBBON_ART_PANEL_X_SEPARATION_SIZE = 6
RIBBON_ART_PANEL_Y_SEPARATION_SIZE = 7
RIBBON_ART_TOOL_GROUP_SEPARATION_SIZE = 8
RIBBON_ART_GALLERY_BITMAP_PADDING_LEFT_SIZE = 9
RIBBON_ART_GALLERY_BITMAP_PADDING_RIGHT_SIZE = 10
RIBBON_ART_GALLERY_BITMAP_PADDING_TOP_SIZE = 11
RIBBON_ART_GALLERY_BITMAP_PADDING_BOTTOM_SIZE = 12
RIBBON_ART_PANEL_LABEL_FONT = 13
RIBBON_ART_BUTTON_BAR_LABEL_FONT = 14
RIBBON_ART_TAB_LABEL_FONT = 15
RIBBON_ART_BUTTON_BAR_LABEL_COLOUR = 16
RIBBON_ART_BUTTON_BAR_HOVER_BORDER_COLOUR = 17
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_COLOUR = 18
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = 19
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_COLOUR = 20
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_GRADIENT_COLOUR = 21
RIBBON_ART_BUTTON_BAR_ACTIVE_BORDER_COLOUR = 22
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_COLOUR = 23
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = 24
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_COLOUR = 25
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_GRADIENT_COLOUR = 26
RIBBON_ART_GALLERY_BORDER_COLOUR = 27
RIBBON_ART_GALLERY_HOVER_BACKGROUND_COLOUR = 28
RIBBON_ART_GALLERY_BUTTON_BACKGROUND_COLOUR = 29
RIBBON_ART_GALLERY_BUTTON_BACKGROUND_GRADIENT_COLOUR = 30
RIBBON_ART_GALLERY_BUTTON_BACKGROUND_TOP_COLOUR = 31
RIBBON_ART_GALLERY_BUTTON_FACE_COLOUR = 32
RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_COLOUR = 33
RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_GRADIENT_COLOUR = 34
RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_TOP_COLOUR = 35
RIBBON_ART_GALLERY_BUTTON_HOVER_FACE_COLOUR = 36
RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_COLOUR = 37
RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_GRADIENT_COLOUR = 38
RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_TOP_COLOUR = 39
RIBBON_ART_GALLERY_BUTTON_ACTIVE_FACE_COLOUR = 40
RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_COLOUR = 41
RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_GRADIENT_COLOUR = 42
RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_TOP_COLOUR = 43
RIBBON_ART_GALLERY_BUTTON_DISABLED_FACE_COLOUR = 44
RIBBON_ART_GALLERY_ITEM_BORDER_COLOUR = 45
RIBBON_ART_TAB_LABEL_COLOUR = 46
RIBBON_ART_TAB_SEPARATOR_COLOUR = 47
RIBBON_ART_TAB_SEPARATOR_GRADIENT_COLOUR = 48
RIBBON_ART_TAB_CTRL_BACKGROUND_COLOUR = 49
RIBBON_ART_TAB_CTRL_BACKGROUND_GRADIENT_COLOUR = 50
RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_COLOUR = 51
RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = 52
RIBBON_ART_TAB_HOVER_BACKGROUND_COLOUR = 53
RIBBON_ART_TAB_HOVER_BACKGROUND_GRADIENT_COLOUR = 54
RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_COLOUR = 55
RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = 56
RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR = 57
RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR = 58
RIBBON_ART_TAB_BORDER_COLOUR = 59
RIBBON_ART_PANEL_BORDER_COLOUR = 60
RIBBON_ART_PANEL_BORDER_GRADIENT_COLOUR = 61
RIBBON_ART_PANEL_MINIMISED_BORDER_COLOUR = 62
RIBBON_ART_PANEL_MINIMISED_BORDER_GRADIENT_COLOUR = 63
RIBBON_ART_PANEL_LABEL_BACKGROUND_COLOUR = 64
RIBBON_ART_PANEL_LABEL_BACKGROUND_GRADIENT_COLOUR = 65
RIBBON_ART_PANEL_LABEL_COLOUR = 66
RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_COLOUR = 67
RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_GRADIENT_COLOUR = 68
RIBBON_ART_PANEL_HOVER_LABEL_COLOUR = 69
RIBBON_ART_PANEL_MINIMISED_LABEL_COLOUR = 70
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_COLOUR = 71
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = 72
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_COLOUR = 73
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_GRADIENT_COLOUR = 74
RIBBON_ART_PANEL_BUTTON_FACE_COLOUR = 75
RIBBON_ART_PANEL_BUTTON_HOVER_FACE_COLOUR = 76
RIBBON_ART_PAGE_BORDER_COLOUR = 77
RIBBON_ART_PAGE_BACKGROUND_TOP_COLOUR = 78
RIBBON_ART_PAGE_BACKGROUND_TOP_GRADIENT_COLOUR = 79
RIBBON_ART_PAGE_BACKGROUND_COLOUR = 80
RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR = 81
RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_COLOUR = 82
RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = 83
RIBBON_ART_PAGE_HOVER_BACKGROUND_COLOUR = 84
RIBBON_ART_PAGE_HOVER_BACKGROUND_GRADIENT_COLOUR = 85
RIBBON_ART_TOOLBAR_BORDER_COLOUR = 86
RIBBON_ART_TOOLBAR_HOVER_BORDER_COLOUR = 87
RIBBON_ART_TOOLBAR_FACE_COLOUR = 88
RIBBON_ART_TOOL_BACKGROUND_TOP_COLOUR = 89
RIBBON_ART_TOOL_BACKGROUND_TOP_GRADIENT_COLOUR = 90
RIBBON_ART_TOOL_BACKGROUND_COLOUR = 91
RIBBON_ART_TOOL_BACKGROUND_GRADIENT_COLOUR = 92
RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_COLOUR = 93
RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = 94
RIBBON_ART_TOOL_HOVER_BACKGROUND_COLOUR = 95
RIBBON_ART_TOOL_HOVER_BACKGROUND_GRADIENT_COLOUR = 96
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_COLOUR = 97
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = 98
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_COLOUR = 99
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_GRADIENT_COLOUR = 100


# RibbonScrollButtonStyle
RIBBON_SCROLL_BTN_LEFT = 0
"""Button will scroll to the left."""
RIBBON_SCROLL_BTN_RIGHT = 1
"""Button will scroll to the right."""
RIBBON_SCROLL_BTN_UP = 2
"""Button will scroll upward."""
RIBBON_SCROLL_BTN_DOWN = 3
"""Button will scroll downward."""

RIBBON_SCROLL_BTN_DIRECTION_MASK = 3
"""A mask to extract direction from a combination of flags."""
RIBBON_SCROLL_BTN_NORMAL = 0
"""Button is not active or hovered."""
RIBBON_SCROLL_BTN_HOVERED = 4
"""Button has a cursor hovering over it."""
RIBBON_SCROLL_BTN_ACTIVE = 8
"""Button is being pressed."""
RIBBON_SCROLL_BTN_STATE_MASK = 12
"""A mask to extract state from a combination of flags."""
RIBBON_SCROLL_BTN_FOR_OTHER = 0
"""Button is not for scrolling tabs nor pages."""
RIBBON_SCROLL_BTN_FOR_TABS = 16
"""Button is for scrolling tabs."""
RIBBON_SCROLL_BTN_FOR_PAGE = 32
"""Button is for scrolling pages."""
RIBBON_SCROLL_BTN_FOR_MASK = 48
"""A mask to extract purpose from a combination of flags."""

# RibbonButtonKind
RIBBON_BUTTON_NORMAL    = 1 << 0
"""Normal button or tool with a clickable area which causes some generic action."""
RIBBON_BUTTON_DROPDOWN  = 1 << 1
"""Dropdown button or tool with a clickable area which typically causes a dropdown menu."""
RIBBON_BUTTON_HYBRID    = RIBBON_BUTTON_NORMAL | RIBBON_BUTTON_DROPDOWN
"""Button or tool with two clickable areas - one which causes a dropdown menu, and one which causes a generic action."""
RIBBON_BUTTON_TOGGLE    = 1 << 2
"""Normal button or tool with a clickable area which toggles the button between a pressed and unpressed state."""

# RibbonButtonBarButtonState
RIBBON_BUTTONBAR_BUTTON_SMALL     = 0 << 0
RIBBON_BUTTONBAR_BUTTON_MEDIUM    = 1 << 0
RIBBON_BUTTONBAR_BUTTON_LARGE     = 2 << 0
RIBBON_BUTTONBAR_BUTTON_SIZE_MASK = 3 << 0

RIBBON_BUTTONBAR_BUTTON_NORMAL_HOVERED    = 1 << 3
RIBBON_BUTTONBAR_BUTTON_DROPDOWN_HOVERED  = 1 << 4
RIBBON_BUTTONBAR_BUTTON_HOVER_MASK        = RIBBON_BUTTONBAR_BUTTON_NORMAL_HOVERED | RIBBON_BUTTONBAR_BUTTON_DROPDOWN_HOVERED
RIBBON_BUTTONBAR_BUTTON_NORMAL_ACTIVE     = 1 << 5
RIBBON_BUTTONBAR_BUTTON_DROPDOWN_ACTIVE   = 1 << 6
RIBBON_BUTTONBAR_BUTTON_ACTIVE_MASK       = RIBBON_BUTTONBAR_BUTTON_NORMAL_ACTIVE | RIBBON_BUTTONBAR_BUTTON_DROPDOWN_ACTIVE
RIBBON_BUTTONBAR_BUTTON_DISABLED          = 1 << 7
RIBBON_BUTTONBAR_BUTTON_TOGGLED           = 1 << 8
RIBBON_BUTTONBAR_BUTTON_STATE_MASK        = 0x1F8


# RibbonGalleryButtonState
RIBBON_GALLERY_BUTTON_NORMAL = 1
RIBBON_GALLERY_BUTTON_HOVERED = 2
RIBBON_GALLERY_BUTTON_ACTIVE = 3
RIBBON_GALLERY_BUTTON_DISABLED = 4


RIBBON_BAR_SHOW_PAGE_LABELS            = 1 << 0
RIBBON_BAR_SHOW_PAGE_ICONS             = 1 << 1
RIBBON_BAR_FLOW_HORIZONTAL             = 0
RIBBON_BAR_FLOW_VERTICAL               = 1 << 2
RIBBON_BAR_SHOW_PANEL_EXT_BUTTONS      = 1 << 3
RIBBON_BAR_SHOW_PANEL_MINIMISE_BUTTONS = 1 << 4
RIBBON_BAR_ALWAYS_SHOW_TABS            = 1 << 5


RIBBON_BAR_DEFAULT_STYLE =  RIBBON_BAR_FLOW_HORIZONTAL | RIBBON_BAR_SHOW_PAGE_LABELS \
                            | RIBBON_BAR_SHOW_PANEL_EXT_BUTTONS

RIBBON_BAR_FOLDBAR_STYLE =  RIBBON_BAR_FLOW_VERTICAL | RIBBON_BAR_SHOW_PAGE_ICONS \
                            | RIBBON_BAR_SHOW_PANEL_EXT_BUTTONS \
                            | RIBBON_BAR_SHOW_PANEL_MINIMISE_BUTTONS

RIBBON_TOOLBAR_TOOL_FIRST             = 1 << 0
RIBBON_TOOLBAR_TOOL_LAST              = 1 << 1
RIBBON_TOOLBAR_TOOL_POSITION_MASK     = RIBBON_TOOLBAR_TOOL_FIRST | RIBBON_TOOLBAR_TOOL_LAST

RIBBON_TOOLBAR_TOOL_NORMAL_HOVERED    = 1 << 3
RIBBON_TOOLBAR_TOOL_DROPDOWN_HOVERED  = 1 << 4
RIBBON_TOOLBAR_TOOL_HOVER_MASK        = RIBBON_TOOLBAR_TOOL_NORMAL_HOVERED | RIBBON_TOOLBAR_TOOL_DROPDOWN_HOVERED
RIBBON_TOOLBAR_TOOL_NORMAL_ACTIVE     = 1 << 5
RIBBON_TOOLBAR_TOOL_DROPDOWN_ACTIVE   = 1 << 6
RIBBON_TOOLBAR_TOOL_ACTIVE_MASK       = RIBBON_TOOLBAR_TOOL_NORMAL_ACTIVE | RIBBON_TOOLBAR_TOOL_DROPDOWN_ACTIVE
RIBBON_TOOLBAR_TOOL_DISABLED          = 1 << 7
RIBBON_TOOLBAR_TOOL_TOGGLED           = 1 << 8
RIBBON_TOOLBAR_TOOL_STATE_MASK        = 0x1F8

RIBBON_PANEL_NO_AUTO_MINIMISE = 1 << 0
RIBBON_PANEL_EXT_BUTTON       = 1 << 3
RIBBON_PANEL_MINIMISE_BUTTON  = 1 << 4
RIBBON_PANEL_STRETCH          = 1 << 5
RIBBON_PANEL_FLEXIBLE         = 1 << 6

RIBBON_PANEL_DEFAULT_STYLE    = 0
