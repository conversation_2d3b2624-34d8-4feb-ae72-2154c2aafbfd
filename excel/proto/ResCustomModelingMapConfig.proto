syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";


message CustomModelingMapConfigData {// @noSvr
  option (resKey) = "sourceTypeId";
  optional int64 sourceTypeId = 1; // 源TypeId
  optional int64 targetTypeId = 2; // 目标TypeId
  optional int32 targetEditSkinShowMode = 3; // 目标编辑时，皮肤的显示模式
}

message table_CustomModelingMapConfig {// @noSvr
  repeated CustomModelingMapConfigData rows = 1;
}