syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";

message MatchWarmRoundScoreQuailfyScope {  // @noCli
  optional QualifyingDegreeInfo left = 1;
  optional QualifyingDegreeInfo right = 2;
}

message MatchWarmRoundScoreCaseInfo {  // @noCli
  option (resKey) = "id";
  optional int32 id = 1;                      // 自增id
  repeated int32 abTestId = 2;                // ABTest编号
  optional MatchWarmRoundScoreCase reason = 3;    // 变化原因
  optional int32 consecutiveTimes = 4;        // 触发所需连续次数
  optional int32 changeScore = 5;             // 分数变化
  optional MatchWarmRoundScoreQuailfyScope qualifyScope = 6;   // 适用的段位范围
  optional int32 startsEffectAfterNDaysOfSeason = 7;           // 赛季开始N天后生效
  optional int32 isIgnoreQualifyScope = 8;           // 是否忽略段位限制
}

message MMRScoreRange {  // @noCli
  optional int32 left = 1;
  optional int32 right = 2;
}

message MMRAiLevelWeight {  // @noCli
  optional int32 lv = 1;        // AI等级
  optional int32 weight = 2;
}

message MatchWarmRoundAiLevelInfo {  // @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  optional MMRScoreRange scoreRange = 2;              // 分数区间[left, right]
  repeated MMRAiLevelWeight aiLevelWeightList = 3;    // 权重
  repeated int32 matchTypeIdList = 4;                 // 玩法id
  optional BattleAiRobotType aiRobotType = 5;                     // AI机器人类型
  optional int32 scriptType = 6;  //剧本类型
  optional MMRScoreType mmrType = 7;  // mmr类型 如果为invalid取玩法模式表类型
  optional int32 controlGroupScriptType = 8;  //对照组剧本类型
  optional int32 multiScriptType = 9;  //多人剧本类型
  optional int32 multiControlGroupScriptType = 10;  //对照组多人剧本类型
}

// 温暖局分数
message table_MatchWarmRoundScoreCaseData {  // @noCli
  repeated MatchWarmRoundScoreCaseInfo rows = 1;
}

// 温暖局AI强度
message table_MatchWarmRoundAiLevelData {  // @noCli
  repeated MatchWarmRoundAiLevelInfo rows = 1;
}

// P_匹配MMR与温暖局.xlsx 副玩法特殊新手温暖局配置
message SecondaryGameplayNewbieWarmRoundCondition {  // @noCli
  option (resKey) = "matchTypeId";
  optional int32 matchTypeId = 1;
  optional int32 openForFirstKRound = 2;
  repeated int32 relatedMatchTypeIds = 3;
}

// P_匹配MMR与温暖局.xlsx 副玩法特殊新手温暖局配置
message table_SecondaryGameplayNewbieWarmRoundData {  // @noCli
  repeated SecondaryGameplayNewbieWarmRoundCondition rows = 1;
}

// P_匹配MMR与温暖局.xlsx 温暖局触发限制
message MatchWarmRoundLimitInfo {  // @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  repeated WarmRoundScoreType warmRoundScoreTypes = 2;          // 温暖分类型列表
  optional MMRScoreType mmrType = 3;                            // mmr类型 如果为invalid取玩法模式表类型
  optional MMRScoreRange scoreRange = 4;                        // 分数区间[left, right]
  optional int32 dayTriggerLimit = 5;                           // 该玩法每日触发温暖局局数上限
}

// P_匹配MMR与温暖局.xlsx
message table_MatchWarmRoundLimitData {  // @noCli
  repeated MatchWarmRoundLimitInfo rows = 1;
}

// 场次区间
message CntRange {  // @noCli
  optional int32 left = 1;
  optional int32 right = 2;
}

// P_匹配MMR与温暖局.xlsx 回流温暖分加分系数
message MatchWarmRoundReturningExtraFactorInfo {  // @noCli
  option (resKey) = "id";
  optional int32 id = 1;              // 自增id
  optional CntRange cntRange = 2;     // 场次区间
  optional int32 factorHundred = 3;   // 变化系数 * 100
}

// P_匹配MMR与温暖局.xlsx 回流温暖分加分系数
message table_MatchWarmRoundReturningAddExtraFactorData {  // @noCli
  repeated MatchWarmRoundReturningExtraFactorInfo rows = 1;
}

// P_匹配MMR与温暖局.xlsx 回流温暖分扣分系数
message table_MatchWarmRoundReturningSubExtraFactorData {  // @noCli
  repeated MatchWarmRoundReturningExtraFactorInfo rows = 1;
}

//  AiLab匹配对照组分割线

// P_匹配AiLab对照组配置.xlsx AiLab对照组匹配温暖局分数
message table_MatchAiLabCGrpWarmRoundScoreCaseData {  // @noCli
  repeated MatchWarmRoundScoreCaseInfo rows = 1;
}

// P_匹配AiLab对照组配置.xlsx AiLab对照组温暖局AI强度
message table_MatchAiLabCGrpWarmRoundAiLevelData {  // @noCli
  repeated MatchWarmRoundAiLevelInfo rows = 1;
}

// P_匹配AiLab对照组配置.xlsx AiLab对照组温暖局触发限制
message table_MatchAiLabCGrpWarmRoundLimitData {  // @noCli
  repeated MatchWarmRoundLimitInfo rows = 1;
}

// P_匹配AiLab对照组配置.xlsx AiLab对照组回流温暖分加分系数
message table_MatchAiLabCGrpWarmRoundReturningAddExtraFactorData {  // @noCli
  repeated MatchWarmRoundReturningExtraFactorInfo rows = 1;
}

// P_匹配AiLab对照组配置.xlsx 回流温暖分扣分系数 AiLab对照组回流温暖分扣分系数
message table_MatchAiLabCGrpWarmRoundReturningSubExtraFactorData {  // @noCli
  repeated MatchWarmRoundReturningExtraFactorInfo rows = 1;
}

// P_匹配MMR与温暖局.xlsx 温暖分结算个人排名静态映射

message MatchWarmRoundSelfRankSToChangeInfo {           // @noCli
  option (resKey) = "matchType,rank";
  option deprecated = true;
  optional int32 matchType = 1;                   // 玩法类型
  optional int32 rank = 2;                        // 排名
  optional int32 scoreChanged = 3;                // 分数变化
}

// P_匹配MMR与温暖局.xlsx 温暖分结算个人排名静态映射
message table_MatchWarmRoundSelfRankSToChangeData {           // @noCli
  option deprecated = true;
  repeated MatchWarmRoundSelfRankSToChangeInfo rows = 1;
}


// P_匹配MMR与温暖局.xlsx 温暖分结算个人排名静态映射
message MatchWarmRoundSelfRankSToChangeInfoV2 {           // @noCli
  option (resKey) = "id";
  optional int32 id = 1;                   // 玩法类型
  repeated int32 matchTypes = 2;                   // 玩法类型
  optional QualifyType qualifyType = 3;                         // 段位类型
  optional MatchWarmRoundScoreQuailfyScope qualifyScope = 4;   // 适用的段位范围
  optional int32 startsEffectAfterNDaysOfSeason = 5;           // 赛季开始N天后生效
  optional int32 rank = 6;                        // 排名
  optional int32 scoreChanged = 7;                // 分数变化
  optional MMRScoreType mmrType = 8;              // mmr类型
  optional MMRScoreRange mmrScoreRange = 9;       // mmr分数区间[left, right]
}

// P_匹配MMR与温暖局.xlsx 温暖分结算个人排名静态映射
message table_MatchWarmRoundSelfRankSToChangeDataV2 {           // @noCli
  repeated MatchWarmRoundSelfRankSToChangeInfoV2 rows = 1;
}

// P_匹配温暖局.xlsx 温暖局MMR分修正
message MatchWarmRoundMmrRangeAndInfo {           // @noCli
  optional MMRScoreRange oriMmrScoreRange = 1;    // 原mmr闭区间
  optional int32 mmrCorrectingPercentage = 2;     // mmr变化分数百分比[0-100]
}

// P_匹配温暖局.xlsx 温暖局MMR分修正
message MatchWarmRoundMmrCorrectingInfo {         // @noCli
  option (resKey) = "id";
  optional int32 id = 1;                          // 自增id
  repeated int32 matchTypeIds = 2;                // 玩法模式列表
  repeated MatchWarmRoundMmrRangeAndInfo rangeAndInfo = 3;  // mmr 对应的变化
}

// P_匹配温暖局.xlsx 温暖局MMR分修正
message table_MatchWarmRoundMmrCorrectingData {    // @noCli
  repeated MatchWarmRoundMmrCorrectingInfo rows = 1;
}

// P_匹配温暖局.xlsx 大王别抓我新手分阵营温暖局房间
message ChaseNewbieSideWarmRoundRoomData {    // @noCli
  option (resKey) = "id";
  optional int32 id = 1;                // 自增id
  optional int32 matchTypeId = 2;       // 玩法模式id
  optional int32 sideId = 3;            // 阵营id
  optional int32 firstKRound = 4;       // 前N轮(从1开始)
  optional int32 roomInfoId = 5;        // 温暖局房间id
}

// P_匹配温暖局.xlsx 大王别抓我新手分阵营温暖局房间
message table_ChaseNewbieSideWarmRoundRoomData {    // @noCli
  repeated ChaseNewbieSideWarmRoundRoomData rows = 1;
}