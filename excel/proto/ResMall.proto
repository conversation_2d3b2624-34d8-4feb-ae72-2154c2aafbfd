syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";
import "ResCondition.proto";
import "google/protobuf/timestamp.proto";

// 商城购买检查条件
message MallCommodityBuyChecker {
  optional MallBuyCheckType limitType = 1;    // 限制类型
  optional int64 limitParam = 2;              // 限制参数
  optional string limitTips = 3;              // 限制提示
}

// 商城道具赠送检查条件
message MallCommodityGiftChecker {
  optional MallGiftCheckType checkType = 1;
  repeated int64 checkParam = 2;
}

//商品状态配置
message MallObjectStateConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional ResConditionGroup StateCondition = 2; //状态条件
  optional int32 jumpId = 3; //该状态下，会使用到的jumpID 跳转ID列表 T_跳转功能表.xlsx
  optional string jumpText = 4; //该状态下，会使用到的跳转文本
}

message MallCommodity {
  option (resKey) = "commodityId";
  optional int32 mallId = 1;
  optional int32 commodityId = 2;
  optional string commodityName = 3;
  optional int32 coinType = 6;
  optional int32 price = 7;
  optional int32 discountPrice = 8;
  optional MallCommodityLimit limitType = 9;
  optional int32 limitNum = 10;
  repeated int32 recommendTag = 11;
  optional google.protobuf.Timestamp beginTime = 12;
  optional google.protobuf.Timestamp endTime = 13;
  optional int32 sort = 14;       //商店排序优先级
  optional int32 quality = 15;    //商品品级
  repeated int32 shopTag = 16;
  optional int32 shopSort = 17;   //商店排序优先级id
  optional int32 effected = 18;//@nocli
  repeated int32 dayCard = 19;//@nocli
  optional string lowVer = 20;    // 最低版本号
  optional string highVer = 21;   // 最高版本号
  optional int32 currencyConfigId = 22; // [j_金币支付midas道具配置]excel表中的id
  optional int32 order = 23;
  optional int32 jumpId = 24;
  optional string jumpText = 25;
  optional int32 gender = 26;
  optional string banner = 27;
  optional int64 activityId = 28; //关联活动id
  optional string minVersion = 29;
  optional int32 bpLimit = 30;  //高级bp限购
  optional int32 boughtNum = 31;  // 已购数量, 表里不要配置, 服务端运行时填充!!
  optional bool canDirectBuy = 32; // 是否支持直购
  optional int32 directBuyPrice = 33;  // 直购价格(元), 表里不要配置, 服务端运行时填充!!
  optional string imgUrl = 34; // 图片
  optional string imgBackUrl = 35; // 底板图片
  optional int32 showRedPoint = 36; // 显示红点
  optional MidasDiscountConf midasDiscountConf = 37;//@nocli
  optional string wordColor = 38; //字体颜色
  repeated int32 itemIds = 39;
  repeated int32 itemNums = 40;
  optional int32 RegionalGroupCommodity = 41; //地区分组_商品
  repeated int32 expireDays = 42; // 道具过期时间(天)
  optional string packageIcon = 43; //礼包图标
  optional string packageDec = 44; //礼包描述
  optional ResConditionGroup buyCondition = 45;//@nocli 购买条件
  repeated google.protobuf.Timestamp expireTimestamps = 46; // 道具过期时间
  optional bool canGift = 47; // 是否可被赠送
  optional google.protobuf.Timestamp beginShowTime = 48;    // 开始展示时间
  optional ResConditionGroup showCondition = 49;//@nocli 展示条件 通过getShopCommodityList方法过滤
  optional string imgBottomBackUrl = 50; // 下底板图片
  optional int32 titleSwitcherIndex = 51; // 文字展示index
  optional bool canAccumulate = 52; // 限购类型为每日时是否支持每日累计
  optional int32 accumulateMax = 53; // 限购类型为每日时是支持每日累计的上限数量
  optional int32 addIntimacy = 54;    // 赠送增加亲密度
  optional bool canShowLimit = 55;    // 是否显示限时标签
  optional int32 cutGiftColor = 56; //特惠礼包颜色
  optional int32 cutGiftStyle = 57; //特惠礼包样式
  optional int32 cutGiftRecommandStyle = 58; //特惠礼包推荐样式
  optional int32 cutGiftDiscountStyle = 59; //特惠礼包折扣样式
  optional int32 RegionalGroupCommon = 60; //地区分组_通用
  optional string overseapackageIconUmg = 61; //海外礼包图标
  optional int32 giftCoinType = 62;
  optional int32 giftPrice = 63;
  optional string cutGiftCenterImgUrlCdn = 64; //特惠礼包cdn中心图片
  optional string cutGiftNormalBgImgUrlCdn = 65; //特惠礼包cdn普通背景图片
  optional string cutGiftRandomBgImgUrlCdn = 66; //特惠礼包cdn随机背景图片
  optional string noOwnedJumpText = 67; // 跳转 未拥有 特殊文本
  optional string ownedJumpText = 68; // 跳转 拥有 特殊文本
  optional MallCommodityGiftChecker giftChecker = 69; // 赠送检查条件
  optional int32 NoShowInMall = 70; // 不展示在商城
  optional int32 giftJumpId = 71; // 商城赠送按钮跳转
  optional int32 unlockJumpId = 72; // 解锁跳转id
  optional int32 hideGiftBuyCountUI = 73; // 隐藏送礼的数量
  optional int32 hideBatchGiftUI = 74; // 隐藏批量赠送
  optional int32 hideGiftCoinUI = 75; // 隐藏商城解锁货币UI
  optional string buyPreCheckConditionKey = 76; // 购买前的检查
  optional int32 unlockNotOwnedJumpId = 77; //解锁跳转id 未拥有时
  optional int32 AvailableTips = 78; //提示气泡
  optional int32 ShowCDTimeRange = 79; //倒计时显示生效时间
  optional bool disableNtf = 80; //屏蔽提示
  optional int32 pakGroup = 81; //资源包Id
  optional bool isGrand = 82; //是否大奖
  optional string pictureUrl = 83;          // 图标CDN路径
  optional bool syncDB = 84;          // 同步到DB
  repeated int32 itemMaxOwnNums = 85; // 检查道具拥有最大数量。作为限购的补充
  optional MallCommodityBuyChecker buyChecker = 86; //购买限制检查
  optional bool bOpenSuit = 87; //同步开启图鉴
  optional int32 suitId = 88; //套装编号
  optional int32 playModeSeasonConfId = 89; // 玩法赛季配置id
  optional bool onlyIosMiniGameCanDemand = 90; // 只有ios小游戏可以发起索要
  optional BuyTimeRange buyTimeRange = 91; // 商品购买时间限制(在上架期间满足购买时间的需求) @noCli
  optional bool noBuySelf = 92; //不允许自购
  optional int32 cumuRecvNumMax = 93; //累计被赠送数量上限
  optional bool canPreviewPackage = 94; //支持预览礼包
  optional CommodityThemeInfo commodityThemeInfo = 95; // 主题商城商品配置
  optional int32 ifShow = 96; // 是否是赛季兑换白名单  
  optional int32 sortId = 97; // 分类id  
  optional string sortName = 98; // 分类名称
  optional google.protobuf.Timestamp newSaleNtfTime = 99; // 上新红点通知时间
  optional bool resetBuyTimesRedPointShow = 100; // 重置购买次数红点通知
  repeated int32 showGameType = 101; // 该礼包在某个游戏类型中展示（不填为全部展示）
  optional bool recommend = 102; // 是否推荐
  optional bool canAskForGift = 103; // 是否可索要，让别人赠送给我
  optional int32 outShow = 104; //是否外显
  optional int32 NoShowInArenaMall = 106; //是否不展示在Arena商城
  optional int32 recommendType = 108; // 推荐类型 1 狼人杀
  optional int32 timeLimitedPrice = 109; //限时价格
  optional int32 clientCanBuyStateID = 110; //可购买状态配置ID @noSvr
  optional int32 clientBoughtStateID = 111; //已购买状态配置ID @noSvr
  optional int32 clientCanNotBuyStateID = 112; //不可购买状态配置ID @noSvr
  optional int32 clientUnknownStateID = 113; //未知状态配置ID @noSvr
  repeated int32 clientCanBuyPreStateIDs = 114; //可购买状态附加配置ID列表（满足可购买状态时，需要同时满足此配置列表才可真正购买） @noSvr
  optional string unknownStateIcon = 115; //未知状态时的特殊图标 @noSvr
  optional google.protobuf.Timestamp jumpBeginTime = 116; // 可以跳转时段开始时间
  optional google.protobuf.Timestamp jumpEndTime = 117; // 可以跳转时段开始时间
}

// 开放购买的时间 每周几(openWeek)或者某天(openDate 0~24点)的startDayTime~endDayTime期间支持购买
message BuyTimeRange { // @noCli
  repeated int32 openWeek = 1;
  optional google.protobuf.Timestamp startDayTime = 2;
  optional google.protobuf.Timestamp endDayTime = 3;
  repeated google.protobuf.Timestamp openDate = 4;
}

message MidasDiscountConf { //@nocli
  optional MallMidasDiscountCondition condition = 1;
  optional string productIdSuffix = 2;
  repeated string params = 3;
}

message CommodityThemeInfo {
  optional int32 themeId = 1;
  optional bool isThemeBuyLimit = 2; // 开关,该商品是否参与限购统计
}

message table_MallCommodityConf {
  repeated MallCommodity rows = 1;
}

message MallInfo {
  option (resKey) = "mallId";
  optional int32 mallId = 1;
  optional ShopType shopType = 2;
  optional string mallName = 3;
  optional string refreshTime = 4;
  repeated int32 params = 5;
  optional ResConditionGroup conditionGroup = 6;//@nocli
  optional google.protobuf.Timestamp openTime = 7;
  optional google.protobuf.Timestamp closeTime = 8;
  optional bool buyRedPoint = 9; // 可购买红点
  optional bool newRedPoint = 10; // 商品上新红点
  optional bool resetRedPoint = 11; //购买次数重置红点
}

message table_MallInfoConf {
  repeated MallInfo rows = 1;
}

message MallRecommendPage{
  option (resKey) = "id";
  optional int32 id = 1;
  optional string pictureUrl = 2;// @noSvr
  repeated int32 commodityId = 3;
  optional google.protobuf.Timestamp beginTime = 4;
  optional google.protobuf.Timestamp endTime = 5;
  optional string buttonText = 6;// @noSvr
  optional int32 jumpId = 7;// @noSvr
  optional string minVersion = 8;
  optional int32 order = 9;
  optional int32 pictureType = 10;// @noSvr
  optional string overseapictureUmg = 11;// @noSvr
  optional int32 pakGroup = 12;// @noSvr
  optional int32 recommendType = 13; //推荐类型
  optional string recommendPictureUrl = 14; // @noSvr
}

message table_MallRecommendPageConf{
  repeated MallRecommendPage rows = 1;
}

message SceneGiftPackageConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional SceneGiftPackageType type = 2;
  optional int32 sort = 3;
  repeated int32 commodityIds = 4; // 对应商城商品配置
  optional ResConditionGroup  pushConditions = 5;
  optional int64 bugExpireTime = 6; // 购买过期时间
  repeated com.tencent.wea.xlsRes.Item items = 7; // 礼包赠品
  optional string minVersion = 8;
  repeated int32 conflictIds = 9; //@nocli 互斥的礼包ID
  repeated int32 originCommodityIds = 10; // 原价商品ID @noSvr
  optional google.protobuf.Timestamp beginTime = 11;
  optional google.protobuf.Timestamp endTime = 12;
  optional int32 showStyle = 13; // 礼包展示样式
  optional bool isExclusiveWindow = 14; // 是否专属窗口
  repeated int32 sceneIds = 15; //场景id
  repeated int32 coinIds = 16; //此类货币不足时需要弹出
  optional SceneGiftFrequency frequency = 17; //弹出频率
  optional string tips_1 = 18; //文本提示1
  optional string tips_2 = 19; //文本提示2
  optional string title = 20; //标题文本
  optional string imgUrl = 21; //图片
  optional int32 jumpId = 22; //跳转id
  optional string jumpText = 23; //跳转文本
  optional ArenaSceneGiftMultimediaShowStrategy strategy = 24; //多媒体显示策略
  optional string widgetName = 25; //对应的widget
  repeated int32 pushLoginPlat = 26; // 推送登录平台限制 PlayerLoginPlat
  repeated int32 operatorDisplayLimit = 27;   //操作系统显示限制   1.ios 2.android  3.hom
  optional int32 giftChargeTask = 28;   //礼包对应的充值任务组id
  optional int32 bugExpireTimeMinute = 29; //礼包过期时间（分钟） 和上面的过期时间叠加使用
  optional int32 resetType = 30;    // 重置类型 0 不重置 1 每日重置
  optional int32 ruleID = 31; //规则ID，对应W_文本表_规则说明_主表
  optional int32 pakGroup = 32; //分包id
}

enum SceneGiftPackageResetType{
  SGPRT_None = 0;
  SGPRT_Daily = 1;
}

message table_SceneGiftPackageConf {
  repeated SceneGiftPackageConfig rows = 1;
}

message SceneGiftShowStyle { //@noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 limitCount = 2;
  optional int32 scrollable = 3;

}

message table_SceneGiftShowStyleConf { //@noSvr
  repeated SceneGiftShowStyle rows = 1;
}

message ResMallSeasonShopTab {
  option (resKey) = "id";
  optional int32  id = 1;
  optional string name = 2; // @noSvr
  optional int32 mallId = 3;
  optional int32 isVisible = 4;
  optional int32 parentTabId = 5; // @noSvr
  optional int32 defaultSelect = 6; // @noSvr
  optional google.protobuf.Timestamp openTime = 7;
  optional google.protobuf.Timestamp closeTime = 8;
  optional string minVersion = 9; // 最低版本号
  optional string ThemeTitle = 10; // @noSvr
  optional int32  helpText= 11; // @noSvr
}

message table_ResMallSeasonShopTab {
  repeated ResMallSeasonShopTab rows = 1;
}

// 七彩石快速修改商品投放配置字段，配置字段元信息
message CommercialConfFieldMeta {
  option (resKey) = "fieldTypeId";
  optional int32  fieldTypeId = 1;  // 字段类型ID
  optional int32 confType = 2;  // 配置类型 enum CommercialConfType
  optional int32 fieldNo = 3; //字段编号
  optional string confTable = 4;  // 配置table名
  optional string confProtoFile = 5;  // 配置proto文件名
  optional string confMessage = 6;  // 配置meta结构名
  optional string fieldName = 7;  // proto字段名
  optional string fieldValueType = 8;  // proto字段数据类型
  optional CommercialConfFieldCategory fieldCategory = 9;  // 字段类别 enum CommercialConfFieldCategory
  optional bool isServerField = 10; // 服务器是否使用
  optional bool isClientField = 11; // 客户端是否使用
}

message table_CommercialConfFieldMeta {
  repeated CommercialConfFieldMeta rows = 1;
}

message CommercialConfField {
  optional string fieldName = 1;  // 字段名
  optional int32 fieldTypeId = 2;  // 字段类型ID
  oneof fieldValue { // 字段值
    int32 i32 = 5;
    int64 i64 = 6;
    string str = 7;
    bool bl = 8;
    google.protobuf.Timestamp ts = 9;
  }
}

// 七彩石快速修改商品投放配置字段，单次允许修改某个配置某个key的单个字段
message CommercialConfModifyCfg { // @noCli
  option (resKey) = "keyFieldTypeId,keyFieldVal,infoFieldTypeId";
  optional int32 keyFieldTypeId = 1;  // resKey字段类型
  optional string keyFieldVal = 2;  // resKey字段值
  optional int32 infoFieldTypeId = 3;  // 修改目标字段类型
  optional string infoFieldVal = 4;  // 修改目标字段值
}

message table_CommercialConfModifyCfg { // @noCli
  repeated CommercialConfModifyCfg rows = 1;
}


message ThemeMallInfoConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string name = 2;
  optional int32 limitNum = 3;
  optional google.protobuf.Timestamp beginTime = 4;
  optional google.protobuf.Timestamp endTime = 5;
  optional bool discountDrawOpen = 6;
  repeated KeyValueInt32 discountWeight = 7; //discount => weight
}

message table_ThemeMallInfoData {
  repeated ThemeMallInfoConfig rows = 1;
}

message RecommenedGamePlayGiftPackage {
  option (resKey) = "id"; 
  optional int32 id = 1; // id
  optional int32 sort = 2; // 推荐顺序
  optional string title = 3; //标题美术字
  optional string resources = 4; // 核心资源展示
  optional string desc = 5; // 描述
  optional int32 giftId = 6; // 礼包id
}

message table_RecommenedGamePlayGiftPackage {
  repeated RecommenedGamePlayGiftPackage rows = 1;
}

message table_NR3E3RecommenedGamePlayGiftPackage {
  repeated RecommenedGamePlayGiftPackage rows = 1;
}

message table_MallObjectStateConfig { //@noSvr
  repeated MallObjectStateConfig rows = 1;
}
