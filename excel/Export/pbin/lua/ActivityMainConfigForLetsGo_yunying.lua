--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/H_活动中心配置_运营.xlsx: 活动配置

local data = {
[60001] = {
id = 60001,
activityType = "ATActivityPermanentExchange",
timeInfo = {
beginTime = {
seconds = 1717084800
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1717084800
}
},
labelId = 1,
activityName = "常驻兑换商店",
activityParam = {
3134
},
isInBottom = 1,
tagId = 1,
activityNameType = "ANTActivityPermanentExchange",
activityShopType = {
104
},
titleType = 0
},
[4566] = {
id = 4566,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = "1.2.100.53",
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563486,
563496,
563506,
563516,
563526,
563536,
563546
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0
},
[4567] = {
id = 4567,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = "1.2.100.53",
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563487,
563497,
563507,
563517,
563527,
563537,
563547
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0
},
[4568] = {
id = 4568,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = "1.2.100.53",
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563488,
563498,
563508,
563518,
563528,
563538,
563548
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0
},
[60002] = {
id = 60002,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1722528000
},
endTime = {
seconds = 1723132799
},
showEndTime = {
seconds = 1723132799
},
showBeginTime = {
seconds = 1722528000
}
},
labelId = 4,
backgroundUrl = {
"langrencaidanju.astc"
},
lowVersion = "1.3.12.69",
activityName = "狼人彩蛋礼",
activityUIDetail = "UI_CommonTask_MulTaskView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1000000,
1000001,
1000002
},
showInCenter = true,
activityNameType = "ANTTotalLogin",
titleType = 0,
activitySubName = "限时对局开放"
},
[18901] = {
id = 18901,
activityType = "ATLuckyRebate",
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 2,
jumpId = 8010,
activityName = "消费返还",
activityParam = {
1,
238
},
activityUIDetail = "UI_LuckyFree_MainView",
isInBottom = 0,
tagId = 9,
slapFace = true,
activityNameType = "ANTRechargeLuckyFree",
titleType = 1
},
[18911] = {
id = 18911,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1748448000
},
endTime = {
seconds = 1750694399
},
showEndTime = {
seconds = 1750694399
},
showBeginTime = {
seconds = 1748448000
}
},
labelId = 3,
backgroundUrl = {
"xianfengshangcheng14.astc"
},
jumpId = 18911,
lowVersion = "1.3.88.1",
activityName = "花惠狂欢",
activityUIDetail = "UI_CommonJumpActivity_View",
tagId = 4,
showInCenter = true,
activityNameType = "ANTGuide",
titleType = 0,
activitySubName = "有才任星"
},
[5000] = {
id = 5000,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = 1710208800
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1710208800
}
},
labelId = 3,
activityName = "回归手册",
tagId = 1,
activityTaskGroup = {
570011,
570021,
570031
},
activityNameType = "ANTReturningTask",
titleType = 0,
activityBeginCleanCoin = {
3543
}
},
[6000] = {
id = 6000,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = 1710208800
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1710208800
}
},
labelId = 3,
activityName = "登录送全套",
tagId = 1,
activityTaskGroup = {
570041
},
activityNameType = "ANTReturningTask",
titleType = 0
}
}

local mt = {
showInCenter = false,
slapFace = false,
isHideMainBackground = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data