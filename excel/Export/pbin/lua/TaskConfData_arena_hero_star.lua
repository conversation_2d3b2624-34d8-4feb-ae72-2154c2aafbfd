--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_峡谷主目标系统_Arena.xlsx: 等级突破任务

local v0 = 26001

local data = {
[10010001] = {
id = 10010001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1001,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1001
},
taskGroupId = 26001
},
[10010002] = {
id = 10010002,
desc = "累计参与15次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1001
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1001
},
taskGroupId = 26001
},
[10010003] = {
id = 10010003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1001,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1001
},
taskGroupId = 26001
},
[10010004] = {
id = 10010004,
desc = "单局内一技能累计命中30次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 30,
subConditionList = {
{
type = 209,
value = {
1001
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1001
},
taskGroupId = 26001
},
[10010005] = {
id = 10010005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1001,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1001
}
},
[10010006] = {
id = 10010006,
desc = "单局内一技能累计造成6000伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 6000,
subConditionList = {
{
type = 209,
value = {
1001
}
},
{
type = 203,
value = {
755
}
}
}
}
}
}
},
reward = {
itemIdList = {
905001
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1001
}
},
[10020001] = {
id = 10020001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1002,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1002
},
taskGroupId = 26001
},
[10020002] = {
id = 10020002,
desc = "累计参与15次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1002
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1002
},
taskGroupId = 26001
},
[10020003] = {
id = 10020003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1002,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1002
},
taskGroupId = 26001
},
[10020004] = {
id = 10020004,
desc = "单局内一技能累计命中30次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 30,
subConditionList = {
{
type = 209,
value = {
1002
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1002
},
taskGroupId = 26001
},
[10020005] = {
id = 10020005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1002,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1002
}
},
[10020006] = {
id = 10020006,
desc = "单局内三技能累计造成3000伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 3000,
subConditionList = {
{
type = 209,
value = {
1002
}
},
{
type = 203,
value = {
761
}
}
}
}
}
}
},
reward = {
itemIdList = {
905002
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1002
}
},
[10030001] = {
id = 10030001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1003,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1003
},
taskGroupId = 26001
},
[10030002] = {
id = 10030002,
desc = "累计摧毁1个建筑",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 1,
subConditionList = {
{
type = 209,
value = {
1003
}
},
{
type = 203,
value = {
751
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1003
},
taskGroupId = 26001
},
[10030003] = {
id = 10030003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1003,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1003
},
taskGroupId = 26001
},
[10030004] = {
id = 10030004,
desc = "单局内三技能累计造成12000伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 12000,
subConditionList = {
{
type = 209,
value = {
1003
}
},
{
type = 203,
value = {
761
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1003
},
taskGroupId = 26001
},
[10030005] = {
id = 10030005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1003,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1003
}
},
[10030006] = {
id = 10030006,
desc = "单局内一技能累计命中70次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 70,
subConditionList = {
{
type = 209,
value = {
1003
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
905003
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1003
}
},
[10040001] = {
id = 10040001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1004,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1004
},
taskGroupId = 26001
},
[10040002] = {
id = 10040002,
desc = "累计摧毁1个建筑",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 1,
subConditionList = {
{
type = 209,
value = {
1004
}
},
{
type = 203,
value = {
751
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1004
},
taskGroupId = 26001
},
[10040003] = {
id = 10040003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1004,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1004
},
taskGroupId = 26001
},
[10040004] = {
id = 10040004,
desc = "单局内二技能累计命中30次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 30,
subConditionList = {
{
type = 209,
value = {
1004
}
},
{
type = 203,
value = {
756
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1004
},
taskGroupId = 26001
},
[10040005] = {
id = 10040005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1004,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1004
}
},
[10040006] = {
id = 10040006,
desc = "单局内一技能累计命中25次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 25,
subConditionList = {
{
type = 209,
value = {
1004
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
905004
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1004
}
},
[10050001] = {
id = 10050001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1005,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1005
},
taskGroupId = 26001
},
[10050002] = {
id = 10050002,
desc = "累计击杀3个史诗野怪",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 3,
subConditionList = {
{
type = 209,
value = {
1005
}
},
{
type = 203,
value = {
750
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1005
},
taskGroupId = 26001
},
[10050003] = {
id = 10050003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1005,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1005
},
taskGroupId = 26001
},
[10050004] = {
id = 10050004,
desc = "单局内一技能累计命中40次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 40,
subConditionList = {
{
type = 209,
value = {
1005
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1005
},
taskGroupId = 26001
},
[10050005] = {
id = 10050005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1005,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1005
}
},
[10050006] = {
id = 10050006,
desc = "单局内三技能累计造成5次击杀",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 5,
subConditionList = {
{
type = 209,
value = {
1005
}
},
{
type = 203,
value = {
760
}
}
}
}
}
}
},
reward = {
itemIdList = {
905005
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1005
}
},
[10060001] = {
id = 10060001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1006,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1006
},
taskGroupId = 26001
},
[10060002] = {
id = 10060002,
desc = "累计参与15次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1006
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1006
},
taskGroupId = 26001
},
[10060003] = {
id = 10060003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1006,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1006
},
taskGroupId = 26001
},
[10060004] = {
id = 10060004,
desc = "累计参与10次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 10,
subConditionList = {
{
type = 209,
value = {
1006
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1006
},
taskGroupId = 26001
},
[10060005] = {
id = 10060005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1006,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1006
}
},
[10060006] = {
id = 10060006,
desc = "单局内一技能累计命中70次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 70,
subConditionList = {
{
type = 209,
value = {
1006
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
905006
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1006
}
},
[10070001] = {
id = 10070001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1007,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1007
},
taskGroupId = 26001
},
[10070002] = {
id = 10070002,
desc = "累计摧毁1个建筑",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 1,
subConditionList = {
{
type = 209,
value = {
1007
}
},
{
type = 203,
value = {
751
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1007
},
taskGroupId = 26001
},
[10070003] = {
id = 10070003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1007,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1007
},
taskGroupId = 26001
},
[10070004] = {
id = 10070004,
desc = "单局内一技能累计造成3次击杀",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 3,
subConditionList = {
{
type = 209,
value = {
1007
}
},
{
type = 203,
value = {
754
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1007
},
taskGroupId = 26001
},
[10070005] = {
id = 10070005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1007,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1007
}
},
[10070006] = {
id = 10070006,
desc = "单局内二技能累计命中25次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 25,
subConditionList = {
{
type = 209,
value = {
1007
}
},
{
type = 203,
value = {
756
}
}
}
}
}
}
},
reward = {
itemIdList = {
905007
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1007
}
},
[10100001] = {
id = 10100001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1010,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1010
},
taskGroupId = 26001
},
[10100002] = {
id = 10100002,
desc = "累计击杀3个史诗野怪",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 3,
subConditionList = {
{
type = 209,
value = {
1010
}
},
{
type = 203,
value = {
750
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1010
},
taskGroupId = 26001
},
[10100003] = {
id = 10100003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1010,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1010
},
taskGroupId = 26001
},
[10100004] = {
id = 10100004,
desc = "单局内三技能累计命中20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 20,
subConditionList = {
{
type = 209,
value = {
1010
}
},
{
type = 203,
value = {
759
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1010
},
taskGroupId = 26001
},
[10100005] = {
id = 10100005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1010,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1010
}
},
[10100006] = {
id = 10100006,
desc = "单局内三技能累计造成12500伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 12500,
subConditionList = {
{
type = 209,
value = {
1010
}
},
{
type = 203,
value = {
761
}
}
}
}
}
}
},
reward = {
itemIdList = {
905011
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1010
}
},
[10110001] = {
id = 10110001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1011,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1011
},
taskGroupId = 26001
},
[10110002] = {
id = 10110002,
desc = "累计参与15次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1011
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1011
},
taskGroupId = 26001
},
[10110003] = {
id = 10110003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1011,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1011
},
taskGroupId = 26001
},
[10110004] = {
id = 10110004,
desc = "单局内三技能累计造成2次击杀",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 2,
subConditionList = {
{
type = 209,
value = {
1011
}
},
{
type = 203,
value = {
760
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1011
},
taskGroupId = 26001
},
[10110005] = {
id = 10110005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1011,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1011
}
},
[10110006] = {
id = 10110006,
desc = "单局内三技能累计命中100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 100,
subConditionList = {
{
type = 209,
value = {
1011
}
},
{
type = 203,
value = {
759
}
}
}
}
}
}
},
reward = {
itemIdList = {
905008
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1011
}
},
[10120001] = {
id = 10120001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1012,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1012
},
taskGroupId = 26001
},
[10120002] = {
id = 10120002,
desc = "累计摧毁1个建筑",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 1,
subConditionList = {
{
type = 209,
value = {
1012
}
},
{
type = 203,
value = {
751
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1012
},
taskGroupId = 26001
},
[10120003] = {
id = 10120003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1012,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1012
},
taskGroupId = 26001
},
[10120004] = {
id = 10120004,
desc = "单局内二技能累计命中20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 20,
subConditionList = {
{
type = 209,
value = {
1012
}
},
{
type = 203,
value = {
756
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1012
},
taskGroupId = 26001
},
[10120005] = {
id = 10120005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1012,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1012
}
},
[10120006] = {
id = 10120006,
desc = "单局内三技能累计命中30次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 30,
subConditionList = {
{
type = 209,
value = {
1012
}
},
{
type = 203,
value = {
759
}
}
}
}
}
}
},
reward = {
itemIdList = {
905009
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1012
}
},
[10130001] = {
id = 10130001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1013,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1013
},
taskGroupId = 26001
},
[10130002] = {
id = 10130002,
desc = "累计参与15次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1013
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1013
},
taskGroupId = 26001
},
[10130003] = {
id = 10130003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1013,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1013
},
taskGroupId = 26001
},
[10130004] = {
id = 10130004,
desc = "累计参与10次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 10,
subConditionList = {
{
type = 209,
value = {
1013
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1013
},
taskGroupId = 26001
},
[10130005] = {
id = 10130005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1013,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1013
}
},
[10130006] = {
id = 10130006,
desc = "单局内二技能累计命中100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 100,
subConditionList = {
{
type = 209,
value = {
1013
}
},
{
type = 203,
value = {
756
}
}
}
}
}
}
},
reward = {
itemIdList = {
905010
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1013
}
},
[10140001] = {
id = 10140001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1014,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1014
},
taskGroupId = 26001
},
[10140002] = {
id = 10140002,
desc = "累计摧毁1个建筑",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 1,
subConditionList = {
{
type = 209,
value = {
1014
}
},
{
type = 203,
value = {
751
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1014
},
taskGroupId = 26001
},
[10140003] = {
id = 10140003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1014,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1014
},
taskGroupId = 26001
},
[10140004] = {
id = 10140004,
desc = "单局内二技能累计命中20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 20,
subConditionList = {
{
type = 209,
value = {
1014
}
},
{
type = 203,
value = {
756
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1014
},
taskGroupId = 26001
},
[10140005] = {
id = 10140005,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1014,
10
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1014
}
},
[10140006] = {
id = 10140006,
desc = "单局内三技能累计造成5次击杀",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 5,
subConditionList = {
{
type = 209,
value = {
1014
}
},
{
type = 203,
value = {
760
}
}
}
}
}
}
},
reward = {
itemIdList = {
905012
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1014
}
},
[10150001] = {
id = 10150001,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1015,
3
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1015
},
taskGroupId = 26001
},
[10150002] = {
id = 10150002,
desc = "累计参与15次击杀（人头+助攻）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1015
}
},
{
type = 203,
value = {
752
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
5
}
},
extraConf = {
arenaHeroId = 1015
},
taskGroupId = 26001
},
[10150003] = {
id = 10150003,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 830,
value = 1,
subConditionList = {
{
type = 206,
value = {
1015,
7
}
}
}
}
}
}
},
extraConf = {
arenaHeroId = 1015
},
taskGroupId = 26001
},
[10150004] = {
id = 10150004,
desc = "单局内一技能累计命中15次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 15,
subConditionList = {
{
type = 209,
value = {
1015
}
},
{
type = 203,
value = {
753
}
}
}
}
}
}
},
reward = {
itemIdList = {
304001
},
numList = {
10
}
},
extraConf = {
arenaHeroId = 1015
},
taskGroupId = 26001
}
}

local mt = {
name = "试炼任务",
desc = "当前英雄达到指定熟练等级"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data