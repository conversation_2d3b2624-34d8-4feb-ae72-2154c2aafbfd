--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/W_玩法回流.xlsx: 回流任务

local data = {
[1801001] = {
id = 1801001,
name = "开启第一天解锁",
desc = "开启第一天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 0,
subConditionList = {
{
type = 605,
value = {
1
}
}
}
}
}
}
},
taskGroupId = 180001
},
[1801002] = {
id = 1801002,
name = "开启第二天解锁",
desc = "开启第二天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 1,
subConditionList = {
{
type = 605,
value = {
1
}
}
}
}
}
}
},
taskGroupId = 180002
},
[1801003] = {
id = 1801003,
name = "开启第三天解锁",
desc = "开启第三天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 2,
subConditionList = {
{
type = 605,
value = {
1
}
}
}
}
}
}
},
taskGroupId = 180003
},
[1801101] = {
id = 1801101,
name = "完成1局天天晋级赛（排位）",
desc = "完成1局天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
jumpId = 1052,
extraConf = {
taskIcon = "CDN:T_Common_Item_System_ModelCard_10"
},
taskGroupId = 180101,
unlockDescription = "第一天解锁"
},
[1801102] = {
id = 1801102,
name = "完成1局天天晋级赛（排位）",
desc = "完成1局天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
jumpId = 1052,
extraConf = {
taskIcon = "CDN:T_Common_Item_System_Bag_107"
},
taskGroupId = 180102,
unlockDescription = "第二天解锁"
},
[1801103] = {
id = 1801103,
name = "完成1局天天晋级赛（排位）",
desc = "完成1局天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
jumpId = 1052,
extraConf = {
taskIcon = "CDN:T_InGame_Icon_Prop88"
},
taskGroupId = 180103,
unlockDescription = "第三天解锁"
},
[1801201] = {
id = 1801201,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
203002
},
numList = {
3
}
},
taskGroupId = 180200
},
[1801202] = {
id = 1801202,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 180200
},
[1801203] = {
id = 1801203,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
340006
},
numList = {
1
}
},
taskGroupId = 180200
},
[1801204] = {
id = 1801204,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
200635
},
numList = {
1
}
},
taskGroupId = 180200
},
[1811001] = {
id = 1811001,
name = "开启第一天解锁",
desc = "开启第一天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 0,
subConditionList = {
{
type = 605,
value = {
2
}
}
}
}
}
}
},
taskGroupId = 181001
},
[1811002] = {
id = 1811002,
name = "开启第二天解锁",
desc = "开启第二天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 1,
subConditionList = {
{
type = 605,
value = {
2
}
}
}
}
}
}
},
taskGroupId = 181002
},
[1811003] = {
id = 1811003,
name = "开启第三天解锁",
desc = "开启第三天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 2,
subConditionList = {
{
type = 605,
value = {
2
}
}
}
}
}
}
},
taskGroupId = 181003
},
[1811101] = {
id = 1811101,
name = "完成1次峡谷5v5",
desc = "完成1次峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 50112,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 181101,
unlockDescription = "第一天解锁"
},
[1811102] = {
id = 1811102,
name = "完成1次峡谷5v5",
desc = "完成1次峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 50112,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 181102,
unlockDescription = "第二天解锁"
},
[1811103] = {
id = 1811103,
name = "完成1次峡谷5v5",
desc = "完成1次峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 50112,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 181103,
unlockDescription = "第三天解锁"
},
[1811201] = {
id = 1811201,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
203002
},
numList = {
3
}
},
taskGroupId = 181200
},
[1811202] = {
id = 1811202,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 181200
},
[1811203] = {
id = 1811203,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
340006
},
numList = {
1
}
},
taskGroupId = 181200
},
[1811204] = {
id = 1811204,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
200635
},
numList = {
1
}
},
taskGroupId = 181200
},
[1821001] = {
id = 1821001,
name = "开启第一天解锁",
desc = "开启第一天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 0,
subConditionList = {
{
type = 605,
value = {
3
}
}
}
}
}
}
},
taskGroupId = 182001
},
[1821002] = {
id = 1821002,
name = "开启第二天解锁",
desc = "开启第二天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 1,
subConditionList = {
{
type = 605,
value = {
3
}
}
}
}
}
}
},
taskGroupId = 182002
},
[1821003] = {
id = 1821003,
name = "开启第三天解锁",
desc = "开启第三天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 2,
subConditionList = {
{
type = 605,
value = {
3
}
}
}
}
}
}
},
taskGroupId = 182003
},
[1821101] = {
id = 1821101,
name = "完成1次谁是狼人",
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 1024,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 182101,
unlockDescription = "第一天解锁"
},
[1821102] = {
id = 1821102,
name = "完成1次谁是狼人",
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 1024,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 182102,
unlockDescription = "第二天解锁"
},
[1821103] = {
id = 1821103,
name = "完成1次谁是狼人",
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 1024,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 182103,
unlockDescription = "第三天解锁"
},
[1821201] = {
id = 1821201,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
203002
},
numList = {
3
}
},
taskGroupId = 182200
},
[1821202] = {
id = 1821202,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 182200
},
[1821203] = {
id = 1821203,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
340006
},
numList = {
1
}
},
taskGroupId = 182200
},
[1821204] = {
id = 1821204,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
200635
},
numList = {
1
}
},
taskGroupId = 182200
},
[1831001] = {
id = 1831001,
name = "开启第一天解锁",
desc = "开启第一天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 0,
subConditionList = {
{
type = 605,
value = {
4
}
}
}
}
}
}
},
taskGroupId = 183001
},
[1831002] = {
id = 1831002,
name = "开启第二天解锁",
desc = "开启第二天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 1,
subConditionList = {
{
type = 605,
value = {
4
}
}
}
}
}
}
},
taskGroupId = 183002
},
[1831003] = {
id = 1831003,
name = "开启第三天解锁",
desc = "开启第三天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 2,
subConditionList = {
{
type = 605,
value = {
4
}
}
}
}
}
}
},
taskGroupId = 183003
},
[1831101] = {
id = 1831101,
name = "完成1局天天晋级赛（排位）",
desc = "完成1局天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
203002
},
numList = {
3
}
},
jumpId = 1052,
extraConf = {
taskIcon = "CDN:T_Common_Item_System_ModelCard_10"
},
taskGroupId = 183101,
unlockDescription = "第一天解锁"
},
[1831102] = {
id = 1831102,
name = "完成1局天天晋级赛（排位）",
desc = "完成1局天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
jumpId = 1052,
extraConf = {
taskIcon = "CDN:T_Common_Item_System_Bag_107"
},
taskGroupId = 183102,
unlockDescription = "第二天解锁"
},
[1831103] = {
id = 1831103,
name = "完成1局天天晋级赛（排位）",
desc = "完成1局天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
340006
},
numList = {
1
}
},
jumpId = 1052,
extraConf = {
taskIcon = "CDN:T_InGame_Icon_Prop88"
},
taskGroupId = 183103,
unlockDescription = "第三天解锁"
},
[1831201] = {
id = 1831201,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
203002
},
numList = {
3
}
},
taskGroupId = 183200
},
[1831202] = {
id = 1831202,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 183200
},
[1831203] = {
id = 1831203,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
340006
},
numList = {
1
}
},
taskGroupId = 183200
},
[1831204] = {
id = 1831204,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
200635
},
numList = {
1
}
},
taskGroupId = 183200
},
[1841001] = {
id = 1841001,
name = "开启第一天解锁",
desc = "开启第一天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 0,
subConditionList = {
{
type = 605,
value = {
5
}
}
}
}
}
}
},
taskGroupId = 184001
},
[1841002] = {
id = 1841002,
name = "开启第二天解锁",
desc = "开启第二天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 1,
subConditionList = {
{
type = 605,
value = {
5
}
}
}
}
}
}
},
taskGroupId = 184002
},
[1841003] = {
id = 1841003,
name = "开启第三天解锁",
desc = "开启第三天解锁",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 314,
value = 2,
subConditionList = {
{
type = 605,
value = {
5
}
}
}
}
}
}
},
taskGroupId = 184003
},
[1841101] = {
id = 1841101,
name = "完成1次大王别抓我",
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 1018,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 184101,
unlockDescription = "第一天解锁"
},
[1841102] = {
id = 1841102,
name = "完成1次大王别抓我",
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 1018,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 184102,
unlockDescription = "第二天解锁"
},
[1841103] = {
id = 1841103,
name = "完成1次大王别抓我",
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
15
}
},
jumpId = 1018,
extraConf = {
taskIcon = "T_Regression_Img_Prop01"
},
taskGroupId = 184103,
unlockDescription = "第三天解锁"
},
[1841201] = {
id = 1841201,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
203002
},
numList = {
3
}
},
taskGroupId = 184200
},
[1841202] = {
id = 1841202,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 184200
},
[1841203] = {
id = 1841203,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
340006
},
numList = {
1
}
},
taskGroupId = 184200
},
[1841204] = {
id = 1841204,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3495
}
}
}
}
}
}
},
reward = {
itemIdList = {
200635
},
numList = {
1
}
},
taskGroupId = 184200
}
}

local mt = {
name = "兑换"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data