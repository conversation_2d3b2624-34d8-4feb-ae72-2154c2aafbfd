com.tencent.wea.xlsRes.table_ChatTypeChatGroupConfigData
excel/xls/L_聊天频道.xlsx sheet:开关配置
rows {
  id: 1
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_Private"
}
rows {
  id: 2
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_TeamGroup"
}
rows {
  id: 3
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_SideGroup"
}
rows {
  id: 4
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_BattleChat"
}
rows {
  id: 8
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_World"
}
rows {
  id: 9
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_Club"
}
rows {
  id: 11
  switch: 0
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 4070880000
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
}
rows {
  id: 12
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_Lobby"
}
rows {
  id: 13
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_LobbyFacility"
}
rows {
  id: 14
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_NewStar"
}
rows {
  id: 15
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_CustomRoom"
}
rows {
  id: 16
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_Xiaowo"
}
rows {
  id: 17
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_UgcCustomRoom"
}
rows {
  id: 18
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_CompetitionRoom"
}
rows {
  id: 19
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_AIChat"
}
rows {
  id: 20
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_Farm"
}
rows {
  id: 22
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_Stranger"
}
rows {
  id: 23
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_FarmHouse"
}
rows {
  id: 24
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_FarmCommunityChannel"
}
rows {
  id: 25
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_ArenaCommunityChannel"
}
rows {
  id: 26
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_WolfKillCommunityChannel"
}
rows {
  id: 27
  switch: 1
  minVersionStr: "0.0.0.0"
  maxVersionStr: "9999.9999.9999.9999"
  forbidBeginTime {
    seconds: 1640966400
  }
  forbidEndTime {
    seconds: 1640966400
  }
  forbidKeyTips: "NewChat_ChatTypeForbid"
  featureLockKey: "FOT_ChatType_CT_TradingCardChannel"
}
