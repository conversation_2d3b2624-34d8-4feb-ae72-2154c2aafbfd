com.tencent.wea.xlsRes.table_ChaseInLevelTargetCfg
excel/xls/Chase/D_大王玩法局内目标.xlsx sheet:大王玩法局内目标
rows {
  ID: 1
  MatchType: 353
  SideId: 0
  Condition: "1,[4],<"
  Text: "激活<ChaseTaskList>{1}/4</>星路仪,化身{名字}"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 2
  MatchType: 353
  SideId: 0
  Condition: "1,[4],>=;3,[0],="
  Text: "前往化身<ChaseTaskList>{名字}</>"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 3
  MatchType: 353
  SideId: 0
  Condition: "1,[4],>=;1,2,<"
  Text: "激活<ChaseTaskList>{1}/{2}</>星路仪,打开大门"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 4
  MatchType: 353
  SideId: 0
  Condition: "1,2,>=;4,[0],="
  Text: "前往打开<ChaseTaskList>大门</>"
  Priority: 4
  StyleId: "1"
}
rows {
  ID: 5
  MatchType: 353
  SideId: 0
  Condition: "4,[0],>;3,[0],>"
  Text: "护送<ChaseTaskList>{名字}</>前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 6
  MatchType: 353
  SideId: 1
  Condition: "1,2,<"
  Text: "阻止<ChaseTaskList>{1}/{2}</>星路仪激活"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 7
  MatchType: 353
  SideId: 1
  Condition: "4,[0],=;3,[0],>"
  Text: "前往击倒<ChaseTaskList>{名字}</>"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 8
  MatchType: 353
  SideId: 1
  Condition: "1,2,>=;4,[0],="
  Text: "阻止打开<ChaseTaskList>大门</>"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 9
  MatchType: 353
  SideId: 1
  Condition: "4,[0],>;3,[0],="
  Text: "阻止化身<ChaseTaskList>{名字}</>"
  Priority: 4
  StyleId: "1"
}
rows {
  ID: 10
  MatchType: 353
  SideId: 1
  Condition: "4,[0],>;3,[0],>"
  Text: "阻止<ChaseTaskList>{名字}</>前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 11
  MatchType: 350
  SideId: 0
  Condition: "1,2,<"
  Text: "激活<ChaseTaskList>{1}/{2}</>星路仪"
  Priority: 1
  StyleId: "1"
}
rows {
  ID: 12
  MatchType: 350
  SideId: 0
  Condition: "1,2,>=;4,[0],="
  Text: "前往打开大门"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 13
  MatchType: 350
  SideId: 0
  Condition: "4,[0],>"
  Text: "前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 14
  MatchType: 350
  SideId: 1
  Condition: "1,2,<"
  Text: "阻止<ChaseTaskList>{1}/{2}</>星路仪激活"
  Priority: 1
  StyleId: "1"
}
rows {
  ID: 15
  MatchType: 350
  SideId: 1
  Condition: "1,2,>=;4,[0],="
  Text: "阻止打开大门"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 16
  MatchType: 350
  SideId: 1
  Condition: "4,[0],>"
  Text: "阻止星宝前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 17
  MatchType: 352
  SideId: 0
  Condition: "1,2,<"
  Text: "激活<ChaseTaskList>{1}/{2}</>星路仪"
  Priority: 1
  StyleId: "1"
}
rows {
  ID: 18
  MatchType: 352
  SideId: 0
  Condition: "1,2,>=;4,[0],="
  Text: "前往打开大门"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 19
  MatchType: 352
  SideId: 0
  Condition: "4,[0],>"
  Text: "前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 20
  MatchType: 352
  SideId: 1
  Condition: "1,2,<"
  Text: "阻止<ChaseTaskList>{1}/{2}</>星路仪激活"
  Priority: 1
  StyleId: "1"
}
rows {
  ID: 21
  MatchType: 352
  SideId: 1
  Condition: "1,2,>=;4,[0],="
  Text: "阻止打开大门"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 22
  MatchType: 352
  SideId: 1
  Condition: "4,[0],>"
  Text: "阻止星宝前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 23
  MatchType: 354
  SideId: 0
  Condition: "1,[4],<"
  Text: "激活<ChaseTaskList>{1}/4</>星路仪,化身{名字}"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 24
  MatchType: 354
  SideId: 0
  Condition: "1,[4],>=;3,[0],="
  Text: "前往化身<ChaseTaskList>{名字}</>"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 25
  MatchType: 354
  SideId: 0
  Condition: "1,[4],>=;1,2,<"
  Text: "激活<ChaseTaskList>{1}/{2}</>星路仪,打开大门"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 26
  MatchType: 354
  SideId: 0
  Condition: "1,2,>=;4,[0],="
  Text: "前往打开<ChaseTaskList>大门</>"
  Priority: 4
  StyleId: "1"
}
rows {
  ID: 27
  MatchType: 354
  SideId: 0
  Condition: "4,[0],>;3,[0],>"
  Text: "护送<ChaseTaskList>{名字}</>前往大门逃离"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 28
  MatchType: 354
  SideId: 1
  Condition: "1,2,<"
  Text: "阻止<ChaseTaskList>{1}/{2}</>星路仪激活"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 29
  MatchType: 354
  SideId: 1
  Condition: "4,[0],=;3,[0],>"
  Text: "前往击倒<ChaseTaskList>{名字}</>"
  Priority: 3
  StyleId: "1"
}
rows {
  ID: 30
  MatchType: 354
  SideId: 1
  Condition: "1,2,>=;4,[0],="
  Text: "阻止打开<ChaseTaskList>大门</>"
  Priority: 2
  StyleId: "1"
}
rows {
  ID: 31
  MatchType: 354
  SideId: 1
  Condition: "4,[0],>;3,[0],="
  Text: "阻止化身<ChaseTaskList>{名字}</>"
  Priority: 4
  StyleId: "1"
}
rows {
  ID: 32
  MatchType: 354
  SideId: 1
  Condition: "4,[0],>;3,[0],>"
  Text: "阻止<ChaseTaskList>{名字}</>前往大门逃离"
  Priority: 3
  StyleId: "1"
}
