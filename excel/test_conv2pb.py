#from conv2pb import main
import multiprocessing

from conv2pb import main
import ImportConverterHelper
import ConverterUtility
from convExcelReader import ConvLoadExcel

# convExcels = {
#     "./xls/U_UGC编辑器.xlsx" : [],
#     "./xls/Arena/Buff/B_ArenaBuff表_花木兰.xlsx" : [],
#     "./xls/Arena/Buff/B_ArenaBuff表_孙悟空.xlsx" : [],
#     #"./xls/G_功能解锁.xlsx" : [],
#     #"./xls/P_排行榜.xlsx" : [],
#     #"./xls/S_商城_商品.xlsx" : [],
#     #"./xls/B_版本兼容组.xlsx" : [],
#     #"./xls/W_玩法模式_Chase.xlsx" : [],
#     #"./xls/D_道具表.xlsx" : [],
#     #"./xls/H_活动中心配置.xlsx" : [],
#     #"./xls/H_活动_开学返利.xlsx" : [],
#     #"./xls/H_活动_开学返利.xlsx" : [],
#     #"./xls/H_活动中心配置.xlsx" : [],
# }

import os
# srcExcel = "./xls/W_文本表_文本配置_System.xlsx"
# srcExcel = "./xls/D_道具表.xlsx"
# srcExcel = "./xls/C_成就系统.xlsx"
# srcExcel = "./xls/R_任务表_策划专用.xlsx"
# srcExcel = "./xls/H_活动_UGC新年.xlsx"
# srcExcel = "./xls/C_充值配置表.xlsx "
# srcExcel = "./xls/S_商城_兑换.xlsx "
# srcExcel = "./xls/X_新手引导表_acm.xlsx"


srcExcel = "./xls"
convExcels = dict()

if os.path.isfile(srcExcel):
    convExcels[srcExcel] = []
else:
    for path, dirs, files in os.walk(srcExcel):
        for fileName in files:
            if fileName.startswith('#') or fileName.startswith('~'):
                continue
            if not fileName.endswith('.xls') \
                and not fileName.endswith('.xlsx') \
                    and not fileName.endswith('.xlsm'):
                continue

            filePath = path + '/' + fileName#os.path.join(path, fileName)
            convExcels.setdefault(filePath, [])


# client
# main(convExcels, False, False, "LetsGo", None) # server
#
convExcels = {
    # "./xls/X_新手引导表_acm.xlsx" : [],
    #"./xls/H_活动中心配置.xlsx" : [],
    # "./xls/R_任务表_策划专用.xlsx" : [],
    # "./xls/D_道具表.xlsx" : [],
    # "./xls/D_道具表_时装.xlsm" : [],
    "./xls/W_文本表_文本配置_System.xlsx": [],
    # "./xls/S_商城_兑换.xlsx" : []
    # "./xls/H_活动_大富翁.xlsx" : []
    # "./xls/H_活动中心配置_特色玩法运营.xlsx" : []
    # "./xls/U_UGC技能编辑器.xlsx" : []
    # "./xls/X_新手奖励系统.xlsx" : []
    # "./xls\A_AIGCNPC配置.xlsx": []
    # "./xls/W_文本表_文本配置.xlsx": []

    ## arena
    # "./xls/G_HZ关卡配置.xlsx": []
    # "./xls/Q_HZ全局配置表.xlsx": []
    # "./xls/U_UGC物件表_游玩.xlsx": []
    # "./xls/D_道具表_UGC徽章.xlsm": [],
    # "./xls/C_产出控制监控预警.xlsx": [],
    #"./xls/C_超级红包配置.xlsx": []
    # "./xls/C_充值配置表.xlsx": ["页签顺序"],
    # "./xls/Y_娱乐向导.xlsx": []
    # "./xls/H_活动_评分引导.xlsx": ["评分引导midasId映射表"]
    # "./xls/H_活动_建造花房.xlsx": ["向导对话"]
    # "./xls/P_排位段位_main_主玩法.xlsx": ["赛季重置邮件"]
    # ./xls/D_道具表_时装.xlsm": ["套装图鉴"]
    # "./xls/W_玩法模式_Chase.xlsx": ["玩法匹配"]
    # "./xls/W_文本表_文本配置_NR3E.xlsx ": []
    # "./xls/D_道具表_个性化_动作.xlsx": []
    # "./xls/G_关卡掉落_hok.xlsx": ["关卡掉落配置"]
    # "./xls/Arena/ArenaInGame/Buff/B_ArenaBuff表_安琪拉.xlsx": []
    # "./xls/H_活动_积攒福气.xlsx": []
    # "./xls/R_肉鸽玩法表.xlsx": ["关卡流程表"]
    # "./xls/U_UGC物件表_游玩.xlsx": ["积木"]
    # "./xls/K_卡牌.xlsx": ["其他杂项配置"]
    # "./xls/P_排行榜.xlsx": ["榜单奖励"]
    # "./xls/H_活动_评分引导.xlsx": ["评分引导midasId映射表"]
    # "./xls/X_新手引导表_chase.xlsx ": []
    # "./xls/D_道具表_小窝.xlsx": []
    # "./xls/C_抽奖奖池_特色玩法": ["幸运值配置"]
    # "./xls/G_gm配置.xlsx": []
    # "./xls/P_排位段位&保分配置_JS_极速飞车.xlsx": ["段位信息"]
    # "./xls/T_通行证.xlsx": ["通行证任务配置值"]
    # "./xls/Arena/ArenaInGame/技能公共表/J_Arena技能公共表_阿轲.xlsx": []
    # "./xls/W_玩法模式_杂项其它.xlsx": ["副玩法成绩"]
    # "./xls/Arena/ArenaInGame/Buff/B_ArenaBuff表_刘禅.xlsx": []
    # "./xls/C_抽奖奖池_特色玩法.xlsx": ["大保底抽数掉率"]
    # "./xls/C_成就系统_成就任务_主表.xlsx": []

}

def _ReadExcel(excel_path):
    abs_path = os.path.abspath("./xls/D_道具表_小窝.xlsx")
    excelBook = ConvLoadExcel(abs_path)
    sheet = excelBook.SheetByName("小窝物件")
    data = {}
    raw_data = sheet.ReadData()
    for row in range(3, sheet.nrows):
        id = raw_data[row][1]
        row_data = data.get(id, {})
        data[id] = row_data
        for col in range(sheet.ncols):
            data[id][col] = raw_data[row][col]
    print("xx")

def Main():
    global convExcels
    feature_dir = ConverterUtility.GetFeatureRootPath('main')
    feature_dir = os.path.join(feature_dir, "excel")
    error_queue = multiprocessing.Queue()


    if False:
        convExcels = {}
        for path, dirs, files in os.walk(os.path.join(feature_dir, "xls")):
            for fileName in files:
                if fileName.startswith('#') or fileName.startswith('~'):
                    continue
                if not fileName.endswith('.xls') \
                        and not fileName.endswith('.xlsx') \
                        and not fileName.endswith('.xlsm'):
                    continue

                filePath = os.path.join(path, fileName)
                convExcels.setdefault(filePath, [])
    import Logger
    # main(feature_dir, convExcels, False, False, "LetsGo", None, IsDebugCI=True)
    Logger.PerformanceTagBegin("All", True)
    # main(feature_dir, convExcels, False, False, "LetsGo", None, IsDebugCI=True)
    while True:
        p = multiprocessing.Process(target=main, args=(feature_dir, convExcels, False, True, "LetsGo", None, False, False, True, True, error_queue))
        p.start()
        p.join()
        if not error_queue.empty():
            error_message = error_queue.get()
            if str.startswith(error_message, "code:0"):
                break

    Logger.PerformanceTagEnd("All", True)

def WriteAllToErrorText(is_client):
    error_xlsx = []

    feature_dir = ConverterUtility.GetFeatureRootPath('main')
    feature_dir = os.path.join(feature_dir, "excel")
    for path, dirs, files in os.walk(os.path.join(feature_dir, "xls")):
        for fileName in files:
            if fileName.startswith('#') or fileName.startswith('~'):
                continue
            if not fileName.endswith('.xls') \
                    and not fileName.endswith('.xlsx') \
                    and not fileName.endswith('.xlsm'):
                continue
            filePath = os.path.join(path, fileName)
            error_xlsx.append(filePath)

    path = "error_xlsx_server.txt"
    if is_client:
        path = "error_xlsx.txt"

    with open(path, "w", encoding="utf-8") as f:
        f.write("\n".join(error_xlsx))


def ConvertErrorExcel(is_client):
    feature_dir = ConverterUtility.GetFeatureRootPath('main')
    feature_dir = os.path.join(feature_dir, "excel")
    error_queue = multiprocessing.Queue()

    path = "error_xlsx_server.txt"
    if is_client:
        path = "error_xlsx.txt"
    suc_path = "suc_xlsx_server.txt"
    if is_client:
        suc_path = "suc_xlsx.txt"
    convExcels = {}
    error_xlsx = []
    suc_xlsx = []
    suc_xlsx_map = {}
    if os.path.exists(suc_path):
        with open(suc_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
            for line in lines:
                filePath = line.strip()
                suc_xlsx_map[filePath] = True

    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()
        for line in lines:
            filePath = line.strip()
            if filePath in suc_xlsx_map:
                continue

            convExcels.setdefault(filePath, [])
            if len(convExcels) > 10:
                main(feature_dir, convExcels, False, is_client, "LetsGo", None, error_queue=error_queue, IsDebugCI=True)
                # p = multiprocessing.Process(target=main, args=(
                # feature_dir, convExcels, False, is_client, "LetsGo", None, False, False, True, True, error_queue, True))
                # p.start()
                # p.join()
                has_error = False
                if error_queue.empty():
                    for excel, _ in convExcels.items():
                        error_xlsx.append(excel)
                        has_error = True
                else:
                    error_message = error_queue.get()
                    if not str.startswith(error_message, "code:0"):
                        for excel, _ in convExcels.items():
                            error_xlsx.append(excel)
                        has_error = True
                    print("Error in subprocess:\n", "excel:", fileName, error_message)

                while not error_queue.empty():
                    error_message = error_queue.get()
                    print("Error in subprocess:\n", error_message)
                if not has_error:
                    with open(suc_path, "a", encoding="utf-8") as fs:
                        fs.write("\n")
                        fs.write("\n".join(convExcels.keys()))
                convExcels = {}


    with open(path, "w", encoding="utf-8") as f:
        f.write("\n".join(error_xlsx))



if __name__ == '__main__':
    # _ReadExcel("./xls/D_道具表_小窝.xlsx")s
    Main()
    # WriteAllToErrorText(False)
    # ConvertErrorExcel(True)
    print("xxx")
