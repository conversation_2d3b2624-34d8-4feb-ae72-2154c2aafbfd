------------------------------------------------------------------------
-- ddMinus.decTest -- decDouble 0-x                                   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decDoubles.
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- Sanity check
ddmns001 minus       +7.50  -> -7.50

-- Infinities
ddmns011 minus  Infinity    -> -Infinity
ddmns012 minus  -Infinity   -> Infinity

-- NaNs, 0 payload
ddmns021 minus         NaN  -> NaN
ddmns022 minus        -NaN  -> -NaN
ddmns023 minus        sNaN  -> NaN  Invalid_operation
ddmns024 minus       -sNaN  -> -NaN Invalid_operation

-- NaNs, non-0 payload
ddmns031 minus       NaN13  -> NaN13
ddmns032 minus      -NaN13  -> -NaN13
ddmns033 minus      sNaN13  -> NaN13   Invalid_operation
ddmns034 minus     -sNaN13  -> -NaN13  Invalid_operation
ddmns035 minus       NaN70  -> NaN70
ddmns036 minus      -NaN70  -> -NaN70
ddmns037 minus      sNaN101 -> NaN101  Invalid_operation
ddmns038 minus     -sNaN101 -> -NaN101 Invalid_operation

-- finites
ddmns101 minus          7   -> -7
ddmns102 minus         -7   -> 7
ddmns103 minus         75   -> -75
ddmns104 minus        -75   -> 75
ddmns105 minus       7.50   -> -7.50
ddmns106 minus      -7.50   -> 7.50
ddmns107 minus       7.500  -> -7.500
ddmns108 minus      -7.500  -> 7.500

-- zeros
ddmns111 minus          0   -> 0
ddmns112 minus         -0   -> 0
ddmns113 minus       0E+4   -> 0E+4
ddmns114 minus      -0E+4   -> 0E+4
ddmns115 minus     0.0000   -> 0.0000
ddmns116 minus    -0.0000   -> 0.0000
ddmns117 minus      0E-141  -> 0E-141
ddmns118 minus     -0E-141  -> 0E-141

-- full coefficients, alternating bits
ddmns121 minus  2682682682682682         -> -2682682682682682
ddmns122 minus  -2682682682682682        -> 2682682682682682
ddmns123 minus  1341341341341341         -> -1341341341341341
ddmns124 minus  -1341341341341341        -> 1341341341341341

-- Nmax, Nmin, Ntiny
ddmns131 minus  9.999999999999999E+384   -> -9.999999999999999E+384
ddmns132 minus  1E-383                   -> -1E-383
ddmns133 minus  1.000000000000000E-383   -> -1.000000000000000E-383
ddmns134 minus  1E-398                   -> -1E-398 Subnormal

ddmns135 minus  -1E-398                  -> 1E-398 Subnormal
ddmns136 minus  -1.000000000000000E-383  -> 1.000000000000000E-383
ddmns137 minus  -1E-383                  -> 1E-383
ddmns138 minus  -9.999999999999999E+384  -> 9.999999999999999E+384
