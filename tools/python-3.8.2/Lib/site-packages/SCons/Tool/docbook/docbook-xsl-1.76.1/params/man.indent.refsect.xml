<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="man.indent.refsect">
<refmeta>
<refentrytitle>man.indent.refsect</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">boolean</refmiscinfo>
</refmeta>
<refnamediv>
<refname>man.indent.refsect</refname>
<refpurpose>Adjust indentation of refsect* and refsection?</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="man.indent.refsect.frag">
<xsl:param name="man.indent.refsect" select="0"/></src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>If the value of <parameter>man.indent.refsect</parameter> is
non-zero, the width of the left margin for
<tag>refsect1</tag>, <tag>refsect2</tag> and
<tag>refsect3</tag> contents and titles (and first-level,
second-level, and third-level nested
<tag>refsection</tag>instances) is adjusted by the value of
the <parameter>man.indent.width</parameter> parameter. With
<parameter>man.indent.width</parameter> set to its default value of
<literal>3n</literal>, the main results are that:

<itemizedlist>
  <listitem>
    <para>contents of <tag>refsect1</tag> are output with a
    left margin of three characters instead the roff default of seven
    or eight characters</para>
  </listitem>
  <listitem>
    <para>contents of <tag>refsect2</tag> are displayed in
    console output with a left margin of six characters instead the of
    the roff default of seven characters</para>
  </listitem>
  <listitem>
    <para> the contents of <tag>refsect3</tag> and nested
    <tag>refsection</tag> instances are adjusted
    accordingly.</para>
  </listitem>
</itemizedlist>

If instead the value of <parameter>man.indent.refsect</parameter> is
zero, no margin adjustment is done for <literal>refsect*</literal>
output.</para>

<tip>
  <para>If your content is primarly comprised of
  <tag>refsect1</tag> and <tag>refsect2</tag> content
  (or the <tag>refsection</tag> equivalent) – with few or
  no <tag>refsect3</tag> or lower nested sections , you may be
  able to “conserve” space in your output by setting
  <parameter>man.indent.refsect</parameter> to a non-zero value. Doing
  so will “squeeze” the left margin in such as way as to provide an
  additional four characters of “room” per line in
  <tag>refsect1</tag> output. That extra room may be useful
  if, for example, you have many verbatim sections with long lines in
  them.</para>
</tip>

</refsection>
</refentry>
