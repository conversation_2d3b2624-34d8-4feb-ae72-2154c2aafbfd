<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="html.longdesc">
<refmeta>
<refentrytitle>html.longdesc</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">boolean</refmiscinfo>
</refmeta>
<refnamediv>
<refname>html.longdesc</refname>
<refpurpose>Should longdesc URIs be created?</refpurpose>
</refnamediv>
<refsynopsisdiv>
<src:fragment xml:id="html.longdesc.frag">
<xsl:param name="html.longdesc" select="1"/>
</src:fragment>
</refsynopsisdiv>
<refsection><info><title>Description</title></info>
<para>If non-zero, HTML files will be created for the
<literal>longdesc</literal> attribute. These files
are created from the <tag>textobject</tag>s in
<tag>mediaobject</tag>s and
<tag>inlinemediaobject</tag>.
</para>
</refsection>
</refentry>
