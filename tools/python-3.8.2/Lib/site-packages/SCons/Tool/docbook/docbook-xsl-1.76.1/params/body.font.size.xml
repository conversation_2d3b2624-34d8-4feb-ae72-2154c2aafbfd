<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="body.font.size">
<refmeta>
<refentrytitle>body.font.size</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">length</refmiscinfo>
</refmeta>
<refnamediv>
<refname>body.font.size</refname>
<refpurpose>Specifies the default font size for body text</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="body.font.size.frag">
<xsl:param name="body.font.size">
 <xsl:value-of select="$body.font.master"/><xsl:text>pt</xsl:text>
</xsl:param></src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>The body font size is specified in two parameters
(<parameter>body.font.master</parameter> and <parameter>body.font.size</parameter>)
so that math can be performed on the font size by XSLT.
</para>

</refsection>
</refentry>
