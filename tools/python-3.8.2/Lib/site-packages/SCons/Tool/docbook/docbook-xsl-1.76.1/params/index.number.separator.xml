<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="index.number.separator">
<refmeta>
<refentrytitle>index.number.separator</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">string</refmiscinfo>
</refmeta>
<refnamediv>
<refname>index.number.separator</refname>
<refpurpose>Override for punctuation separating page numbers in index</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="index.number.separator.frag">
<xsl:param name="index.number.separator"></xsl:param>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>This parameter permits you to override the text to insert between
page references in a formatted index entry.  Typically 
that would be a comma and a space.
</para>

<para>Because this text may be locale dependent,
this parameter's value is normally taken from a gentext
template named 'number-separator' in the
context 'index' in the stylesheet
locale file for the language
of the current document.
This parameter can be used to override the gentext string,
and would typically be used on the command line.
This parameter would apply to all languages.
</para>

<para>So this text string can be customized in two ways.
You can reset the default gentext string using
the <parameter>local.l10n.xml</parameter> parameter, or you can
override the gentext with the content of this parameter.
The content can be a simple string, or it can be
something more complex such as a call-template.
</para>

<para>In HTML index output, section title references are used instead of
page number references.  This punctuation appears between
such section titles in an HTML index.
</para>

</refsection>
</refentry>
