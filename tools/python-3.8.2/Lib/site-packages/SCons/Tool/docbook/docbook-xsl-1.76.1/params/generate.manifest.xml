<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="generate.manifest">
  <refmeta>
    <refentrytitle>generate.manifest</refentrytitle>
    <refmiscinfo class="other" otherclass="datatype">boolean</refmiscinfo>
  </refmeta>
  <refnamediv>
    <refname>generate.manifest</refname>
    <refpurpose>Generate a manifest file?</refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <src:fragment xml:id="generate.manifest.frag"><xsl:param name="generate.manifest" select="0"/></src:fragment>
  </refsynopsisdiv>

  <refsection><info><title>Description</title></info>

    <para>If non-zero, a list of HTML files generated by the
      stylesheet transformation is written to the file named by
      the <parameter>manifest</parameter> parameter.</para>

  </refsection>
</refentry>
