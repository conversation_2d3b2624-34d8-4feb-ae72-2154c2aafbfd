<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="autolayout-file">
<refmeta>
<refentrytitle>autolayout-file</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">filename</refmiscinfo>
</refmeta>
<refnamediv>
<refname>autolayout-file</refname>
<refpurpose>Identifies the autolayout.xml file</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="autolayout-file.frag">
<xsl:param name="autolayout-file">autolayout.xml</xsl:param>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>
<para>When the source pages are spread over several directories, this
parameter can be set (for example, from the command line of a batch-mode
XSLT processor) to indicate the location of the autolayout.xml file.</para>
<para>FIXME: for browser-based use, there needs to be a PI for this...
</para>
</refsection>
</refentry>
