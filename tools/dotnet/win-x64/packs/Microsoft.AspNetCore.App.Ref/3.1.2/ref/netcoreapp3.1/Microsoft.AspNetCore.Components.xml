<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Components</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Components.BindConverter">
            <summary>
            Performs conversions during binding.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.String,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.Boolean},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.Int32},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Int64,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.Int64},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Single,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.Single},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Double,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.Double},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Decimal,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> for inclusion in an attribute.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.Decimal},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.DateTime,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.DateTime,System.String,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="format">The format to use. Provided to <see cref="M:System.DateTimeOffset.ToString(System.String,System.IFormatProvider)"/>.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.DateTime},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.DateTime},System.String,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="format">The format to use. Provided to <see cref="M:System.DateTime.ToString(System.String,System.IFormatProvider)"/>.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.DateTimeOffset,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.DateTimeOffset,System.String,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="format">The format to use. Provided to <see cref="M:System.DateTimeOffset.ToString(System.String,System.IFormatProvider)"/>.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.DateTimeOffset},System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue(System.Nullable{System.DateTimeOffset},System.String,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="format">The format to use. Provided to <see cref="M:System.DateTimeOffset.ToString(System.String,System.IFormatProvider)"/>.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.FormatValue``1(``0,System.Globalization.CultureInfo)">
            <summary>
            Formats the provided <paramref name="value"/> as a <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">
            The <see cref="T:System.Globalization.CultureInfo"/> to use while formatting. Defaults to <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>.
            </param>
            <returns>The formatted value.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToString(System.Object,System.Globalization.CultureInfo,System.String@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.String"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToBool(System.Object,System.Globalization.CultureInfo,System.Boolean@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.Boolean"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableBool(System.Object,System.Globalization.CultureInfo,System.Nullable{System.Boolean}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.Boolean"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToInt(System.Object,System.Globalization.CultureInfo,System.Int32@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.Int32"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableInt(System.Object,System.Globalization.CultureInfo,System.Nullable{System.Int32}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.Int32"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToLong(System.Object,System.Globalization.CultureInfo,System.Int64@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.Int64"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableLong(System.Object,System.Globalization.CultureInfo,System.Nullable{System.Int64}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.Int64"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToFloat(System.Object,System.Globalization.CultureInfo,System.Single@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.Single"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableFloat(System.Object,System.Globalization.CultureInfo,System.Nullable{System.Single}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.Single"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToDouble(System.Object,System.Globalization.CultureInfo,System.Double@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.Double"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableDouble(System.Object,System.Globalization.CultureInfo,System.Nullable{System.Double}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.Double"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToDecimal(System.Object,System.Globalization.CultureInfo,System.Decimal@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.Decimal"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableDecimal(System.Object,System.Globalization.CultureInfo,System.Nullable{System.Decimal}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.Decimal"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToDateTime(System.Object,System.Globalization.CultureInfo,System.DateTime@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.DateTime"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToDateTime(System.Object,System.Globalization.CultureInfo,System.String,System.DateTime@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.DateTime"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="format">The format string to use in conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableDateTime(System.Object,System.Globalization.CultureInfo,System.Nullable{System.DateTime}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.DateTime"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableDateTime(System.Object,System.Globalization.CultureInfo,System.String,System.Nullable{System.DateTime}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.DateTime"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="format">The format string to use in conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToDateTimeOffset(System.Object,System.Globalization.CultureInfo,System.DateTimeOffset@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.DateTimeOffset"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToDateTimeOffset(System.Object,System.Globalization.CultureInfo,System.String,System.DateTimeOffset@)">
            <summary>
            Attempts to convert a value to a <see cref="T:System.DateTimeOffset"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="format">The format string to use in conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableDateTimeOffset(System.Object,System.Globalization.CultureInfo,System.Nullable{System.DateTimeOffset}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.DateTimeOffset"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertToNullableDateTimeOffset(System.Object,System.Globalization.CultureInfo,System.String,System.Nullable{System.DateTimeOffset}@)">
            <summary>
            Attempts to convert a value to a nullable <see cref="T:System.DateTimeOffset"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="format">The format string to use in conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindConverter.TryConvertTo``1(System.Object,System.Globalization.CultureInfo,``0@)">
            <summary>
            Attempts to convert a value to a value of type <typeparamref name="T"/>.
            </summary>
            <param name="obj">The object to convert.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use for conversion.</param>
            <param name="value">The converted value.</param>
            <returns><c>true</c> if conversion is successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.BindElementAttribute">
            <summary>
            Configures options for binding specific element types.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.BindElementAttribute.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.BindElementAttribute"/>.
            </summary>
            <param name="element">The tag name of the element.</param>
            <param name="suffix">The suffix value. For example, set this to <code>value</code> for <code>bind-value</code>, or set this to <code>null</code> for <code>bind</code>.</param>
            <param name="valueAttribute">The name of the value attribute to be bound.</param>
            <param name="changeAttribute">The name of an attribute that will register an associated change event.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.BindElementAttribute.Element">
            <summary>
            Gets the tag name of the element.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.BindElementAttribute.Suffix">
            <summary>
            Gets the suffix value.
            For example, this will be <code>value</code> to mean <code>bind-value</code>, or <code>null</code> to mean <code>bind</code>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.BindElementAttribute.ValueAttribute">
            <summary>
            Gets the name of the value attribute to be bound.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.BindElementAttribute.ChangeAttribute">
            <summary>
            Gets the name of an attribute that will register an associated change event.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.CascadingParameterAttribute">
            <summary>
            Denotes the target member as a cascading component parameter. Its value will be
            supplied by the closest ancestor <see cref="T:Microsoft.AspNetCore.Components.CascadingValue`1"/> component that
            supplies values with a compatible type and name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.CascadingParameterAttribute.Name">
             <summary>
             If specified, the parameter value will be supplied by the closest
             ancestor <see cref="T:Microsoft.AspNetCore.Components.CascadingValue`1"/> that supplies a value with
             this name.
            
             If null, the parameter value will be supplied by the closest ancestor
             <see cref="T:Microsoft.AspNetCore.Components.CascadingValue`1"/>  that supplies a value with a compatible
             type.
             </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.CascadingValue`1">
            <summary>
            A component that provides a cascading value to all descendant components.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.CascadingValue`1.ChildContent">
            <summary>
            The content to which the value should be provided.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.CascadingValue`1.Value">
            <summary>
            The value to be provided.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.CascadingValue`1.Name">
             <summary>
             Optionally gives a name to the provided value. Descendant components
             will be able to receive the value by specifying this name.
            
             If no name is specified, then descendant components will receive the
             value based the type of value they are requesting.
             </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.CascadingValue`1.IsFixed">
            <summary>
            If true, indicates that <see cref="P:Microsoft.AspNetCore.Components.CascadingValue`1.Value"/> will not change. This is a
            performance optimization that allows the framework to skip setting up
            change notifications. Set this flag only if you will not change
            <see cref="P:Microsoft.AspNetCore.Components.CascadingValue`1.Value"/> during the component's lifetime.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.CascadingValue`1.Attach(Microsoft.AspNetCore.Components.RenderHandle)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.CascadingValue`1.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ChangeEventArgs">
            <summary>
            Supplies information about an change event that is being raised.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ChangeEventArgs.Value">
            <summary>
            Gets or sets the new value.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.CompilerServices.RuntimeHelpers">
            <summary>
            Used by generated code produced by the Components code generator. Not intended or supported
            for use in application code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.CompilerServices.RuntimeHelpers.TypeCheck``1(``0)">
            <summary>
            Not intended for use by application code.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.CompilerServices.RuntimeHelpers.CreateInferredEventCallback``1(System.Object,System.Action{``0},``0)">
            <summary>
            Not intended for use by application code.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.CompilerServices.RuntimeHelpers.CreateInferredEventCallback``1(System.Object,System.Func{``0,System.Threading.Tasks.Task},``0)">
            <summary>
            Not intended for use by application code.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ComponentBase">
            <summary>
            Optional base class for components. Alternatively, components may
            implement <see cref="T:Microsoft.AspNetCore.Components.IComponent"/> directly.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.#ctor">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.ComponentBase"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder)">
            <summary>
            Renders the component to the supplied <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/>.
            </summary>
            <param name="builder">A <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/> that will receive the render output.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.OnInitialized">
            <summary>
            Method invoked when the component is ready to start, having received its
            initial parameters from its parent in the render tree.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.OnInitializedAsync">
             <summary>
             Method invoked when the component is ready to start, having received its
             initial parameters from its parent in the render tree.
            
             Override this method if you will perform an asynchronous operation and
             want the component to refresh when that operation is completed.
             </summary>
             <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing any asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.OnParametersSet">
            <summary>
            Method invoked when the component has received parameters from its parent in
            the render tree, and the incoming values have been assigned to properties.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.OnParametersSetAsync">
            <summary>
            Method invoked when the component has received parameters from its parent in
            the render tree, and the incoming values have been assigned to properties.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing any asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged">
            <summary>
            Notifies the component that its state has changed. When applicable, this will
            cause the component to be re-rendered.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.ShouldRender">
            <summary>
            Returns a flag to indicate whether the component should render.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRender(System.Boolean)">
            <summary>
            Method invoked after each time the component has been rendered.
            </summary>
            <param name="firstRender">
            Set to <c>true</c> if this is the first time <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRender(System.Boolean)"/> has been invoked
            on this component instance; otherwise <c>false</c>.
            </param>
            <remarks>
            The <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRender(System.Boolean)"/> and <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRenderAsync(System.Boolean)"/> lifecycle methods
            are useful for performing interop, or interacting with values recieved from <c>@ref</c>.
            Use the <paramref name="firstRender"/> parameter to ensure that initialization work is only performed
            once.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRenderAsync(System.Boolean)">
            <summary>
            Method invoked after each time the component has been rendered. Note that the component does
            not automatically re-render after the completion of any returned <see cref="T:System.Threading.Tasks.Task"/>, because
            that would cause an infinite render loop.
            </summary>
            <param name="firstRender">
            Set to <c>true</c> if this is the first time <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRender(System.Boolean)"/> has been invoked
            on this component instance; otherwise <c>false</c>.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing any asynchronous operation.</returns>
            <remarks>
            The <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRender(System.Boolean)"/> and <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.OnAfterRenderAsync(System.Boolean)"/> lifecycle methods
            are useful for performing interop, or interacting with values recieved from <c>@ref</c>.
            Use the <paramref name="firstRender"/> parameter to ensure that initialization work is only performed
            once.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.InvokeAsync(System.Action)">
            <summary>
            Executes the supplied work item on the associated renderer's
            synchronization context.
            </summary>
            <param name="workItem">The work item to execute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.InvokeAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Executes the supplied work item on the associated renderer's
            synchronization context.
            </summary>
            <param name="workItem">The work item to execute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)">
            <summary>
            Sets parameters supplied by the component's parent in the render tree.
            </summary>
            <param name="parameters">The parameters.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that completes when the component has finished updating and rendering itself.</returns>
            <remarks>
            <para>
            The <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)"/> method should be passed the entire set of parameter values each
            time <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)"/> is called. It not required that the caller supply a parameter
            value for all parameters that are logically understood by the component.
            </para>
            <para>
            The default implementation of <see cref="M:Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)"/> will set the value of each property
            decorated with <see cref="T:Microsoft.AspNetCore.Components.ParameterAttribute" /> or <see cref="T:Microsoft.AspNetCore.Components.CascadingParameterAttribute" /> that has
            a corresponding value in the <see cref="T:Microsoft.AspNetCore.Components.ParameterView" />. Parameters that do not have a corresponding value
            will be unchanged.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ComponentFactory">
            <remarks>
            The <see cref="F:Microsoft.AspNetCore.Components.ComponentFactory.Instance"/> property on this type is used as a static global cache. Ensure any changes to this type
            are thread safe and can be safely cached statically.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Dispatcher">
            <summary>
            Dispatches external actions to be executed on the context of a <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.CreateDefault">
            <summary>
            Creates a default instance of <see cref="T:Microsoft.AspNetCore.Components.Dispatcher"/>.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Components.Dispatcher"/> instance.</returns>
        </member>
        <member name="E:Microsoft.AspNetCore.Components.Dispatcher.UnhandledException">
            <summary>
            Provides notifications of unhandled exceptions that occur within the dispatcher.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.AssertAccess">
            <summary>
            Validates that the currently executing code is running inside the dispatcher.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.CheckAccess">
            <summary>
            Returns a value that determines whether using the dispatcher to invoke a work item is required
            from the current context.
            </summary>
            <returns><c>true</c> if invoking is required, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.InvokeAsync(System.Action)">
            <summary>
            Invokes the given <see cref="T:System.Action"/> in the context of the associated <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
            <param name="workItem">The action to execute.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that will be completed when the action has finished executing.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.InvokeAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Invokes the given <see cref="T:System.Func`1"/> in the context of the associated <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
            <param name="workItem">The asynchronous action to execute.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that will be completed when the action has finished executing.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.InvokeAsync``1(System.Func{``0})">
            <summary>
            Invokes the given <see cref="T:System.Func`1"/> in the context of the associated <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
            <param name="workItem">The function to execute.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that will be completed when the function has finished executing.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.InvokeAsync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Invokes the given <see cref="T:System.Func`1"/> in the context of the associated <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
            <param name="workItem">The asynchronous function to execute.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that will be completed when the function has finished executing.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Dispatcher.OnUnhandledException(System.UnhandledExceptionEventArgs)">
            <summary>
            Called to notify listeners of an unhandled exception.
            </summary>
            <param name="e">The <see cref="T:System.UnhandledExceptionEventArgs"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ElementReference">
            <summary>
            Represents a reference to a rendered element.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ElementReference.Id">
            <summary>
            Gets a unique identifier for <see cref="T:Microsoft.AspNetCore.Components.ElementReference" />.
            </summary>
            <remarks>
            The Id is unique at least within the scope of a given user/circuit.
            This property is public to support Json serialization and should not be used by user code.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventCallback">
            <summary>
            A bound event handler delegate.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.EventCallback.Factory">
            <summary>
            Gets a reference to the <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.EventCallback.Empty">
            <summary>
            Gets an empty <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallback.#ctor(Microsoft.AspNetCore.Components.IHandleEvent,System.MulticastDelegate)">
            <summary>
            Creates the new <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="delegate">The delegate to bind.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.EventCallback.HasDelegate">
            <summary>
            Gets a value that indicates whether the delegate associated with this event dispatcher is non-null.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallback.InvokeAsync(System.Object)">
            <summary>
            Invokes the delegate associated with this binding and dispatches an event notification to the
            appropriate component.
            </summary>
            <param name="arg">The argument.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which completes asynchronously once event processing has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventCallbackFactory">
            <summary>
            A factory for creating <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> and <see cref="T:Microsoft.AspNetCore.Components.EventCallback`1"/>
            instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create(System.Object,Microsoft.AspNetCore.Components.EventCallback)">
            <summary>
            Returns the provided <paramref name="callback"/>. For internal framework use only.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create(System.Object,System.Action)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create(System.Object,System.Action{System.Object})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create(System.Object,System.Func{System.Threading.Tasks.Task})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create(System.Object,System.Func{System.Object,System.Threading.Tasks.Task})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create``1(System.Object,Microsoft.AspNetCore.Components.EventCallback)">
            <summary>
            Returns the provided <paramref name="callback"/>. For internal framework use only.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create``1(System.Object,Microsoft.AspNetCore.Components.EventCallback{``0})">
            <summary>
            Returns the provided <paramref name="callback"/>. For internal framework use only.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create``1(System.Object,System.Action)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create``1(System.Object,System.Action{``0})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create``1(System.Object,System.Func{System.Threading.Tasks.Task})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.Create``1(System.Object,System.Func{``0,System.Threading.Tasks.Task})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.CreateInferred``1(System.Object,System.Action{``0},``0)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>. For internal framework use only.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactory.CreateInferred``1(System.Object,System.Func{``0,System.Threading.Tasks.Task},``0)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>. For internal framework use only.
            </summary>
            <param name="receiver"></param>
            <param name="callback"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions">
            <summary>
            Contains extension methods for two-way binding using <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>. For internal use only.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.String},System.String,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Boolean},System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.Boolean}},System.Nullable{System.Boolean},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Int32},System.Int32,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.Int32}},System.Nullable{System.Int32},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Int64},System.Int64,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.Int64}},System.Nullable{System.Int64},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Single},System.Single,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.Single}},System.Nullable{System.Single},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Double},System.Double,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.Double}},System.Nullable{System.Double},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Decimal},System.Decimal,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.Decimal}},System.Nullable{System.Decimal},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.DateTime},System.DateTime,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.DateTime},System.DateTime,System.String,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="format"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.DateTime}},System.Nullable{System.DateTime},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.DateTime}},System.Nullable{System.DateTime},System.String,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="format"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.DateTimeOffset},System.DateTimeOffset,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.DateTimeOffset},System.DateTimeOffset,System.String,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="format"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.DateTimeOffset}},System.Nullable{System.DateTimeOffset},System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.Nullable{System.DateTimeOffset}},System.Nullable{System.DateTimeOffset},System.String,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="format"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryBinderExtensions.CreateBinder``1(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{``0},``0,System.Globalization.CultureInfo)">
            <summary>
            For internal use only.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="factory"></param>
            <param name="receiver"></param>
            <param name="setter"></param>
            <param name="existingValue"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventCallbackFactoryEventArgsExtensions">
            <summary>
            Provides extension methods for <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/> and <see cref="T:System.EventArgs"/> types.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryEventArgsExtensions.Create(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{System.EventArgs})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="factory">The <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.</param>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryEventArgsExtensions.Create(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Func{System.EventArgs,System.Threading.Tasks.Task})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="factory">The <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.</param>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryEventArgsExtensions.Create(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Action{Microsoft.AspNetCore.Components.ChangeEventArgs})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="factory">The <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.</param>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackFactoryEventArgsExtensions.Create(Microsoft.AspNetCore.Components.EventCallbackFactory,System.Object,System.Func{Microsoft.AspNetCore.Components.ChangeEventArgs,System.Threading.Tasks.Task})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> for the provided <paramref name="receiver"/> and
            <paramref name="callback"/>.
            </summary>
            <param name="factory">The <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.</param>
            <param name="receiver">The event receiver.</param>
            <param name="callback">The event callback.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventCallback`1">
            <summary>
            A bound event handler delegate.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.EventCallback`1.Empty">
            <summary>
            Gets an empty <see cref="T:Microsoft.AspNetCore.Components.EventCallback`1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallback`1.#ctor(Microsoft.AspNetCore.Components.IHandleEvent,System.MulticastDelegate)">
            <summary>
            Creates the new <see cref="T:Microsoft.AspNetCore.Components.EventCallback`1"/>.
            </summary>
            <param name="receiver">The event receiver.</param>
            <param name="delegate">The delegate to bind.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.EventCallback`1.HasDelegate">
            <summary>
            Gets a value that indicates whether the delegate associated with this event dispatcher is non-null.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallback`1.InvokeAsync(`0)">
            <summary>
            Invokes the delegate associated with this binding and dispatches an event notification to the
            appropriate component.
            </summary>
            <param name="arg">The argument.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which completes asynchronously once event processing has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventCallbackWorkItem">
            <summary>
            Wraps a callback delegate associated with an event.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.EventCallbackWorkItem.Empty">
            <summary>
            An empty <see cref="T:Microsoft.AspNetCore.Components.EventCallbackWorkItem"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackWorkItem.#ctor(System.MulticastDelegate)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Components.EventCallbackWorkItem"/> with the provided <paramref name="delegate"/>.
            </summary>
            <param name="delegate">The callback delegate.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventCallbackWorkItem.InvokeAsync(System.Object)">
            <summary>
            Invokes the delegate associated with this <see cref="T:Microsoft.AspNetCore.Components.EventCallbackWorkItem"/>.
            </summary>
            <param name="arg">The argument to provide to the delegate. May be <c>null</c>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> then will complete asynchronously once the delegate has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.EventHandlerAttribute">
            <summary>
            Associates an event argument type with an event attribute name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventHandlerAttribute.#ctor(System.String,System.Type)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.EventHandlerAttribute"/>.
            </summary>
            <param name="attributeName"></param>
            <param name="eventArgsType"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.EventHandlerAttribute.#ctor(System.String,System.Type,System.Boolean,System.Boolean)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.EventHandlerAttribute"/>.
            </summary>
            <param name="attributeName"></param>
            <param name="eventArgsType"></param>
            <param name="enableStopPropagation"></param>
            <param name="enablePreventDefault"></param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.EventHandlerAttribute.AttributeName">
            <summary>
            Gets the attribute name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.EventHandlerAttribute.EventArgsType">
            <summary>
            Gets the event argument type.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.EventHandlerAttribute.EnableStopPropagation">
            <summary>
            Gets the event's ability to stop propagation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.EventHandlerAttribute.EnablePreventDefault">
            <summary>
            Gets the event's ability to prevent default event flow.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.IComponent">
            <summary>
            Represents a UI component.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.IComponent.Attach(Microsoft.AspNetCore.Components.RenderHandle)">
            <summary>
            Attaches the component to a <see cref="T:Microsoft.AspNetCore.Components.RenderHandle" />.
            </summary>
            <param name="renderHandle">A <see cref="T:Microsoft.AspNetCore.Components.RenderHandle"/> that allows the component to be rendered.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.IComponent.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)">
            <summary>
            Sets parameters supplied by the component's parent in the render tree.
            </summary>
            <param name="parameters">The parameters.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that completes when the component has finished updating and rendering itself.</returns>
            <remarks>
            The <see cref="M:Microsoft.AspNetCore.Components.IComponent.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)"/> method should be passed the entire set of parameter values each
            time <see cref="M:Microsoft.AspNetCore.Components.IComponent.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)"/> is called. It not required that the caller supply a parameter
            value for all parameters that are logically understood by the component.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.IHandleAfterRender">
            <summary>
            Interface implemented by components that receive notification that they have been rendered.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.IHandleAfterRender.OnAfterRenderAsync">
            <summary>
            Notifies the component that it has been rendered.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous event handling operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.IHandleEvent">
            <summary>
            Interface implemented by components that receive notification of state changes.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.IHandleEvent.HandleEventAsync(Microsoft.AspNetCore.Components.EventCallbackWorkItem,System.Object)">
            <summary>
            Notifies the a state change has been triggered.
            </summary>
            <param name="item">The <see cref="T:Microsoft.AspNetCore.Components.EventCallbackWorkItem"/> associated with this event.</param>
            <param name="arg">The argument associated with this event.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that completes once the component has processed the state change.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.InjectAttribute">
            <summary>
            Indicates that the associated property should have a value injected from the
            service provider during initialization.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.LayoutAttribute">
            <summary>
            Indicates that the associated component type uses a specified layout.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.LayoutAttribute.#ctor(System.Type)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.LayoutAttribute"/>.
            </summary>
            <param name="layoutType">The type of the layout.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.LayoutAttribute.LayoutType">
            <summary>
            The type of the layout. The type must implement <see cref="T:Microsoft.AspNetCore.Components.IComponent"/>
            and must accept a parameter with the name 'Body'.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.LayoutComponentBase">
            <summary>
            Optional base class for components that represent a layout.
            Alternatively, components may implement <see cref="T:Microsoft.AspNetCore.Components.IComponent"/> directly
            and declare their own parameter named <see cref="P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body">
            <summary>
            Gets the content to be rendered inside the layout.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.LayoutView">
            <summary>
            Displays the specified content inside the specified layout and any further
            nested layouts.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.LayoutView.ChildContent">
            <summary>
            Gets or sets the content to display.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.LayoutView.Layout">
            <summary>
            Gets or sets the type of the layout in which to display the content.
            The type must implement <see cref="T:Microsoft.AspNetCore.Components.IComponent"/> and accept a parameter named <see cref="P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.LayoutView.Attach(Microsoft.AspNetCore.Components.RenderHandle)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.LayoutView.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.LocationChangeException">
            <summary>
            An exception thrown when <see cref="E:Microsoft.AspNetCore.Components.NavigationManager.LocationChanged"/> throws an exception.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.LocationChangeException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Components.LocationChangeException"/>.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.MarkupString">
            <summary>
            A string value that can be rendered as markup such as HTML.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.MarkupString.#ctor(System.String)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.MarkupString"/>.
            </summary>
            <param name="value">The value for the new instance.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.MarkupString.Value">
            <summary>
            Gets the value of the <see cref="T:Microsoft.AspNetCore.Components.MarkupString"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.MarkupString.op_Explicit(System.String)~Microsoft.AspNetCore.Components.MarkupString">
            <summary>
            Casts a <see cref="T:System.String"/> to a <see cref="T:Microsoft.AspNetCore.Components.MarkupString"/>.
            </summary>
            <param name="value">The <see cref="T:System.String"/> value.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.MarkupString.ToString">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.NavigationException">
            <summary>
            Exception thrown when an <see cref="T:Microsoft.AspNetCore.Components.NavigationManager"/> is not able to navigate to a different url.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationException.#ctor(System.String)">
            <summary>
            Initializes a new <see cref="T:Microsoft.AspNetCore.Components.NavigationException"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.NavigationException.Location">
            <summary>
            Gets the uri to which navigation was attempted.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.NavigationManager">
            <summary>
            Provides an abstraction for querying and mananging URI navigation.
            </summary>
        </member>
        <member name="E:Microsoft.AspNetCore.Components.NavigationManager.LocationChanged">
            <summary>
            An event that fires when the navigation location has changed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.NavigationManager.BaseUri">
            <summary>
            Gets or sets the current base URI. The <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.BaseUri" /> is always represented as an absolute URI in string form with trailing slash.
            Typically this corresponds to the 'href' attribute on the document's &lt;base&gt; element.
            </summary>
            <remarks>
            Setting <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.BaseUri" /> will not trigger the <see cref="E:Microsoft.AspNetCore.Components.NavigationManager.LocationChanged" /> event.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.NavigationManager.Uri">
            <summary>
            Gets or sets the current URI. The <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.Uri" /> is always represented as an absolute URI in string form.
            </summary>
            <remarks>
            Setting <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.Uri" /> will not trigger the <see cref="E:Microsoft.AspNetCore.Components.NavigationManager.LocationChanged" /> event.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.NavigateTo(System.String,System.Boolean)">
            <summary>
            Navigates to the specified URI.
            </summary>
            <param name="uri">The destination URI. This can be absolute, or relative to the base URI
            (as returned by <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.BaseUri"/>).</param>
            <param name="forceLoad">If true, bypasses client-side routing and forces the browser to load the new page from the server, whether or not the URI would normally be handled by the client-side router.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.NavigateToCore(System.String,System.Boolean)">
            <summary>
            Navigates to the specified URI.
            </summary>
            <param name="uri">The destination URI. This can be absolute, or relative to the base URI
            (as returned by <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.BaseUri"/>).</param>
            <param name="forceLoad">If true, bypasses client-side routing and forces the browser to load the new page from the server, whether or not the URI would normally be handled by the client-side router.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.Initialize(System.String,System.String)">
            <summary>
            Called to initialize BaseURI and current URI before these values are used for the first time.
            Override <see cref="M:Microsoft.AspNetCore.Components.NavigationManager.EnsureInitialized" /> and call this method to dynamically calculate these values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.EnsureInitialized">
            <summary>
            Allows derived classes to lazyly self-initialize. Implementations that support lazy-initialization should override
            this method and call <see cref="M:Microsoft.AspNetCore.Components.NavigationManager.Initialize(System.String,System.String)" />.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.ToAbsoluteUri(System.String)">
            <summary>
            Converts a relative URI into an absolute one (by resolving it
            relative to the current absolute URI).
            </summary>
            <param name="relativeUri">The relative URI.</param>
            <returns>The absolute URI.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.ToBaseRelativePath(System.String)">
            <summary>
            Given a base URI (e.g., one previously returned by <see cref="P:Microsoft.AspNetCore.Components.NavigationManager.BaseUri"/>),
            converts an absolute URI into one relative to the base URI prefix.
            </summary>
            <param name="uri">An absolute URI that is within the space of the base URI.</param>
            <returns>A relative URI path.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.NavigationManager.NotifyLocationChanged(System.Boolean)">
            <summary>
            Triggers the <see cref="E:Microsoft.AspNetCore.Components.NavigationManager.LocationChanged"/> event with the current URI value.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.OwningComponentBase">
            <summary>
            A base class that creates a service provider scope.
            </summary>
            <remarks>
            Use the <see cref="T:Microsoft.AspNetCore.Components.OwningComponentBase"/> class as a base class to author components that control
            the lifetime of a service provider scope. This is useful when using a transient or scoped service that
            requires disposal such as a repository or database abstraction. Using <see cref="T:Microsoft.AspNetCore.Components.OwningComponentBase"/>
            as a base class ensures that the service provider scope is disposed with the component.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.OwningComponentBase.IsDisposed">
            <summary>
            Gets a value determining if the component and associated services have been disposed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.OwningComponentBase.ScopedServices">
            <summary>
            Gets the scoped <see cref="T:System.IServiceProvider"/> that is associated with this component.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.OwningComponentBase.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.OwningComponentBase`1">
            <summary>
            A base class that creates a service provider scope, and resolves a service of type <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The service type.</typeparam>
            <remarks>
            Use the <see cref="T:Microsoft.AspNetCore.Components.OwningComponentBase`1"/> class as a base class to author components that control
            the lifetime of a service or multiple services. This is useful when using a transient or scoped service that
            requires disposal such as a repository or database abstraction. Using <see cref="T:Microsoft.AspNetCore.Components.OwningComponentBase`1"/>
            as a base class ensures that the service and relates services that share its scope are disposed with the component.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.OwningComponentBase`1.Service">
            <summary>
            Gets the <typeparamref name="TService"/> that is associated with this component.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ParameterAttribute">
            <summary>
            Denotes the target member as a component parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ParameterAttribute.CaptureUnmatchedValues">
            <summary>
            Gets or sets a value that determines whether the parameter will capture values that
            don't match any other parameter.
            </summary>
            <remarks>
            <para>
            <see cref="P:Microsoft.AspNetCore.Components.ParameterAttribute.CaptureUnmatchedValues"/> allows a component to accept arbitrary additional
            attributes, and pass them to another component, or some element of the underlying markup.
            </para>
            <para>
            <see cref="P:Microsoft.AspNetCore.Components.ParameterAttribute.CaptureUnmatchedValues"/> can be used on at most one parameter per component.
            </para>
            <para>
            <see cref="P:Microsoft.AspNetCore.Components.ParameterAttribute.CaptureUnmatchedValues"/> should only be applied to parameters of a type that
            can be used with <see cref="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddMultipleAttributes(System.Int32,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})"/>
            such as <see cref="T:System.Collections.Generic.Dictionary`2"/>.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ParameterValue">
            <summary>
            Represents a single parameter supplied to an <see cref="T:Microsoft.AspNetCore.Components.IComponent"/>
            by its parent in the render tree.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ParameterValue.Name">
            <summary>
            Gets the name of the parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ParameterValue.Value">
            <summary>
            Gets the value being supplied for the parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ParameterValue.Cascading">
            <summary>
            Gets a value to indicate whether the parameter is cascading, meaning that it
            was supplied by a <see cref="T:Microsoft.AspNetCore.Components.CascadingValue`1"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ParameterView">
            <summary>
            Represents a collection of parameters supplied to an <see cref="T:Microsoft.AspNetCore.Components.IComponent"/>
            by its parent in the render tree.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ParameterView.Empty">
            <summary>
            Gets an empty <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.
            </summary>
            <returns>The enumerator.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.TryGetValue``1(System.String,``0@)">
            <summary>
            Gets the value of the parameter with the specified name.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="parameterName">The name of the parameter.</param>
            <param name="result">Receives the result, if any.</param>
            <returns>True if a matching parameter was found; false otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.GetValueOrDefault``1(System.String)">
            <summary>
            Gets the value of the parameter with the specified name, or a default value
            if no such parameter exists in the collection.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="parameterName">The name of the parameter.</param>
            <returns>The parameter value if found; otherwise the default value for the specified type.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.GetValueOrDefault``1(System.String,``0)">
            <summary>
            Gets the value of the parameter with the specified name, or a specified default value
            if no such parameter exists in the collection.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="parameterName">The name of the parameter.</param>
            <param name="defaultValue">The default value to return if no such parameter exists in the collection.</param>
            <returns>The parameter value if found; otherwise <paramref name="defaultValue"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.ToDictionary">
            <summary>
            Returns a dictionary populated with the contents of the <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.
            </summary>
            <returns>A dictionary populated with the contents of the <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.FromDictionary(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/> from the given <see cref="T:System.Collections.Generic.IDictionary`2"/>.
            </summary>
            <param name="parameters">The <see cref="T:System.Collections.Generic.IDictionary`2"/> with the parameters.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.SetParameterProperties(System.Object)">
            <summary>
            For each parameter property on <paramref name="target"/>, updates its value to
            match the corresponding entry in the <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.
            </summary>
            <param name="target">An object that has a public writable property matching each parameter's name and type.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.ParameterView.Enumerator">
            <summary>
            An enumerator that iterates through a <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.ParameterView.Enumerator.Current">
            <summary>
            Gets the current value of the enumerator.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.ParameterView.Enumerator.MoveNext">
            <summary>
            Instructs the enumerator to move to the next value in the sequence.
            </summary>
            <returns>A flag to indicate whether or not there is a next value.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderFragment">
            <summary>
            Represents a segment of UI content, implemented as a delegate that
            writes the content to a <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/> to which the content should be written.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderFragment`1">
            <summary>
            Represents a segment of UI content for an object of type <typeparamref name="TValue"/>, implemented as
            a function that returns a <see cref="T:Microsoft.AspNetCore.Components.RenderFragment"/>.
            </summary>
            <typeparam name="TValue">The type of object.</typeparam>
            <param name="value">The value used to build the content.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderHandle">
            <summary>
            Allows a component to interact with its renderer.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderHandle.Dispatcher">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Components.Dispatcher" /> associated with the component.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderHandle.IsInitialized">
            <summary>
            Gets a value that indicates whether the <see cref="T:Microsoft.AspNetCore.Components.RenderHandle"/> has been
            initialized and is ready to use.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderHandle.Render(Microsoft.AspNetCore.Components.RenderFragment)">
            <summary>
            Notifies the renderer that the component should be rendered.
            </summary>
            <param name="renderFragment">The content that should be rendered.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Rendering.ComponentState">
            <summary>
            Tracks the rendering state associated with an <see cref="T:Microsoft.AspNetCore.Components.IComponent"/> instance
            within the context of a <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>. This is an internal implementation
            detail of <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.ComponentState.#ctor(Microsoft.AspNetCore.Components.RenderTree.Renderer,System.Int32,Microsoft.AspNetCore.Components.IComponent,Microsoft.AspNetCore.Components.Rendering.ComponentState)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.Rendering.ComponentState"/>.
            </summary>
            <param name="renderer">The <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/> with which the new instance should be associated.</param>
            <param name="componentId">The externally visible identifier for the <see cref="T:Microsoft.AspNetCore.Components.IComponent"/>. The identifier must be unique in the context of the <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.</param>
            <param name="component">The <see cref="T:Microsoft.AspNetCore.Components.IComponent"/> whose state is being tracked.</param>
            <param name="parentComponentState">The <see cref="T:Microsoft.AspNetCore.Components.Rendering.ComponentState"/> for the parent component, or null if this is a root component.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Rendering.RenderBatchBuilder">
            <summary>
            Collects the data produced by the rendering system during the course
            of rendering a single batch. This tracks both the final output data
            and the intermediate states (such as the queue of components still to
            be rendered).
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder">
            <summary>
            Provides methods for building a collection of <see cref="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame"/> entries.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.ChildContent">
            <summary>
            The reserved parameter name used for supplying child content.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenElement(System.Int32,System.String)">
            <summary>
            Appends a frame representing an element, i.e., a container for other frames.
            In order for the <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/> state to be valid, you must
            also call <see cref="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.CloseElement"/> immediately after appending the
            new element's child frames.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="elementName">A value representing the type of the element.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.CloseElement">
            <summary>
            Marks a previously appended element frame as closed. Calls to this method
            must be balanced with calls to <see cref="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenElement(System.Int32,System.String)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddMarkupContent(System.Int32,System.String)">
            <summary>
            Appends a frame representing markup content.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="markupContent">Content for the new markup frame.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(System.Int32,System.String)">
            <summary>
            Appends a frame representing text content.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="textContent">Content for the new text frame.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(System.Int32,Microsoft.AspNetCore.Components.RenderFragment)">
            <summary>
            Appends frames representing an arbitrary fragment of content.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="fragment">Content to append.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent``1(System.Int32,Microsoft.AspNetCore.Components.RenderFragment{``0},``0)">
            <summary>
            Appends frames representing an arbitrary fragment of content.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="fragment">Content to append.</param>
            <param name="value">The value used by <paramref name="fragment"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(System.Int32,Microsoft.AspNetCore.Components.MarkupString)">
            <summary>
            Appends a frame representing markup content.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="markupContent">Content for the new markup frame.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(System.Int32,System.Object)">
            <summary>
            Appends a frame representing text content.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="textContent">Content for the new text frame.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute(System.Int32,System.String,System.Boolean)">
            <summary>
            <para>
            Appends a frame representing a bool-valued attribute.
            </para>
            <para>
            The attribute is associated with the most recently added element. If the value is <c>false</c> and the
            current element is not a component, the frame will be omitted.
            </para>
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="name">The name of the attribute.</param>
            <param name="value">The value of the attribute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute(System.Int32,System.String,System.String)">
            <summary>
            <para>
            Appends a frame representing a string-valued attribute.
            </para>
            <para>
            The attribute is associated with the most recently added element. If the value is <c>null</c> and the
            current element is not a component, the frame will be omitted.
            </para>
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="name">The name of the attribute.</param>
            <param name="value">The value of the attribute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute(System.Int32,System.String,System.MulticastDelegate)">
            <summary>
            <para>
            Appends a frame representing a delegate-valued attribute.
            </para>
            <para>
            The attribute is associated with the most recently added element. If the value is <c>null</c> and the
            current element is not a component, the frame will be omitted.
            </para>
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="name">The name of the attribute.</param>
            <param name="value">The value of the attribute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute(System.Int32,System.String,Microsoft.AspNetCore.Components.EventCallback)">
            <summary>
            <para>
            Appends a frame representing an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> attribute.
            </para>
            <para>
            The attribute is associated with the most recently added element. If the value is <c>null</c> and the
            current element is not a component, the frame will be omitted.
            </para>
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="name">The name of the attribute.</param>
            <param name="value">The value of the attribute.</param>
            <remarks>
            This method is provided for infrastructure purposes, and is used to support generated code
            that uses <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute``1(System.Int32,System.String,Microsoft.AspNetCore.Components.EventCallback{``0})">
            <summary>
            <para>
            Appends a frame representing an <see cref="T:Microsoft.AspNetCore.Components.EventCallback"/> attribute.
            </para>
            <para>
            The attribute is associated with the most recently added element. If the value is <c>null</c> and the
            current element is not a component, the frame will be omitted.
            </para>
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="name">The name of the attribute.</param>
            <param name="value">The value of the attribute.</param>
            <remarks>
            This method is provided for infrastructure purposes, and is used to support generated code
            that uses <see cref="T:Microsoft.AspNetCore.Components.EventCallbackFactory"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute(System.Int32,System.String,System.Object)">
            <summary>
            Appends a frame representing a string-valued attribute.
            The attribute is associated with the most recently added element. If the value is <c>null</c>, or
            the <see cref="T:System.Boolean" /> value <c>false</c> and the current element is not a component, the
            frame will be omitted.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="name">The name of the attribute.</param>
            <param name="value">The value of the attribute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddAttribute(System.Int32,Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame@)">
            <summary>
            <para>
            Appends a frame representing an attribute.
            </para>
            <para>
            The attribute is associated with the most recently added element.
            </para>
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="frame">A <see cref="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame"/> holding the name and value of the attribute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddMultipleAttributes(System.Int32,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Adds frames representing multiple attributes with the same sequence number.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="attributes">A collection of key-value pairs representing attributes.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.SetUpdatesAttributeName(System.String)">
            <summary>
            <para>
            Indicates that the preceding attribute represents an event handler
            whose execution updates the attribute with name <paramref name="updatesAttributeName"/>.
            </para>
            <para>
            This information is used by the rendering system to determine whether
            to accept a value update for the other attribute when receiving a
            call to the event handler.
            </para>
            </summary>
            <param name="updatesAttributeName">The name of another attribute whose value can be updated when the event handler is executed.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenComponent``1(System.Int32)">
            <summary>
            Appends a frame representing a child component.
            </summary>
            <typeparam name="TComponent">The type of the child component.</typeparam>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenComponent(System.Int32,System.Type)">
            <summary>
            Appends a frame representing a child component.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="componentType">The type of the child component.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.SetKey(System.Object)">
            <summary>
            Assigns the specified key value to the current element or component.
            </summary>
            <param name="value">The value for the key.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.CloseComponent">
            <summary>
            Marks a previously appended component frame as closed. Calls to this method
            must be balanced with calls to <see cref="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenComponent``1(System.Int32)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddElementReferenceCapture(System.Int32,System.Action{Microsoft.AspNetCore.Components.ElementReference})">
            <summary>
            Appends a frame representing an instruction to capture a reference to the parent element.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="elementReferenceCaptureAction">An action to be invoked whenever the reference value changes.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddComponentReferenceCapture(System.Int32,System.Action{System.Object})">
            <summary>
            Appends a frame representing an instruction to capture a reference to the parent component.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
            <param name="componentReferenceCaptureAction">An action to be invoked whenever the reference value changes.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenRegion(System.Int32)">
            <summary>
            Appends a frame representing a region of frames.
            </summary>
            <param name="sequence">An integer that represents the position of the instruction in the source code.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.CloseRegion">
            <summary>
            Marks a previously appended region frame as closed. Calls to this method
            must be balanced with calls to <see cref="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.OpenRegion(System.Int32)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.Clear">
            <summary>
            Clears the builder.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.GetFrames">
            <summary>
            Returns the <see cref="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame"/> values that have been appended.
            </summary>
            <returns>An array range of <see cref="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame"/> values.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderExtensions.ToRange``1(Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder{``0})">
            <summary>
            Produces an <see cref="T:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1"/> structure describing the current contents.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderExtensions.ToSegment``1(Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder{``0},System.Int32,System.Int32)">
            <summary>
            Produces an <see cref="T:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderSegment`1"/> structure describing the selected contents.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1"/></param>
            <param name="fromIndexInclusive">The index of the first item in the segment.</param>
            <param name="toIndexExclusive">One plus the index of the last item in the segment.</param>
            <returns>The <see cref="T:System.ArraySegment`1"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderSegment`1">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
            <typeparam name="T">The type of the elements in the array</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderSegment`1.Array">
            <summary>
            Gets the current underlying array holding the segment's elements.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderSegment`1.Offset">
            <summary>
            Gets the offset into the underlying array holding the segment's elements.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderSegment`1.Count">
            <summary>
            Gets the number of items in the segment.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilderSegment`1.Item(System.Int32)">
            <summary>
            Gets the specified item from the segment.
            </summary>
            <param name="index">The index into the segment.</param>
            <returns>The array entry at the specified index within the segment.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1.Array">
            <summary>
            Gets the underlying array instance.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1.Count">
            <summary>
            Gets the number of items in the array that are considered to be in use.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1.#ctor(`0[],System.Int32)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1"/>.
            </summary>
            <param name="array">The array.</param>
            <param name="count">The number of items in the array that are in use.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayRange`1.Clone">
            <summary>
            Creates a shallow clone of the instance.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.EventFieldInfo">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in a future release.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.EventFieldInfo.ComponentId">
            <summary>
            Identifies the component whose render tree contains the affected form field.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.EventFieldInfo.FieldValue">
            <summary>
            Specifies the form field's new value.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderBatch">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in a future release.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.RenderBatch.UpdatedComponents">
            <summary>
            Gets the changes to components that were added or updated.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.RenderBatch.ReferenceFrames">
            <summary>
            Gets render frames that may be referenced by entries in <see cref="P:Microsoft.AspNetCore.Components.RenderTree.RenderBatch.UpdatedComponents"/>.
            For example, edit entries of type <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PrependFrame"/>
            will point to an entry in this array to specify the subtree to be prepended.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.RenderBatch.DisposedComponentIDs">
            <summary>
            Gets the IDs of the components that were disposed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.RenderBatch.DisposedEventHandlerIDs">
            <summary>
            Gets the IDs of the event handlers that were disposed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.Renderer">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in a future release.
            </summary>
        </member>
        <member name="E:Microsoft.AspNetCore.Components.RenderTree.Renderer.UnhandledSynchronizationException">
            <summary>
            Allows the caller to handle exceptions from the SynchronizationContext when one is available.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.#ctor(System.IServiceProvider,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
            <param name="serviceProvider">The <see cref="T:System.IServiceProvider"/> to be used when initializing components.</param>
            <param name="loggerFactory">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.Renderer.Dispatcher">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Components.Dispatcher" /> associated with this <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer" />.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateComponent(System.Type)">
            <summary>
            Constructs a new component of the specified type.
            </summary>
            <param name="componentType">The type of the component to instantiate.</param>
            <returns>The component instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.AssignRootComponentId(Microsoft.AspNetCore.Components.IComponent)">
            <summary>
            Associates the <see cref="T:Microsoft.AspNetCore.Components.IComponent"/> with the <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>, assigning
            an identifier that is unique within the scope of the <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/>.
            </summary>
            <param name="component">The component.</param>
            <returns>The component's assigned identifier.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.GetCurrentRenderTreeFrames(System.Int32)">
            <summary>
            Gets the current render tree for a given component.
            </summary>
            <param name="componentId">The id for the component.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/> representing the current render tree.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(System.Int32)">
            <summary>
            Performs the first render for a root component, waiting for this component and all
            children components to finish rendering in case there is any asynchronous work being
            done by any of the components. After this, the root component
            makes its own decisions about when to re-render, so there is no need to call
            this more than once.
            </summary>
            <param name="componentId">The ID returned by <see cref="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.AssignRootComponentId(Microsoft.AspNetCore.Components.IComponent)"/>.</param>
            <remarks>
            Rendering a root component is an asynchronous operation. Clients may choose to not await the returned task to
            start, but not wait for the entire render to complete.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(System.Int32,Microsoft.AspNetCore.Components.ParameterView)">
            <summary>
            Performs the first render for a root component, waiting for this component and all
            children components to finish rendering in case there is any asynchronous work being
            done by any of the components. After this, the root component
            makes its own decisions about when to re-render, so there is no need to call
            this more than once.
            </summary>
            <param name="componentId">The ID returned by <see cref="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.AssignRootComponentId(Microsoft.AspNetCore.Components.IComponent)"/>.</param>
            <param name="initialParameters">The <see cref="T:Microsoft.AspNetCore.Components.ParameterView"/>with the initial parameters to use for rendering.</param>
            <remarks>
            Rendering a root component is an asynchronous operation. Clients may choose to not await the returned task to
            start, but not wait for the entire render to complete.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleException(System.Exception)">
            <summary>
            Allows derived types to handle exceptions during rendering. Defaults to rethrowing the original exception.
            </summary>
            <param name="exception">The <see cref="T:System.Exception"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.UpdateDisplayAsync(Microsoft.AspNetCore.Components.RenderTree.RenderBatch@)">
            <summary>
            Updates the visible UI.
            </summary>
            <param name="renderBatch">The changes to the UI since the previous call.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> to represent the UI update process.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.DispatchEventAsync(System.UInt64,Microsoft.AspNetCore.Components.RenderTree.EventFieldInfo,System.EventArgs)">
            <summary>
            Notifies the renderer that an event has occurred.
            </summary>
            <param name="eventHandlerId">The <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.AttributeEventHandlerId"/> value from the original event attribute.</param>
            <param name="eventArgs">Arguments to be passed to the event handler.</param>
            <param name="fieldInfo">Information that the renderer can use to update the state of the existing render tree to match the UI.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> which will complete once all asynchronous processing related to the event
            has completed.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(System.Int32,Microsoft.AspNetCore.Components.RenderFragment)">
            <summary>
            Schedules a render for the specified <paramref name="componentId"/>. Its display
            will be populated using the specified <paramref name="renderFragment"/>.
            </summary>
            <param name="componentId">The ID of the component to render.</param>
            <param name="renderFragment">A <see cref="T:Microsoft.AspNetCore.Components.RenderFragment"/> that will supply the updated UI contents.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessPendingRender">
            <summary>
            Processses pending renders requests from components if there are any.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.Dispose(System.Boolean)">
            <summary>
            Releases all resources currently used by this <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/> instance.
            </summary>
            <param name="disposing"><see langword="true"/> if this method is being invoked by <see cref="M:System.IDisposable.Dispose"/>, otherwise <see langword="false"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.Renderer.Dispose">
            <summary>
            Releases all resources currently used by this <see cref="T:Microsoft.AspNetCore.Components.RenderTree.Renderer"/> instance.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiff">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiff.ComponentId">
            <summary>
            Gets the ID of the component.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiff.Edits">
            <summary>
            Gets the changes to the render tree since a previous state.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.DiffContext">
             <summary>
             Exists only so that the various methods in this class can call each other without
             constantly building up long lists of parameters. Is private to this class, so the
             fact that it's a mutable struct is manageable.
            
             Always pass by ref to avoid copying, and because the 'SiblingIndex' is mutable.
             </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.Type">
            <summary>
            Gets the type of the edit operation.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.SiblingIndex">
            <summary>
            Gets the index of the sibling frame that the edit relates to.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.ReferenceFrameIndex">
            <summary>
            Gets the index of related data in an associated render frames array. For example, if the
            <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.Type"/> value is <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PrependFrame"/>, gets the
            index of the new frame data in an associated render tree.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.MoveToSiblingIndex">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.Type"/> value is <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PermutationListEntry"/>,
            gets the sibling index to which the frame should be moved.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.RemovedAttributeName">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEdit.Type"/> value is <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.RemoveAttribute"/>,
            gets the name of the attribute that is being removed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PrependFrame">
            <summary>
            Indicates that a new frame should be inserted before the specified tree frame.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.RemoveFrame">
            <summary>
            Indicates that the specified tree frame should be removed.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.SetAttribute">
            <summary>
            Indicates that an attribute value should be applied to the specified frame.
            This may be a change to an existing attribute, or the addition of a new attribute.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.RemoveAttribute">
            <summary>
            Indicates that a named attribute should be removed from the specified frame.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.UpdateText">
            <summary>
            Indicates that the text content of the specified frame (which must be a text frame)
            should be updated.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.StepIn">
            <summary>
            Indicates that the edit position should move inside the specified frame.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.StepOut">
            <summary>
            Indicates that there are no further edit operations on the current frame, and the
            edit position should move back to the parent frame.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.UpdateMarkup">
            <summary>
            Indicates that the markup content of the specified frame (which must be a markup frame)
            should be updated.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PermutationListEntry">
            <summary>
            An entry in a sparse permutation list. That is, a list of old indices with
            corresponding new indices, which altogether describe a valid permutation of
            the children at the current edit position.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PermutationListEnd">
            <summary>
            Indicates that the preceding series of <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeEditType.PermutationListEntry"/> entries
            is now complete.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.Sequence">
            <summary>
            Gets the sequence number of the frame. Sequence numbers indicate the relative source
            positions of the instructions that inserted the frames. Sequence numbers are only
            comparable within the same sequence (typically, the same source method).
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType">
            <summary>
            Describes the type of this frame.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ElementSubtreeLength">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Element"/>
            gets the number of frames in the subtree for which this frame is the root.
            The value is zero if the frame has not yet been closed.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ElementName">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Element"/>,
            gets a name representing the type of the element. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ElementKey">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Element"/>,
            gets the element's diffing key, or null if none was specified.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.TextContent">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Text"/>,
            gets the content of the text frame. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.AttributeEventHandlerId">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Attribute"/>
            gets the ID of the corresponding event handler, if any.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.AttributeName">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Attribute"/>,
            gets the attribute name. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.AttributeValue">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Attribute"/>,
            gets the attribute value. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.AttributeEventUpdatesAttributeName">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Attribute"/>,
            and the attribute represents an event handler, gets the name of another attribute whose value
            can be updated to represent the UI state prior to executing the event handler. This is
            primarily used in two-way bindings.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentSubtreeLength">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component"/>
            gets the number of frames in the subtree for which this frame is the root.
            The value is zero if the frame has not yet been closed.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentId">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component"/>,
            gets the child component instance identifier.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentType">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component"/>,
            gets the type of the child component.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentState">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component"/>,
            gets the child component state object. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentKey">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component"/>,
            gets the component's diffing key, or null if none was specified.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.Component">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component"/>,
            gets the child component instance. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.RegionSubtreeLength">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Region"/>
            gets the number of frames in the subtree for which this frame is the root.
            The value is zero if the frame has not yet been closed.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ElementReferenceCaptureId">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.ElementReferenceCapture"/>,
            gets the ID of the reference capture. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ElementReferenceCaptureAction">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.ElementReferenceCapture"/>,
            gets the action that writes the reference to its target. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentReferenceCaptureParentFrameIndex">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.ComponentReferenceCapture"/>,
            gets the index of the parent frame representing the component being captured. Otherwise, the value is undefined.
            WARNING: This index can only be used in the context of the frame's original render tree. If the frame is
                     copied elsewhere, such as to the ReferenceFrames buffer of a RenderTreeDiff, then the index will
                     not relate to entries in that other buffer.
                     Currently there's no scenario where this matters, but if there was, we could change all of the subtree
                     initialization logic in RenderTreeDiffBuilder to walk the frames hierarchically, then it would know
                     the parent index at the point where it wants to initialize the ComponentReferenceCapture frame.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ComponentReferenceCaptureAction">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.ComponentReferenceCapture"/>,
            gets the action that writes the reference to its target. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.MarkupContent">
            <summary>
            If the <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.FrameType"/> property equals <see cref="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Markup"/>,
            gets the content of the markup frame. Otherwise, the value is undefined.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame.ToString">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType">
            <summary>
            Types in the Microsoft.AspNetCore.Components.RenderTree are not recommended for use outside
            of the Blazor framework. These types will change in future release.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.None">
            <summary>
            Used only for unintialized frames.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Element">
            <summary>
            Represents a container for other frames.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Text">
            <summary>
            Represents text content.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Attribute">
            <summary>
            Represents a key-value pair associated with another <see cref="T:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrame"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Component">
            <summary>
            Represents a child component.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Region">
            <summary>
            Defines the boundary around range of sibling frames that should be treated as an
            unsplittable group for the purposes of diffing. This is typically used when appending
            a tree fragment generated by external code, because the sequence numbers in that tree
            fragment are not comparable to sequence numbers outside it.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.ElementReferenceCapture">
            <summary>
            Represents an instruction to capture or update a reference to the parent element.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.ComponentReferenceCapture">
            <summary>
            Represents an instruction to capture or update a reference to the parent component.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.RenderTree.RenderTreeFrameType.Markup">
            <summary>
            Represents a block of markup content.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1">
            <summary>
            Implements a list that uses an array of objects to store the elements.
            
            This differs from a <see cref="T:System.Collections.Generic.List`1"/> in that
            it not only grows as required but also shrinks if cleared with significant
            excess capacity. This makes it useful for component rendering, because
            components can be long-lived and re-render frequently, with the rendered size
            varying dramatically depending on the user's navigation in the app.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.#ctor(System.Int32,System.Buffers.ArrayPool{`0})">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.Count">
            <summary>
            Gets the number of items.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.Buffer">
            <summary>
            Gets the underlying buffer.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.Append(`0@)">
            <summary>
            Appends a new item, automatically resizing the underlying array if necessary.
            </summary>
            <param name="item">The item to append.</param>
            <returns>The index of the appended item.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.Overwrite(System.Int32,`0@)">
            <summary>
            Sets the supplied value at the specified index. The index must be within
            range for the array.
            </summary>
            <param name="index">The index.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.RemoveLast">
            <summary>
            Removes the last item.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.InsertExpensive(System.Int32,`0)">
            <summary>
            Inserts the item at the specified index, moving the contents of the subsequent entries along by one.
            </summary>
            <param name="index">The index at which the value is to be inserted.</param>
            <param name="value">The value to insert.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.Clear">
            <summary>
            Marks the array as empty, also shrinking the underlying storage if it was
            not being used to near its full capacity.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RouteAttribute">
            <summary>
            Indicates that the associated component should match the specified route template pattern.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RouteAttribute.#ctor(System.String)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.RouteAttribute"/>.
            </summary>
            <param name="template">The route template.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RouteAttribute.Template">
            <summary>
            Gets the route template.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RouteView">
            <summary>
            Displays the specified page component, rendering it inside its layout
            and any further nested layouts.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RouteView.RouteData">
            <summary>
            Gets or sets the route data. This determines the page that will be
            displayed and the parameter values that will be supplied to the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RouteView.DefaultLayout">
            <summary>
            Gets or sets the type of a layout to be used if the page does not
            declare any layout. If specified, the type must implement <see cref="T:Microsoft.AspNetCore.Components.IComponent"/>
            and accept a parameter named <see cref="P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RouteView.Attach(Microsoft.AspNetCore.Components.RenderHandle)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RouteView.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RouteView.Render(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder)">
            <summary>
            Renders the component.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Routing.IHostEnvironmentNavigationManager">
            <summary>
            An optional interface for <see cref="T:Microsoft.AspNetCore.Components.NavigationManager" /> implementations that must be initialized
            by the host.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Routing.IHostEnvironmentNavigationManager.Initialize(System.String,System.String)">
            <summary>
            Initializes the <see cref="T:Microsoft.AspNetCore.Components.NavigationManager" />.
            </summary>
            <param name="baseUri">The base URI.</param>
            <param name="uri">The absolute URI.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Routing.INavigationInterception">
            <summary>
            Contract to setup navigation interception on the client.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Routing.INavigationInterception.EnableNavigationInterceptionAsync">
            <summary>
            Enables navigation interception on the client.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs">
            <summary>
            <see cref="T:System.EventArgs" /> for <see cref="E:Microsoft.AspNetCore.Components.NavigationManager.LocationChanged" />.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs" />.
            </summary>
            <param name="location">The location.</param>
            <param name="isNavigationIntercepted">A value that determines if navigation for the link was intercepted.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs.Location">
            <summary>
            Gets the changed location.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs.IsNavigationIntercepted">
            <summary>
            Gets a value that determines if navigation for the link was intercepted.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Routing.Router">
            <summary>
            A component that supplies route data corresponding to the current navigation state.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Routing.Router.AppAssembly">
            <summary>
            Gets or sets the assembly that should be searched for components matching the URI.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Routing.Router.AdditionalAssemblies">
            <summary>
            Gets or sets a collection of additional assemblies that should be searched for components
            that can match URIs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Routing.Router.NotFound">
            <summary>
            Gets or sets the content to display when no match is found for the requested route.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Routing.Router.Found">
            <summary>
            Gets or sets the content to display when a match is found for the requested route.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Routing.Router.Attach(Microsoft.AspNetCore.Components.RenderHandle)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Routing.Router.SetParametersAsync(Microsoft.AspNetCore.Components.ParameterView)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Routing.Router.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Routing.TypeRouteConstraint`1">
            <summary>
            A route constraint that requires the value to be parseable as a specified type.
            </summary>
            <typeparam name="T">The type to which the value must be parseable.</typeparam>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RouteData">
            <summary>
            Describes information determined during routing that specifies
            the page to be displayed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RouteData.#ctor(System.Type,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.RouteData"/>.
            </summary>
            <param name="pageType">The type of the page matching the route, which must implement <see cref="T:Microsoft.AspNetCore.Components.IComponent"/>.</param>
            <param name="routeValues">The route parameter values extracted from the matched route.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RouteData.PageType">
            <summary>
            Gets the type of the page matching the route.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.RouteData.RouteValues">
            <summary>
            Gets route parameter values extracted from the matched route.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.RouteTableFactory">
            <summary>
            Resolves components for an application.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.RouteTableFactory.RouteComparison(Microsoft.AspNetCore.Components.Routing.RouteEntry,Microsoft.AspNetCore.Components.Routing.RouteEntry)">
             <summary>
             Route precedence algorithm.
             We collect all the routes and sort them from most specific to
             less specific. The specificity of a route is given by the specificity
             of its segments and the position of those segments in the route.
             * A literal segment is more specific than a parameter segment.
             * A parameter segment with more constraints is more specific than one with fewer constraints
             * Segment earlier in the route are evaluated before segments later in the route.
             For example:
             /Literal is more specific than /Parameter
             /Route/With/{parameter} is more specific than /{multiple}/With/{parameters}
             /Product/{id:int} is more specific than /Product/{id}
            
             Routes can be ambiguous if:
             They are composed of literals and those literals have the same values (case insensitive)
             They are composed of a mix of literals and parameters, in the same relative order and the
             literals have the same values.
             For example:
             * /literal and /Literal
             /{parameter}/literal and /{something}/literal
             /{parameter:constraint}/literal and /{something:constraint}/literal
            
             To calculate the precedence we sort the list of routes as follows:
             * Shorter routes go first.
             * A literal wins over a parameter in precedence.
             * For literals with different values (case insensitive) we choose the lexical order
             * For parameters with different numbers of constraints, the one with more wins
             If we get to the end of the comparison routing we've detected an ambiguous pair of routes.
             </summary>
        </member>
    </members>
</doc>
