{"runtimeTarget": {"name": ".NETCoreApp,Version=v2.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v2.1": {"package/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CommunicationUtilities": "16.3.0", "Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.CrossPlatEngine": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.TestPlatform.TestHostRuntimeProvider": "16.3.0", "Microsoft.TestPlatform.Utilities": "16.3.0", "Microsoft.TestPlatform.VsTestConsole.TranslationLayer": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Client": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Common": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger": "16.3.0", "vstest.console": "16.3.0"}, "runtime": {"package.dll": {}}}, "Microsoft.CSharp/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}}, "Microsoft.Extensions.DependencyModel/3.0.0-preview4-27615-11": {"dependencies": {"System.Memory": "4.5.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Microsoft.Extensions.FileSystemGlobbing/1.1.1": {"runtime": {"lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.30427"}}}, "Microsoft.Win32.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "Microsoft.Win32.Registry/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0"}}, "Newtonsoft.Json/9.0.1": {"dependencies": {"Microsoft.CSharp": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}, "runtime": {"lib/netstandard1.0/Newtonsoft.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.1.19813"}}}, "NuGet.Frameworks/5.0.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "5.0.0.6", "fileVersion": "5.0.0.5923"}}}, "runtime.native.System/4.0.0": {}, "System.Collections/4.0.11": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Diagnostics.Debug/4.0.11": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Diagnostics.FileVersionInfo/4.0.0": {"dependencies": {"System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}}, "System.Diagnostics.Process/4.1.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.0.1", "Microsoft.Win32.Registry": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10", "runtime.native.System": "4.0.0"}}, "System.Diagnostics.TextWriterTraceListener/4.0.0": {"dependencies": {"System.Diagnostics.TraceSource": "4.0.0", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}}, "System.Diagnostics.Tools/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Diagnostics.TraceSource/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Globalization/4.0.11": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.IO/4.1.0": {"dependencies": {"System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem/4.0.1": {"dependencies": {"System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Linq/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Linq.Expressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Memory/4.5.2": {}, "System.ObjectModel/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}}, "System.Reflection/4.1.0": {"dependencies": {"System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit/4.0.1": {"dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.Lightweight/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Extensions/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Resources.ResourceManager/4.0.1": {"dependencies": {"System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Runtime/4.1.0": {}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "4.0.4.1", "fileVersion": "4.6.26919.2"}}}, "System.Runtime.Extensions/4.1.0": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Runtime.Handles/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Runtime.InteropServices/4.1.0": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Runtime.Loader/4.0.0": {"dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Runtime.Serialization.Primitives/4.1.1": {"dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Security.Claims/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Principal": "4.0.1"}}, "System.Security.Principal/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Security.Principal.Windows/4.0.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Principal": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}}, "System.Text.Encoding/4.0.11": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Text.Encoding.Extensions/4.0.11": {"dependencies": {"System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}}, "System.Text.RegularExpressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Threading/4.0.11": {"dependencies": {"System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}}, "System.Threading.Tasks/4.0.11": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Threading.Tasks.Extensions/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}}, "System.Threading.Thread/4.0.0": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Threading.ThreadPool/4.0.10": {"dependencies": {"System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Xml.ReaderWriter/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0"}}, "System.Xml.XDocument/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "Microsoft.TestPlatform.CommunicationUtilities/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Common": "16.3.0", "Newtonsoft.Json": "9.0.1"}, "runtime": {"Microsoft.TestPlatform.CommunicationUtilities.dll": {}}, "resources": {"cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.CoreUtilities/16.3.0": {"dependencies": {"Microsoft.TestPlatform.PlatformAbstractions": "16.3.0", "System.Diagnostics.FileVersionInfo": "4.0.0"}, "runtime": {"Microsoft.TestPlatform.CoreUtilities.dll": {}}, "resources": {"cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.CrossPlatEngine/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CommunicationUtilities": "16.3.0", "Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.TestPlatform.Utilities": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Common": "16.3.0"}, "runtime": {"Microsoft.TestPlatform.CrossPlatEngine.dll": {}}, "resources": {"cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.ObjectModel/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CoreUtilities": "16.3.0", "NuGet.Frameworks": "5.0.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resources": {"cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.PlatformAbstractions/16.3.0": {"dependencies": {"System.Diagnostics.Process": "4.1.0", "System.Diagnostics.TextWriterTraceListener": "4.0.0", "System.Diagnostics.TraceSource": "4.0.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Loader": "4.0.0", "System.Threading.Thread": "4.0.0"}, "runtime": {"Microsoft.TestPlatform.PlatformAbstractions.dll": {}}}, "Microsoft.TestPlatform.TestHostRuntimeProvider/16.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0-preview4-27615-11", "Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.TestPlatform.PlatformAbstractions": "16.3.0", "Newtonsoft.Json": "9.0.1", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "runtime": {"Microsoft.TestPlatform.TestHostRuntimeProvider.dll": {}}, "resources": {"cs/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "cs"}, "de/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "de"}, "es/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "es"}, "fr/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "fr"}, "it/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "it"}, "ja/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "ja"}, "ko/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "ko"}, "pl/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "ru"}, "tr/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.TestPlatform.TestHostRuntimeProvider.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.Utilities/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0"}, "runtime": {"Microsoft.TestPlatform.Utilities.dll": {}}, "resources": {"cs/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "cs"}, "de/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "de"}, "es/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "es"}, "fr/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "fr"}, "it/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "it"}, "ja/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "ja"}, "ko/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "ko"}, "pl/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "ru"}, "tr/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.TestPlatform.Utilities.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.VsTestConsole.TranslationLayer/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CommunicationUtilities": "16.3.0", "Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Common": "16.3.0"}, "runtime": {"Microsoft.TestPlatform.VsTestConsole.TranslationLayer.dll": {}}, "resources": {"cs/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "cs"}, "de/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "de"}, "es/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "es"}, "fr/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "fr"}, "it/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "it"}, "ja/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "ja"}, "ko/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "ko"}, "pl/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "ru"}, "tr/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.TestPlatform.VsTestConsole.TranslationLayer.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.TestPlatform.Client/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CommunicationUtilities": "16.3.0", "Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.CrossPlatEngine": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Common": "16.3.0"}, "runtime": {"Microsoft.VisualStudio.TestPlatform.Client.dll": {}}, "resources": {"cs/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "cs"}, "de/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "de"}, "es/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "es"}, "fr/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "fr"}, "it/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "it"}, "ja/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "ja"}, "ko/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "ko"}, "pl/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "ru"}, "tr/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.VisualStudio.TestPlatform.Client.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.TestPlatform.Common/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.TestPlatform.Utilities": "16.3.0"}, "runtime": {"Microsoft.VisualStudio.TestPlatform.Common.dll": {}}, "resources": {"cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0"}, "runtime": {"Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.dll": {}}, "resources": {"cs/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "cs"}, "de/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "de"}, "es/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "es"}, "fr/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "fr"}, "it/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "it"}, "ja/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "ja"}, "ko/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "ko"}, "pl/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "ru"}, "tr/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger/16.3.0": {"dependencies": {"Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "System.Security.Principal.Windows": "4.0.0"}, "runtime": {"Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.dll": {}}, "resources": {"cs/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "cs"}, "de/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "de"}, "es/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "es"}, "fr/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "fr"}, "it/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "it"}, "ja/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "ja"}, "ko/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "ko"}, "pl/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "ru"}, "tr/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "vstest.console/16.3.0": {"dependencies": {"Microsoft.Extensions.FileSystemGlobbing": "1.1.1", "Microsoft.TestPlatform.CommunicationUtilities": "16.3.0", "Microsoft.TestPlatform.CoreUtilities": "16.3.0", "Microsoft.TestPlatform.CrossPlatEngine": "16.3.0", "Microsoft.TestPlatform.ObjectModel": "16.3.0", "Microsoft.TestPlatform.PlatformAbstractions": "16.3.0", "Microsoft.TestPlatform.Utilities": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Client": "16.3.0", "Microsoft.VisualStudio.TestPlatform.Common": "16.3.0"}, "runtime": {"vstest.console.dll": {}}, "resources": {"cs/vstest.console.resources.dll": {"locale": "cs"}, "de/vstest.console.resources.dll": {"locale": "de"}, "es/vstest.console.resources.dll": {"locale": "es"}, "fr/vstest.console.resources.dll": {"locale": "fr"}, "it/vstest.console.resources.dll": {"locale": "it"}, "ja/vstest.console.resources.dll": {"locale": "ja"}, "ko/vstest.console.resources.dll": {"locale": "ko"}, "pl/vstest.console.resources.dll": {"locale": "pl"}, "pt-BR/vstest.console.resources.dll": {"locale": "pt-BR"}, "ru/vstest.console.resources.dll": {"locale": "ru"}, "tr/vstest.console.resources.dll": {"locale": "tr"}, "zh-Hans/vstest.console.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/vstest.console.resources.dll": {"locale": "zh-Han<PERSON>"}}}}}, "libraries": {"package/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.CSharp/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-17h8b5mXa87XYKrrVqdgZ38JefSUqLChUQpXgSnpzsM0nDOhE40FTeNWOJ/YmySGV6tG6T8+hjz6vxbknHJr6A==", "path": "microsoft.csharp/4.0.1", "hashPath": "microsoft.csharp.4.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.0.0-preview4-27615-11": {"type": "package", "serviceable": true, "sha512": "sha512-a8EWS55Bh9LrTa1oDg59/7rmkAOhZlU12X8LJN7+ivEFMLdg+cc2YcAUaUVf3qpYRh6yzZuhFo8LHY8IrU5e1Q==", "path": "microsoft.extensions.dependencymodel/3.0.0-preview4-27615-11", "hashPath": "microsoft.extensions.dependencymodel.3.0.0-preview4-27615-11.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-aSnRFPBR2uHhhQgsUXaryw6BBYFGwIN/743L7lloxydcTTJylpSZjCjd8/w62e2xzyypCVa7fkc6IRzkUEIwyg==", "path": "microsoft.extensions.filesystemglobbing/1.1.1", "hashPath": "microsoft.extensions.filesystemglobbing.1.1.1.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "path": "microsoft.win32.primitives/4.0.1", "hashPath": "microsoft.win32.primitives.4.0.1.nupkg.sha512"}, "Microsoft.Win32.Registry/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q+eLtROUAQ3OxYA5mpQrgyFgzLQxIyrfT2eLpYX5IEPlHmIio2nh4F5bgOaQoGOV865kFKZZso9Oq9RlazvXtg==", "path": "microsoft.win32.registry/4.0.0", "hashPath": "microsoft.win32.registry.4.0.0.nupkg.sha512"}, "Newtonsoft.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw==", "path": "newtonsoft.json/9.0.1", "hashPath": "newtonsoft.json.9.0.1.nupkg.sha512"}, "NuGet.Frameworks/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c5JVjuVAm4f7E9Vj+v09Z9s2ZsqFDjBpcsyS3M9xRo0bEdm/LVZSzLxxNvfvAwRiiE8nwe1h2G4OwiwlzFKXlA==", "path": "nuget.frameworks/5.0.0", "hashPath": "nuget.frameworks.5.0.0.nupkg.sha512"}, "runtime.native.System/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "path": "runtime.native.system/4.0.0", "hashPath": "runtime.native.system.4.0.0.nupkg.sha512"}, "System.Collections/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "path": "system.collections/4.0.11", "hashPath": "system.collections.4.0.11.nupkg.sha512"}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "path": "system.diagnostics.debug/4.0.11", "hashPath": "system.diagnostics.debug.4.0.11.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qjF74OTAU+mRhLaL4YSfiWy3vj6T3AOz8AW37l5zCwfbBfj0k7E94XnEsRaf2TnhE/7QaV6Hvqakoy2LoV8MVg==", "path": "system.diagnostics.fileversioninfo/4.0.0", "hashPath": "system.diagnostics.fileversioninfo.4.0.0.nupkg.sha512"}, "System.Diagnostics.Process/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpVZ5bnlSs3tTeJ6jYyDJEIa6tavhAd88lxq1zbYhkkCu0Pno2+gHXcvZcoygq2d8JxW3gojXqNJMTAshduqZA==", "path": "system.diagnostics.process/4.1.0", "hashPath": "system.diagnostics.process.4.1.0.nupkg.sha512"}, "System.Diagnostics.TextWriterTraceListener/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w36Dr8yKy8xP150qPANe7Td+/zOI3G62ImRcHDIEW+oUXUuTKZHd4DHmqRx5+x8RXd85v3tXd1uhNTfsr+yxjA==", "path": "system.diagnostics.textwritertracelistener/4.0.0", "hashPath": "system.diagnostics.textwritertracelistener.4.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "path": "system.diagnostics.tools/4.0.1", "hashPath": "system.diagnostics.tools.4.0.1.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6WVCczFZKXwpWpzd/iJkYnsmWTSFFiU24Xx/YdHXBcu+nFI/ehTgeqdJQFbtRPzbrO3KtRNjvkhtj4t5/WwWsA==", "path": "system.diagnostics.tracesource/4.0.0", "hashPath": "system.diagnostics.tracesource.4.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "path": "system.dynamic.runtime/4.0.11", "hashPath": "system.dynamic.runtime.4.0.11.nupkg.sha512"}, "System.Globalization/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "path": "system.globalization/4.0.11", "hashPath": "system.globalization.4.0.11.nupkg.sha512"}, "System.IO/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "path": "system.io/4.1.0", "hashPath": "system.io.4.1.0.nupkg.sha512"}, "System.IO.FileSystem/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "path": "system.io.filesystem/4.0.1", "hashPath": "system.io.filesystem.4.0.1.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "path": "system.io.filesystem.primitives/4.0.1", "hashPath": "system.io.filesystem.primitives.4.0.1.nupkg.sha512"}, "System.Linq/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "path": "system.linq/4.1.0", "hashPath": "system.linq.4.1.0.nupkg.sha512"}, "System.Linq.Expressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "path": "system.linq.expressions/4.1.0", "hashPath": "system.linq.expressions.4.1.0.nupkg.sha512"}, "System.Memory/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-fvq1GNmUFwbKv+aLVYYdgu/+gc8Nu9oFujOxIjPrsf+meis9JBzTPDL6aP/eeGOz9yPj6rRLUbOjKMpsMEWpNg==", "path": "system.memory/4.5.2", "hashPath": "system.memory.4.5.2.nupkg.sha512"}, "System.ObjectModel/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "path": "system.objectmodel/4.0.12", "hashPath": "system.objectmodel.4.0.12.nupkg.sha512"}, "System.Reflection/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "path": "system.reflection/4.1.0", "hashPath": "system.reflection.4.1.0.nupkg.sha512"}, "System.Reflection.Emit/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "path": "system.reflection.emit/4.0.1", "hashPath": "system.reflection.emit.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "path": "system.reflection.emit.ilgeneration/4.0.1", "hashPath": "system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "path": "system.reflection.emit.lightweight/4.0.1", "hashPath": "system.reflection.emit.lightweight.4.0.1.nupkg.sha512"}, "System.Reflection.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "path": "system.reflection.extensions/4.0.1", "hashPath": "system.reflection.extensions.4.0.1.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "path": "system.reflection.primitives/4.0.1", "hashPath": "system.reflection.primitives.4.0.1.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "path": "system.resources.resourcemanager/4.0.1", "hashPath": "system.resources.resourcemanager.4.0.1.nupkg.sha512"}, "System.Runtime/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g==", "path": "system.runtime/4.1.0", "hashPath": "system.runtime.4.1.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Runtime.Extensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "path": "system.runtime.extensions/4.1.0", "hashPath": "system.runtime.extensions.4.1.0.nupkg.sha512"}, "System.Runtime.Handles/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "path": "system.runtime.handles/4.0.1", "hashPath": "system.runtime.handles.4.0.1.nupkg.sha512"}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "path": "system.runtime.interopservices/4.1.0", "hashPath": "system.runtime.interopservices.4.1.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4UN78GOVU/mbDFcXkEWtetJT/sJ0yic2gGk1HSlSpWI0TDf421xnrZTDZnwNBapk1GQeYN7U1lTj/aQB1by6ow==", "path": "system.runtime.loader/4.0.0", "hashPath": "system.runtime.loader.4.0.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg==", "path": "system.runtime.serialization.primitives/4.1.1", "hashPath": "system.runtime.serialization.primitives.4.1.1.nupkg.sha512"}, "System.Security.Claims/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4Jlp0OgJLS/Voj1kyFP6MJlIYp3crgfH8kNQk2p7+4JYfc1aAmh9PZyAMMbDhuoolGNtux9HqSOazsioRiDvCw==", "path": "system.security.claims/4.0.1", "hashPath": "system.security.claims.4.0.1.nupkg.sha512"}, "System.Security.Principal/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-On+SKhXY5rzxh/S8wlH1Rm0ogBlu7zyHNxeNBiXauNrhHRXAe9EuX8Yl5IOzLPGU5Z4kLWHMvORDOCG8iu9hww==", "path": "system.security.principal/4.0.1", "hashPath": "system.security.principal.4.0.1.nupkg.sha512"}, "System.Security.Principal.Windows/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iFx15AF3RMEPZn3COh8+Bb2Thv2zsmLd93RchS1b8Mj5SNYeGqbYNCSn5AES1+gq56p4ujGZPrl0xN7ngkXOHg==", "path": "system.security.principal.windows/4.0.0", "hashPath": "system.security.principal.windows.4.0.0.nupkg.sha512"}, "System.Text.Encoding/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA==", "path": "system.text.encoding/4.0.11", "hashPath": "system.text.encoding.4.0.11.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "path": "system.text.encoding.extensions/4.0.11", "hashPath": "system.text.encoding.extensions.4.0.11.nupkg.sha512"}, "System.Text.RegularExpressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g==", "path": "system.text.regularexpressions/4.1.0", "hashPath": "system.text.regularexpressions.4.1.0.nupkg.sha512"}, "System.Threading/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "path": "system.threading/4.0.11", "hashPath": "system.threading.4.0.11.nupkg.sha512"}, "System.Threading.Tasks/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "path": "system.threading.tasks/4.0.11", "hashPath": "system.threading.tasks.4.0.11.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pH4FZDsZQ/WmgJtN4LWYmRdJAEeVkyriSwrv2Teoe5FOU0Yxlb6II6GL8dBPOfRmutHGATduj3ooMt7dJ2+i+w==", "path": "system.threading.tasks.extensions/4.0.0", "hashPath": "system.threading.tasks.extensions.4.0.0.nupkg.sha512"}, "System.Threading.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIdJqDXlOr5W9zeqFErLw3dsOsiShSCYtF9SEHitACycmvNvY8odf9kiKvp6V7aibc8C4HzzNBkWXjyfn7plbQ==", "path": "system.threading.thread/4.0.0", "hashPath": "system.threading.thread.4.0.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-IMXgB5Vf/5Qw1kpoVgJMOvUO1l32aC+qC3OaIZjWJOjvcxuxNWOK2ZTWWYXfij22NHxT2j1yWX5vlAeQWld9vA==", "path": "system.threading.threadpool/4.0.10", "hashPath": "system.threading.threadpool.4.0.10.nupkg.sha512"}, "System.Xml.ReaderWriter/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg==", "path": "system.xml.readerwriter/4.0.11", "hashPath": "system.xml.readerwriter.4.0.11.nupkg.sha512"}, "System.Xml.XDocument/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "path": "system.xml.xdocument/4.0.11", "hashPath": "system.xml.xdocument.4.0.11.nupkg.sha512"}, "Microsoft.TestPlatform.CommunicationUtilities/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.CoreUtilities/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.CrossPlatEngine/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.ObjectModel/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.PlatformAbstractions/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.TestHostRuntimeProvider/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.Utilities/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.TestPlatform.VsTestConsole.TranslationLayer/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.VisualStudio.TestPlatform.Client/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.VisualStudio.TestPlatform.Common/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.VisualStudio.TestPlatform.Extensions.Html.TestLogger/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.VisualStudio.TestPlatform.Extensions.Trx.TestLogger/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "vstest.console/16.3.0": {"type": "project", "serviceable": false, "sha512": ""}}}